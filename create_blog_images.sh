#!/bin/bash

# إنشاء صور نموذجية للمدونة

# إنشاء الصور باستخدام curl وplaceholder service
echo "إنشاء صور نموذجية للمدونة..."

# صور للمقالات
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post1.jpg" "https://picsum.photos/800/600?random=1" 2>/dev/null
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post2.jpg" "https://picsum.photos/800/600?random=2" 2>/dev/null
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post3.jpg" "https://picsum.photos/800/600?random=3" 2>/dev/null
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post4.jpg" "https://picsum.photos/800/600?random=4" 2>/dev/null
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post5.jpg" "https://picsum.photos/800/600?random=5" 2>/dev/null
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post6.jpg" "https://picsum.photos/800/600?random=6" 2>/dev/null
curl -o "/Users/<USER>/Herd/makkah-gold-jewelry/storage/app/public/blog/post7.jpg" "https://picsum.photos/800/600?random=7" 2>/dev/null

echo "تم إنشاء الصور بنجاح!"
