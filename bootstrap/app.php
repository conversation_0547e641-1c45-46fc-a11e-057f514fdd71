<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Register the SetLocale middleware
        $middleware->web([\App\Http\Middleware\SetLocale::class]);

        // Register the SetCurrency middleware
        $middleware->web([\App\Http\Middleware\SetCurrency::class]);

        // Register other custom middleware
        $middleware->web([\App\Http\Middleware\CheckSiteStatus::class]);
        $middleware->web([\App\Http\Middleware\CheckDisplayOnlyMode::class]);

        // Register API middleware
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'permission' => \App\Http\Middleware\CheckPermission::class,
            'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
            'check.registration' => \App\Http\Middleware\CheckRegistrationEnabled::class,
            'super.admin.protection' => \App\Http\Middleware\SuperAdminProtectionMiddleware::class,
        ]);

        // Register CORS middleware
        $middleware->api([
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->withProviders([
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        App\Providers\Filament\AdminPanelProvider::class,
        App\Providers\FilamentRolesServiceProvider::class,
    ])
    ->create();
