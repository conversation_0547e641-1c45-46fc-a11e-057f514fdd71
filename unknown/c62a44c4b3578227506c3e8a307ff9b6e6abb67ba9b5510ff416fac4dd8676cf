<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use App\Traits\HasPagePermissionFiltering;
use App\Services\FilamentPermissionService;

class DashboardPage extends Page
{
    use HasPagePermissionFiltering;

    protected static ?string $slug = 'dashboard';

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static ?string $navigationLabel = 'لوحة التحكم';

    protected static ?string $title = 'لوحة التحكم';

    protected static ?int $navigationSort = -2;

    protected static string $view = 'filament.pages.dashboard';

    protected function getHeaderWidgets(): array
    {
        $widgets = [
            \App\Filament\Widgets\MainStatsOverview::class,
            \App\Filament\Widgets\ContentStatsWidget::class,
            \App\Filament\Widgets\EngagementStatsWidget::class,
            \App\Filament\Widgets\MetalPricesWidget::class,
            \App\Filament\Widgets\RecentOrdersWidget::class,
            \App\Filament\Widgets\UpcomingAppointmentsWidget::class,
            \App\Filament\Widgets\SalesChartWidget::class,
            \App\Filament\Widgets\SystemStatusWidget::class,
        ];

        return FilamentPermissionService::filterWidgets($widgets);
    }

    protected function getFooterWidgets(): array
    {
        // لا نحتاج Footer Widgets لأن جميع الويدجت في Header
        return [];
    }
}
