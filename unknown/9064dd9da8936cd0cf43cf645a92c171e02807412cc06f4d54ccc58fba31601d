<x-filament-widgets::widget>
    <x-filament::section>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                <x-heroicon-o-cog-6-tooth class="h-6 w-6 text-red-500" />
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    حالة النظام
                </h3>
            </div>

            <!-- System Features Status -->
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-white">ميزات النظام</h4>
                
                <div class="grid grid-cols-2 gap-3">
                    <!-- Maintenance Mode -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <x-heroicon-o-wrench-screwdriver class="h-4 w-4 text-gray-500" />
                            <span class="text-sm font-medium">وضع الصيانة</span>
                        </div>
                        <div class="flex items-center">
                            @if($maintenance_mode)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    مفعل
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    معطل
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- User Registration -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <x-heroicon-o-user-plus class="h-4 w-4 text-gray-500" />
                            <span class="text-sm font-medium">تسجيل المستخدمين</span>
                        </div>
                        <div class="flex items-center">
                            @if($user_registration)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    مفعل
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    معطل
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Online Payments -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <x-heroicon-o-credit-card class="h-4 w-4 text-gray-500" />
                            <span class="text-sm font-medium">الدفع الإلكتروني</span>
                        </div>
                        <div class="flex items-center">
                            @if($online_payments)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    مفعل
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    معطل
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Appointment Booking -->
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <x-heroicon-o-calendar class="h-4 w-4 text-gray-500" />
                            <span class="text-sm font-medium">حجز المواعيد</span>
                        </div>
                        <div class="flex items-center">
                            @if($appointment_booking)
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    مفعل
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    معطل
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Health -->
            <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-white">صحة النظام</h4>
                
                <div class="space-y-3">
                    <!-- Database Status -->
                    <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <x-heroicon-o-circle-stack class="h-4 w-4 text-gray-500" />
                            <span class="text-sm font-medium">قاعدة البيانات</span>
                        </div>
                        <div class="flex items-center">
                            @if($database_status['status'] === 'connected')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    {{ $database_status['message'] }}
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    {{ $database_status['message'] }}
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Cache Status -->
                    <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <x-heroicon-o-bolt class="h-4 w-4 text-gray-500" />
                            <span class="text-sm font-medium">الكاش</span>
                        </div>
                        <div class="flex items-center">
                            @if($cache_status['status'] === 'working')
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    {{ $cache_status['message'] }}
                                </span>
                            @else
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                    {{ $cache_status['message'] }}
                                </span>
                            @endif
                        </div>
                    </div>

                    <!-- Storage Usage -->
                    @if(isset($storage_usage['percentage']))
                        <div class="p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                    <x-heroicon-o-server class="h-4 w-4 text-gray-500" />
                                    <span class="text-sm font-medium">استخدام التخزين</span>
                                </div>
                                <span class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ $storage_usage['percentage'] }}%
                                </span>
                            </div>
                            
                            <div class="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                                <div class="h-2 rounded-full {{ $storage_usage['status'] === 'critical' ? 'bg-red-600' : ($storage_usage['status'] === 'warning' ? 'bg-yellow-600' : 'bg-green-600') }}" 
                                     style="width: {{ $storage_usage['percentage'] }}%"></div>
                            </div>
                            
                            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                <span>مستخدم: {{ $storage_usage['used'] }}</span>
                                <span>متاح: {{ $storage_usage['free'] }}</span>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Last Update -->
            <div class="text-center text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-3">
                آخر تحديث: {{ now()->format('Y-m-d H:i:s') }}
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
