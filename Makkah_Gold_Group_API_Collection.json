{
    "info": {
        "_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "name": "Makkah Gold Group Jewelry API - مجوهرات مكة جولد جروب",
        "description": {
            "content": "# مجوهرات مكة جولد جروب - واجهة برمجة التطبيقات\n\n## نظرة عامة\nواجهة برمجة التطبيقات الشاملة لتطبيق مجوهرات مكة جولد جروب، توفر جميع الخدمات المطلوبة للتطبيق المحمول بما في ذلك:\n\n- **أسعار المعادن المباشرة**: جلب أسعار الذهب والفضة الحالية والتاريخية\n- **إدارة المنتجات**: عرض المنتجات والفئات مع التصفية والبحث\n- **حاسبات متخصصة**: حاسبة الزكاة وحاسبة قيمة المجوهرات\n- **إعدادات التطبيق**: معلومات الشركة والاتصال والسياسات\n- **المحتوى الرئيسي**: الشرائح والمحتوى التفاعلي\n\n## ✨ مجموعة موحدة مع متغيرات البيئة\nهذه المجموعة تحتوي على جميع متغيرات البيئة المطلوبة مدمجة داخلياً، مما يلغي الحاجة لاستيراد ملفات منفصلة. ما عليك سوى استيراد هذا الملف الواحد وستكون جاهزاً للاستخدام فوراً!\n\n## المتطلبات التقنية\n- **Base URL**: https://05f9-2c0f-fc89-8032-f4e4-8dc9-9359-8e83-9d45.ngrok-free.app\n- **API Version**: v1\n- **Content-Type**: application/json\n- **Language**: Arabic (ar) primary\n- **Authentication**: Not required for public endpoints\n- **Setup**: استيراد ملف واحد فقط - لا حاجة لملفات بيئة منفصلة\n\n## بنية الاستجابة\nجميع الاستجابات تتبع النمط التالي:\n```json\n{\n  \"success\": true,\n  \"message\": \"رسالة نجاح العملية\",\n  \"data\": {},\n  \"meta\": {\n    \"current_page\": 1,\n    \"per_page\": 12,\n    \"total\": 50\n  }\n}\n```\n\n## معالجة الأخطاء\nفي حالة الخطأ، ستحصل على:\n```json\n{\n  \"success\": false,\n  \"message\": \"رسالة الخطأ\",\n  \"errors\": {}\n}\n```",
            "type": "text/markdown"
        },
        "version": "2.0.0",
        "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    },
    "item": [
        {
            "name": "📱 App Settings APIs - واجهات إعدادات التطبيق",
            "description": "جميع الواجهات المتعلقة بإعدادات التطبيق ومعلومات الشركة",
            "item": [
                {
                    "name": "Get All App Settings - جلب جميع إعدادات التطبيق",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Response time is less than 2000ms\", function () {",
                                    "    pm.expect(pm.response.responseTime).to.be.below(2000);",
                                    "});",
                                    "",
                                    "pm.test(\"Response has required structure\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData).to.have.property('success');",
                                    "    pm.expect(jsonData).to.have.property('message');",
                                    "    pm.expect(jsonData).to.have.property('data');",
                                    "});",
                                    "",
                                    "pm.test(\"API call was successful\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.success).to.be.true;",
                                    "});",
                                    "",
                                    "pm.test(\"App settings data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('app_info');",
                                    "    pm.expect(jsonData.data).to.have.property('contact_info');",
                                    "    pm.expect(jsonData.data).to.have.property('social_links');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/settings",
                            "host": ["{{full_api_url}}"],
                            "path": ["settings"]
                        },
                        "description": "جلب جميع إعدادات التطبيق بما في ذلك معلومات الشركة، بيانات الاتصال، روابط وسائل التواصل الاجتماعي، والإعدادات العامة للتطبيق."
                    },
                    "response": []
                },
                {
                    "name": "Get Contact Information - جلب معلومات الاتصال",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Contact info is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('contact_info');",
                                    "    pm.expect(jsonData.data.contact_info).to.have.property('email');",
                                    "    pm.expect(jsonData.data.contact_info).to.have.property('phone');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/settings/contact",
                            "host": ["{{full_api_url}}"],
                            "path": ["settings", "contact"]
                        },
                        "description": "جلب معلومات الاتصال فقط (البريد الإلكتروني، الهاتف، واتساب، العنوان) للاستخدام في صفحة تواصل معنا."
                    },
                    "response": []
                },
                {
                    "name": "Get Privacy Policy - جلب سياسة الخصوصية",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Privacy settings are present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('privacy_settings');",
                                    "    pm.expect(jsonData.data.privacy_settings).to.have.property('privacy_policy');",
                                    "    pm.expect(jsonData.data.privacy_settings).to.have.property('terms_conditions');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/settings/privacy",
                            "host": ["{{full_api_url}}"],
                            "path": ["settings", "privacy"]
                        },
                        "description": "جلب النصوص القانونية (سياسة الخصوصية، شروط الاستخدام، سياسة الإرجاع) للاستخدام في الصفحات القانونية."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "🏪 Stores APIs - واجهات الفروع",
            "description": "جميع الواجهات المتعلقة بفروع المتاجر ومواقعها",
            "item": [
                {
                    "name": "Get All Stores - جلب جميع الفروع",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Response time is less than 2000ms\", function () {",
                                    "    pm.expect(pm.response.responseTime).to.be.below(2000);",
                                    "});",
                                    "",
                                    "pm.test(\"Response has required structure\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData).to.have.property('success');",
                                    "    pm.expect(jsonData).to.have.property('message');",
                                    "    pm.expect(jsonData).to.have.property('data');",
                                    "});",
                                    "",
                                    "pm.test(\"API call was successful\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.success).to.be.true;",
                                    "});",
                                    "",
                                    "pm.test(\"Stores data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "    if (jsonData.data.length > 0) {",
                                    "        const store = jsonData.data[0];",
                                    "        pm.expect(store).to.have.property('id');",
                                    "        pm.expect(store).to.have.property('name');",
                                    "        pm.expect(store).to.have.property('address');",
                                    "        pm.expect(store).to.have.property('phone');",
                                    "        pm.expect(store).to.have.property('latitude');",
                                    "        pm.expect(store).to.have.property('longitude');",
                                    "    }",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/stores",
                            "host": ["{{full_api_url}}"],
                            "path": ["stores"]
                        },
                        "description": "جلب جميع الفروع النشطة مع معلومات كاملة عن كل فرع بما في ذلك الاسم، العنوان، الهاتف، ساعات العمل، والموقع الجغرافي."
                    },
                    "response": []
                },
                {
                    "name": "Get Store Details - جلب تفاصيل فرع محدد",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Store details are present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('object');",
                                    "    pm.expect(jsonData.data).to.have.property('id');",
                                    "    pm.expect(jsonData.data).to.have.property('name');",
                                    "    pm.expect(jsonData.data).to.have.property('address');",
                                    "    pm.expect(jsonData.data).to.have.property('working_hours');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/stores/{{store_id}}",
                            "host": ["{{full_api_url}}"],
                            "path": ["stores", "{{store_id}}"]
                        },
                        "description": "جلب تفاصيل فرع محدد بمعرفه الفريد مع جميع المعلومات التفصيلية."
                    },
                    "response": []
                },
                {
                    "name": "Find Nearby Stores - البحث عن الفروع القريبة",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Nearby stores data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "    if (jsonData.data.length > 0) {",
                                    "        const store = jsonData.data[0];",
                                    "        pm.expect(store).to.have.property('distance');",
                                    "        pm.expect(store.distance).to.be.a('number');",
                                    "    }",
                                    "});",
                                    "",
                                    "pm.test(\"Meta information is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData).to.have.property('meta');",
                                    "    pm.expect(jsonData.meta).to.have.property('radius_km');",
                                    "    pm.expect(jsonData.meta).to.have.property('user_location');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "POST",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "Content-Type",
                                "value": "{{content_type}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"latitude\": 30.0444,\n    \"longitude\": 31.2357,\n    \"radius\": 50\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{full_api_url}}/stores/nearby",
                            "host": ["{{full_api_url}}"],
                            "path": ["stores", "nearby"]
                        },
                        "description": "البحث عن الفروع القريبة من موقع المستخدم باستخدام الإحداثيات الجغرافية. يحسب المسافة ويرتب النتائج حسب القرب."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "💰 Metal Prices APIs - واجهات أسعار المعادن",
            "description": "جميع الواجهات المتعلقة بأسعار المعادن الثمينة (ذهب وفضة)",
            "item": [
                {
                    "name": "Get Current Metal Prices - جلب أسعار المعادن الحالية",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Metal prices data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('object');",
                                    "    pm.expect(jsonData.data).to.have.property('gold');",
                                    "});",
                                    "",
                                    "pm.test(\"Gold prices have required structure\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    if (jsonData.data.gold && jsonData.data.gold.prices) {",
                                    "        pm.expect(jsonData.data.gold.prices).to.be.an('array');",
                                    "        if (jsonData.data.gold.prices.length > 0) {",
                                    "            pm.expect(jsonData.data.gold.prices[0]).to.have.property('purity');",
                                    "            pm.expect(jsonData.data.gold.prices[0]).to.have.property('price_per_gram');",
                                    "        }",
                                    "    }",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/metal-prices/current",
                            "host": ["{{full_api_url}}"],
                            "path": ["metal-prices", "current"]
                        },
                        "description": "جلب أحدث أسعار المعادن الثمينة (ذهب وفضة) بجميع العيارات المتاحة. يستخدم في الصفحة الرئيسية وصفحة الأسعار."
                    },
                    "response": []
                },
                {
                    "name": "Get Metal Price History - جلب تاريخ أسعار المعادن",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Price history data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/metal-prices/history?metal_type=gold&purity=21K&period=week",
                            "host": ["{{full_api_url}}"],
                            "path": ["metal-prices", "history"],
                            "query": [
                                {
                                    "key": "metal_type",
                                    "value": "gold",
                                    "description": "نوع المعدن (gold, silver)"
                                },
                                {
                                    "key": "purity",
                                    "value": "21K",
                                    "description": "درجة النقاء (24K, 21K, 18K, 14K للذهب - 925 للفضة)"
                                },
                                {
                                    "key": "period",
                                    "value": "week",
                                    "description": "الفترة الزمنية (day, week, month, year)"
                                }
                            ]
                        },
                        "description": "جلب تاريخ أسعار معدن محدد لفترة زمنية معينة. يستخدم في الرسوم البيانية وتحليل الاتجاهات."
                    },
                    "response": []
                },
                {
                    "name": "Get Metal Price Statistics - جلب إحصائيات أسعار المعادن",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Statistics data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('object');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/metal-prices/statistics?metal_type=gold&purity=21K&period=month",
                            "host": ["{{full_api_url}}"],
                            "path": ["metal-prices", "statistics"],
                            "query": [
                                {
                                    "key": "metal_type",
                                    "value": "gold",
                                    "description": "نوع المعدن (gold, silver)"
                                },
                                {
                                    "key": "purity",
                                    "value": "21K",
                                    "description": "درجة النقاء"
                                },
                                {
                                    "key": "period",
                                    "value": "month",
                                    "description": "الفترة الزمنية للإحصائيات"
                                }
                            ]
                        },
                        "description": "جلب إحصائيات أسعار معدن محدد (أعلى سعر، أقل سعر، المتوسط، نسبة التغيير) لفترة زمنية معينة."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "🛍️ Products APIs - واجهات المنتجات",
            "description": "جميع الواجهات المتعلقة بالمنتجات والفئات",
            "item": [
                {
                    "name": "Get All Products - جلب جميع المنتجات",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Products data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});",
                                    "",
                                    "pm.test(\"Product has required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    if (jsonData.data.length > 0) {",
                                    "        const product = jsonData.data[0];",
                                    "        pm.expect(product).to.have.property('id');",
                                    "        pm.expect(product).to.have.property('name_ar');",
                                    "        pm.expect(product).to.have.property('price');",
                                    "    }",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/products?page=1&per_page=12&category_id=1&search=خاتم",
                            "host": ["{{full_api_url}}"],
                            "path": ["products"],
                            "query": [
                                {
                                    "key": "page",
                                    "value": "1",
                                    "description": "رقم الصفحة"
                                },
                                {
                                    "key": "per_page",
                                    "value": "12",
                                    "description": "عدد المنتجات في الصفحة"
                                },
                                {
                                    "key": "category_id",
                                    "value": "1",
                                    "description": "معرف الفئة للتصفية"
                                },
                                {
                                    "key": "search",
                                    "value": "خاتم",
                                    "description": "كلمة البحث"
                                }
                            ]
                        },
                        "description": "جلب قائمة المنتجات مع إمكانية التصفية والبحث والتصفح. يدعم البحث بالاسم العربي والإنجليزي ورقم المنتج."
                    },
                    "response": []
                },
                {
                    "name": "Get Product Details - جلب تفاصيل منتج",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Product details are present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('object');",
                                    "    pm.expect(jsonData.data).to.have.property('id');",
                                    "    pm.expect(jsonData.data).to.have.property('name_ar');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/products/{{product_id}}",
                            "host": ["{{full_api_url}}"],
                            "path": ["products", "{{product_id}}"]
                        },
                        "description": "جلب تفاصيل منتج محدد مع جميع المعلومات والصور والمواصفات."
                    },
                    "response": []
                },
                {
                    "name": "Get Featured Products - جلب المنتجات المميزة",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Featured products data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/products/featured?limit=8",
                            "host": ["{{full_api_url}}"],
                            "path": ["products", "featured"],
                            "query": [
                                {
                                    "key": "limit",
                                    "value": "8",
                                    "description": "عدد المنتجات المميزة المطلوبة"
                                }
                            ]
                        },
                        "description": "جلب المنتجات المميزة للعرض في الصفحة الرئيسية والأقسام الخاصة."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "📂 Categories APIs - واجهات الفئات",
            "description": "جميع الواجهات المتعلقة بفئات المنتجات",
            "item": [
                {
                    "name": "Get All Categories - جلب جميع الفئات",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Categories data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});",
                                    "",
                                    "pm.test(\"Category has required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    if (jsonData.data.length > 0) {",
                                    "        const category = jsonData.data[0];",
                                    "        pm.expect(category).to.have.property('id');",
                                    "        pm.expect(category).to.have.property('name_ar');",
                                    "        pm.expect(category).to.have.property('products');",
                                    "    }",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/categories",
                            "host": ["{{full_api_url}}"],
                            "path": ["categories"]
                        },
                        "description": "جلب جميع فئات المنتجات مع آخر 4 منتجات لكل فئة. يستخدم في الصفحة الرئيسية وصفحة الفئات."
                    },
                    "response": []
                },
                {
                    "name": "Get Category Products - جلب منتجات فئة محددة",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Category products data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('object');",
                                    "    pm.expect(jsonData.data).to.have.property('products');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/categories/{{category_id}}/products?page=1&per_page=12",
                            "host": ["{{full_api_url}}"],
                            "path": [
                                "categories",
                                "{{category_id}}",
                                "products"
                            ],
                            "query": [
                                {
                                    "key": "page",
                                    "value": "1",
                                    "description": "رقم الصفحة"
                                },
                                {
                                    "key": "per_page",
                                    "value": "12",
                                    "description": "عدد المنتجات في الصفحة"
                                }
                            ]
                        },
                        "description": "جلب جميع منتجات فئة محددة مع إمكانية التصفح والتقسيم إلى صفحات."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "🧮 Calculator APIs - واجهات الحاسبات",
            "description": "جميع الواجهات المتعلقة بحاسبة الزكاة وحاسبة قيمة المجوهرات",
            "item": [
                {
                    "name": "Get Metal Types - جلب أنواع المعادن",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Metal types data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/metals/types",
                            "host": ["{{full_api_url}}"],
                            "path": ["metals", "types"]
                        },
                        "description": "جلب أنواع المعادن المتاحة (ذهب، فضة) للاستخدام في الحاسبات."
                    },
                    "response": []
                },
                {
                    "name": "Get Metal Purities - جلب درجات النقاء",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Metal purities data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/metals/purities/{{metal_type}}",
                            "host": ["{{full_api_url}}"],
                            "path": ["metals", "purities", "{{metal_type}}"]
                        },
                        "description": "جلب درجات النقاء المتاحة لمعدن محدد (24K, 21K, 18K, 14K للذهب - 925 للفضة)."
                    },
                    "response": []
                },
                {
                    "name": "Get Zakat Formulas - جلب صيغ حساب الزكاة",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Zakat formulas data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('object');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/zakat/formulas",
                            "host": ["{{full_api_url}}"],
                            "path": ["zakat", "formulas"]
                        },
                        "description": "جلب صيغ وقواعد حساب زكاة الذهب والفضة مع النصاب والمعدلات المطلوبة."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "🏠 Home Content APIs - واجهات المحتوى الرئيسي",
            "description": "جميع الواجهات المتعلقة بمحتوى الصفحة الرئيسية",
            "item": [
                {
                    "name": "Get Home Sliders - جلب شرائح الصفحة الرئيسية",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Sliders data is present\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{full_api_url}}/sliders",
                            "host": ["{{full_api_url}}"],
                            "path": ["sliders"]
                        },
                        "description": "جلب صور وبيانات شرائح الصفحة الرئيسية للعرض في التطبيق."
                    },
                    "response": []
                }
            ]
        },
        {
            "name": "🔍 Health Check - فحص حالة الخادم",
            "description": "فحص حالة الخادم والاتصال بقاعدة البيانات",
            "item": [
                {
                    "name": "Health Check - فحص حالة النظام",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"System is healthy\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData).to.have.property('status');",
                                    "    pm.expect(jsonData.status).to.equal('ok');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "{{accept}}",
                                "type": "text"
                            },
                            {
                                "key": "ngrok-skip-browser-warning",
                                "value": "{{ngrok_header}}",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/health",
                            "host": ["{{base_url}}"],
                            "path": ["health"]
                        },
                        "description": "فحص حالة الخادم والتأكد من عمل جميع الخدمات والاتصال بقاعدة البيانات."
                    },
                    "response": []
                }
            ]
        }
    ],
    "event": [
        {
            "listen": "prerequest",
            "script": {
                "type": "text/javascript",
                "exec": [
                    "// تسجيل وقت بداية الطلب",
                    "pm.globals.set(\"request_start_time\", new Date().toISOString());",
                    "",
                    "// إضافة timestamp للطلبات التي تحتاجه",
                    "if (pm.request.url.query && pm.request.url.query.has(\"timestamp\")) {",
                    "    pm.request.url.query.upsert({",
                    "        key: \"timestamp\",",
                    "        value: Date.now().toString()",
                    "    });",
                    "}"
                ]
            }
        },
        {
            "listen": "test",
            "script": {
                "type": "text/javascript",
                "exec": [
                    "// تسجيل وقت انتهاء الطلب",
                    "pm.globals.set(\"request_end_time\", new Date().toISOString());",
                    "",
                    "// التحقق من وجود Content-Type header",
                    "pm.test(\"Content-Type is application/json\", function () {",
                    "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");",
                    "});"
                ]
            }
        }
        },
        {
            "name": "🏭 Bars & Coins APIs - واجهات السبائك والعملات",
            "description": "جميع الواجهات المتعلقة بصفحة السبائك والعملات وحساب الأسعار",
            "item": [
                {
                    "name": "Get Companies for Type - جلب الشركات حسب النوع",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Response has required structure\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData).to.have.property('success');",
                                    "    pm.expect(jsonData).to.have.property('data');",
                                    "    pm.expect(jsonData.data).to.be.an('array');",
                                    "});",
                                    "",
                                    "pm.test(\"Companies have required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    if (jsonData.data.length > 0) {",
                                    "        const company = jsonData.data[0];",
                                    "        pm.expect(company).to.have.property('id');",
                                    "        pm.expect(company).to.have.property('name');",
                                    "        pm.expect(company).to.have.property('logo');",
                                    "        pm.expect(company).to.have.property('products_count');",
                                    "    }",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/app/v1/bars-coins/companies?type={{product_type}}",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "app",
                                "v1",
                                "bars-coins",
                                "companies"
                            ],
                            "query": [
                                {
                                    "key": "type",
                                    "value": "{{product_type}}",
                                    "description": "نوع المنتج (سبيكة أو عملة)"
                                }
                            ]
                        },
                        "description": "جلب قائمة الشركات النشطة التي تنتج نوع منتج معين (سبائك أو عملات)\n\n**المعاملات:**\n- `type` (اختياري): نوع المنتج - \"سبيكة\" أو \"عملة\" (افتراضي: سبيكة)\n\n**الاستجابة:**\n- قائمة بالشركات مع معلومات أساسية\n- عدد المنتجات لكل شركة\n- رابط اللوجو إن وجد"
                    },
                    "response": [
                        {
                            "name": "Success Response - استجابة ناجحة",
                            "originalRequest": {
                                "method": "GET",
                                "header": [
                                    {
                                        "key": "Accept",
                                        "value": "application/json",
                                        "type": "text"
                                    }
                                ],
                                "url": {
                                    "raw": "{{base_url}}/api/app/v1/bars-coins/companies?type=سبيكة",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "app",
                                        "v1",
                                        "bars-coins",
                                        "companies"
                                    ],
                                    "query": [
                                        {
                                            "key": "type",
                                            "value": "سبيكة"
                                        }
                                    ]
                                }
                            },
                            "status": "OK",
                            "code": 200,
                            "_postman_previewlanguage": "json",
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json"
                                }
                            ],
                            "cookie": [],
                            "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 21,\n            \"name\": \"BTC\",\n            \"logo\": \"http://127.0.0.1:8001/storage/companies/logos/01JWD46BZ5T6JZFTECM2KNJY23.jpg\",\n            \"products_count\": 14\n        },\n        {\n            \"id\": 22,\n            \"name\": \"ElGalla Gold\",\n            \"logo\": null,\n            \"products_count\": 14\n        },\n        {\n            \"id\": 23,\n            \"name\": \"GFG\",\n            \"logo\": null,\n            \"products_count\": 14\n        }\n    ],\n    \"message\": \"تم جلب قائمة الشركات بنجاح\"\n}"
                        }
                    ]
                },
                {
                    "name": "Get Product Types - جلب أنواع المنتجات",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Product types have required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    if (jsonData.data.length > 0) {",
                                    "        const productType = jsonData.data[0];",
                                    "        pm.expect(productType).to.have.property('id');",
                                    "        pm.expect(productType).to.have.property('name');",
                                    "        pm.expect(productType).to.have.property('weight');",
                                    "        pm.expect(productType).to.have.property('metal_purity');",
                                    "    }",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/app/v1/bars-coins/product-types?type={{product_type}}&company_id={{company_id}}",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "app",
                                "v1",
                                "bars-coins",
                                "product-types"
                            ],
                            "query": [
                                {
                                    "key": "type",
                                    "value": "{{product_type}}",
                                    "description": "نوع المنتج (سبيكة أو عملة)"
                                },
                                {
                                    "key": "company_id",
                                    "value": "{{company_id}}",
                                    "description": "معرف الشركة (اختياري)"
                                }
                            ]
                        },
                        "description": "جلب أنواع المنتجات المتاحة لنوع معين أو لشركة معينة\n\n**المعاملات:**\n- `type` (مطلوب): نوع المنتج - \"سبيكة\" أو \"عملة\"\n- `company_id` (اختياري): معرف الشركة لفلترة المنتجات\n\n**الاستجابة:**\n- قائمة بأنواع المنتجات مع التفاصيل\n- الوزن والعيار لكل نوع\n- التنسيق المُعد للعرض"
                    },
                    "response": [
                        {
                            "name": "Success Response - استجابة ناجحة",
                            "originalRequest": {
                                "method": "GET",
                                "header": [
                                    {
                                        "key": "Accept",
                                        "value": "application/json",
                                        "type": "text"
                                    }
                                ],
                                "url": {
                                    "raw": "{{base_url}}/api/app/v1/bars-coins/product-types?type=سبيكة&company_id=21",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "app",
                                        "v1",
                                        "bars-coins",
                                        "product-types"
                                    ],
                                    "query": [
                                        {
                                            "key": "type",
                                            "value": "سبيكة"
                                        },
                                        {
                                            "key": "company_id",
                                            "value": "21"
                                        }
                                    ]
                                }
                            },
                            "status": "OK",
                            "code": 200,
                            "_postman_previewlanguage": "json",
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json"
                                }
                            ],
                            "cookie": [],
                            "body": "{\n    \"success\": true,\n    \"data\": [\n        {\n            \"id\": 1,\n            \"name\": \"سبيكة 1 جرام\",\n            \"type\": \"سبيكة\",\n            \"weight\": 1.0,\n            \"metal_purity\": \"24\",\n            \"formatted_weight\": \"1.000 جرام\",\n            \"formatted_purity\": \"عيار 24\",\n            \"type_with_icon\": \"🟨 سبيكة\"\n        },\n        {\n            \"id\": 2,\n            \"name\": \"سبيكة 2.5 جرام\",\n            \"type\": \"سبيكة\",\n            \"weight\": 2.5,\n            \"metal_purity\": \"24\",\n            \"formatted_weight\": \"2.500 جرام\",\n            \"formatted_purity\": \"عيار 24\",\n            \"type_with_icon\": \"🟨 سبيكة\"\n        }\n    ],\n    \"message\": \"تم جلب أنواع المنتجات بنجاح\"\n}"
                        }
                    ]
                },
                {
                    "name": "Get Company Product Details - جلب تفاصيل منتج الشركة",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Product details have required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('company');",
                                    "    pm.expect(jsonData.data).to.have.property('product_type');",
                                    "    pm.expect(jsonData.data).to.have.property('manufacturing_cost_per_gram');",
                                    "    pm.expect(jsonData.data).to.have.property('refund_value_per_gram');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/app/v1/bars-coins/company-product?company_id={{company_id}}&product_type_id={{product_type_id}}",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "app",
                                "v1",
                                "bars-coins",
                                "company-product"
                            ],
                            "query": [
                                {
                                    "key": "company_id",
                                    "value": "{{company_id}}",
                                    "description": "معرف الشركة"
                                },
                                {
                                    "key": "product_type_id",
                                    "value": "{{product_type_id}}",
                                    "description": "معرف نوع المنتج"
                                }
                            ]
                        },
                        "description": "جلب تفاصيل منتج معين لشركة معينة\n\n**المعاملات:**\n- `company_id` (مطلوب): معرف الشركة\n- `product_type_id` (مطلوب): معرف نوع المنتج\n\n**الاستجابة:**\n- تفاصيل الشركة والمنتج\n- تكلفة التصنيع وقيمة الاسترداد\n- جميع المعلومات المطلوبة للحسابات"
                    },
                    "response": [
                        {
                            "name": "Success Response - استجابة ناجحة",
                            "originalRequest": {
                                "method": "GET",
                                "header": [
                                    {
                                        "key": "Accept",
                                        "value": "application/json",
                                        "type": "text"
                                    }
                                ],
                                "url": {
                                    "raw": "{{base_url}}/api/app/v1/bars-coins/company-product?company_id=21&product_type_id=1",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "app",
                                        "v1",
                                        "bars-coins",
                                        "company-product"
                                    ],
                                    "query": [
                                        {
                                            "key": "company_id",
                                            "value": "21"
                                        },
                                        {
                                            "key": "product_type_id",
                                            "value": "1"
                                        }
                                    ]
                                }
                            },
                            "status": "OK",
                            "code": 200,
                            "_postman_previewlanguage": "json",
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json"
                                }
                            ],
                            "cookie": [],
                            "body": "{\n    \"success\": true,\n    \"data\": {\n        \"id\": 295,\n        \"company\": {\n            \"id\": 21,\n            \"name\": \"BTC\",\n            \"logo\": \"http://127.0.0.1:8001/storage/companies/logos/01JWD46BZ5T6JZFTECM2KNJY23.jpg\"\n        },\n        \"product_type\": {\n            \"id\": 1,\n            \"name\": \"سبيكة 1 جرام\",\n            \"type\": \"سبيكة\",\n            \"weight\": 1.0,\n            \"metal_purity\": \"24\",\n            \"formatted_weight\": \"1.000 جرام\",\n            \"formatted_purity\": \"عيار 24\"\n        },\n        \"manufacturing_cost_per_gram\": 25.0,\n        \"refund_value_per_gram\": 15.51\n    },\n    \"message\": \"تم جلب تفاصيل المنتج بنجاح\"\n}"
                        }
                    ]
                },
                {
                    "name": "Get Current Metal Prices - جلب أسعار المعادن الحالية",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Prices have required structure\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('gold_24k');",
                                    "    pm.expect(jsonData.data).to.have.property('gold_21k');",
                                    "    pm.expect(jsonData.data.gold_24k).to.have.property('buy_price');",
                                    "    pm.expect(jsonData.data.gold_24k).to.have.property('sell_price');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/app/v1/bars-coins/current-prices",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "app",
                                "v1",
                                "bars-coins",
                                "current-prices"
                            ]
                        },
                        "description": "جلب أسعار المعادن الحالية للذهب عيار 24 و 21\n\n**الاستجابة:**\n- أسعار الشراء والبيع للذهب عيار 24\n- أسعار الشراء والبيع للذهب عيار 21\n- تاريخ آخر تحديث للأسعار"
                    },
                    "response": [
                        {
                            "name": "Success Response - استجابة ناجحة",
                            "originalRequest": {
                                "method": "GET",
                                "header": [
                                    {
                                        "key": "Accept",
                                        "value": "application/json",
                                        "type": "text"
                                    }
                                ],
                                "url": {
                                    "raw": "{{base_url}}/api/app/v1/bars-coins/current-prices",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "app",
                                        "v1",
                                        "bars-coins",
                                        "current-prices"
                                    ]
                                }
                            },
                            "status": "OK",
                            "code": 200,
                            "_postman_previewlanguage": "json",
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json"
                                }
                            ],
                            "cookie": [],
                            "body": "{\n    \"success\": true,\n    \"data\": {\n        \"gold_24k\": {\n            \"buy_price\": \"5268.00\",\n            \"sell_price\": \"5291.00\",\n            \"last_updated\": \"2025-07-25 21:34:14\"\n        },\n        \"gold_21k\": {\n            \"buy_price\": \"4610.00\",\n            \"sell_price\": \"4630.00\",\n            \"last_updated\": \"2025-07-25 20:43:49\"\n        }\n    },\n    \"message\": \"تم جلب أسعار المعادن الحالية بنجاح\"\n}"
                        }
                    ]
                },
                {
                    "name": "Calculate Product Prices - حساب أسعار المنتج",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Calculated prices have required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('raw_gold_price_per_gram');",
                                    "    pm.expect(jsonData.data).to.have.property('total_price');",
                                    "    pm.expect(jsonData.data).to.have.property('resale_price');",
                                    "    pm.expect(jsonData.data).to.have.property('weight');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "POST",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "application/json",
                                "type": "text"
                            },
                            {
                                "key": "Content-Type",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "body": {
                            "mode": "raw",
                            "raw": "{\n    \"company_id\": {{company_id}},\n    \"product_type_id\": {{product_type_id}}\n}",
                            "options": {
                                "raw": {
                                    "language": "json"
                                }
                            }
                        },
                        "url": {
                            "raw": "{{base_url}}/api/app/v1/bars-coins/calculate-prices",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "app",
                                "v1",
                                "bars-coins",
                                "calculate-prices"
                            ]
                        },
                        "description": "حساب جميع أسعار منتج معين بناءً على أسعار المعادن الحالية\n\n**البيانات المطلوبة:**\n- `company_id` (مطلوب): معرف الشركة\n- `product_type_id` (مطلوب): معرف نوع المنتج\n\n**الاستجابة:**\n- سعر الذهب الخام للجرام والإجمالي\n- تكلفة التصنيع\n- السعر الإجمالي للشراء\n- سعر إعادة البيع\n- الوزن والعيار"
                    },
                    "response": [
                        {
                            "name": "Success Response - استجابة ناجحة",
                            "originalRequest": {
                                "method": "POST",
                                "header": [
                                    {
                                        "key": "Accept",
                                        "value": "application/json",
                                        "type": "text"
                                    },
                                    {
                                        "key": "Content-Type",
                                        "value": "application/json",
                                        "type": "text"
                                    }
                                ],
                                "body": {
                                    "mode": "raw",
                                    "raw": "{\n    \"company_id\": 21,\n    \"product_type_id\": 1\n}",
                                    "options": {
                                        "raw": {
                                            "language": "json"
                                        }
                                    }
                                },
                                "url": {
                                    "raw": "{{base_url}}/api/app/v1/bars-coins/calculate-prices",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "app",
                                        "v1",
                                        "bars-coins",
                                        "calculate-prices"
                                    ]
                                }
                            },
                            "status": "OK",
                            "code": 200,
                            "_postman_previewlanguage": "json",
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json"
                                }
                            ],
                            "cookie": [],
                            "body": "{\n    \"success\": true,\n    \"data\": {\n        \"raw_gold_price_per_gram\": 5291.0,\n        \"raw_gold_price\": 5291.0,\n        \"manufacturing_per_gram\": 25.0,\n        \"total_price\": 5316.0,\n        \"refund_per_gram\": 15.51,\n        \"resale_price\": 5283.51,\n        \"weight\": 1.0,\n        \"purity\": \"24\"\n    },\n    \"message\": \"تم حساب أسعار المنتج بنجاح\"\n}"
                        }
                    ]
                },
                {
                    "name": "Get Site Settings - جلب إعدادات الموقع",
                    "event": [
                        {
                            "listen": "test",
                            "script": {
                                "exec": [
                                    "pm.test(\"Status code is 200\", function () {",
                                    "    pm.response.to.have.status(200);",
                                    "});",
                                    "",
                                    "pm.test(\"Site settings have required fields\", function () {",
                                    "    const jsonData = pm.response.json();",
                                    "    pm.expect(jsonData.data).to.have.property('default_logo');",
                                    "    pm.expect(jsonData.data).to.have.property('site_name');",
                                    "});"
                                ],
                                "type": "text/javascript"
                            }
                        }
                    ],
                    "request": {
                        "method": "GET",
                        "header": [
                            {
                                "key": "Accept",
                                "value": "application/json",
                                "type": "text"
                            }
                        ],
                        "url": {
                            "raw": "{{base_url}}/api/app/v1/bars-coins/site-settings",
                            "host": [
                                "{{base_url}}"
                            ],
                            "path": [
                                "api",
                                "app",
                                "v1",
                                "bars-coins",
                                "site-settings"
                            ]
                        },
                        "description": "جلب إعدادات الموقع الأساسية\n\n**الاستجابة:**\n- اللوجو الافتراضي للموقع\n- اسم الموقع\n- إعدادات أخرى مطلوبة للتطبيق"
                    },
                    "response": [
                        {
                            "name": "Success Response - استجابة ناجحة",
                            "originalRequest": {
                                "method": "GET",
                                "header": [
                                    {
                                        "key": "Accept",
                                        "value": "application/json",
                                        "type": "text"
                                    }
                                ],
                                "url": {
                                    "raw": "{{base_url}}/api/app/v1/bars-coins/site-settings",
                                    "host": [
                                        "{{base_url}}"
                                    ],
                                    "path": [
                                        "api",
                                        "app",
                                        "v1",
                                        "bars-coins",
                                        "site-settings"
                                    ]
                                }
                            },
                            "status": "OK",
                            "code": 200,
                            "_postman_previewlanguage": "json",
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json"
                                }
                            ],
                            "cookie": [],
                            "body": "{\n    \"success\": true,\n    \"data\": {\n        \"default_logo\": \"http://127.0.0.1:8001/storage/site-settings/01JWD46BZ5T6JZFTECM2KNJY23.jpg\",\n        \"site_name\": \"مجوهرات مكة جولد جروب\"\n    },\n    \"message\": \"تم جلب إعدادات الموقع بنجاح\"\n}"
                        }
                    ]
                }
            ]
        }
    ],
    "variable": [
        {
            "key": "base_url",
            "value": "https://05f9-2c0f-fc89-8032-f4e4-8dc9-9359-8e83-9d45.ngrok-free.app",
            "type": "string",
            "description": "Base URL for the API server (ngrok tunnel)"
        },
        {
            "key": "api_path",
            "value": "/api/app/v1",
            "type": "string",
            "description": "API path prefix for mobile app endpoints"
        },
        {
            "key": "full_api_url",
            "value": "{{base_url}}{{api_path}}",
            "type": "string",
            "description": "Complete API URL combining base URL and API path"
        },
        {
            "key": "content_type",
            "value": "application/json",
            "type": "string",
            "description": "Content-Type header for requests"
        },
        {
            "key": "accept",
            "value": "application/json",
            "type": "string",
            "description": "Accept header for requests"
        },
        {
            "key": "ngrok_header",
            "value": "true",
            "type": "string",
            "description": "Header to skip ngrok browser warning"
        },
        {
            "key": "product_id",
            "value": "1",
            "type": "string",
            "description": "Sample product ID for testing"
        },
        {
            "key": "category_id",
            "value": "1",
            "type": "string",
            "description": "Sample category ID for testing"
        },
        {
            "key": "metal_type",
            "value": "gold",
            "type": "string",
            "description": "Metal type for calculator APIs (gold, silver)"
        },
        {
            "key": "purity",
            "value": "21K",
            "type": "string",
            "description": "Metal purity for testing (24K, 21K, 18K, 14K for gold, 925 for silver)"
        },
        {
            "key": "period",
            "value": "week",
            "type": "string",
            "description": "Time period for price history (day, week, month, year)"
        },
        {
            "key": "page",
            "value": "1",
            "type": "string",
            "description": "Page number for pagination"
        },
        {
            "key": "per_page",
            "value": "12",
            "type": "string",
            "description": "Number of items per page"
        },
        {
            "key": "search_term",
            "value": "خاتم",
            "type": "string",
            "description": "Sample search term in Arabic"
        },
        {
            "key": "limit",
            "value": "8",
            "type": "string",
            "description": "Limit for featured products"
        },
        {
            "key": "store_id",
            "value": "1",
            "type": "string",
            "description": "Sample store ID for testing store details"
        }
    ]
}
