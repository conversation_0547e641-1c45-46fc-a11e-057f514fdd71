<?php

namespace Database\Factories;

use App\Models\Store;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Store>
 */
class StoreFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Store::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cities = [
            ['ar' => 'القاهرة', 'en' => 'Cairo'],
            ['ar' => 'الإسكندرية', 'en' => 'Alexandria'],
            ['ar' => 'الجيزة', 'en' => 'Giza'],
            ['ar' => 'شرم الشيخ', 'en' => 'Sharm El Sheikh'],
            ['ar' => 'الغردقة', 'en' => 'Hurghada'],
            ['ar' => 'أسوان', 'en' => 'Aswan'],
            ['ar' => 'الأقصر', 'en' => 'Luxor'],
            ['ar' => 'المنصورة', 'en' => 'Mansoura'],
            ['ar' => 'طنطا', 'en' => 'Tanta'],
            ['ar' => 'بورسعيد', 'en' => 'Port Said'],
        ];
        
        $city = $this->faker->randomElement($cities);
        
        return [
            'name_ar' => 'مكة جولد - فرع ' . $city['ar'],
            'name_en' => 'Makkah Gold - ' . $city['en'] . ' Branch',
            'address_ar' => 'شارع ' . $this->faker->streetName() . '، ' . $city['ar'],
            'address_en' => $this->faker->streetAddress() . ', ' . $city['en'],
            'city_ar' => $city['ar'],
            'city_en' => $city['en'],
            'country_ar' => 'مصر',
            'country_en' => 'Egypt',
            'phone' => '+2' . $this->faker->numerify('01########'),
            'email' => strtolower($city['en']) . '@makkahgold.com',
            'working_hours_ar' => 'من السبت إلى الخميس: 10 صباحًا - 10 مساءً، الجمعة: 2 ظهرًا - 10 مساءً',
            'working_hours_en' => 'Saturday to Thursday: 10 AM - 10 PM, Friday: 2 PM - 10 PM',
            'latitude' => $this->faker->latitude(22.0, 31.5),
            'longitude' => $this->faker->longitude(25.0, 35.0),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
        ];
    }
}
