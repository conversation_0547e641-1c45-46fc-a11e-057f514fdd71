<?php

namespace Database\Factories;

use App\Models\MetalPrice;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\MetalPrice>
 */
class MetalPriceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = MetalPrice::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $metalTypes = ['gold', 'silver', 'platinum'];
        $metalType = $this->faker->randomElement($metalTypes);

        $purities = [
            'gold' => ['24K', '22K', '21K', '18K', '14K'],
            'silver' => ['925'],
            'platinum' => ['950'],
        ];

        $purity = $this->faker->randomElement($purities[$metalType]);

        $basePrices = [
            'gold' => [
                '24K' => 2900,
                '22K' => 2650,
                '21K' => 2550,
                '18K' => 2175,
                '14K' => 1700,
            ],
            'silver' => [
                '925' => 35,
            ],
            'platinum' => [
                '950' => 1500,
            ],
        ];

        $basePrice = $basePrices[$metalType][$purity];
        $price = $basePrice + $this->faker->randomFloat(2, -100, 100);

        return [
            'metal_type' => $metalType,
            'purity' => $purity,
            'price_per_gram' => $price,
            'price_per_ounce' => $price * 31.1035, // Convert gram to ounce
            'price_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
            'currency' => 'EGP',
            'source' => 'مكة جولد',
        ];
    }
}
