<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Category>
 */
class CategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name_ar = $this->faker->unique()->words(2, true);
        $name_en = $this->faker->unique()->words(2, true);
        $slug = \Illuminate\Support\Str::slug($name_en);

        return [
            'name_ar' => 'فئة ' . $name_ar,
            'name_en' => ucfirst($name_en),
            'description_ar' => 'وصف ' . $name_ar,
            'description_en' => 'Description for ' . $name_en,
            'slug' => $slug,
            'image' => null,
            'parent_id' => null,
            'order' => $this->faker->numberBetween(0, 10),
            'is_active' => $this->faker->boolean(80),
        ];
    }
}
