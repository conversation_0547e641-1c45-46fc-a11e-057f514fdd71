<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Order::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statuses = ['pending', 'processing', 'completed', 'cancelled', 'refunded'];
        $paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
        $paymentMethods = ['cash', 'credit_card', 'bank_transfer'];

        $status = $this->faker->randomElement($statuses);
        $paymentStatus = $this->faker->randomElement($paymentStatuses);

        // If order is completed, payment should be paid
        if ($status === 'completed') {
            $paymentStatus = 'paid';
        }

        // If payment failed, order should be pending or cancelled
        if ($paymentStatus === 'failed') {
            $status = $this->faker->randomElement(['pending', 'cancelled']);
        }

        $subtotal = $this->faker->randomFloat(2, 1000, 10000);
        $tax = $subtotal * 0.14; // 14% VAT
        $shipping = $this->faker->randomFloat(2, 50, 200);
        $discount = $this->faker->randomFloat(2, 0, 500);
        $total = $subtotal + $tax + $shipping - $discount;

        $createdAt = $this->faker->dateTimeBetween('-60 days', 'now');

        return [
            'user_id' => User::role('user')->inRandomOrder()->first()->id ?? User::factory()->create()->id,
            'order_number' => 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT),
            'status' => $status,
            'payment_status' => $paymentStatus,
            'payment_method' => $this->faker->randomElement($paymentMethods),
            'total_amount' => $total,
            'tax_amount' => $tax,
            'shipping_amount' => $shipping,
            'discount_amount' => $discount,
            'currency' => 'EGP',
            'payment_transaction_id' => 'TXN-' . strtoupper(substr(md5(rand()), 0, 8)),
            'shipping_name' => $this->faker->name(),
            'shipping_address' => $this->faker->streetAddress(),
            'shipping_city' => $this->faker->city(),
            'shipping_country' => 'مصر',
            'shipping_phone' => '+2' . $this->faker->numerify('01########'),
            'shipping_email' => $this->faker->email(),
            'shipping_postal_code' => $this->faker->postcode(),
            'notes' => $this->faker->optional(0.7)->sentence(),
            'is_guest' => $this->faker->boolean(20), // 20% chance of being a guest order
            'guest_email' => function (array $attributes) {
                return $attributes['is_guest'] ? $this->faker->email() : null;
            },
            'created_at' => $createdAt,
            'updated_at' => $this->faker->dateTimeBetween($createdAt, 'now'),
        ];
    }
}
