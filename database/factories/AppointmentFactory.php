<?php

namespace Database\Factories;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Appointment>
 */
class AppointmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Appointment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $statuses = ['pending', 'confirmed', 'cancelled', 'completed'];
        $serviceTypes = [
            ['ar' => 'تصميم مجوهرات مخصصة', 'en' => 'Custom Jewelry Design'],
            ['ar' => 'إصلاح مجوهرات', 'en' => 'Jewelry Repair'],
            ['ar' => 'تقييم مجوهرات', 'en' => 'Jewelry Appraisal'],
            ['ar' => 'استشارة شراء', 'en' => 'Purchase Consultation'],
            ['ar' => 'تنظيف مجوهرات', 'en' => 'Jewelry Cleaning'],
        ];

        $serviceType = $this->faker->randomElement($serviceTypes);

        // Generate random appointment date (past, present, future)
        $daysOffset = $this->faker->numberBetween(-30, 30);
        $appointmentDate = now()->addDays($daysOffset)->setHour($this->faker->numberBetween(10, 19))->setMinute(0)->setSecond(0);

        $status = $this->faker->randomElement($statuses);

        // If appointment is in the past, it should be completed or cancelled
        if ($daysOffset < 0) {
            $status = $this->faker->randomElement(['completed', 'cancelled']);
        }

        // If appointment is today or in the future, it should be pending or confirmed
        if ($daysOffset >= 0) {
            $status = $this->faker->randomElement(['pending', 'confirmed']);
        }

        // Generate confirmation code
        $confirmationCode = strtoupper(substr(md5(rand()), 0, 6));

        // Determine if reminder was sent
        $reminderSent = $daysOffset > -7 && $daysOffset <= 0;

        $createdAt = $appointmentDate->copy()->subDays($this->faker->numberBetween(1, 7));

        return [
            'user_id' => User::role('user')->inRandomOrder()->first()->id ?? User::factory()->create()->id,
            'store_id' => rand(1, 5),
            'appointment_date' => $appointmentDate,
            'status' => $status,
            'service_type_ar' => $serviceType['ar'],
            'service_type_en' => $serviceType['en'],
            'confirmation_code' => $confirmationCode,
            'reminder_sent' => $reminderSent,
            'notes' => $this->faker->optional(0.7)->sentence(),
            'created_at' => $createdAt,
            'updated_at' => $this->faker->dateTimeBetween($createdAt, 'now'),
        ];
    }
}
