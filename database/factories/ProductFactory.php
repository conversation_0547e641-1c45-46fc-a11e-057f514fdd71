<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name_ar = $this->faker->unique()->words(3, true);
        $name_en = $this->faker->unique()->words(3, true);
        $slug = \Illuminate\Support\Str::slug($name_en);
        $price = $this->faker->numberBetween(1000, 50000);
        $weight = $this->faker->randomFloat(3, 1, 100);

        $metalTypes = ['ذهب', 'فضة', 'بلاتين'];
        $metalPurities = ['18K', '21K', '24K', '925', '950', '999'];

        return [
            'name_ar' => 'مجوهرات ' . $name_ar,
            'name_en' => ucfirst($name_en),
            'description_ar' => 'وصف تفصيلي لمنتج ' . $name_ar,
            'description_en' => 'Detailed description for ' . $name_en,
            'slug' => $slug,
            'category_id' => \App\Models\Category::inRandomOrder()->first()->id ?? 1,
            'weight' => $weight,
            'metal_purity' => $this->faker->randomElement($metalPurities),
            'dimensions' => $this->faker->numberBetween(10, 50) . 'x' . $this->faker->numberBetween(10, 50) . 'x' . $this->faker->numberBetween(1, 10) . ' mm',
            'material_type' => $this->faker->randomElement($metalTypes),
            'price' => $price,
            'stock_quantity' => $this->faker->numberBetween(0, 50),
            'is_featured' => $this->faker->boolean(20),
            'is_active' => $this->faker->boolean(90),
            'sku' => strtoupper($this->faker->bothify('??###??')),
        ];
    }
}
