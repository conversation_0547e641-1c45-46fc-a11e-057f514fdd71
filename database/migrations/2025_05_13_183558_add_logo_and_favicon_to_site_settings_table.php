<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إضافة أعمدة الشعار وأيقونة الموقع
            $table->string('logo')->nullable()->after('site_description')->comment('مسار شعار الموقع');
            $table->string('favicon')->nullable()->after('logo')->comment('مسار أيقونة الموقع');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // حذف أعمدة الشعار وأيقونة الموقع
            $table->dropColumn(['logo', 'favicon']);
        });
    }
};
