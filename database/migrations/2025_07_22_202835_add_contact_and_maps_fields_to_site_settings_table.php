<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إضافة حقول المقر الرئيسي
            if (!Schema::hasColumn('site_settings', 'headquarters_address_ar')) {
                $table->text('headquarters_address_ar')->nullable()->comment('عنوان المقر الرئيسي بالعربية');
            }
            if (!Schema::hasColumn('site_settings', 'headquarters_address_en')) {
                $table->text('headquarters_address_en')->nullable()->comment('عنوان المقر الرئيسي بالإنجليزية');
            }

            // إضافة حقول ساعات العمل
            if (!Schema::hasColumn('site_settings', 'working_hours_ar')) {
                $table->text('working_hours_ar')->nullable()->comment('ساعات العمل بالعربية');
            }
            if (!Schema::hasColumn('site_settings', 'working_hours_en')) {
                $table->text('working_hours_en')->nullable()->comment('ساعات العمل بالإنجليزية');
            }

            // إضافة حقول الخريطة
            if (!Schema::hasColumn('site_settings', 'map_latitude')) {
                $table->decimal('map_latitude', 10, 7)->nullable()->comment('خط العرض للموقع');
            }
            if (!Schema::hasColumn('site_settings', 'map_longitude')) {
                $table->decimal('map_longitude', 10, 7)->nullable()->comment('خط الطول للموقع');
            }
            if (!Schema::hasColumn('site_settings', 'map_zoom')) {
                $table->integer('map_zoom')->default(15)->comment('مستوى التكبير في الخريطة');
            }
            if (!Schema::hasColumn('site_settings', 'map_marker_title')) {
                $table->string('map_marker_title')->nullable()->comment('عنوان العلامة في الخريطة');
            }

            // إضافة Google Maps API Key
            if (!Schema::hasColumn('site_settings', 'google_maps_api_key')) {
                $table->string('google_maps_api_key')->nullable()->comment('مفتاح Google Maps API');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // حذف الحقول الجديدة فقط (تجنب حذف الحقول الموجودة من migrations أخرى)
            $columnsToCheck = [
                'headquarters_address_ar',
                'headquarters_address_en',
                'working_hours_ar',
                'working_hours_en',
                'map_latitude',
                'map_longitude',
                'map_zoom',
                'map_marker_title',
                'google_maps_api_key',
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('site_settings', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
