<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // إضافة فهارس لتحسين أداء البحث
            $table->index(['is_active', 'created_at'], 'products_active_created_index');
            $table->index(['is_active', 'price'], 'products_active_price_index');
            $table->index(['is_active', 'category_id'], 'products_active_category_index');
            $table->index(['is_active', 'metal_type_id'], 'products_active_metal_type_index');
            $table->index(['is_active', 'metal_purity_id'], 'products_active_metal_purity_index');
            
            // فهرس مركب للبحث المتقدم
            $table->index(['is_active', 'category_id', 'metal_type_id', 'price'], 'products_search_composite_index');
            
            // فهرس للنص الكامل (إذا كان مدعوماً)
            if (config('database.default') === 'mysql') {
                DB::statement('ALTER TABLE products ADD FULLTEXT search_index (name_ar, name_en, description_ar, description_en)');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // إزالة الفهارس
            $table->dropIndex('products_active_created_index');
            $table->dropIndex('products_active_price_index');
            $table->dropIndex('products_active_category_index');
            $table->dropIndex('products_active_metal_type_index');
            $table->dropIndex('products_active_metal_purity_index');
            $table->dropIndex('products_search_composite_index');
            
            // إزالة فهرس النص الكامل
            if (config('database.default') === 'mysql') {
                DB::statement('ALTER TABLE products DROP INDEX search_index');
            }
        });
    }
};
