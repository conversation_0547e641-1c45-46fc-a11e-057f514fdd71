<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pages', function (Blueprint $table) {
            // إضافة حقول متعددة اللغات منفصلة
            $table->string('title_ar')->nullable()->after('title')->comment('العنوان بالعربية');
            $table->string('title_en')->nullable()->after('title_ar')->comment('العنوان بالإنجليزية');
            $table->text('content_ar')->nullable()->after('content')->comment('المحتوى بالعربية');
            $table->text('content_en')->nullable()->after('content_ar')->comment('المحتوى بالإنجليزية');

            // إضافة حقول SEO متعددة اللغات
            $table->string('meta_title_ar')->nullable()->after('meta_title')->comment('عنوان الميتا بالعربية');
            $table->string('meta_title_en')->nullable()->after('meta_title_ar')->comment('عنوان الميتا بالإنجليزية');
            $table->text('meta_description_ar')->nullable()->after('meta_description')->comment('وصف الميتا بالعربية');
            $table->text('meta_description_en')->nullable()->after('meta_description_ar')->comment('وصف الميتا بالإنجليزية');
            $table->string('meta_keywords_ar')->nullable()->after('meta_keywords')->comment('الكلمات المفتاحية بالعربية');
            $table->string('meta_keywords_en')->nullable()->after('meta_keywords_ar')->comment('الكلمات المفتاحية بالإنجليزية');

            // إضافة حقول إضافية للتحسين
            $table->string('featured_image')->nullable()->after('meta_keywords_en')->comment('الصورة المميزة للصفحة');
            $table->text('excerpt')->nullable()->after('featured_image')->comment('مقتطف من الصفحة');
            $table->text('excerpt_ar')->nullable()->after('excerpt')->comment('مقتطف بالعربية');
            $table->text('excerpt_en')->nullable()->after('excerpt_ar')->comment('مقتطف بالإنجليزية');

            // إضافة حقول للتحكم في العرض
            $table->boolean('show_in_menu')->default(false)->after('is_active')->comment('إظهار في القائمة');
            $table->boolean('show_breadcrumbs')->default(true)->after('show_in_menu')->comment('إظهار مسار التنقل');
            $table->boolean('allow_comments')->default(false)->after('show_breadcrumbs')->comment('السماح بالتعليقات');

            // إضافة حقول للتخصيص
            $table->string('template')->nullable()->after('section')->comment('قالب الصفحة المخصص');
            $table->json('custom_fields')->nullable()->after('template')->comment('حقول مخصصة إضافية');

            // إضافة حقول للتتبع
            $table->integer('views_count')->default(0)->after('order')->comment('عدد المشاهدات');
            $table->timestamp('published_at')->nullable()->after('views_count')->comment('تاريخ النشر');

            // إضافة فهارس للأداء
            $table->index(['type', 'is_active']);
            $table->index(['section', 'is_active']);
            $table->index(['show_in_menu', 'is_active']);
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pages', function (Blueprint $table) {
            // حذف الفهارس أولاً
            $table->dropIndex(['type', 'is_active']);
            $table->dropIndex(['section', 'is_active']);
            $table->dropIndex(['show_in_menu', 'is_active']);
            $table->dropIndex(['published_at']);

            // حذف الحقول المضافة
            $table->dropColumn([
                'title_ar',
                'title_en',
                'content_ar',
                'content_en',
                'meta_title_ar',
                'meta_title_en',
                'meta_description_ar',
                'meta_description_en',
                'meta_keywords_ar',
                'meta_keywords_en',
                'featured_image',
                'excerpt',
                'excerpt_ar',
                'excerpt_en',
                'show_in_menu',
                'show_breadcrumbs',
                'allow_comments',
                'template',
                'custom_fields',
                'views_count',
                'published_at',
            ]);
        });
    }
};
