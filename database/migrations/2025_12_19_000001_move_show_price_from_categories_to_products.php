<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة حقل show_price إلى جدول products
        Schema::table('products', function (Blueprint $table) {
            $table->boolean('show_price')->default(true)->after('is_active');
        });

        // نقل البيانات من categories إلى products
        // تحديث show_price في products بناءً على الفئة التي ينتمي إليها المنتج
        DB::statement('
            UPDATE products p 
            INNER JOIN categories c ON p.category_id = c.id 
            SET p.show_price = c.show_price
        ');

        // إزالة حقل show_price من جدول categories
        Schema::table('categories', function (Blueprint $table) {
            $table->dropColumn('show_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة إضافة حقل show_price إلى جدول categories
        Schema::table('categories', function (Blueprint $table) {
            $table->boolean('show_price')->default(true)->after('is_active');
        });

        // نقل البيانات من products إلى categories
        // تحديث show_price في categories بناءً على أول منتج في كل فئة
        DB::statement('
            UPDATE categories c 
            INNER JOIN (
                SELECT category_id, show_price 
                FROM products 
                WHERE id IN (
                    SELECT MIN(id) 
                    FROM products 
                    GROUP BY category_id
                )
            ) p ON c.id = p.category_id 
            SET c.show_price = p.show_price
        ');

        // إزالة حقل show_price من جدول products
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('show_price');
        });
    }
};
