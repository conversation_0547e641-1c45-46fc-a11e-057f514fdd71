<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('site_settings', function (Blueprint $table) {
            $table->id();

            // Información básica del sitio
            $table->string('site_name');
            $table->text('site_description')->nullable();

            // Información de contacto
            $table->string('contact_email')->nullable();
            $table->string('contact_phone')->nullable();
            $table->text('address')->nullable();

            // Redes sociales
            $table->string('facebook_url')->nullable();
            $table->string('instagram_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('whatsapp_number')->nullable();

            // Precios del oro
            $table->decimal('gold_price_24k', 10, 2)->nullable();
            $table->decimal('gold_price_21k', 10, 2)->nullable();
            $table->decimal('gold_price_18k', 10, 2)->nullable();

            // Texto del pie de página
            $table->text('footer_text')->nullable();

            // Configuración de SEO
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();
            $table->text('meta_keywords')->nullable();

            // Configuración de la tienda
            $table->boolean('maintenance_mode')->default(false);
            $table->text('maintenance_message')->nullable();

            // Configuración de envío
            $table->decimal('shipping_cost', 10, 2)->nullable();
            $table->decimal('free_shipping_threshold', 10, 2)->nullable();

            // Configuración de impuestos
            $table->decimal('tax_rate', 5, 2)->nullable();
            $table->boolean('prices_include_tax')->default(false);

            // Configuración de la página de inicio
            $table->boolean('show_featured_products')->default(true);
            $table->boolean('show_new_arrivals')->default(true);
            $table->boolean('show_categories')->default(true);
            $table->boolean('show_gold_prices')->default(true);

            // Configuración de correo electrónico
            $table->string('mail_from_address')->nullable();
            $table->string('mail_from_name')->nullable();
            $table->string('mail_host')->nullable();
            $table->integer('mail_port')->nullable();
            $table->string('mail_username')->nullable();
            $table->string('mail_password')->nullable();
            $table->string('mail_encryption')->nullable();

            // Configuración de redes sociales adicionales
            $table->string('youtube_url')->nullable();
            $table->string('tiktok_url')->nullable();
            $table->string('linkedin_url')->nullable();

            // Configuración de Google Analytics y herramientas de marketing
            $table->string('google_analytics_id')->nullable();
            $table->string('facebook_pixel_id')->nullable();
            $table->text('custom_header_scripts')->nullable();
            $table->text('custom_footer_scripts')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('site_settings');
    }
};
