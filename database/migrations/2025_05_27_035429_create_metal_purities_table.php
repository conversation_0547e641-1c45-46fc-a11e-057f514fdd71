<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('metal_purities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('metal_type_id')->constrained()->onDelete('cascade');
            $table->string('name'); // 24K, 22K, 999, جنيه ذهب
            $table->string('name_ar'); // 24 عيار, 22 عيار, فضة 999, جنيه ذهب
            $table->decimal('purity_percentage', 5, 2)->nullable(); // نسبة النقاء (مثل 99.9 للذهب 24K)
            $table->decimal('weight_grams', 8, 3)->nullable(); // وزن القطعة بالجرام (للجنيهات الذهبية)
            $table->text('description')->nullable(); // وصف العيار
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->timestamps();

            // فهرس مركب لضمان عدم تكرار العيار لنفس المعدن
            $table->unique(['metal_type_id', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('metal_purities');
    }
};
