<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Privacy Policy fields
            $table->string('privacy_title_ar')->nullable()->comment('عنوان سياسة الخصوصية بالعربية');
            $table->string('privacy_title_en')->nullable()->comment('عنوان سياسة الخصوصية بالإنجليزية');
            $table->longText('privacy_content_ar')->nullable()->comment('محتوى سياسة الخصوصية بالعربية');
            $table->longText('privacy_content_en')->nullable()->comment('محتوى سياسة الخصوصية بالإنجليزية');
            $table->text('privacy_meta_description_ar')->nullable()->comment('وصف سياسة الخصوصية للمحركات البحث (عربي)');
            $table->text('privacy_meta_description_en')->nullable()->comment('وصف سياسة الخصوصية للمحركات البحث (إنجليزي)');
            $table->string('privacy_meta_keywords')->nullable()->comment('الكلمات المفتاحية لسياسة الخصوصية');
            $table->timestamp('privacy_last_updated')->nullable()->comment('تاريخ آخر تحديث لسياسة الخصوصية');
            $table->boolean('privacy_show_last_updated')->default(true)->comment('إظهار تاريخ آخر تحديث لسياسة الخصوصية');

            // Terms and Conditions fields
            $table->string('terms_title_ar')->nullable()->comment('عنوان الشروط والأحكام بالعربية');
            $table->string('terms_title_en')->nullable()->comment('عنوان الشروط والأحكام بالإنجليزية');
            $table->longText('terms_content_ar')->nullable()->comment('محتوى الشروط والأحكام بالعربية');
            $table->longText('terms_content_en')->nullable()->comment('محتوى الشروط والأحكام بالإنجليزية');
            $table->text('terms_meta_description_ar')->nullable()->comment('وصف الشروط والأحكام للمحركات البحث (عربي)');
            $table->text('terms_meta_description_en')->nullable()->comment('وصف الشروط والأحكام للمحركات البحث (إنجليزي)');
            $table->string('terms_meta_keywords')->nullable()->comment('الكلمات المفتاحية للشروط والأحكام');
            $table->timestamp('terms_last_updated')->nullable()->comment('تاريخ آخر تحديث للشروط والأحكام');
            $table->boolean('terms_show_last_updated')->default(true)->comment('إظهار تاريخ آخر تحديث للشروط والأحكام');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Drop Privacy Policy fields
            $table->dropColumn([
                'privacy_title_ar',
                'privacy_title_en',
                'privacy_content_ar',
                'privacy_content_en',
                'privacy_meta_description_ar',
                'privacy_meta_description_en',
                'privacy_meta_keywords',
                'privacy_last_updated',
                'privacy_show_last_updated',
            ]);

            // Drop Terms and Conditions fields
            $table->dropColumn([
                'terms_title_ar',
                'terms_title_en',
                'terms_content_ar',
                'terms_content_en',
                'terms_meta_description_ar',
                'terms_meta_description_en',
                'terms_meta_keywords',
                'terms_last_updated',
                'terms_show_last_updated',
            ]);
        });
    }
};
