<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // فحص الحقول المفقودة فقط وإضافتها
            if (!Schema::hasColumn('site_settings', 'contact_faqs')) {
                $table->json('contact_faqs')->nullable()->comment('الأسئلة الشائعة لصفحة الاتصال');
            }

            if (!Schema::hasColumn('site_settings', 'show_map')) {
                $table->boolean('show_map')->default(true)->comment('إظهار الخريطة في صفحة الاتصال');
            }

            if (!Schema::hasColumn('site_settings', 'show_faqs')) {
                $table->boolean('show_faqs')->default(true)->comment('إظهار الأسئلة الشائعة');
            }

            if (!Schema::hasColumn('site_settings', 'show_social_media')) {
                $table->boolean('show_social_media')->default(true)->comment('إظهار روابط وسائل التواصل');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->dropColumn([
                'contact_faqs',
                'show_map',
                'show_faqs',
                'show_social_media',
            ]);
        });
    }
};
