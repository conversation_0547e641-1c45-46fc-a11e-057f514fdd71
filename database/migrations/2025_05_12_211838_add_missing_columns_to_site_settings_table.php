<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إضافة الأعمدة المفقودة إذا لم تكن موجودة
            if (!Schema::hasColumn('site_settings', 'show_ratings')) {
                $table->boolean('show_ratings')->default(true)->comment('عرض تقييمات النجوم للمنتجات');
            }

            if (!Schema::hasColumn('site_settings', 'show_wishlist')) {
                $table->boolean('show_wishlist')->default(true)->comment('عرض المفضلة وكل ما يتعلق بها');
            }

            if (!Schema::hasColumn('site_settings', 'enable_multilingual')) {
                $table->boolean('enable_multilingual')->default(true)->comment('تفعيل تعدد اللغات في الموقع');
            }

            if (!Schema::hasColumn('site_settings', 'default_language')) {
                $table->string('default_language')->default('ar')->comment('اللغة الافتراضية للموقع');
            }

            if (!Schema::hasColumn('site_settings', 'show_features')) {
                $table->boolean('show_features')->default(true)->comment('عرض ميزات الموقع في الصفحة الرئيسية');
            }

            if (!Schema::hasColumn('site_settings', 'show_testimonials')) {
                $table->boolean('show_testimonials')->default(true)->comment('عرض آراء العملاء في الصفحة الرئيسية');
            }

            if (!Schema::hasColumn('site_settings', 'display_only_mode')) {
                $table->boolean('display_only_mode')->default(false)->comment('عرض المنتجات فقط بدون إمكانية الشراء');
            }

            if (!Schema::hasColumn('site_settings', 'show_newsletter')) {
                $table->boolean('show_newsletter')->default(true)->comment('عرض النشرة الإخبارية في الموقع');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // حذف الأعمدة المضافة
            $table->dropColumn([
                'show_ratings',
                'show_wishlist',
                'enable_multilingual',
                'default_language',
                'show_features',
                'show_testimonials',
                'display_only_mode',
                'show_newsletter'
            ]);
        });
    }
};
