<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('store_id')->nullable()->after('user_id')->constrained('stores');
            $table->decimal('subtotal', 10, 2)->nullable()->after('discount_amount');
            $table->decimal('tax', 10, 2)->nullable()->after('subtotal');
            $table->decimal('shipping', 10, 2)->nullable()->after('tax');
            $table->decimal('discount', 10, 2)->nullable()->after('shipping');
            $table->decimal('total', 10, 2)->nullable()->after('discount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['store_id']);
            $table->dropColumn('store_id');
            $table->dropColumn('subtotal');
            $table->dropColumn('tax');
            $table->dropColumn('shipping');
            $table->dropColumn('discount');
            $table->dropColumn('total');
        });
    }
};
