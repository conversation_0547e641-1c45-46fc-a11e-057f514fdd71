<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // إضافة الحقول الجديدة للعلاقات
        Schema::table('products', function (Blueprint $table) {
            $table->foreignId('metal_type_id')->nullable()->after('material_type')->constrained('metal_types')->onDelete('set null');
            $table->foreignId('metal_purity_id')->nullable()->after('metal_purity')->constrained('metal_purities')->onDelete('set null');
        });

        // نقل البيانات من الحقول النصية إلى العلاقات
        $this->migrateMetalTypeData();
        $this->migrateMetalPurityData();

        // إزالة الحقول غير المطلوبة
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'dimensions',
                'sku',
                'metal_type',
                'purity',
                'gallery'
            ]);
        });

        // تحويل الحقول النصية إلى nullable مؤقتاً قبل الحذف النهائي
        Schema::table('products', function (Blueprint $table) {
            $table->string('material_type')->nullable()->change();
            $table->string('metal_purity')->nullable()->change();
        });
    }

    /**
     * نقل بيانات أنواع المعادن
     */
    private function migrateMetalTypeData(): void
    {
        // الحصول على جميع المنتجات مع أنواع المعادن
        $products = DB::table('products')->whereNotNull('material_type')->get();

        foreach ($products as $product) {
            $metalTypeName = $this->mapMaterialTypeToMetalType($product->material_type);
            
            if ($metalTypeName) {
                $metalType = DB::table('metal_types')->where('name', $metalTypeName)->first();
                
                if ($metalType) {
                    DB::table('products')
                        ->where('id', $product->id)
                        ->update(['metal_type_id' => $metalType->id]);
                }
            }
        }
    }

    /**
     * نقل بيانات عيارات المعادن
     */
    private function migrateMetalPurityData(): void
    {
        // الحصول على جميع المنتجات مع عيارات المعادن
        $products = DB::table('products')->whereNotNull('metal_purity')->get();

        foreach ($products as $product) {
            $purityName = $this->mapMetalPurityName($product->metal_purity);
            
            if ($purityName) {
                $metalPurity = DB::table('metal_purities')->where('name', $purityName)->first();
                
                if ($metalPurity) {
                    DB::table('products')
                        ->where('id', $product->id)
                        ->update(['metal_purity_id' => $metalPurity->id]);
                }
            }
        }
    }

    /**
     * تحويل أنواع المواد إلى أنواع المعادن
     */
    private function mapMaterialTypeToMetalType(string $materialType): ?string
    {
        $mapping = [
            'ذهب' => 'gold',
            'فضة' => 'silver',
            'بلاتين' => 'platinum',
            'gold' => 'gold',
            'silver' => 'silver',
            'platinum' => 'platinum',
        ];

        return $mapping[$materialType] ?? null;
    }

    /**
     * تحويل عيارات المعادن
     */
    private function mapMetalPurityName(string $metalPurity): ?string
    {
        // إزالة المسافات والتنظيف
        $cleanPurity = trim($metalPurity);
        
        $mapping = [
            '24K' => '24K',
            '22K' => '22K',
            '21K' => '21K',
            '18K' => '18K',
            '14K' => '14K',
            '925' => '925',
            '950' => '950',
            '999' => '999',
        ];

        return $mapping[$cleanPurity] ?? null;
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة إضافة الحقول المحذوفة
        Schema::table('products', function (Blueprint $table) {
            $table->string('dimensions')->nullable()->after('weight');
            $table->string('sku')->nullable()->after('is_active');
            $table->string('metal_type')->nullable()->after('image');
            $table->string('purity')->nullable()->after('metal_type');
            $table->json('gallery')->nullable()->after('image');
        });

        // إعادة تحويل الحقول إلى مطلوبة
        Schema::table('products', function (Blueprint $table) {
            $table->string('material_type')->nullable(false)->change();
            $table->string('metal_purity')->nullable(false)->change();
        });

        // إزالة العلاقات الجديدة
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['metal_type_id']);
            $table->dropForeign(['metal_purity_id']);
            $table->dropColumn(['metal_type_id', 'metal_purity_id']);
        });
    }
};
