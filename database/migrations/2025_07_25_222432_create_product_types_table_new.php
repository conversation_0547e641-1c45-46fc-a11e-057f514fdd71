<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول أنواع المنتجات
 * يحتوي على تعريف أنواع السبائك والعملات المختلفة بأوزانها وعياراتها
 */
return new class extends Migration
{
    /**
     * تشغيل الـ migration - إنشاء جدول product_types
     */
    public function up(): void
    {
        // التحقق من عدم وجود الجدول قبل إنشاؤه
        if (Schema::hasTable('product_types')) {
            return;
        }

        Schema::create('product_types', function (Blueprint $table) {
            // المفتاح الأساسي
            $table->id()->comment('المعرف الفريد لنوع المنتج');

            // معلومات المنتج الأساسية
            $table->string('name', 255)->comment('اسم نوع المنتج (مثل: سبيكة 1 جرام)');
            $table->enum('type', ['سبيكة', 'عملة'])->comment('نوع المنتج: سبيكة أو عملة');

            // خصائص المنتج
            $table->decimal('weight', 8, 3)->comment('وزن المنتج بالجرام');
            $table->enum('metal_purity', ['24', '21', '18'])->comment('عيار المعدن (24، 21، 18)');

            // حالة النشاط
            $table->boolean('is_active')->default(true)->comment('حالة نشاط نوع المنتج');

            // طوابع زمنية
            $table->timestamps();

            // الفهارس
            $table->index(['type', 'metal_purity'], 'product_types_type_metal_purity_index');
            $table->index('is_active', 'product_types_is_active_index');
            $table->index('type');
            $table->index('weight');
        });
    }

    /**
     * التراجع عن الـ migration - حذف جدول product_types
     */
    public function down(): void
    {
        Schema::dropIfExists('product_types');
    }
};
