<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Inicio de sesión con redes sociales
            $table->boolean('enable_social_login')->default(false);
            $table->boolean('enable_facebook_login')->default(false);
            $table->boolean('enable_google_login')->default(false);
            $table->boolean('enable_twitter_login')->default(false);

            // Claves de API para inicio de sesión con redes sociales
            $table->string('facebook_app_id')->nullable();
            $table->string('facebook_app_secret')->nullable();
            $table->string('google_client_id')->nullable();
            $table->string('google_client_secret')->nullable();
            $table->string('twitter_client_id')->nullable();
            $table->string('twitter_client_secret')->nullable();

            // Compartir en redes sociales
            $table->boolean('enable_social_sharing')->default(true);
            $table->boolean('share_on_facebook')->default(true);
            $table->boolean('share_on_twitter')->default(true);
            $table->boolean('share_on_whatsapp')->default(true);
            $table->boolean('share_on_pinterest')->default(false);
            $table->boolean('share_on_linkedin')->default(false);

            // Feeds de redes sociales
            $table->boolean('show_instagram_feed')->default(false);
            $table->string('instagram_token')->nullable();
            $table->integer('instagram_count')->default(6);
            $table->boolean('show_facebook_feed')->default(false);
            $table->string('facebook_page_id')->nullable();
            $table->integer('facebook_count')->default(3);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Eliminar inicio de sesión con redes sociales
            $table->dropColumn([
                'enable_social_login',
                'enable_facebook_login',
                'enable_google_login',
                'enable_twitter_login',
            ]);

            // Eliminar claves de API para inicio de sesión con redes sociales
            $table->dropColumn([
                'facebook_app_id',
                'facebook_app_secret',
                'google_client_id',
                'google_client_secret',
                'twitter_client_id',
                'twitter_client_secret',
            ]);

            // Eliminar compartir en redes sociales
            $table->dropColumn([
                'enable_social_sharing',
                'share_on_facebook',
                'share_on_twitter',
                'share_on_whatsapp',
                'share_on_pinterest',
                'share_on_linkedin',
            ]);

            // Eliminar feeds de redes sociales
            $table->dropColumn([
                'show_instagram_feed',
                'instagram_token',
                'instagram_count',
                'show_facebook_feed',
                'facebook_page_id',
                'facebook_count',
            ]);
        });
    }
};
