<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('image')->nullable()->after('sku');
            $table->json('gallery')->nullable()->after('image');
            $table->string('metal_type')->nullable()->after('gallery');
            $table->string('purity')->nullable()->after('metal_type');
            $table->decimal('sale_price', 12, 2)->nullable()->after('price');
            $table->decimal('old_price', 12, 2)->nullable()->after('sale_price');
            $table->decimal('discount_percentage', 5, 2)->nullable()->after('old_price');
            $table->boolean('featured')->default(false)->after('is_featured');
            $table->integer('stock')->default(0)->after('stock_quantity');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'image',
                'gallery',
                'metal_type',
                'purity',
                'sale_price',
                'old_price',
                'discount_percentage',
                'featured',
                'stock',
            ]);
        });
    }
};
