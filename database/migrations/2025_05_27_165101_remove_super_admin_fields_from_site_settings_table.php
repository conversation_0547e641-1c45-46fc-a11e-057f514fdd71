<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إزالة الحقول المنقولة إلى super_admin_settings
            $table->dropColumn([
                'show_ratings',
                'show_wishlist',
                'enable_guest_checkout',
                'enable_local_pickup',
                'enable_multilingual',
                'tax_rate',
                'prices_include_tax',
                'min_order_amount',
                'order_prefix',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إعادة إضافة الحقول المحذوفة
            $table->boolean('show_ratings')->default(true);
            $table->boolean('show_wishlist')->default(true);
            $table->boolean('enable_guest_checkout')->default(true);
            $table->boolean('enable_local_pickup')->default(false);
            $table->boolean('enable_multilingual')->default(true);
            $table->decimal('tax_rate', 5, 2)->default(14.00);
            $table->boolean('prices_include_tax')->default(true);
            $table->decimal('min_order_amount', 10, 2)->default(0.00);
            $table->string('order_prefix', 10)->default('MGJ-');
        });
    }
};
