<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null')->comment('المستخدم الذي أضاف التعليق');
            $table->string('name')->nullable()->comment('اسم المعلق (للزوار)');
            $table->string('email')->nullable()->comment('البريد الإلكتروني للمعلق (للزوار)');
            $table->text('content')->comment('محتوى التعليق');
            $table->morphs('commentable'); // العنصر الذي تم التعليق عليه (مقال، منتج، إلخ)
            $table->foreignId('parent_id')->nullable()->constrained('comments')->onDelete('cascade')->comment('التعليق الأب (للردود)');
            $table->boolean('is_approved')->default(false)->comment('هل التعليق معتمد');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
};
