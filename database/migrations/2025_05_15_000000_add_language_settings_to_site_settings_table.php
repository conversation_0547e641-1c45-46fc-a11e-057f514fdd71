<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إضافة الأعمدة فقط إذا لم تكن موجودة
            if (!Schema::hasColumn('site_settings', 'enable_multilingual')) {
                $table->boolean('enable_multilingual')->default(true)->comment('تفعيل تعدد اللغات في الموقع');
            }

            if (!Schema::hasColumn('site_settings', 'default_language')) {
                $table->string('default_language')->default('ar')->comment('اللغة الافتراضية للموقع');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // حذف الأعمدة إذا كانت موجودة
            $columns = [];

            if (Schema::hasColumn('site_settings', 'enable_multilingual')) {
                $columns[] = 'enable_multilingual';
            }

            if (Schema::hasColumn('site_settings', 'default_language')) {
                $columns[] = 'default_language';
            }

            if (!empty($columns)) {
                $table->dropColumn($columns);
            }
        });
    }
};
