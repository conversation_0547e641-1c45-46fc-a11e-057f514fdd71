<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // جعل service_type_ar و service_type_en اختياريين
            $table->string('service_type_ar')->nullable()->change();
            $table->string('service_type_en')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // إعادة service_type_ar و service_type_en إلى NOT NULL
            $table->string('service_type_ar')->nullable(false)->change();
            $table->string('service_type_en')->nullable(false)->change();
        });
    }
};
