<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول الشركات
 * يحتوي على معلومات الشركات المصنعة للسبائك والعملات الذهبية
 */
return new class extends Migration
{
    /**
     * تشغيل الـ migration - إنشاء جدول companies
     */
    public function up(): void
    {
        // التحقق من عدم وجود الجدول قبل إنشاؤه
        if (Schema::hasTable('companies')) {
            return;
        }

        Schema::create('companies', function (Blueprint $table) {
            // المفتاح الأساسي
            $table->id()->comment('المعرف الفريد للشركة');

            // معلومات الشركة الأساسية
            $table->string('name', 255)->comment('اسم الشركة');
            $table->string('logo', 255)->nullable()->comment('مسار لوجو الشركة');

            // حالة النشاط
            $table->boolean('is_active')->default(true)->comment('حالة نشاط الشركة');

            // طوابع زمنية
            $table->timestamps();

            // الفهارس
            $table->index('name', 'companies_name_index');
            $table->index('is_active');
        });
    }

    /**
     * التراجع عن الـ migration - حذف جدول companies
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
    }
};
