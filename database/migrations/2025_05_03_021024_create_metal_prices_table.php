<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('metal_prices', function (Blueprint $table) {
            $table->id();
            $table->enum('metal_type', ['gold', 'silver', 'platinum']);
            $table->string('purity')->comment('Karat or percentage');
            $table->decimal('price_per_gram', 12, 2);
            $table->decimal('price_per_ounce', 12, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->date('price_date');
            $table->string('source')->nullable()->comment('API or source of price data');
            $table->timestamps();

            // Unique constraint to prevent duplicate entries for the same metal, purity, and date
            $table->unique(['metal_type', 'purity', 'price_date', 'currency']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('metal_prices');
    }
};
