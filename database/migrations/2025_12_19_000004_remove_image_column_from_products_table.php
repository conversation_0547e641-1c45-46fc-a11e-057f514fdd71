<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // حذف الحقول المتعلقة بالصور القديمة
            $table->dropColumn(['image', 'product_images', 'primary_image_index']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // إعادة إضافة الحقول في حالة rollback
            $table->string('image')->nullable()->after('show_price');
            $table->json('product_images')->nullable()->after('image');
            $table->integer('primary_image_index')->default(0)->after('product_images');
        });
    }
};
