<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('faqs', function (Blueprint $table) {
            $table->id();

            // الأسئلة والأجوبة متعددة اللغات
            $table->string('question_ar')->comment('السؤال بالعربية');
            $table->string('question_en')->nullable()->comment('السؤال بالإنجليزية');
            $table->text('answer_ar')->comment('الإجابة بالعربية');
            $table->text('answer_en')->nullable()->comment('الإجابة بالإنجليزية');

            // التصنيف والترتيب
            $table->string('category')->default('general')->comment('تصنيف السؤال');
            $table->integer('sort_order')->default(0)->comment('ترتيب العرض');

            // الحالة والنشر
            $table->boolean('is_active')->default(true)->comment('نشط/غير نشط');
            $table->boolean('is_featured')->default(false)->comment('مميز في الصفحة الرئيسية');

            // إحصائيات
            $table->integer('views')->default(0)->comment('عدد المشاهدات');
            $table->integer('helpful_votes')->default(0)->comment('عدد الأصوات المفيدة');

            // معلومات إضافية
            $table->json('tags')->nullable()->comment('العلامات');
            $table->string('slug')->unique()->nullable()->comment('الرابط الفريد');

            $table->timestamps();

            // الفهارس
            $table->index(['is_active', 'sort_order']);
            $table->index(['category', 'is_active']);
            $table->index('is_featured');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('faqs');
    }
};
