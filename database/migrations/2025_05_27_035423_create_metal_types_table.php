<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('metal_types', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique(); // gold, silver, gold_coin
            $table->string('name_ar'); // ذهب, فضة, جنيهات ذهبية
            $table->string('icon')->nullable(); // أيقونة المعدن
            $table->string('color')->default('#6b7280'); // لون المعدن في الواجهة
            $table->text('description')->nullable(); // وصف المعدن
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('metal_types');
    }
};
