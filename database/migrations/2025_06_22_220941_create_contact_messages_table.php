<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_messages', function (Blueprint $table) {
            $table->id();

            // معلومات المرسل
            $table->string('name');
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('subject');
            $table->text('message');

            // حالة الرسالة
            $table->enum('status', ['new', 'read', 'replied'])->default('new');

            // معلومات الرد
            $table->text('reply_message')->nullable();
            $table->timestamp('replied_at')->nullable();
            $table->unsignedBigInteger('replied_by')->nullable();

            // معلومات إضافية
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();

            // تواريخ
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            // فهارس
            $table->index(['status', 'created_at']);
            $table->index('email');

            // علاقات خارجية
            $table->foreign('replied_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_messages');
    }
};
