<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('metal_prices', function (Blueprint $table) {
            // إضافة عمود سعر الشراء بعد سعر البيع
            $table->decimal('purchase_price_per_gram', 12, 2)->nullable()->after('price_per_gram');
            $table->decimal('purchase_price_per_ounce', 12, 2)->nullable()->after('price_per_ounce');
            $table->decimal('purchase_price_per_piece', 12, 2)->nullable()->after('price_per_piece');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('metal_prices', function (Blueprint $table) {
            $table->dropColumn([
                'purchase_price_per_gram',
                'purchase_price_per_ounce', 
                'purchase_price_per_piece'
            ]);
        });
    }
};
