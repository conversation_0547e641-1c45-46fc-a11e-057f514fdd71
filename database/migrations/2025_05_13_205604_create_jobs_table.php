<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('career_jobs', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('عنوان الوظيفة');
            $table->string('slug')->unique()->comment('المعرف الفريد للوظيفة في الرابط');
            $table->text('description')->comment('وصف الوظيفة');
            $table->text('requirements')->comment('متطلبات الوظيفة');
            $table->text('responsibilities')->nullable()->comment('مسؤوليات الوظيفة');
            $table->string('location')->comment('موقع الوظيفة');
            $table->string('type')->comment('نوع الوظيفة (دوام كامل، دوام جزئي، إلخ)');
            $table->string('department')->nullable()->comment('القسم');
            $table->decimal('salary_min', 10, 2)->nullable()->comment('الحد الأدنى للراتب');
            $table->decimal('salary_max', 10, 2)->nullable()->comment('الحد الأقصى للراتب');
            $table->boolean('is_active')->default(true)->comment('حالة الوظيفة (نشطة/غير نشطة)');
            $table->boolean('is_featured')->default(false)->comment('هل الوظيفة مميزة');
            $table->timestamp('expires_at')->nullable()->comment('تاريخ انتهاء الوظيفة');
            $table->json('translations')->nullable()->comment('ترجمات الوظيفة');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('career_jobs');
    }
};
