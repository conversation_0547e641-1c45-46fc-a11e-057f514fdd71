<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إضافة إعداد عرض أسعار الفضة بعد إعداد أسعار الذهب
            $table->boolean('show_silver_prices')->default(true)->after('show_gold_prices')
                ->comment('عرض أسعار الفضة في الصفحة الرئيسية');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->dropColumn('show_silver_prices');
        });
    }
};
