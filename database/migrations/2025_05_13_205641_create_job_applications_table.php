<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('job_applications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('job_id')->constrained('career_jobs')->onDelete('cascade')->comment('الوظيفة المتقدم لها');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null')->comment('المستخدم المتقدم (إذا كان مسجلاً)');
            $table->string('name')->comment('اسم المتقدم');
            $table->string('email')->comment('البريد الإلكتروني للمتقدم');
            $table->string('phone')->comment('رقم هاتف المتقدم');
            $table->text('cover_letter')->nullable()->comment('رسالة التقديم');
            $table->string('resume')->comment('السيرة الذاتية (مسار الملف)');
            $table->string('status')->default('pending')->comment('حالة الطلب (قيد المراجعة، مقبول، مرفوض)');
            $table->text('notes')->nullable()->comment('ملاحظات داخلية');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('job_applications');
    }
};
