<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_posts', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('عنوان المقال');
            $table->string('slug')->unique()->comment('المعرف الفريد للمقال في الرابط');
            $table->text('excerpt')->nullable()->comment('مقتطف من المقال');
            $table->text('content')->comment('محتوى المقال');
            $table->string('featured_image')->nullable()->comment('صورة المقال الرئيسية');
            $table->foreignId('author_id')->nullable()->constrained('users')->onDelete('set null')->comment('كاتب المقال');
            $table->foreignId('category_id')->nullable()->constrained('categories')->onDelete('set null')->comment('تصنيف المقال');
            $table->string('meta_title')->nullable()->comment('عنوان الميتا للمقال');
            $table->text('meta_description')->nullable()->comment('وصف الميتا للمقال');
            $table->string('meta_keywords')->nullable()->comment('الكلمات المفتاحية للمقال');
            $table->boolean('is_featured')->default(false)->comment('هل المقال مميز');
            $table->boolean('is_active')->default(true)->comment('حالة المقال (نشط/غير نشط)');
            $table->integer('views')->default(0)->comment('عدد مشاهدات المقال');
            $table->timestamp('published_at')->nullable()->comment('تاريخ نشر المقال');
            $table->json('translations')->nullable()->comment('ترجمات المقال');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_posts');
    }
};
