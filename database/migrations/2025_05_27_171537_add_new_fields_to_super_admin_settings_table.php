<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('super_admin_settings', function (Blueprint $table) {
            // حالة الموقع والصيانة
            $table->boolean('maintenance_mode')->default(false);
            $table->text('maintenance_message')->nullable();
            $table->boolean('display_only_mode')->default(false);

            // إعدادات اللغة
            $table->string('default_language', 2)->default('ar');

            // إعدادات الدفع
            $table->boolean('enable_credit_card')->default(true);
            $table->boolean('enable_paypal')->default(true);
            $table->boolean('enable_bank_transfer')->default(false);
            $table->boolean('enable_cash_on_delivery')->default(false);
            $table->boolean('enable_fawry')->default(false);

            // إعدادات Stripe
            $table->string('stripe_key')->nullable();
            $table->string('stripe_secret')->nullable();
            $table->boolean('stripe_sandbox_mode')->default(true);

            // إعدادات PayPal
            $table->string('paypal_client_id')->nullable();
            $table->string('paypal_secret')->nullable();
            $table->boolean('paypal_sandbox_mode')->default(true);

            // إعدادات الشحن المتقدمة
            $table->decimal('local_pickup_discount', 10, 2)->default(0.00);
            $table->text('shipping_policy')->nullable();

            // إعدادات الفواتير
            $table->boolean('enable_invoices')->default(true);
            $table->string('invoice_prefix', 10)->default('INV-');
            $table->string('company_name_invoice')->nullable();
            $table->text('company_address_invoice')->nullable();
            $table->string('company_tax_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('super_admin_settings', function (Blueprint $table) {
            $table->dropColumn([
                'maintenance_mode',
                'maintenance_message',
                'display_only_mode',
                'default_language',
                'enable_credit_card',
                'enable_paypal',
                'enable_bank_transfer',
                'enable_cash_on_delivery',
                'enable_fawry',
                'stripe_key',
                'stripe_secret',
                'stripe_sandbox_mode',
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox_mode',
                'local_pickup_discount',
                'shipping_policy',
                'enable_invoices',
                'invoice_prefix',
                'company_name_invoice',
                'company_address_invoice',
                'company_tax_id',
            ]);
        });
    }
};
