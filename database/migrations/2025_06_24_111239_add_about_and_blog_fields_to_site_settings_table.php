<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // حقول صفحة من نحن
            $table->text('about_hero_title_ar')->nullable()->comment('عنوان البطل في صفحة من نحن - عربي');
            $table->text('about_hero_title_en')->nullable()->comment('عنوان البطل في صفحة من نحن - إنجليزي');
            $table->text('about_hero_subtitle_ar')->nullable()->comment('العنوان الفرعي للبطل - عربي');
            $table->text('about_hero_subtitle_en')->nullable()->comment('العنوان الفرعي للبطل - إنجليزي');
            $table->string('about_hero_image')->nullable()->comment('صورة البطل في صفحة من نحن');

            // قيم الشركة
            $table->json('company_values')->nullable()->comment('قيم الشركة (JSON)');

            // فريق العمل
            $table->json('team_members')->nullable()->comment('أعضاء فريق العمل (JSON)');

            // آراء العملاء
            $table->json('testimonials')->nullable()->comment('آراء العملاء (JSON)');

            // إعدادات عرض صفحة من نحن
            $table->boolean('show_about_values')->default(true)->comment('إظهار قسم القيم');
            $table->boolean('show_about_team')->default(true)->comment('إظهار قسم الفريق');
            $table->boolean('show_about_testimonials')->default(true)->comment('إظهار قسم آراء العملاء');
            $table->boolean('show_about_cta')->default(true)->comment('إظهار قسم الدعوة للعمل');

            // حقول المدونة
            $table->text('blog_hero_title_ar')->nullable()->comment('عنوان البطل في صفحة المدونة - عربي');
            $table->text('blog_hero_title_en')->nullable()->comment('عنوان البطل في صفحة المدونة - إنجليزي');
            $table->text('blog_hero_subtitle_ar')->nullable()->comment('العنوان الفرعي للمدونة - عربي');
            $table->text('blog_hero_subtitle_en')->nullable()->comment('العنوان الفرعي للمدونة - إنجليزي');
            $table->string('blog_hero_image')->nullable()->comment('صورة البطل في صفحة المدونة');

            // إعدادات عرض المدونة
            $table->boolean('show_blog_featured')->default(true)->comment('إظهار المقالات المميزة');
            $table->boolean('show_blog_categories')->default(true)->comment('إظهار تصنيفات المدونة');
            $table->boolean('show_blog_search')->default(true)->comment('إظهار البحث في المدونة');
            $table->integer('blog_posts_per_page')->default(9)->comment('عدد المقالات في الصفحة');

            // إعدادات عامة للصفحات
            $table->string('company_founded_year')->nullable()->comment('سنة تأسيس الشركة');
            $table->text('company_mission_ar')->nullable()->comment('رسالة الشركة - عربي');
            $table->text('company_mission_en')->nullable()->comment('رسالة الشركة - إنجليزي');
            $table->text('company_vision_ar')->nullable()->comment('رؤية الشركة - عربي');
            $table->text('company_vision_en')->nullable()->comment('رؤية الشركة - إنجليزي');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->dropColumn([
                'about_hero_title_ar',
                'about_hero_title_en',
                'about_hero_subtitle_ar',
                'about_hero_subtitle_en',
                'about_hero_image',
                'company_values',
                'team_members',
                'testimonials',
                'show_about_values',
                'show_about_team',
                'show_about_testimonials',
                'show_about_cta',
                'blog_hero_title_ar',
                'blog_hero_title_en',
                'blog_hero_subtitle_ar',
                'blog_hero_subtitle_en',
                'blog_hero_image',
                'show_blog_featured',
                'show_blog_categories',
                'show_blog_search',
                'blog_posts_per_page',
                'company_founded_year',
                'company_mission_ar',
                'company_mission_en',
                'company_vision_ar',
                'company_vision_en',
            ]);
        });
    }
};
