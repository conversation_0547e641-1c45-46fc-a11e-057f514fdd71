<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('metal_prices', function (Blueprint $table) {
            // تعديل العمود ليكون له قيمة افتراضية
            $table->timestamp('price_date')->default(now())->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('metal_prices', function (Blueprint $table) {
            // إرجاع العمود لحالته الأصلية
            $table->timestamp('price_date')->nullable(false)->change();
        });
    }
};
