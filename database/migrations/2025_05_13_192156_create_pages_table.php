<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('عنوان الصفحة');
            $table->string('slug')->unique()->comment('المعرف الفريد للصفحة في الرابط');
            $table->text('content')->nullable()->comment('محتوى الصفحة');
            $table->string('meta_title')->nullable()->comment('عنوان الميتا للصفحة');
            $table->text('meta_description')->nullable()->comment('وصف الميتا للصفحة');
            $table->string('meta_keywords')->nullable()->comment('الكلمات المفتاحية للصفحة');
            $table->boolean('is_active')->default(true)->comment('حالة الصفحة (نشطة/غير نشطة)');
            $table->integer('order')->default(0)->comment('ترتيب الصفحة');
            $table->string('type')->default('page')->comment('نوع الصفحة (صفحة عادية، سياسة، شروط، إلخ)');
            $table->string('section')->nullable()->comment('القسم الذي تنتمي إليه الصفحة (خدمة العملاء، القانونية، إلخ)');
            $table->json('translations')->nullable()->comment('ترجمات الصفحة');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};
