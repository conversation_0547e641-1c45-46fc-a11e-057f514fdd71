<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // إضافة الحقول المفقودة فقط إذا لم تكن موجودة
            if (!Schema::hasColumn('site_settings', 'headquarters_address_ar')) {
                $table->text('headquarters_address_ar')->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'headquarters_address_en')) {
                $table->text('headquarters_address_en')->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'working_hours_ar')) {
                $table->text('working_hours_ar')->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'working_hours_en')) {
                $table->text('working_hours_en')->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'map_latitude')) {
                $table->decimal('map_latitude', 10, 7)->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'map_longitude')) {
                $table->decimal('map_longitude', 10, 7)->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'map_zoom')) {
                $table->integer('map_zoom')->default(15);
            }
            if (!Schema::hasColumn('site_settings', 'map_marker_title')) {
                $table->string('map_marker_title')->nullable();
            }
            if (!Schema::hasColumn('site_settings', 'google_maps_api_key')) {
                $table->string('google_maps_api_key')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $columnsToCheck = [
                'headquarters_address_ar',
                'headquarters_address_en',
                'working_hours_ar',
                'working_hours_en',
                'map_latitude',
                'map_longitude',
                'map_zoom',
                'map_marker_title',
                'google_maps_api_key',
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('site_settings', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
