<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->boolean('display_only_mode')->default(false)->comment('عرض المنتجات فقط بدون إمكانية الشراء');
            $table->boolean('show_ratings')->default(true)->comment('عرض تقييمات النجوم للمنتجات');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            $table->dropColumn(['display_only_mode', 'show_ratings']);
        });
    }
};
