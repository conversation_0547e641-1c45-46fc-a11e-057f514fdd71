<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Configuración de métodos de pago
            $table->boolean('enable_credit_card')->default(true);
            $table->boolean('enable_paypal')->default(true);
            $table->boolean('enable_bank_transfer')->default(false);
            $table->boolean('enable_cash_on_delivery')->default(false);
            $table->boolean('enable_fawry')->default(false);

            // Configuración de Stripe
            $table->string('stripe_key')->nullable();
            $table->string('stripe_secret')->nullable();
            $table->boolean('stripe_sandbox_mode')->default(true);

            // Configuración de PayPal
            $table->string('paypal_client_id')->nullable();
            $table->string('paypal_secret')->nullable();
            $table->boolean('paypal_sandbox_mode')->default(true);

            // Configuración de envío adicional
            $table->json('shipping_zones')->nullable();
            $table->boolean('enable_local_pickup')->default(false);
            $table->decimal('local_pickup_discount', 10, 2)->default(0);
            $table->text('shipping_policy')->nullable();

            // Configuración de pedidos
            $table->decimal('min_order_amount', 10, 2)->default(0);
            $table->integer('order_prefix')->nullable();
            $table->boolean('enable_guest_checkout')->default(true);

            // Configuración de facturación
            $table->boolean('enable_invoices')->default(true);
            $table->string('invoice_prefix')->nullable();
            $table->string('company_name_invoice')->nullable();
            $table->string('company_address_invoice')->nullable();
            $table->string('company_tax_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Eliminar configuración de métodos de pago
            $table->dropColumn([
                'enable_credit_card',
                'enable_paypal',
                'enable_bank_transfer',
                'enable_cash_on_delivery',
                'enable_fawry',
            ]);

            // Eliminar configuración de Stripe
            $table->dropColumn([
                'stripe_key',
                'stripe_secret',
                'stripe_sandbox_mode',
            ]);

            // Eliminar configuración de PayPal
            $table->dropColumn([
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox_mode',
            ]);

            // Eliminar configuración de envío adicional
            $table->dropColumn([
                'shipping_zones',
                'enable_local_pickup',
                'local_pickup_discount',
                'shipping_policy',
            ]);

            // Eliminar configuración de pedidos
            $table->dropColumn([
                'min_order_amount',
                'order_prefix',
                'enable_guest_checkout',
            ]);

            // Eliminar configuración de facturación
            $table->dropColumn([
                'enable_invoices',
                'invoice_prefix',
                'company_name_invoice',
                'company_address_invoice',
                'company_tax_id',
            ]);
        });
    }
};
