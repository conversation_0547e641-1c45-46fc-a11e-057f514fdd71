<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop the existing foreign key constraint
            $table->dropForeign(['store_id']);

            // Add the foreign key with SET NULL on delete
            $table->foreign('store_id')
                  ->references('id')
                  ->on('stores')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop the modified foreign key
            $table->dropForeign(['store_id']);

            // Restore the original foreign key constraint
            $table->foreign('store_id')
                  ->references('id')
                  ->on('stores');
        });
    }
};
