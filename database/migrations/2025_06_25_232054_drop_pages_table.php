<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::dropIfExists('pages');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // إعادة إنشاء جدول pages إذا لزم الأمر
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable()->comment('عنوان الصفحة');
            $table->string('title_ar')->nullable()->comment('العنوان بالعربية');
            $table->string('title_en')->nullable()->comment('العنوان بالإنجليزية');
            $table->string('slug')->unique()->comment('المعرف الفريد للصفحة في الرابط');
            $table->text('content')->nullable()->comment('محتوى الصفحة');
            $table->text('content_ar')->nullable()->comment('المحتوى بالعربية');
            $table->text('content_en')->nullable()->comment('المحتوى بالإنجليزية');
            $table->string('meta_title')->nullable()->comment('عنوان الميتا للصفحة');
            $table->string('meta_title_ar')->nullable()->comment('عنوان الميتا بالعربية');
            $table->string('meta_title_en')->nullable()->comment('عنوان الميتا بالإنجليزية');
            $table->text('meta_description')->nullable()->comment('وصف الميتا للصفحة');
            $table->text('meta_description_ar')->nullable()->comment('وصف الميتا بالعربية');
            $table->text('meta_description_en')->nullable()->comment('وصف الميتا بالإنجليزية');
            $table->string('meta_keywords')->nullable()->comment('الكلمات المفتاحية للصفحة');
            $table->string('meta_keywords_ar')->nullable()->comment('الكلمات المفتاحية بالعربية');
            $table->string('meta_keywords_en')->nullable()->comment('الكلمات المفتاحية بالإنجليزية');
            $table->string('featured_image')->nullable()->comment('الصورة المميزة للصفحة');
            $table->text('excerpt')->nullable()->comment('مقتطف من الصفحة');
            $table->text('excerpt_ar')->nullable()->comment('مقتطف بالعربية');
            $table->text('excerpt_en')->nullable()->comment('مقتطف بالإنجليزية');
            $table->boolean('is_active')->default(true)->comment('حالة الصفحة (نشطة/غير نشطة)');
            $table->boolean('show_in_menu')->default(false)->comment('إظهار في القائمة');
            $table->boolean('show_breadcrumbs')->default(true)->comment('إظهار مسار التنقل');
            $table->boolean('allow_comments')->default(false)->comment('السماح بالتعليقات');
            $table->integer('order')->default(0)->comment('ترتيب الصفحة');
            $table->integer('views_count')->default(0)->comment('عدد المشاهدات');
            $table->timestamp('published_at')->nullable()->comment('تاريخ النشر');
            $table->string('type')->default('page')->comment('نوع الصفحة (صفحة عادية، سياسة، شروط، إلخ)');
            $table->string('section')->nullable()->comment('القسم الذي تنتمي إليه الصفحة (خدمة العملاء، القانونية، إلخ)');
            $table->string('template')->nullable()->comment('قالب الصفحة المخصص');
            $table->json('custom_fields')->nullable()->comment('حقول مخصصة إضافية');
            $table->json('translations')->nullable()->comment('ترجمات الصفحة');
            $table->timestamps();

            // الفهارس
            $table->index(['type', 'is_active']);
            $table->index(['section', 'is_active']);
            $table->index(['show_in_menu', 'is_active']);
            $table->index('published_at');
        });
    }
};
