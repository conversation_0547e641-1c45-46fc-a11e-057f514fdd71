<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('metal_prices', function (Blueprint $table) {
            // إضافة حقل السعر لكل قطعة للجنيهات الذهبية
            $table->decimal('price_per_piece', 12, 2)->nullable()->after('price_per_ounce');
        });

        // تحديث enum لإضافة gold_coin
        DB::statement("ALTER TABLE metal_prices MODIFY COLUMN metal_type ENUM('gold', 'silver', 'platinum', 'gold_coin')");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('metal_prices', function (Blueprint $table) {
            // حذف حقل السعر لكل قطعة
            $table->dropColumn('price_per_piece');
        });

        // إرجاع enum إلى الحالة السابقة
        DB::statement("ALTER TABLE metal_prices MODIFY COLUMN metal_type ENUM('gold', 'silver', 'platinum')");
    }
};
