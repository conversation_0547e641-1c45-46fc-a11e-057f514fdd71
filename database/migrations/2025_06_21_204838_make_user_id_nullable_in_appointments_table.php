<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // جعل user_id اختياري للسماح بالحجز للزوار
            $table->foreignId('user_id')->nullable()->change();

            // جعل store_id اختياري أيضاً لأننا نستخدم store كـ string
            $table->foreignId('store_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('appointments', function (Blueprint $table) {
            // إعادة user_id و store_id إلى NOT NULL
            $table->foreignId('user_id')->nullable(false)->change();
            $table->foreignId('store_id')->nullable(false)->change();
        });
    }
};
