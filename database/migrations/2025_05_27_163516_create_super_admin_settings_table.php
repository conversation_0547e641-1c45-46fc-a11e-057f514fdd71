<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('super_admin_settings', function (Blueprint $table) {
            $table->id();

            // ميزات الموقع
            $table->boolean('show_ratings')->default(true);
            $table->boolean('show_wishlist')->default(true);
            $table->boolean('enable_guest_checkout')->default(true);
            $table->boolean('enable_local_pickup')->default(false);
            $table->boolean('enable_multilingual')->default(true);

            // الضرائب والأسعار
            $table->decimal('tax_rate', 5, 2)->default(14.00);
            $table->boolean('prices_include_tax')->default(true);
            $table->decimal('min_order_amount', 10, 2)->default(0.00);
            $table->string('order_prefix', 10)->default('MGJ-');

            // إعدادات التسجيل
            $table->boolean('enable_registration')->default(true);
            $table->text('registration_disabled_message')->nullable();

            // إعدادات العملات
            $table->string('default_currency', 3)->default('EGP');
            $table->json('supported_currencies')->nullable();
            $table->string('currency_api_key')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('super_admin_settings');
    }
};
