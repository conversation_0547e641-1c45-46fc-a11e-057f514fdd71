<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Textos legales
            $table->longText('privacy_policy')->nullable();
            $table->longText('terms_conditions')->nullable();
            $table->longText('return_policy')->nullable();
            $table->longText('shipping_policy_text')->nullable();

            // Cookies
            $table->boolean('show_cookie_banner')->default(true);
            $table->text('cookie_banner_text')->nullable();
            $table->string('cookie_banner_button_text')->nullable();

            // GDPR
            $table->boolean('enable_gdpr_compliance')->default(false);
            $table->text('gdpr_compliance_text')->nullable();

            // Consentimiento de marketing
            $table->boolean('require_marketing_consent')->default(false);
            $table->text('marketing_consent_text')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('site_settings', function (Blueprint $table) {
            // Eliminar textos legales
            $table->dropColumn([
                'privacy_policy',
                'terms_conditions',
                'return_policy',
                'shipping_policy_text',
            ]);

            // Eliminar cookies
            $table->dropColumn([
                'show_cookie_banner',
                'cookie_banner_text',
                'cookie_banner_button_text',
            ]);

            // Eliminar GDPR
            $table->dropColumn([
                'enable_gdpr_compliance',
                'gdpr_compliance_text',
            ]);

            // Eliminar consentimiento de marketing
            $table->dropColumn([
                'require_marketing_consent',
                'marketing_consent_text',
            ]);
        });
    }
};
