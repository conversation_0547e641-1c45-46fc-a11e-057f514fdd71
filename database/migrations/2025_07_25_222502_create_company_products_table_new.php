<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * Migration لإنشاء جدول منتجات الشركات
 * يربط بين الشركات وأنواع المنتجات مع تحديد تكلفة التصنيع وقيمة الاسترداد
 */
return new class extends Migration
{
    /**
     * تشغيل الـ migration - إنشاء جدول company_products
     */
    public function up(): void
    {
        // التحقق من عدم وجود الجدول قبل إنشاؤه
        if (Schema::hasTable('company_products')) {
            return;
        }

        Schema::create('company_products', function (Blueprint $table) {
            // المفتاح الأساسي
            $table->id()->comment('المعرف الفريد لمنتج الشركة');

            // المفاتيح الخارجية
            $table->foreignId('company_id')
                  ->constrained('companies')
                  ->onDelete('cascade')
                  ->comment('معرف الشركة المصنعة');

            $table->foreignId('product_type_id')
                  ->constrained('product_types')
                  ->onDelete('cascade')
                  ->comment('معرف نوع المنتج');

            // معلومات التسعير
            $table->decimal('manufacturing_cost_per_gram', 8, 2)
                  ->comment('تكلفة التصنيع للجرام الواحد بالجنيه المصري');

            $table->decimal('refund_value_per_gram', 8, 2)
                  ->comment('قيمة الاسترداد للجرام الواحد بالجنيه المصري');

            // طوابع زمنية
            $table->timestamps();

            // الفهارس
            $table->index('company_id', 'company_products_company_id_index');
            $table->index('product_type_id');

            // فهرس مركب لضمان عدم تكرار نفس المنتج لنفس الشركة
            $table->unique(['company_id', 'product_type_id'], 'company_product_unique');
        });
    }

    /**
     * التراجع عن الـ migration - حذف جدول company_products
     */
    public function down(): void
    {
        Schema::dropIfExists('company_products');
    }
};
