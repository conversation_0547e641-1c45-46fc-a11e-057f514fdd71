<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('super_admin_settings', function (Blueprint $table) {
            // تكامل وسائل التواصل الاجتماعي
            $table->boolean('enable_social_login')->default(false);
            $table->boolean('enable_facebook_login')->default(false);
            $table->boolean('enable_google_login')->default(false);
            $table->boolean('enable_twitter_login')->default(false);

            // مفاتيح API لتسجيل الدخول
            $table->string('facebook_app_id')->nullable();
            $table->string('facebook_app_secret')->nullable();
            $table->string('google_client_id')->nullable();
            $table->string('google_client_secret')->nullable();
            $table->string('twitter_client_id')->nullable();
            $table->string('twitter_client_secret')->nullable();

            // مشاركة المنتجات
            $table->boolean('enable_social_sharing')->default(true);
            $table->boolean('share_on_facebook')->default(true);
            $table->boolean('share_on_twitter')->default(true);
            $table->boolean('share_on_whatsapp')->default(true);
            $table->boolean('share_on_pinterest')->default(false);
            $table->boolean('share_on_linkedin')->default(false);

            // Feeds وسائل التواصل
            $table->boolean('show_instagram_feed')->default(false);
            $table->string('instagram_token')->nullable();
            $table->integer('instagram_count')->default(6);
            $table->boolean('show_facebook_feed')->default(false);
            $table->string('facebook_page_id')->nullable();
            $table->integer('facebook_count')->default(5);

            // ملفات تعريف الارتباط والخصوصية
            $table->boolean('show_cookie_banner')->default(true);
            $table->text('cookie_banner_text')->nullable();
            $table->string('cookie_banner_button_text')->default('أوافق');
            $table->boolean('enable_gdpr_compliance')->default(false);
            $table->text('gdpr_compliance_text')->nullable();
            $table->boolean('require_marketing_consent')->default(false);
            $table->text('marketing_consent_text')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('super_admin_settings', function (Blueprint $table) {
            // حذف حقول تكامل وسائل التواصل الاجتماعي
            $table->dropColumn([
                'enable_social_login',
                'enable_facebook_login',
                'enable_google_login',
                'enable_twitter_login',
            ]);

            // حذف مفاتيح API لتسجيل الدخول
            $table->dropColumn([
                'facebook_app_id',
                'facebook_app_secret',
                'google_client_id',
                'google_client_secret',
                'twitter_client_id',
                'twitter_client_secret',
            ]);

            // حذف حقول مشاركة المنتجات
            $table->dropColumn([
                'enable_social_sharing',
                'share_on_facebook',
                'share_on_twitter',
                'share_on_whatsapp',
                'share_on_pinterest',
                'share_on_linkedin',
            ]);

            // حذف Feeds وسائل التواصل
            $table->dropColumn([
                'show_instagram_feed',
                'instagram_token',
                'instagram_count',
                'show_facebook_feed',
                'facebook_page_id',
                'facebook_count',
            ]);

            // حذف ملفات تعريف الارتباط والخصوصية
            $table->dropColumn([
                'show_cookie_banner',
                'cookie_banner_text',
                'cookie_banner_button_text',
                'enable_gdpr_compliance',
                'gdpr_compliance_text',
                'require_marketing_consent',
                'marketing_consent_text',
            ]);
        });
    }
};
