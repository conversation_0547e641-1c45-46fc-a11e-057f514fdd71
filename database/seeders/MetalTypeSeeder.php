<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MetalTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('metal_types')->count() > 0) {
            $this->command->info('metal_types data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding metal_types...');

        $data = [
            [
                'id' => 1,
                'name' => 'gold',
                'name_ar' => 'ذهب',
                'icon' => 'fas fa-coins',
                'color' => '#fbbf24',
                'description' => 'المعدن الثمين الأكثر شهرة وقيمة',
                'is_active' => 1,
                'sort_order' => 1,
                'created_at' => '2025-05-29 02:36:13',
                'updated_at' => '2025-05-29 02:36:13',
            ],
            [
                'id' => 2,
                'name' => 'silver',
                'name_ar' => 'فضة',
                'icon' => 'fas fa-coins',
                'color' => '#9ca3af',
                'description' => 'المعدن الثمين الأبيض اللامع',
                'is_active' => 1,
                'sort_order' => 2,
                'created_at' => '2025-05-29 02:36:13',
                'updated_at' => '2025-05-29 02:36:13',
            ],
            [
                'id' => 3,
                'name' => 'gold_coin',
                'name_ar' => 'جنيهات ذهبية',
                'icon' => 'fas fa-coins',
                'color' => '#f59e0b',
                'description' => 'الجنيهات الذهبية والعملات الذهبية',
                'is_active' => 1,
                'sort_order' => 3,
                'created_at' => '2025-05-29 02:36:13',
                'updated_at' => '2025-05-29 02:36:13',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('metal_types')->insert($chunk);
        }

        $this->command->info('metal_types seeded successfully!');
    }
}
