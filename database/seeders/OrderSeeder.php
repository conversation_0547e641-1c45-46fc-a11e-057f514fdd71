<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\User;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure we have users
        if (User::role('user')->count() === 0) {
            $this->call(DatabaseSeeder::class);
        }

        // Get customer IDs
        $customerIds = User::role('user')->pluck('id')->toArray();

        // Create orders with different statuses
        $statuses = ['pending', 'processing', 'completed', 'cancelled', 'refunded'];
        $paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
        $paymentMethods = ['cash', 'credit_card', 'bank_transfer'];

        // التحقق من وجود طلبات مسبقاً
        if (Order::count() >= 50) {
            $this->command->info('الطلبات موجودة مسبقاً (' . Order::count() . ' طلب)، تم تخطي الإنشاء.');
            return;
        }

        // إنشاء طلبات جديدة حتى نصل لـ 50 طلب
        $existingCount = Order::count();
        $neededCount = 50 - $existingCount;

        if (count($customerIds) > 0 && $neededCount > 0) {
            for ($i = 0; $i < $neededCount; $i++) {
                $status = $statuses[array_rand($statuses)];
                $paymentStatus = $paymentStatuses[array_rand($paymentStatuses)];

                // إذا كان الطلب مكتمل، يجب أن تكون حالة الدفع مدفوعة
                if ($status === 'completed') {
                    $paymentStatus = 'paid';
                }

                // إذا فشل الدفع، يجب أن يكون الطلب معلقًا أو ملغيًا
                if ($paymentStatus === 'failed') {
                    $status = rand(0, 1) ? 'pending' : 'cancelled';
                }

                $createdAt = now()->subDays(rand(1, 60));

                Order::create([
                    'user_id' => $customerIds[array_rand($customerIds)],
                    'store_id' => rand(1, 5), // افتراض وجود 5 متاجر
                    'order_number' => 'ORD-' . date('Ymd') . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                    'status' => $status,
                    'payment_status' => $paymentStatus,
                    'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                    'total_amount' => 0, // سيتم تحديثه لاحقًا في OrderItemSeeder
                    'tax_amount' => 0,
                    'shipping_amount' => 0,
                    'discount_amount' => 0,
                    'currency' => 'EGP',
                    'payment_transaction_id' => 'TXN-' . strtoupper(substr(md5(rand()), 0, 8)),
                    'shipping_name' => 'عميل تجريبي',
                    'shipping_address' => 'عنوان تجريبي، شارع ' . rand(1, 100),
                    'shipping_city' => 'القاهرة',
                    'shipping_country' => 'مصر',
                    'shipping_phone' => '+2' . rand(1000000000, 1999999999),
                    'shipping_email' => 'customer' . rand(1, 1000) . '@example.com',
                    'shipping_postal_code' => rand(10000, 99999),
                    'notes' => 'طلب تجريبي #' . ($i + 1),
                    'is_guest' => false,
                    'created_at' => $createdAt,
                    'updated_at' => $createdAt->addHours(rand(1, 24)),
                ]);
            }

            $this->command->info('تم إنشاء ' . Order::count() . ' طلب تجريبي بنجاح!');

            // تشغيل بذرة عناصر الطلبات
            $this->call(OrderItemSeeder::class);
        }
    }
}
