<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('languages')->count() > 0) {
            $this->command->info('languages data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding languages...');

        $data = [
            [
                'id' => 1,
                'code' => 'ar',
                'name' => 'Arabic',
                'native_name' => 'العربية',
                'is_rtl' => 1,
                'is_default' => 1,
                'is_active' => 1,
                'created_at' => '2025-05-28 21:53:16',
                'updated_at' => '2025-05-28 21:53:16',
            ],
            [
                'id' => 2,
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'is_rtl' => 0,
                'is_default' => 0,
                'is_active' => 1,
                'created_at' => '2025-05-28 21:53:16',
                'updated_at' => '2025-05-28 21:53:16',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('languages')->insert($chunk);
        }

        $this->command->info('languages seeded successfully!');
    }
}
