<?php

namespace Database\Seeders;

use App\Models\Address;
use App\Models\User;
use Illuminate\Database\Seeder;

class AddressSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود عناوين مسبقاً
        if (Address::count() > 0) {
            $this->command->info('العناوين موجودة مسبقاً، تم تخطي الإنشاء.');
            return;
        }
        $this->command->info('جاري إنشاء العناوين التجريبية...');

        // التأكد من وجود مستخدمين
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('لا يوجد مستخدمين لإنشاء عناوين لهم!');
            return;
        }

        // المدن السعودية
        $cities = [
            'الرياض',
            'جدة',
            'مكة المكرمة',
            'المدينة المنورة',
            'الدمام',
            'الخبر',
            'الطائف',
            'تبوك',
            'القصيم',
            'حائل',
            'أبها',
            'نجران',
            'جازان',
            'الباحة',
        ];

        // المناطق
        $regions = [
            'منطقة الرياض',
            'منطقة مكة المكرمة',
            'منطقة المدينة المنورة',
            'منطقة القصيم',
            'المنطقة الشرقية',
            'منطقة عسير',
            'منطقة تبوك',
            'منطقة حائل',
            'منطقة الحدود الشمالية',
            'منطقة جازان',
            'منطقة نجران',
            'منطقة الباحة',
            'منطقة الجوف',
        ];

        // إنشاء عناوين لكل مستخدم
        foreach ($users as $user) {
            // عدد عشوائي من العناوين لكل مستخدم (1-3)
            $count = rand(1, 3);

            for ($i = 0; $i < $count; $i++) {
                $city = $cities[array_rand($cities)];
                $region = $regions[array_rand($regions)];

                $addressDetails = 'شارع ' . rand(1, 100) . '، حي ' . $this->getRandomDistrict();
                $addressDetails .= '، مبنى رقم ' . rand(1, 999) . '، شقة ' . rand(1, 50);

                Address::create([
                    'user_id' => $user->id,
                    'name' => 'عنوان ' . ($i + 1),
                    'address' => $addressDetails,
                    'city' => $city,
                    'country' => 'المملكة العربية السعودية',
                    'postal_code' => rand(10000, 99999),
                    'phone' => '05' . rand(10000000, 99999999),
                    'is_default' => $i === 0, // العنوان الأول هو الافتراضي
                    'created_at' => now()->subDays(rand(0, 30)),
                ]);
            }
        }

        $this->command->info('تم إنشاء ' . Address::count() . ' عنوان تجريبي بنجاح!');
    }

    /**
     * الحصول على اسم حي عشوائي
     */
    private function getRandomDistrict(): string
    {
        $districts = [
            'النزهة',
            'العليا',
            'الروضة',
            'الخالدية',
            'الفيصلية',
            'المروج',
            'الياسمين',
            'الملز',
            'السليمانية',
            'الربوة',
            'النسيم',
            'الفلاح',
            'الشفا',
            'الرحمانية',
            'الورود',
        ];

        return $districts[array_rand($districts)];
    }
}
