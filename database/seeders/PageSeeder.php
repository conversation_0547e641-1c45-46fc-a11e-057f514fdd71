<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // حذف البيانات الموجودة وإعادة إنشاؤها
        DB::table('pages')->truncate();

        $this->command->info('Seeding pages...');

        $data = [
            [
                'title' => 'الأسئلة الشائعة',
                'slug' => 'faq',
                'content' => '<p>هذه هي صفحة الأسئلة الشائعة. يمكنك تعديل هذا المحتوى من لوحة التحكم.</p>',
                'meta_title' => 'الأسئلة الشائعة - مكة جولد',
                'meta_description' => 'الأسئلة الشائعة حول منتجات وخدمات مكة جولد',
                'meta_keywords' => 'أسئلة شائعة، مكة جولد، مجوهرات، ذهب',
                'is_active' => 1,
                'order' => 1,
                'type' => 'faq',
                'section' => 'customer-service',
                'translations' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'سياسة الخصوصية',
                'slug' => 'privacy',
                'content' => '<p>هذه هي صفحة سياسة الخصوصية. يمكنك تعديل هذا المحتوى من لوحة التحكم.</p>',
                'meta_title' => 'سياسة الخصوصية - مكة جولد',
                'meta_description' => 'سياسة الخصوصية وحماية البيانات لموقع مكة جولد',
                'meta_keywords' => 'سياسة الخصوصية، حماية البيانات، مكة جولد',
                'is_active' => 1,
                'order' => 4,
                'type' => 'privacy',
                'section' => 'legal',
                'translations' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'الشروط والأحكام',
                'slug' => 'terms',
                'content' => '<p>هذه هي صفحة الشروط والأحكام. يمكنك تعديل هذا المحتوى من لوحة التحكم.</p>',
                'meta_title' => 'الشروط والأحكام - مكة جولد',
                'meta_description' => 'الشروط والأحكام لاستخدام موقع وخدمات مكة جولد',
                'meta_keywords' => 'الشروط والأحكام، شروط الاستخدام، مكة جولد',
                'is_active' => 1,
                'order' => 5,
                'type' => 'terms',
                'section' => 'legal',
                'translations' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'من نحن',
                'slug' => 'about',
                'content' => '<p>مرحبًا بكم في مكة جولد، المتجر الرائد للمجوهرات والذهب في المملكة العربية السعودية.</p>',
                'meta_title' => 'من نحن - مكة جولد',
                'meta_description' => 'تعرف على مكة جولد، المتجر الرائد للمجوهرات والذهب في المملكة العربية السعودية',
                'meta_keywords' => 'من نحن، مكة جولد، مجوهرات، ذهب، المملكة العربية السعودية',
                'is_active' => 1,
                'order' => 6,
                'type' => 'about',
                'section' => 'about',
                'translations' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'title' => 'اتصل بنا',
                'slug' => 'contact',
                'content' => '<p>نحن نقدر تواصلك معنا ونسعى دائمًا لتقديم أفضل خدمة لعملائنا.</p>',
                'meta_title' => 'اتصل بنا - مكة جولد',
                'meta_description' => 'تواصل مع مكة جولد، المتجر الرائد للمجوهرات والذهب في المملكة العربية السعودية',
                'meta_keywords' => 'اتصل بنا، مكة جولد، مجوهرات، ذهب، المملكة العربية السعودية',
                'is_active' => 1,
                'order' => 7,
                'type' => 'contact',
                'section' => 'about',
                'translations' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('pages')->insert($chunk);
        }

        $this->command->info('pages seeded successfully!');
    }
}
