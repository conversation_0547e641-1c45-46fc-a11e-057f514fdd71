<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('categories')->count() > 0) {
            $this->command->info('categories data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding categories...');

        $data = [
            [
                'id' => 1,
                'name_ar' => 'خواتم',
                'name_en' => 'Rings',
                'description_ar' => 'تشكيلة متنوعة من الخواتم الفاخرة',
                'description_en' => 'A diverse collection of luxury rings',
                'slug' => 'rings',
                'image' => 'categories/1-category-1.jpg',
                'parent_id' => null,
                'order' => 1,
                'is_active' => 1,
                'show_price' => 1,
                'allow_purchase' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 2,
                'name_ar' => 'أساور',
                'name_en' => 'Bracelets',
                'description_ar' => 'أساور مميزة بتصاميم فريدة',
                'description_en' => 'Distinctive bracelets with unique designs',
                'slug' => 'bracelets',
                'image' => 'categories/2-category-4.jpg',
                'parent_id' => null,
                'order' => 2,
                'is_active' => 1,
                'show_price' => 1,
                'allow_purchase' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 3,
                'name_ar' => 'قلائد',
                'name_en' => 'Necklaces',
                'description_ar' => 'قلائد فاخرة لمناسبات مختلفة',
                'description_en' => 'Luxury necklaces for various occasions',
                'slug' => 'necklaces',
                'image' => null,
                'parent_id' => null,
                'order' => 3,
                'is_active' => 1,
                'show_price' => 1,
                'allow_purchase' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 21:53:51',
            ],
            [
                'id' => 4,
                'name_ar' => 'أقراط',
                'name_en' => 'Earrings',
                'description_ar' => 'أقراط بتصاميم عصرية وكلاسيكية',
                'description_en' => 'Earrings with modern and classic designs',
                'slug' => 'earrings',
                'image' => 'categories/4-category-2.jpg',
                'parent_id' => null,
                'order' => 4,
                'is_active' => 1,
                'show_price' => 1,
                'allow_purchase' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 23:38:39',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            // إزالة show_price من كل عنصر
            $cleanedChunk = array_map(function ($item) {
                unset($item['show_price']);
                return $item;
            }, $chunk);

            DB::table('categories')->insert($cleanedChunk);
        }

        $this->command->info('categories seeded successfully!');
    }
}
