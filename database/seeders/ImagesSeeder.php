<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class ImagesSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود صور مسبقاً (فحص المنتجات والفئات التي لديها صور)
        $productsWithImages = Product::whereNotNull('image')->count();
        $categoriesWithImages = Category::whereNotNull('image')->count();

        if ($productsWithImages > 0 || $categoriesWithImages > 0) {
            $this->command->info('الصور موجودة مسبقاً، تم تخطي الإنشاء.');
            return;
        }
        $this->command->info('جاري تحميل الصور للمنتجات والأقسام...');

        // التأكد من وجود المجلدات اللازمة
        if (!Storage::disk('public')->exists('categories')) {
            Storage::disk('public')->makeDirectory('categories');
        }

        if (!Storage::disk('public')->exists('products')) {
            Storage::disk('public')->makeDirectory('products');
        }

        // التأكد من وجود رابط رمزي للتخزين العام
        $this->createStorageLink();

        // تحميل صور الأقسام
        $this->seedCategoryImages();

        // تحميل صور المنتجات
        $this->seedProductImages();

        $this->command->info('تم تحميل الصور بنجاح!');
    }

    /**
     * تحميل صور الأقسام
     */
    private function seedCategoryImages(): void
    {
        $categories = Category::all();
        $categoryImages = [
            'rings' => 'category-1.jpg',
            'bracelets' => 'category-4.jpg',
            'necklaces' => 'category-0.jpg',
            'earrings' => 'category-2.jpg',
            'chains' => 'category-0.jpg',
            'anklets' => 'category-4.jpg',
            'gifts' => 'category-5.jpg',
        ];

        foreach ($categories as $category) {
            if (isset($categoryImages[$category->slug])) {
                $imageName = $categoryImages[$category->slug];
                $sourcePath = public_path('images/categories/' . $imageName);

                // التحقق من وجود الصورة المصدر
                if (File::exists($sourcePath)) {
                    $destinationPath = 'categories/' . $category->id . '-' . $imageName;

                    // نسخ الصورة إلى مجلد التخزين العام
                    Storage::disk('public')->put(
                        $destinationPath,
                        File::get($sourcePath)
                    );

                    // تحديث مسار الصورة في قاعدة البيانات
                    $category->image = $destinationPath;
                    $category->save();

                    $this->command->info("تم تحميل صورة للقسم: {$category->name_ar}");
                } else {
                    // استخدام صورة افتراضية إذا لم تكن الصورة المحددة موجودة
                    $defaultImage = 'category-0.jpg';
                    $defaultSourcePath = public_path('images/categories/' . $defaultImage);

                    if (File::exists($defaultSourcePath)) {
                        $destinationPath = 'categories/' . $category->id . '-' . $defaultImage;

                        Storage::disk('public')->put(
                            $destinationPath,
                            File::get($defaultSourcePath)
                        );

                        $category->image = $destinationPath;
                        $category->save();

                        $this->command->info("تم تحميل صورة افتراضية للقسم: {$category->name_ar}");
                    }
                }
            }
        }
    }

    /**
     * تحميل صور المنتجات
     */
    private function seedProductImages(): void
    {
        $products = Product::all();

        foreach ($products as $index => $product) {
            // تحديد رقم الصورة (1-12 للتنوع)
            $imageNumber = ($index % 12) + 1;
            $imageName = "product-{$imageNumber}.jpg";
            $sourcePath = public_path("images/products/{$imageName}");

            // التحقق من وجود الصورة المصدر
            if (File::exists($sourcePath)) {
                $destinationPath = 'products/' . $product->id . '-' . $imageName;

                // نسخ الصورة إلى مجلد التخزين العام
                Storage::disk('public')->put(
                    $destinationPath,
                    File::get($sourcePath)
                );

                // تحديث مسار الصورة في قاعدة البيانات
                $product->image = $destinationPath;
                $product->save();

                $this->command->info("تم تحميل صورة للمنتج: {$product->name_ar}");
            } else {
                // استخدام صورة افتراضية إذا لم تكن الصورة المحددة موجودة
                $defaultImage = "product-1.jpg";
                $defaultSourcePath = public_path("images/products/{$defaultImage}");

                if (File::exists($defaultSourcePath)) {
                    $destinationPath = 'products/' . $product->id . '-' . $defaultImage;

                    Storage::disk('public')->put(
                        $destinationPath,
                        File::get($defaultSourcePath)
                    );

                    $product->image = $destinationPath;
                    $product->save();

                    $this->command->info("تم تحميل صورة افتراضية للمنتج: {$product->name_ar}");
                }
            }
        }
    }

    /**
     * إنشاء رابط رمزي للتخزين العام إذا لم يكن موجودًا
     */
    private function createStorageLink(): void
    {
        $targetPath = storage_path('app/public');
        $linkPath = public_path('storage');

        if (!file_exists($linkPath)) {
            if (File::exists($linkPath)) {
                // إذا كان الرابط موجودًا ولكنه غير صالح
                unlink($linkPath);
            }

            $this->command->info('إنشاء رابط رمزي للتخزين العام...');
            symlink($targetPath, $linkPath);
            $this->command->info('تم إنشاء الرابط الرمزي بنجاح!');
        }
    }
}
