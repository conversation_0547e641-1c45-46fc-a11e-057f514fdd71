<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SuperAdminSetting;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء دور السوبر أدمن إذا لم يكن موجوداً
        $superAdminRole = Role::firstOrCreate([
            'name' => 'super_admin',
            'guard_name' => 'web'
        ]);

        // إنشاء جميع الصلاحيات للسوبر أدمن
        $permissions = Permission::all();
        if ($permissions->count() > 0) {
            $superAdminRole->syncPermissions($permissions);
        }

        // إنشاء مستخدم السوبر أدمن الافتراضي
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('SuperAdmin@123'),
                'email_verified_at' => now(),
            ]
        );

        // تعيين دور السوبر أدمن للمستخدم
        if (!$superAdmin->hasRole('super_admin')) {
            $superAdmin->assignRole('super_admin');
        }

        // إنشاء إعدادات السوبر أدمن الافتراضية
        SuperAdminSetting::firstOrCreate([], SuperAdminSetting::getDefaults());

        $this->command->info('تم إنشاء السوبر أدمن وإعداداته بنجاح!');
        $this->command->info('البريد الإلكتروني: <EMAIL>');
        $this->command->info('كلمة المرور: SuperAdmin@123');
    }
}
