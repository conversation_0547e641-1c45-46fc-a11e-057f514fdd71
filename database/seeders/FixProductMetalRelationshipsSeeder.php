<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\MetalType;
use App\Models\MetalPurity;

class FixProductMetalRelationshipsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Fixing product metal relationships...');

        // الحصول على المنتجات بدون علاقات معادن
        $productsWithoutMetalType = Product::whereNull('metal_type_id')->get();
        $productsWithoutMetalPurity = Product::whereNull('metal_purity_id')->get();

        $this->command->info("Found {$productsWithoutMetalType->count()} products without metal_type_id");
        $this->command->info("Found {$productsWithoutMetalPurity->count()} products without metal_purity_id");

        // إصلاح المنتجات بدون نوع معدن
        foreach ($productsWithoutMetalType as $product) {
            $randomMetalType = MetalType::where('is_active', true)->inRandomOrder()->first();
            if ($randomMetalType) {
                $product->metal_type_id = $randomMetalType->id;
                $product->save();
                $this->command->line("Updated product '{$product->name_ar}' with metal type: {$randomMetalType->name_ar}");
            }
        }

        // إصلاح المنتجات بدون عيار معدن
        $allProducts = Product::whereNull('metal_purity_id')->get();
        foreach ($allProducts as $product) {
            if ($product->metal_type_id) {
                $randomPurity = MetalPurity::where('metal_type_id', $product->metal_type_id)
                    ->where('is_active', true)
                    ->inRandomOrder()
                    ->first();
                
                if ($randomPurity) {
                    $product->metal_purity_id = $randomPurity->id;
                    $product->save();
                    $this->command->line("Updated product '{$product->name_ar}' with purity: {$randomPurity->name_ar}");
                }
            }
        }

        // التحقق من النتائج
        $remainingWithoutMetalType = Product::whereNull('metal_type_id')->count();
        $remainingWithoutMetalPurity = Product::whereNull('metal_purity_id')->count();

        $this->command->info("Remaining products without metal_type_id: {$remainingWithoutMetalType}");
        $this->command->info("Remaining products without metal_purity_id: {$remainingWithoutMetalPurity}");
        
        if ($remainingWithoutMetalType == 0 && $remainingWithoutMetalPurity == 0) {
            $this->command->info('✅ All products now have metal relationships!');
        } else {
            $this->command->warn('⚠️ Some products still missing metal relationships');
        }
    }
}
