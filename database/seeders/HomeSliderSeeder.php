<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class HomeSliderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('home_sliders')->count() > 0) {
            $this->command->info('home_sliders data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding home_sliders...');

        $data = [
            [
                'id' => 1,
                'title_ar' => 'مجموعة مجوهرات فاخرة',
                'title_en' => 'Luxury Jewelry Collection',
                'description_ar' => 'اكتشف مجموعتنا الحصرية من المجوهرات الذهبية الفاخرة',
                'description_en' => 'Discover our exclusive collection of luxury gold jewelry',
                'button_text_ar' => 'تسوق الآن',
                'button_text_en' => 'Shop Now',
                'button_link' => '/products',
                'image' => 'sliders/slider-1.jpg',
                'order' => 1,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 2,
                'title_ar' => 'خصم 20% على الخواتم',
                'title_en' => '20% Off on Rings',
                'description_ar' => 'استمتع بخصم 20% على جميع الخواتم الذهبية لفترة محدودة',
                'description_en' => 'Enjoy 20% discount on all gold rings for a limited time',
                'button_text_ar' => 'اكتشف العروض',
                'button_text_en' => 'Discover Offers',
                'button_link' => '/products?category=rings',
                'image' => 'sliders/slider-2.jpg',
                'order' => 2,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 3,
                'title_ar' => 'مجوهرات الزفاف',
                'title_en' => 'Wedding Jewelry',
                'description_ar' => 'مجموعة مميزة من مجوهرات الزفاف لتكملي إطلالتك في يومك المميز',
                'description_en' => 'A special collection of wedding jewelry to complete your look on your special day',
                'button_text_ar' => 'تسوق المجموعة',
                'button_text_en' => 'Shop Collection',
                'button_link' => '/products?category=wedding',
                'image' => 'sliders/slider-3.jpg',
                'order' => 3,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 4,
                'title_ar' => 'هدايا مميزة',
                'title_en' => 'Special Gifts',
                'description_ar' => 'هدايا مميزة لمناسباتك الخاصة بتغليف أنيق',
                'description_en' => 'Special gifts for your special occasions with elegant packaging',
                'button_text_ar' => 'اكتشف الهدايا',
                'button_text_en' => 'Discover Gifts',
                'button_link' => '/products?category=gifts',
                'image' => 'sliders/slider-4.jpg',
                'order' => 4,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 5,
                'title_ar' => 'تخفيضات موسمية',
                'title_en' => 'Seasonal Discounts',
                'description_ar' => 'تخفيضات موسمية تصل إلى 50% على تشكيلة مختارة من المجوهرات',
                'description_en' => 'Seasonal discounts up to 50% on selected jewelry',
                'button_text_ar' => 'تسوق الآن',
                'button_text_en' => 'Shop Now',
                'button_link' => '/sale',
                'image' => 'sliders/slider-5.jpg',
                'order' => 5,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('home_sliders')->insert($chunk);
        }

        $this->command->info('home_sliders seeded successfully!');
    }
}
