<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NewsletterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('newsletters')->count() > 0) {
            $this->command->info('newsletters data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding newsletters...');

        $data = [
            [
                'id' => 1,
                'email' => '<EMAIL>',
                'name' => 'أحمد محمد',
                'is_active' => 1,
                'token' => 'BCYLKWtskQRlswo0NuQ22upvmBJNARiJ',
                'subscribed_at' => '2025-04-29 02:38:11',
                'unsubscribed_at' => null,
                'created_at' => '2025-05-29 02:38:11',
                'updated_at' => '2025-05-29 02:38:11',
            ],
            [
                'id' => 2,
                'email' => '<EMAIL>',
                'name' => 'سارة أحمد',
                'is_active' => 1,
                'token' => 'zyYkSPmz9WCCpWBYb4bFj59ewGkpVS8Q',
                'subscribed_at' => '2025-05-04 02:38:11',
                'unsubscribed_at' => null,
                'created_at' => '2025-05-29 02:38:11',
                'updated_at' => '2025-05-29 02:38:11',
            ],
            [
                'id' => 3,
                'email' => '<EMAIL>',
                'name' => 'محمد علي',
                'is_active' => 1,
                'token' => 'hCnzUbfsBrYOB49c4v0wtFcYwRUFRW0h',
                'subscribed_at' => '2025-05-09 02:38:11',
                'unsubscribed_at' => null,
                'created_at' => '2025-05-29 02:38:11',
                'updated_at' => '2025-05-29 02:38:11',
            ],
            [
                'id' => 4,
                'email' => '<EMAIL>',
                'name' => 'نورا حسن',
                'is_active' => 0,
                'token' => '4piXlk5wgTBudb994IjgERzniLN8qAUG',
                'subscribed_at' => '2025-05-14 02:38:11',
                'unsubscribed_at' => '2025-05-24 02:38:11',
                'created_at' => '2025-05-29 02:38:11',
                'updated_at' => '2025-05-29 02:38:11',
            ],
            [
                'id' => 5,
                'email' => '<EMAIL>',
                'name' => 'خالد عبدالله',
                'is_active' => 1,
                'token' => 'hLCkmEHHFaLtYlXeU4mfXIlAta5zdT0T',
                'subscribed_at' => '2025-05-19 02:38:11',
                'unsubscribed_at' => null,
                'created_at' => '2025-05-29 02:38:11',
                'updated_at' => '2025-05-29 02:38:11',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('newsletters')->insert($chunk);
        }

        $this->command->info('newsletters seeded successfully!');
    }
}
