<?php

namespace Database\Seeders;

use App\Models\Appointment;
use App\Models\User;
use Illuminate\Database\Seeder;

class AppointmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Make sure we have users
        if (User::role('user')->count() === 0) {
            $this->call(DatabaseSeeder::class);
        }

        // Get customer IDs
        $customerIds = User::role('user')->pluck('id')->toArray();

        // Create appointments with different statuses
        $statuses = ['pending', 'confirmed', 'cancelled', 'completed'];
        $serviceTypes = [
            ['ar' => 'تصميم مجوهرات مخصصة', 'en' => 'Custom Jewelry Design'],
            ['ar' => 'إصلاح مجوهرات', 'en' => 'Jewelry Repair'],
            ['ar' => 'تقييم مجوهرات', 'en' => 'Jewelry Appraisal'],
            ['ar' => 'استشارة شراء', 'en' => 'Purchase Consultation'],
            ['ar' => 'تنظيف مجوهرات', 'en' => 'Jewelry Cleaning'],
        ];

        // Create 30 appointments if we have less than that
        if (Appointment::count() < 30 && count($customerIds) > 0) {
            $appointmentsToCreate = 30 - Appointment::count();

            for ($i = 0; $i < $appointmentsToCreate; $i++) {
                $status = $statuses[array_rand($statuses)];
                $serviceType = $serviceTypes[array_rand($serviceTypes)];

                // Generate random appointment date (past, present, future)
                $daysOffset = rand(-30, 30);
                $appointmentDate = now()->addDays($daysOffset)->setHour(rand(10, 19))->setMinute(0)->setSecond(0);

                // If appointment is in the past, it should be completed or cancelled
                if ($daysOffset < 0) {
                    $status = rand(0, 1) ? 'completed' : 'cancelled';
                }

                // If appointment is today or in the future, it should be pending or confirmed
                if ($daysOffset >= 0) {
                    $status = rand(0, 1) ? 'pending' : 'confirmed';
                }

                // Generate confirmation code
                $confirmationCode = strtoupper(substr(md5(rand()), 0, 6));

                // Determine if reminder was sent
                $reminderSent = $daysOffset > -7 && $daysOffset <= 0;

                Appointment::create([
                    'user_id' => $customerIds[array_rand($customerIds)],
                    'store_id' => rand(1, 5),
                    'appointment_date' => $appointmentDate,
                    'status' => $status,
                    'service_type_ar' => $serviceType['ar'],
                    'service_type_en' => $serviceType['en'],
                    'confirmation_code' => $confirmationCode,
                    'reminder_sent' => $reminderSent,
                    'notes' => 'موعد تجريبي #' . ($i + 1),
                    'created_at' => $appointmentDate->copy()->subDays(rand(1, 7)),
                ]);
            }
        }
    }
}
