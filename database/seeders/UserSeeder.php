<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('users')->count() > 0) {
            $this->command->info('users data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding users...');

        $data = [
            [
                'id' => 1,
                'name' => 'مدير النظام',
                'email' => '<EMAIL>',
                'email_verified_at' => '2025-05-28 21:53:50',
                'password' => '$2y$12$jduqxKbaSE4c4qn9bwJ61eIO.TK4GQSFgHwOML3Gqas20Z.WW1THm',
                'remember_token' => 'tT4RTYBhoUDQwTJYLQHFoDd102G67DEuoDre4CXExyhvQXQPy09Wt6kobphS',
                'created_at' => '2025-05-28 21:53:50',
                'updated_at' => '2025-05-28 21:53:50',
                'phone' => null,
                'address' => null,
                'city' => null,
                'country' => null,
                'postal_code' => null,
                'is_active' => 1,
                'profile_image' => null,
                'last_login_at' => null,
            ],
            [
                'id' => 2,
                'name' => 'مدير المتجر',
                'email' => '<EMAIL>',
                'email_verified_at' => '2025-05-28 21:53:50',
                'password' => '$2y$12$5iagQM7jr7wMmv8kXfo/NeB.BQvIM2Ei4/mn1lDh8qA2YPU0HJXKu',
                'remember_token' => 'BIdmvGOi0v',
                'created_at' => '2025-05-28 21:53:50',
                'updated_at' => '2025-05-28 21:53:50',
                'phone' => null,
                'address' => null,
                'city' => null,
                'country' => null,
                'postal_code' => null,
                'is_active' => 1,
                'profile_image' => null,
                'last_login_at' => null,
            ],
            [
                'id' => 11,
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'email_verified_at' => '2025-05-28 21:53:51',
                'password' => '$2y$12$IEOf.f.SoS/Ji1mPB8EO5uXuZ70KIgl4sluqIyMDdaOySzKilQhoG',
                'remember_token' => null,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 21:53:51',
                'phone' => null,
                'address' => null,
                'city' => null,
                'country' => null,
                'postal_code' => null,
                'is_active' => 1,
                'profile_image' => null,
                'last_login_at' => null,
            ],
            [
                'id' => 12,
                'name' => 'algmaal',
                'email' => '<EMAIL>',
                'email_verified_at' => null,
                'password' => '$2y$12$765LaG/dmqdYcG1c50hsr.905gg6teQ7cwER7fssX2gfrxzITvQZm',
                'remember_token' => null,
                'created_at' => '2025-05-28 21:55:29',
                'updated_at' => '2025-05-28 21:55:29',
                'phone' => null,
                'address' => null,
                'city' => null,
                'country' => null,
                'postal_code' => null,
                'is_active' => 1,
                'profile_image' => null,
                'last_login_at' => null,
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('users')->insert($chunk);
        }

        $this->command->info('users seeded successfully!');
    }
}
