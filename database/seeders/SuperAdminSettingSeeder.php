<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SuperAdminSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // حذف البيانات الموجودة وإعادة إنشاؤها
        DB::table('super_admin_settings')->truncate();

        $this->command->info('Seeding super_admin_settings...');

        $data = [
            [
                'id' => 1,
                'show_ratings' => 0,
                'show_wishlist' => 0,
                'enable_guest_checkout' => 0,
                'enable_local_pickup' => 0,
                'enable_multilingual' => 0,
                'tax_rate' => 14.00,
                'prices_include_tax' => 0,
                'min_order_amount' => 0.00,
                'order_prefix' => 'MGJ-',
                'enable_registration' => 0,
                'registration_disabled_message' => 'التسجيل غير متاح حالياً، يرجى المحاولة لاحقاً.',
                'default_currency' => 'EGP',
                'supported_currencies' => '["EGP", "USD", "EUR"]',
                'currency_api_key' => null,
                'created_at' => now(),
                'updated_at' => now(),
                'maintenance_mode' => 0,
                'maintenance_message' => 'الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.',
                'display_only_mode' => 1,
                'default_language' => 'ar',
                'enable_credit_card' => 0,
                'enable_paypal' => 0,
                'enable_bank_transfer' => 0,
                'enable_cash_on_delivery' => 0,
                'enable_fawry' => 0,
                'stripe_key' => null,
                'stripe_secret' => null,
                'stripe_sandbox_mode' => 0,
                'paypal_client_id' => null,
                'paypal_secret' => null,
                'paypal_sandbox_mode' => 0,
                'local_pickup_discount' => 0.00,
                'shipping_policy' => null,
                'enable_invoices' => 0,
                'invoice_prefix' => 'INV-',
                'company_name_invoice' => null,
                'company_address_invoice' => null,
                'company_tax_id' => null,
                'enable_social_login' => 0,
                'enable_facebook_login' => 0,
                'enable_google_login' => 0,
                'enable_twitter_login' => 0,
                'facebook_app_id' => null,
                'facebook_app_secret' => null,
                'google_client_id' => null,
                'google_client_secret' => null,
                'twitter_client_id' => null,
                'twitter_client_secret' => null,
                'enable_social_sharing' => 1,
                'share_on_facebook' => 1,
                'share_on_twitter' => 1,
                'share_on_whatsapp' => 1,
                'share_on_pinterest' => 0,
                'share_on_linkedin' => 0,
                'show_instagram_feed' => 0,
                'instagram_token' => null,
                'instagram_count' => 6,
                'show_facebook_feed' => 0,
                'facebook_page_id' => null,
                'facebook_count' => 5,
                'show_cookie_banner' => 0,
                'cookie_banner_text' => 'هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.',
                'cookie_banner_button_text' => 'أوافق',
                'enable_gdpr_compliance' => 0,
                'gdpr_compliance_text' => 'نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية وفقاً للائحة العامة لحماية البيانات (GDPR).',
                'require_marketing_consent' => 0,
                'marketing_consent_text' => 'أوافق على تلقي رسائل تسويقية من مكة جولد عبر البريد الإلكتروني والرسائل القصيرة.',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('super_admin_settings')->insert($chunk);
        }

        $this->command->info('super_admin_settings seeded successfully!');
    }
}
