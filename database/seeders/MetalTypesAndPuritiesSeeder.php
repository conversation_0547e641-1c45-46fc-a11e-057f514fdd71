<?php

namespace Database\Seeders;

use App\Models\MetalType;
use App\Models\MetalPurity;
use Illuminate\Database\Seeder;

class MetalTypesAndPuritiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('🔧 بدء إنشاء أنواع المعادن والعيارات...');
        }

        // إنشاء أنواع المعادن
        $this->createMetalTypes();

        // إنشاء العيارات
        $this->createMetalPurities();

        if ($this->command) {
            $this->command->info('✅ تم إنشاء أنواع المعادن والعيارات بنجاح');
        }
    }

    /**
     * إنشاء أنواع المعادن
     */
    private function createMetalTypes(): void
    {
        $metalTypes = [
            [
                'name' => 'gold',
                'name_ar' => 'ذهب',
                'icon' => 'fas fa-coins',
                'color' => '#fbbf24',
                'description' => 'المعدن الثمين الأكثر شهرة وقيمة',
                'sort_order' => 1,
                'is_active' => true,
            ],
            [
                'name' => 'silver',
                'name_ar' => 'فضة',
                'icon' => 'fas fa-coins',
                'color' => '#9ca3af',
                'description' => 'المعدن الثمين الأبيض اللامع',
                'sort_order' => 2,
                'is_active' => true,
            ],
            [
                'name' => 'gold_coin',
                'name_ar' => 'جنيهات ذهبية',
                'icon' => 'fas fa-coins',
                'color' => '#f59e0b',
                'description' => 'الجنيهات الذهبية والعملات الذهبية',
                'sort_order' => 3,
                'is_active' => true,
            ],
        ];

        foreach ($metalTypes as $metalType) {
            MetalType::updateOrCreate(
                ['name' => $metalType['name']],
                $metalType
            );
        }

        if ($this->command) {
            $this->command->info('📊 تم إنشاء ' . count($metalTypes) . ' أنواع معادن');
        }
    }

    /**
     * إنشاء العيارات
     */
    private function createMetalPurities(): void
    {
        $goldType = MetalType::where('name', 'gold')->first();
        $silverType = MetalType::where('name', 'silver')->first();
        $goldCoinType = MetalType::where('name', 'gold_coin')->first();

        $purities = [];

        // عيارات الذهب
        if ($goldType) {
            $purities = array_merge($purities, [
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '24K',
                    'name_ar' => '24 عيار (خالص)',
                    'purity_percentage' => 99.9,
                    'sort_order' => 1,
                ],
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '22K',
                    'name_ar' => '22 عيار',
                    'purity_percentage' => 91.7,
                    'sort_order' => 2,
                ],
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '21K',
                    'name_ar' => '21 عيار',
                    'purity_percentage' => 87.5,
                    'sort_order' => 3,
                ],
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '18K',
                    'name_ar' => '18 عيار',
                    'purity_percentage' => 75.0,
                    'sort_order' => 4,
                ],
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '14K',
                    'name_ar' => '14 عيار',
                    'purity_percentage' => 58.3,
                    'sort_order' => 5,
                ],
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '12K',
                    'name_ar' => '12 عيار',
                    'purity_percentage' => 50.0,
                    'sort_order' => 6,
                ],
                [
                    'metal_type_id' => $goldType->id,
                    'name' => '9K',
                    'name_ar' => '9 عيار',
                    'purity_percentage' => 37.5,
                    'sort_order' => 7,
                ],
            ]);
        }

        // عيارات الفضة
        if ($silverType) {
            $purities = array_merge($purities, [
                [
                    'metal_type_id' => $silverType->id,
                    'name' => '999',
                    'name_ar' => 'فضة 999 (خالصة)',
                    'purity_percentage' => 99.9,
                    'sort_order' => 1,
                ],
                [
                    'metal_type_id' => $silverType->id,
                    'name' => '925',
                    'name_ar' => 'فضة 925 (استرليني)',
                    'purity_percentage' => 92.5,
                    'sort_order' => 2,
                ],
                [
                    'metal_type_id' => $silverType->id,
                    'name' => '900',
                    'name_ar' => 'فضة 900',
                    'purity_percentage' => 90.0,
                    'sort_order' => 3,
                ],
                [
                    'metal_type_id' => $silverType->id,
                    'name' => '800',
                    'name_ar' => 'فضة 800',
                    'purity_percentage' => 80.0,
                    'sort_order' => 4,
                ],
                [
                    'metal_type_id' => $silverType->id,
                    'name' => '600',
                    'name_ar' => 'فضة 600',
                    'purity_percentage' => 60.0,
                    'sort_order' => 5,
                ],
            ]);
        }

        // الجنيهات الذهبية (جنيه كامل + نصف + ربع)
        if ($goldCoinType) {
            $purities = array_merge($purities, [
                [
                    'metal_type_id' => $goldCoinType->id,
                    'name' => 'جنيه ذهب',
                    'name_ar' => 'جنيه ذهب',
                    'purity_percentage' => 91.7, // عادة 22 عيار
                    'weight_grams' => 8.0,
                    'sort_order' => 1,
                ],
                [
                    'metal_type_id' => $goldCoinType->id,
                    'name' => 'نصف جنيه ذهب',
                    'name_ar' => 'نصف جنيه ذهب',
                    'purity_percentage' => 91.7,
                    'weight_grams' => 4.0,
                    'sort_order' => 2,
                ],
                [
                    'metal_type_id' => $goldCoinType->id,
                    'name' => 'ربع جنيه ذهب',
                    'name_ar' => 'ربع جنيه ذهب',
                    'purity_percentage' => 91.7,
                    'weight_grams' => 2.0,
                    'sort_order' => 3,
                ],
            ]);
        }

        // إنشاء العيارات
        foreach ($purities as $purity) {
            MetalPurity::updateOrCreate(
                [
                    'metal_type_id' => $purity['metal_type_id'],
                    'name' => $purity['name']
                ],
                $purity
            );
        }

        if ($this->command) {
            $this->command->info('📊 تم إنشاء ' . count($purities) . ' عيار معدن');
        }
    }
}
