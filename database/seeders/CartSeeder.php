<?php

namespace Database\Seeders;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;

class CartSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود عربات تسوق مسبقاً
        if (Cart::count() > 0) {
            $this->command->info('عربات التسوق موجودة مسبقاً، تم تخطي الإنشاء.');
            return;
        }
        $this->command->info('جاري إنشاء سلات التسوق التجريبية...');

        // التأكد من وجود مستخدمين ومنتجات
        $users = User::all();
        $products = Product::all();

        if ($users->isEmpty()) {
            $this->command->warn('لا يوجد مستخدمين لإنشاء سلات تسوق لهم!');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->warn('لا يوجد منتجات لإضافتها إلى سلات التسوق!');
            return;
        }

        // إنشاء سلات تسوق لكل مستخدم
        foreach ($users as $user) {
            // إنشاء سلة تسوق للمستخدم
            $cart = Cart::create([
                'user_id' => $user->id,
                'created_at' => now()->subDays(rand(0, 7)),
            ]);

            // عدد عشوائي من المنتجات لكل سلة (1-3)
            $count = rand(1, 3);

            // اختيار منتجات عشوائية
            $randomProducts = $products->random($count);

            foreach ($randomProducts as $product) {
                // كمية عشوائية لكل منتج (1-3)
                $quantity = rand(1, 3);

                CartItem::create([
                    'cart_id' => $cart->id,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'price' => $product->price,
                    'total' => $product->price * $quantity,
                    'created_at' => $cart->created_at,
                ]);
            }
        }

        $this->command->info('تم إنشاء ' . Cart::count() . ' سلة تسوق و ' . CartItem::count() . ' عنصر في سلات التسوق بنجاح!');
    }
}
