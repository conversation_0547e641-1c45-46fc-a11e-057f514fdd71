<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🚀 بدء عملية Seeding الشاملة...');
        $this->command->info('=====================================');

        // المرحلة 1: الجداول الأساسية (بدون foreign keys)
        $this->command->info('🔸 المرحلة 1: الجداول الأساسية');
        $this->call([
            LanguageSeeder::class,
            RoleSeeder::class,
            PermissionSeeder::class,
        ]);

        // المرحلة 2: المستخدمين والأدوار
        $this->command->info('🔸 المرحلة 2: المستخدمين والأدوار');
        $this->call([
            UserSeeder::class,
            UserRoleSeeder::class,
            RolePermissionSeeder::class,
        ]);

        // المرحلة 3: إعدادات النظام
        $this->command->info('🔸 المرحلة 3: إعدادات النظام');
        $this->call([
            SiteSettingSeeder::class,
            SuperAdminSettingSeeder::class,
        ]);

        // المرحلة 4: المحتوى الأساسي
        $this->command->info('🔸 المرحلة 4: المحتوى الأساسي');
        $this->call([
            CategorySeeder::class,
            FeatureSeeder::class,
            HomeSliderSeeder::class,
            PageSeeder::class,
            BlogPostSeeder::class,
        ]);

        // المرحلة 5: المعادن والأسعار
        $this->command->info('🔸 المرحلة 5: المعادن والأسعار');
        $this->call([
            MetalTypeSeeder::class,
            MetalPuritySeeder::class,
            MetalPriceSeeder::class,
        ]);

        // المرحلة 6: المنتجات والمتاجر والشركات
        $this->command->info('🔸 المرحلة 6: المنتجات والمتاجر والشركات');
        $this->call([
            ProductSeeder::class,
            StoreSeeder::class,
            // الشركات وأنواع المنتجات (بالترتيب الصحيح)
            CompaniesTableSeeder::class,
            ProductTypesTableSeeder::class,
            CompanyProductsTableSeeder::class,
        ]);

        // المرحلة 7: التفاعل والاشتراكات
        $this->command->info('🔸 المرحلة 7: التفاعل والاشتراكات');
        $this->call([
            NewsletterSeeder::class,
        ]);

        $this->command->info('🎉 تم إكمال عملية Seeding بنجاح!');
        $this->command->info('✅ تم إدراج جميع البيانات الأساسية');
        $this->command->info('🚀 النظام جاهز للاستخدام');
    }
}
