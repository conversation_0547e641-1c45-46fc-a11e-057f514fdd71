<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MetalPrice;
use Carbon\Carbon;

class MetalPriceHistorySeeder extends Seeder
{
    /**
     * إضافة بيانات تجريبية لتاريخ أسعار المعادن
     */
    public function run(): void
    {
        // حذف البيانات الموجودة
        MetalPrice::truncate();

        // أسعار الذهب الأساسية
        $goldBasePrices = [
            '24K' => 5280,
            '21K' => 4620,
            '18K' => 3960,
            '14K' => 3080,
            '12K' => 2640,
        ];

        // أسعار الفضة الأساسية
        $silverBasePrices = [
            '999' => 85,
            '925' => 78,
        ];

        // إضافة بيانات تاريخية للذهب (آخر 3 شهور)
        for ($i = 90; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            
            foreach ($goldBasePrices as $purity => $basePrice) {
                // إضافة تغيير عشوائي للسعر
                $variation = rand(-100, 100);
                $price = $basePrice + $variation;
                
                MetalPrice::create([
                    'metal_type' => 'gold',
                    'purity' => $purity,
                    'price_per_gram' => $price,
                    'price_per_ounce' => $price * 31.1035,
                    'currency' => 'EGP',
                    'price_date' => $date,
                    'source' => 'test_data',
                    'is_active' => true,
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);
            }
        }

        // إضافة بيانات تاريخية للفضة (آخر 3 شهور)
        for ($i = 90; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            
            foreach ($silverBasePrices as $purity => $basePrice) {
                // إضافة تغيير عشوائي للسعر
                $variation = rand(-5, 5);
                $price = $basePrice + $variation;
                
                MetalPrice::create([
                    'metal_type' => 'silver',
                    'purity' => $purity,
                    'price_per_gram' => $price,
                    'price_per_ounce' => $price * 31.1035,
                    'currency' => 'EGP',
                    'price_date' => $date,
                    'source' => 'test_data',
                    'is_active' => true,
                    'created_at' => $date,
                    'updated_at' => $date,
                ]);
            }
        }

        $this->command->info('تم إضافة بيانات تاريخ أسعار المعادن بنجاح');
    }
}
