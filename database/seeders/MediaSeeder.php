<?php

namespace Database\Seeders;

// use App\Models\Media; // الموديل غير موجود
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class MediaSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود الوسائط مسبقاً
        $existingMedia = DB::table('media')->count();
        if ($existingMedia >= 50) {
            $this->command->info("الوسائط موجودة مسبقاً ({$existingMedia} سجل)، تم تخطي الإنشاء.");
            return;
        }

        $this->command->info('جاري إنشاء الوسائط التجريبية...');

        // التأكد من وجود المجلدات اللازمة
        if (!Storage::disk('public')->exists('media')) {
            Storage::disk('public')->makeDirectory('media');
        }

        // أنواع الوسائط
        $types = ['image', 'video', 'document'];

        // أسماء الوسائط
        $names = [
            'صورة المنتج',
            'فيديو توضيحي',
            'كتالوج المنتجات',
            'صورة الفئة',
            'صورة الشعار',
            'صورة الخلفية',
            'فيديو تعريفي',
            'دليل المستخدم',
            'شهادة الجودة',
            'صورة العرض الترويجي',
        ];

        // مسارات الوسائط
        $paths = [];

        // إنشاء مسارات للصور
        for ($i = 1; $i <= 10; $i++) {
            $paths[] = "media/image-{$i}.jpg";
        }

        // إنشاء مسارات للفيديوهات
        for ($i = 1; $i <= 3; $i++) {
            $paths[] = "media/video-{$i}.mp4";
        }

        // إنشاء مسارات للمستندات
        for ($i = 1; $i <= 2; $i++) {
            $paths[] = "media/document-{$i}.pdf";
        }

        // نسخ الصور من المجلد العام إلى مجلد التخزين
        $this->copyMediaFiles();

        // إنشاء سجلات الوسائط
        for ($i = 0; $i < 20; $i++) {
            $type = $types[array_rand($types)];
            $nameIndex = array_rand($names);
            $pathIndex = array_rand($paths);

            DB::table('media')->insert([
                'model_type' => 'App\\Models\\Product',
                'model_id' => rand(1, 100),
                'uuid' => \Illuminate\Support\Str::uuid(),
                'collection_name' => 'default',
                'name' => $names[$nameIndex] . ' ' . ($i + 1),
                'file_name' => $paths[$pathIndex],
                'mime_type' => $this->getMimeType($type),
                'disk' => 'public',
                'conversions_disk' => 'public',
                'size' => rand(100, 10000),
                'manipulations' => '[]',
                'custom_properties' => '[]',
                'generated_conversions' => '[]',
                'responsive_images' => '[]',
                'order_column' => $i + 1,
                'created_at' => now()->subDays(rand(0, 30)),
                'updated_at' => now(),
            ]);
        }

        $mediaCount = DB::table('media')->count();
        $this->command->info('تم إنشاء ' . $mediaCount . ' وسائط تجريبية بنجاح!');
    }

    /**
     * نسخ ملفات الوسائط من المجلد العام إلى مجلد التخزين
     */
    private function copyMediaFiles(): void
    {
        // نسخ الصور
        for ($i = 1; $i <= 10; $i++) {
            $sourcePath = public_path("images/products/product-{$i}.jpg");
            $destinationPath = "media/image-{$i}.jpg";

            if (File::exists($sourcePath)) {
                Storage::disk('public')->put(
                    $destinationPath,
                    File::get($sourcePath)
                );
            }
        }

        // إنشاء ملفات وهمية للفيديوهات والمستندات
        for ($i = 1; $i <= 3; $i++) {
            Storage::disk('public')->put(
                "media/video-{$i}.mp4",
                "This is a dummy video file {$i}"
            );
        }

        for ($i = 1; $i <= 2; $i++) {
            Storage::disk('public')->put(
                "media/document-{$i}.pdf",
                "This is a dummy document file {$i}"
            );
        }
    }

    /**
     * الحصول على نوع MIME بناءً على نوع الوسائط
     */
    private function getMimeType(string $type): string
    {
        switch ($type) {
            case 'image':
                return 'image/jpeg';
            case 'video':
                return 'video/mp4';
            case 'document':
                return 'application/pdf';
            default:
                return 'application/octet-stream';
        }
    }
}
