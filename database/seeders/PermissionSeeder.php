<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('permissions')->count() > 0) {
            $this->command->info('permissions data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding permissions...');

        $data = [
            [
                'id' => 1,
                'name' => 'view_any_appointment',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 2,
                'name' => 'create_appointment',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 3,
                'name' => 'update_appointment',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 4,
                'name' => 'delete_appointment',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 5,
                'name' => 'view_any_blog::post',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 6,
                'name' => 'create_blog::post',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 7,
                'name' => 'update_blog::post',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 8,
                'name' => 'delete_blog::post',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 9,
                'name' => 'view_any_category',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 10,
                'name' => 'create_category',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 11,
                'name' => 'update_category',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 12,
                'name' => 'delete_category',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 13,
                'name' => 'view_any_feature',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 14,
                'name' => 'create_feature',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 15,
                'name' => 'update_feature',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 16,
                'name' => 'delete_feature',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 17,
                'name' => 'view_any_home::slider',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 18,
                'name' => 'create_home::slider',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 19,
                'name' => 'update_home::slider',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 20,
                'name' => 'delete_home::slider',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 21,
                'name' => 'view_any_job',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 22,
                'name' => 'create_job',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 23,
                'name' => 'update_job',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 24,
                'name' => 'delete_job',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 25,
                'name' => 'view_any_job::application',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 26,
                'name' => 'create_job::application',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 27,
                'name' => 'update_job::application',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 28,
                'name' => 'delete_job::application',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 29,
                'name' => 'view_any_language',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 30,
                'name' => 'create_language',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 31,
                'name' => 'update_language',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 32,
                'name' => 'delete_language',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 33,
                'name' => 'view_any_language::manager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 34,
                'name' => 'create_language::manager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 35,
                'name' => 'update_language::manager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 36,
                'name' => 'delete_language::manager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 37,
                'name' => 'view_any_metal::price',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 38,
                'name' => 'create_metal::price',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 39,
                'name' => 'update_metal::price',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 40,
                'name' => 'delete_metal::price',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 41,
                'name' => 'view_any_metal::purity',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 42,
                'name' => 'create_metal::purity',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 43,
                'name' => 'update_metal::purity',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 44,
                'name' => 'delete_metal::purity',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 45,
                'name' => 'view_any_metal::type',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 46,
                'name' => 'create_metal::type',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 47,
                'name' => 'update_metal::type',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 48,
                'name' => 'delete_metal::type',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 49,
                'name' => 'view_any_newsletter',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 50,
                'name' => 'create_newsletter',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 51,
                'name' => 'update_newsletter',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 52,
                'name' => 'delete_newsletter',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 53,
                'name' => 'view_any_order',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 54,
                'name' => 'create_order',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 55,
                'name' => 'update_order',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 56,
                'name' => 'delete_order',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 57,
                'name' => 'view_any_page',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 58,
                'name' => 'create_page',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 59,
                'name' => 'update_page',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 60,
                'name' => 'delete_page',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 61,
                'name' => 'view_any_permission',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 62,
                'name' => 'create_permission',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 63,
                'name' => 'update_permission',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 64,
                'name' => 'delete_permission',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 65,
                'name' => 'view_any_product',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 66,
                'name' => 'create_product',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 67,
                'name' => 'update_product',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 68,
                'name' => 'delete_product',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 69,
                'name' => 'view_any_role',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 70,
                'name' => 'create_role',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 71,
                'name' => 'update_role',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 72,
                'name' => 'delete_role',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 73,
                'name' => 'view_any_setting::change',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 74,
                'name' => 'create_setting::change',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 75,
                'name' => 'update_setting::change',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 76,
                'name' => 'delete_setting::change',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 77,
                'name' => 'view_any_site::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 78,
                'name' => 'create_site::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 79,
                'name' => 'update_site::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 80,
                'name' => 'delete_site::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 81,
                'name' => 'view_any_store',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 82,
                'name' => 'create_store',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 83,
                'name' => 'update_store',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 84,
                'name' => 'delete_store',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 85,
                'name' => 'view_any_super::admin::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 86,
                'name' => 'create_super::admin::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 87,
                'name' => 'update_super::admin::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 88,
                'name' => 'delete_super::admin::setting',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 89,
                'name' => 'view_any_testimonial',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 90,
                'name' => 'create_testimonial',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 91,
                'name' => 'update_testimonial',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 92,
                'name' => 'delete_testimonial',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 93,
                'name' => 'view_any_user',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 94,
                'name' => 'create_user',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 95,
                'name' => 'update_user',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 96,
                'name' => 'delete_user',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 97,
                'name' => 'page_DashboardPage',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 98,
                'name' => 'page_PreviewSettings',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 99,
                'name' => 'page_SearchSettings',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 100,
                'name' => 'page_SiteSettingsManager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 101,
                'name' => 'page_TranslationsManager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 102,
                'name' => 'widget_MaintenanceModeToggle',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 103,
                'name' => 'widget_StatsOverview',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 104,
                'name' => 'widget_SalesChart',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 105,
                'name' => 'widget_GoldPriceChart',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 106,
                'name' => 'widget_LatestOrders',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 107,
                'name' => 'widget_UpcomingAppointments',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:28:45',
                'updated_at' => '2025-05-28 23:28:45',
            ],
            [
                'id' => 108,
                'name' => 'view_user',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 03:43:33',
                'updated_at' => '2025-05-29 03:43:33',
            ],
            [
                'id' => 109,
                'name' => 'view_role',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 03:43:33',
                'updated_at' => '2025-05-29 03:43:33',
            ],
            [
                'id' => 110,
                'name' => 'view_any_review',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:23:35',
                'updated_at' => '2025-05-29 04:23:35',
            ],
            [
                'id' => 111,
                'name' => 'widget_MainStatsOverview',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 112,
                'name' => 'widget_ContentStatsWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 113,
                'name' => 'widget_EngagementStatsWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 114,
                'name' => 'widget_MetalPricesWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 115,
                'name' => 'widget_RecentOrdersWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 116,
                'name' => 'widget_UpcomingAppointmentsWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 117,
                'name' => 'widget_SalesChartWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
            [
                'id' => 118,
                'name' => 'widget_SystemStatusWidget',
                'guard_name' => 'web',
                'created_at' => '2025-05-29 04:27:31',
                'updated_at' => '2025-05-29 04:27:31',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('permissions')->insert($chunk);
        }

        $this->command->info('permissions seeded successfully!');
    }
}
