<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Database\Seeder;

class OrderItemSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        $this->command->info('جاري إنشاء عناصر الطلبات التجريبية...');

        // التأكد من وجود طلبات ومنتجات
        $orders = Order::all();
        $products = Product::all();

        if ($orders->isEmpty()) {
            $this->command->warn('لا يوجد طلبات لإضافة عناصر لها!');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->warn('لا يوجد منتجات لإضافتها إلى الطلبات!');
            return;
        }

        // التحقق من وجود عناصر طلبات مسبقاً
        if (OrderItem::count() > 0) {
            $this->command->info('عناصر الطلبات موجودة مسبقاً، تم تخطي الإنشاء.');
            return;
        }

        // إنشاء عناصر للطلبات
        foreach ($orders as $order) {
            // عدد عشوائي من المنتجات لكل طلب (1-5)
            $count = rand(1, 5);

            // اختيار منتجات عشوائية
            $randomProducts = $products->random($count);

            $orderTotal = 0;

            foreach ($randomProducts as $product) {
                // كمية عشوائية لكل منتج (1-3)
                $quantity = rand(1, 3);

                // حساب السعر الإجمالي للعنصر
                $totalPrice = $product->price * $quantity;
                $orderTotal += $totalPrice;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name_ar' => $product->name_ar,
                    'product_name_en' => $product->name_en,
                    'product_sku' => $product->sku ?? 'SKU-' . $product->id,
                    'quantity' => $quantity,
                    'unit_price' => $product->price,
                    'total_price' => $totalPrice,
                    'weight' => $product->weight,
                    'metal_purity' => $product->metal_purity,
                    'created_at' => $order->created_at,
                    'updated_at' => $order->updated_at,
                ]);
            }

            // تحديث إجمالي الطلب
            $tax = $orderTotal * 0.14; // 14% ضريبة القيمة المضافة
            $shipping = $orderTotal > 1000 ? 0 : 50; // شحن مجاني للطلبات أكثر من 1000 جنيه
            $discount = rand(0, 200); // خصم عشوائي
            $total = $orderTotal + $tax + $shipping - $discount;

            $order->update([
                'subtotal' => $orderTotal,
                'tax' => $tax,
                'shipping' => $shipping,
                'discount' => $discount,
                'total' => $total,
                'total_amount' => $total,
                'tax_amount' => $tax,
                'shipping_amount' => $shipping,
                'discount_amount' => $discount,
            ]);
        }

        $this->command->info('تم إنشاء ' . OrderItem::count() . ' عنصر طلب تجريبي بنجاح!');
    }
}
