<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('roles')->count() > 0) {
            $this->command->info('roles data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding roles...');

        $data = [
            [
                'id' => 1,
                'name' => 'super_admin',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 21:53:50',
                'updated_at' => '2025-05-28 21:53:50',
            ],
            [
                'id' => 2,
                'name' => 'admin',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:38:31',
                'updated_at' => '2025-05-28 23:38:31',
            ],
            [
                'id' => 3,
                'name' => 'manager',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:38:31',
                'updated_at' => '2025-05-28 23:38:31',
            ],
            [
                'id' => 4,
                'name' => 'user',
                'guard_name' => 'web',
                'created_at' => '2025-05-28 23:38:31',
                'updated_at' => '2025-05-28 23:38:31',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('roles')->insert($chunk);
        }

        $this->command->info('roles seeded successfully!');
    }
}
