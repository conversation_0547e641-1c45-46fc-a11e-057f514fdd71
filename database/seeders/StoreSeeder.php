<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StoreSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('stores')->count() > 0) {
            $this->command->info('stores data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding stores...');

        $data = [
            [
                'id' => 1,
                'name_ar' => 'مكة جولد - فرع القاهرة',
                'name_en' => 'Makkah Gold - Cairo Branch',
                'address_ar' => 'شارع التحرير، وسط البلد، القاهرة',
                'address_en' => 'Tahrir Street, Downtown, Cairo',
                'city_ar' => 'القاهرة',
                'city_en' => 'Cairo',
                'country_ar' => 'مصر',
                'country_en' => 'Egypt',
                'phone' => +20123456789,
                'email' => '<EMAIL>',
                'working_hours_ar' => 'من السبت إلى الخميس: 10 صباحًا - 10 مساءً، الجمعة: 2 ظهرًا - 10 مساءً',
                'working_hours_en' => 'Saturday to Thursday: 10 AM - 10 PM, Friday: 2 PM - 10 PM',
                'latitude' => 30.0444000,
                'longitude' => 31.2357000,
                'image' => null,
                'is_active' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 21:53:51',
            ],
            [
                'id' => 2,
                'name_ar' => 'مكة جولد - فرع الإسكندرية',
                'name_en' => 'Makkah Gold - Alexandria Branch',
                'address_ar' => 'شارع فؤاد، وسط البلد، الإسكندرية',
                'address_en' => 'Fouad Street, Downtown, Alexandria',
                'city_ar' => 'الإسكندرية',
                'city_en' => 'Alexandria',
                'country_ar' => 'مصر',
                'country_en' => 'Egypt',
                'phone' => +20123456790,
                'email' => '<EMAIL>',
                'working_hours_ar' => 'من السبت إلى الخميس: 10 صباحًا - 10 مساءً، الجمعة: 2 ظهرًا - 10 مساءً',
                'working_hours_en' => 'Saturday to Thursday: 10 AM - 10 PM, Friday: 2 PM - 10 PM',
                'latitude' => 31.2001000,
                'longitude' => 29.9187000,
                'image' => null,
                'is_active' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 21:53:51',
            ],
            [
                'id' => 3,
                'name_ar' => 'مكة جولد - فرع الجيزة',
                'name_en' => 'Makkah Gold - Giza Branch',
                'address_ar' => 'شارع الهرم، الجيزة',
                'address_en' => 'Haram Street, Giza',
                'city_ar' => 'الجيزة',
                'city_en' => 'Giza',
                'country_ar' => 'مصر',
                'country_en' => 'Egypt',
                'phone' => +20123456791,
                'email' => '<EMAIL>',
                'working_hours_ar' => 'من السبت إلى الخميس: 10 صباحًا - 10 مساءً، الجمعة: 2 ظهرًا - 10 مساءً',
                'working_hours_en' => 'Saturday to Thursday: 10 AM - 10 PM, Friday: 2 PM - 10 PM',
                'latitude' => 30.0131000,
                'longitude' => 31.2089000,
                'image' => null,
                'is_active' => 1,
                'created_at' => '2025-05-28 21:53:51',
                'updated_at' => '2025-05-28 21:53:51',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('stores')->insert($chunk);
        }

        $this->command->info('stores seeded successfully!');
    }
}
