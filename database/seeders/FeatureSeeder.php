<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FeatureSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('features')->count() > 0) {
            $this->command->info('features data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding features...');

        $data = [
            [
                'id' => 1,
                'title_ar' => 'مجوهرات أصلية',
                'title_en' => 'Authentic Jewelry',
                'description_ar' => 'جميع مجوهراتنا مصنوعة من معادن ثمينة أصلية ومرفقة بشهادة ضمان',
                'description_en' => 'All our jewelry is made from genuine precious metals and comes with a warranty certificate',
                'icon' => 'fas fa-gem',
                'order' => 1,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 2,
                'title_ar' => 'شحن سريع',
                'title_en' => 'Fast Shipping',
                'description_ar' => 'نوفر خدمة شحن سريعة وآمنة لجميع أنحاء مصر',
                'description_en' => 'We provide fast and secure shipping service to all parts of Egypt',
                'icon' => 'fas fa-truck',
                'order' => 2,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 3,
                'title_ar' => 'ضمان الجودة',
                'title_en' => 'Quality Guarantee',
                'description_ar' => 'نقدم ضمان لمدة عام على جميع منتجاتنا ضد عيوب الصناعة',
                'description_en' => 'We offer a one-year warranty on all our products against manufacturing defects',
                'icon' => 'fas fa-shield-alt',
                'order' => 3,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
            [
                'id' => 4,
                'title_ar' => 'سياسة إرجاع مرنة',
                'title_en' => 'Flexible Return Policy',
                'description_ar' => 'يمكنك إرجاع المنتج خلال 14 يومًا من تاريخ الاستلام',
                'description_en' => 'You can return the product within 14 days of receipt',
                'icon' => 'fas fa-exchange-alt',
                'order' => 4,
                'is_active' => 1,
                'created_at' => '2025-05-28 23:38:39',
                'updated_at' => '2025-05-28 23:38:39',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('features')->insert($chunk);
        }

        $this->command->info('features seeded successfully!');
    }
}
