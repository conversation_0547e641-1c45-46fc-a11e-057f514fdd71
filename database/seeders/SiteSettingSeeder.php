<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SiteSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('site_settings')->count() > 0) {
            $this->command->info('site_settings data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding site_settings...');

        $data = [
            [
                'id' => 1,
                'site_name' => 'مجوهرات مكة جولد جروب',
                'site_description' => 'متجر مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية',
                'logo' => 'site-settings/01JWD46BZ5T6JZFTECM2KNJY23.jpg',
                'favicon' => 'site-settings/01JWD46BZ7N72X196Y9E1ZNZ98.jpg',
                'contact_email' => null,
                'contact_phone' => null,
                'address' => null,
                'facebook_url' => null,
                'instagram_url' => null,
                'twitter_url' => null,
                'whatsapp_number' => null,
                'gold_price_24k' => null,
                'gold_price_21k' => null,
                'gold_price_18k' => null,
                'footer_text' => null,
                'meta_title' => null,
                'meta_description' => null,
                'meta_keywords' => null,
                'shipping_cost' => null,
                'free_shipping_threshold' => null,
                'show_featured_products' => 1,
                'show_new_arrivals' => 1,
                'show_categories' => 1,
                'show_gold_prices' => 1,
                'mail_from_address' => null,
                'mail_from_name' => null,
                'mail_host' => null,
                'mail_port' => null,
                'mail_username' => null,
                'mail_password' => null,
                'mail_encryption' => null,
                'youtube_url' => null,
                'tiktok_url' => null,
                'linkedin_url' => null,
                'google_analytics_id' => null,
                'facebook_pixel_id' => null,
                'custom_header_scripts' => null,
                'custom_footer_scripts' => null,
                'created_at' => '2025-05-28 23:38:44',
                'updated_at' => '2025-05-29 05:19:33',
                'shipping_zones' => null,
                'privacy_policy' => null,
                'terms_conditions' => null,
                'return_policy' => null,
                'shipping_policy_text' => null,
                'show_features' => 1,
                'show_testimonials' => 0,
                'show_newsletter' => 1,
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('site_settings')->insert($chunk);
        }

        $this->command->info('site_settings seeded successfully!');
    }
}
