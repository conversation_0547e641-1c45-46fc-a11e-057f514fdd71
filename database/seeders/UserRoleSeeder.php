<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UserRoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('model_has_roles')->count() > 0) {
            $this->command->info('model_has_roles data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding model_has_roles...');

        $data = [
            [
                'role_id' => 2,
                'model_type' => 'App\\Models\\User',
                'model_id' => 1,
            ],
            [
                'role_id' => 3,
                'model_type' => 'App\\Models\\User',
                'model_id' => 2,
            ],
            [
                'role_id' => 1,
                'model_type' => 'App\\Models\\User',
                'model_id' => 11,
            ],
            [
                'role_id' => 1,
                'model_type' => 'App\\Models\\User',
                'model_id' => 12,
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('model_has_roles')->insert($chunk);
        }

        $this->command->info('model_has_roles seeded successfully!');
    }
}
