<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MetalPriceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // فحص وجود البيانات لتجنب التكرار
        if (DB::table('metal_prices')->count() > 0) {
            $this->command->info('metal_prices data already exists. Skipping...');
            return;
        }

        $this->command->info('Seeding metal_prices...');

        $data = [
            [
                'id' => 1,
                'metal_type' => 'gold',
                'purity' => '24K',
                'price_per_gram' => 5280.00,
                'price_per_ounce' => 164226.48,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 2,
                'metal_type' => 'gold',
                'purity' => '22K',
                'price_per_gram' => 4840.00,
                'price_per_ounce' => 150540.94,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 3,
                'metal_type' => 'gold',
                'purity' => '21K',
                'price_per_gram' => 4620.00,
                'price_per_ounce' => 143698.17,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 4,
                'metal_type' => 'gold',
                'purity' => '18K',
                'price_per_gram' => 3960.00,
                'price_per_ounce' => 123169.86,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 5,
                'metal_type' => 'gold',
                'purity' => '14K',
                'price_per_gram' => 3080.00,
                'price_per_ounce' => 95798.78,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 6,
                'metal_type' => 'gold',
                'purity' => '12K',
                'price_per_gram' => 2640.00,
                'price_per_ounce' => 82113.24,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 7,
                'metal_type' => 'gold',
                'purity' => '9K',
                'price_per_gram' => 1980.00,
                'price_per_ounce' => 61584.93,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 8,
                'metal_type' => 'silver',
                'purity' => 999,
                'price_per_gram' => 59.00,
                'price_per_ounce' => 1835.11,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 9,
                'metal_type' => 'silver',
                'purity' => 925,
                'price_per_gram' => 54.75,
                'price_per_ounce' => 1702.92,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 10,
                'metal_type' => 'silver',
                'purity' => 900,
                'price_per_gram' => 53.25,
                'price_per_ounce' => 1656.26,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 11,
                'metal_type' => 'silver',
                'purity' => 800,
                'price_per_gram' => 47.25,
                'price_per_ounce' => 1469.64,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
            [
                'id' => 12,
                'metal_type' => 'silver',
                'purity' => 600,
                'price_per_gram' => 35.50,
                'price_per_ounce' => 1104.17,
                'price_per_piece' => null,
                'currency' => 'EGP',
                'price_date' => '2025-05-29 05:33:50',
                'source' => 'isagha_scraping',
                'is_active' => 1,
                'created_at' => '2025-05-29 05:33:50',
                'updated_at' => '2025-05-29 05:33:50',
            ],
        ];

        // إدراج البيانات على دفعات لتحسين الأداء
        $chunks = array_chunk($data, 100);
        foreach ($chunks as $chunk) {
            DB::table('metal_prices')->insert($chunk);
        }

        $this->command->info('metal_prices seeded successfully!');
    }
}
