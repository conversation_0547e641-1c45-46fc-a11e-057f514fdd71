<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class FullDatabaseSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        $this->command->info('بدء تحميل البيانات التجريبية الكاملة...');

        // إنشاء رابط رمزي للتخزين العام إذا لم يكن موجودًا
        $this->createStorageLink();

        // تنظيف الذاكرة المؤقتة
        Artisan::call('cache:clear');
        $this->command->info('تم تنظيف الذاكرة المؤقتة');

        // تحميل البيانات التجريبية بالترتيب الصحيح
        $this->call([
            // المستخدمين والإعدادات
            UserSeeder::class,
            SiteSettingSeeder::class,

            // الفئات والمنتجات
            CategorySeeder::class,
            ProductSeeder::class,

            // المتاجر وأسعار المعادن
            StoreSeeder::class,
            MetalPriceSeeder::class,

            // المراجعات والطلبات والمواعيد
            ReviewSeeder::class,
            OrderSeeder::class,
            OrderItemSeeder::class,
            AppointmentSeeder::class,

            // قوائم الرغبات وسلة التسوق
            WishlistSeeder::class,
            CartSeeder::class,

            // العناوين والإشعارات
            AddressSeeder::class,
            NotificationSeeder::class,

            // الوسائط وشرائح الصفحة الرئيسية
            MediaSeeder::class,
            HomeSliderSeeder::class,

            // تحميل الصور
            ImagesSeeder::class,
        ]);

        $this->command->info('تم تحميل البيانات التجريبية الكاملة بنجاح!');
    }

    /**
     * إنشاء رابط رمزي للتخزين العام إذا لم يكن موجودًا
     */
    private function createStorageLink(): void
    {
        $targetPath = storage_path('app/public');
        $linkPath = public_path('storage');

        if (!file_exists($linkPath)) {
            $this->command->info('إنشاء رابط رمزي للتخزين العام...');

            try {
                if (file_exists($linkPath)) {
                    unlink($linkPath);
                }

                symlink($targetPath, $linkPath);
                $this->command->info('تم إنشاء الرابط الرمزي بنجاح!');
            } catch (\Exception $e) {
                $this->command->error('فشل إنشاء الرابط الرمزي: ' . $e->getMessage());
                $this->command->info('يرجى تشغيل الأمر التالي يدويًا: php artisan storage:link');
            }
        } else {
            $this->command->info('الرابط الرمزي للتخزين العام موجود بالفعل');
        }
    }
}
