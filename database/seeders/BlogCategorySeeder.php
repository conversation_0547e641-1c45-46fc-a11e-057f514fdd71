<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\BlogCategory;
use Illuminate\Support\Str;

class BlogCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name_ar' => 'نصائح المجوهرات',
                'name_en' => 'Jewelry Tips',
                'description_ar' => 'نصائح وإرشادات مفيدة حول المجوهرات',
                'description_en' => 'Useful tips and guidance about jewelry',
            ],
            [
                'name_ar' => 'العناية بالمجوهرات',
                'name_en' => 'Jewelry Care',
                'description_ar' => 'كيفية العناية بالمجوهرات والحفاظ عليها',
                'description_en' => 'How to care for and maintain your jewelry',
            ],
            [
                'name_ar' => 'اتجاهات الموضة',
                'name_en' => 'Fashion Trends',
                'description_ar' => 'أحدث اتجاهات الموضة في عالم المجوهرات',
                'description_en' => 'Latest fashion trends in the world of jewelry',
            ],
            [
                'name_ar' => 'دليل الشراء',
                'name_en' => 'Buying Guide',
                'description_ar' => 'دليل شامل لشراء المجوهرات',
                'description_en' => 'Comprehensive guide to buying jewelry',
            ],
            [
                'name_ar' => 'تاريخ المجوهرات',
                'name_en' => 'Jewelry History',
                'description_ar' => 'تاريخ وحضارة المجوهرات عبر العصور',
                'description_en' => 'History and civilization of jewelry through the ages',
            ],
            [
                'name_ar' => 'الاستثمار في المجوهرات',
                'name_en' => 'Jewelry Investment',
                'description_ar' => 'كيفية الاستثمار في المجوهرات والذهب',
                'description_en' => 'How to invest in jewelry and gold',
            ],
            [
                'name_ar' => 'المناسبات الخاصة',
                'name_en' => 'Special Occasions',
                'description_ar' => 'مجوهرات المناسبات الخاصة والأعراس',
                'description_en' => 'Jewelry for special occasions and weddings',
            ],
            [
                'name_ar' => 'الأحجار الكريمة',
                'name_en' => 'Gemstones',
                'description_ar' => 'معلومات عن الأحجار الكريمة وخصائصها',
                'description_en' => 'Information about gemstones and their properties',
            ],
        ];

        foreach ($categories as $index => $category) {
            BlogCategory::create([
                'name_ar' => $category['name_ar'],
                'name_en' => $category['name_en'],
                'slug' => Str::slug($category['name_en']),
                'description_ar' => $category['description_ar'],
                'description_en' => $category['description_en'],
                'is_active' => true,
                'sort_order' => $index + 1,
            ]);
        }
    }
}
