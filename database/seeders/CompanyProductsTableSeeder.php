<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * Seeder لجدول منتجات الشركات
 * يربط بين الشركات وأنواع المنتجات مع تحديد تكلفة التصنيع وقيمة الاسترداد
 */
class CompanyProductsTableSeeder extends Seeder
{
    /**
     * تشغيل الـ seeder - إدراج بيانات منتجات الشركات
     */
    public function run(): void
    {
        // التحقق من وجود البيانات لتجنب التكرار
        if (DB::table('company_products')->count() > 0) {
            $this->command->info('جدول منتجات الشركات يحتوي على بيانات بالفعل. تم تخطي الـ seeder.');
            return;
        }

        $this->command->info('بدء إدراج بيانات منتجات الشركات...');

        // التأكد من وجود الشركات وأنواع المنتجات
        $companies = DB::table('companies')->pluck('id')->toArray();
        $productTypes = DB::table('product_types')->pluck('id')->toArray();

        if (empty($companies) || empty($productTypes)) {
            $this->command->error('يجب تشغيل seeders الشركات وأنواع المنتجات أولاً!');
            return;
        }

        $companyProducts = [];
        $now = now();

        // إنشاء منتجات لكل شركة مع كل نوع منتج
        foreach ($companies as $companyId) {
            foreach ($productTypes as $productTypeId) {
                $companyProducts[] = [
                    'company_id' => $companyId,
                    'product_type_id' => $productTypeId,
                    'manufacturing_cost_per_gram' => $this->generateManufacturingCost(),
                    'refund_value_per_gram' => $this->generateRefundValue(),
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        // إدراج البيانات في دفعات لتحسين الأداء
        $chunks = array_chunk($companyProducts, 50);
        foreach ($chunks as $chunk) {
            DB::table('company_products')->insert($chunk);
        }

        $this->command->info('تم إدراج ' . count($companyProducts) . ' منتج شركة بنجاح.');
    }

    /**
     * توليد تكلفة تصنيع عشوائية واقعية
     */
    private function generateManufacturingCost(): float
    {
        // تكلفة التصنيع تتراوح بين 15-200 جنيه للجرام
        return round(rand(1500, 20000) / 100, 2);
    }

    /**
     * توليد قيمة استرداد عشوائية واقعية
     */
    private function generateRefundValue(): float
    {
        // قيمة الاسترداد تتراوح بين 10-50 جنيه للجرام
        return round(rand(1000, 5000) / 100, 2);
    }
}
