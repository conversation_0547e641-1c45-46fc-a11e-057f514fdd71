<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MetalPrice;
use Carbon\Carbon;

class YesterdayPricesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $yesterday = Carbon::now()->subDay();

        // التحقق من وجود أسعار أمس مسبقاً
        $existingCount = MetalPrice::whereDate('created_at', $yesterday->format('Y-m-d'))->count();
        if ($existingCount > 0) {
            $this->command->info("أسعار أمس موجودة مسبقاً ({$existingCount} سجل)، تم تخطي الإنشاء.");
            return;
        }

        $prices = [
            // أسعار الذهب أمس
            [
                'metal_type' => 'gold',
                'purity' => '24K',
                'price_per_gram' => 2800.00,
                'price_per_ounce' => 2800.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
            [
                'metal_type' => 'gold',
                'purity' => '22K',
                'price_per_gram' => 2600.00,
                'price_per_ounce' => 2600.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
            [
                'metal_type' => 'gold',
                'purity' => '21K',
                'price_per_gram' => 4500.00,
                'price_per_ounce' => 4500.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
            [
                'metal_type' => 'gold',
                'purity' => '18K',
                'price_per_gram' => 2400.00,
                'price_per_ounce' => 2400.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
            [
                'metal_type' => 'gold',
                'purity' => '14K',
                'price_per_gram' => 2000.00,
                'price_per_ounce' => 2000.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
            // أسعار الفضة أمس
            [
                'metal_type' => 'silver',
                'purity' => '999',
                'price_per_gram' => 45.00,
                'price_per_ounce' => 45.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
            [
                'metal_type' => 'silver',
                'purity' => '925',
                'price_per_gram' => 42.00,
                'price_per_ounce' => 42.00 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $yesterday->format('Y-m-d'),
                'source' => 'manual',
                'is_active' => false,
                'created_at' => $yesterday->setTime(8, 0, 0),
                'updated_at' => $yesterday->setTime(8, 0, 0),
            ],
        ];

        foreach ($prices as $price) {
            MetalPrice::create($price);
        }

        $this->command->info('تم إضافة أسعار أمس بنجاح!');
        $this->command->info('إجمالي الأسعار: ' . MetalPrice::count());
    }
}
