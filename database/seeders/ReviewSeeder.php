<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود تقييمات مسبقاً
        $existingReviews = Review::count();
        if ($existingReviews >= 500) {
            $this->command->info("التقييمات موجودة مسبقاً ({$existingReviews} تقييم)، تم تخطي الإنشاء.");
            return;
        }

        $this->command->info('جاري إنشاء التقييمات التجريبية...');

        // التأكد من وجود مستخدمين ومنتجات
        $users = User::all();
        $products = Product::all();

        if ($users->isEmpty()) {
            $this->command->warn('لا يوجد مستخدمين لإنشاء تقييمات لهم!');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->warn('لا يوجد منتجات لإضافة تقييمات لها!');
            return;
        }

        // التعليقات الإيجابية
        $positiveComments = [
            'منتج رائع، أنصح به بشدة!',
            'جودة ممتازة وسعر مناسب.',
            'وصل المنتج بسرعة وبحالة ممتازة.',
            'تصميم جميل جدًا وجودة عالية.',
            'أفضل من المتوقع، سأشتري منه مرة أخرى.',
            'خدمة عملاء ممتازة وتوصيل سريع.',
            'المنتج مطابق للصورة تمامًا.',
            'سعيد جدًا بهذا الشراء!',
            'قطعة فريدة من نوعها وجميلة جدًا.',
            'لمعان رائع وتشطيب ممتاز.',
        ];

        // التعليقات المتوسطة
        $neutralComments = [
            'منتج جيد ولكن السعر مرتفع قليلاً.',
            'جودة مقبولة مقارنة بالسعر.',
            'التوصيل تأخر قليلاً لكن المنتج جيد.',
            'تصميم جميل لكن الحجم أصغر مما توقعت.',
            'منتج عادي، لا شيء مميز.',
            'التغليف كان بسيطًا جدًا.',
            'المنتج يختلف قليلاً عن الصورة.',
            'سعر مناسب لكن الجودة متوسطة.',
            'لا بأس به، لكن كنت أتوقع أفضل.',
            'خدمة العملاء متوسطة.',
        ];

        // التعليقات السلبية
        $negativeComments = [
            'جودة رديئة مقارنة بالسعر.',
            'المنتج مختلف تمامًا عن الصورة.',
            'وصل المنتج متأخرًا وبحالة سيئة.',
            'لا أنصح بهذا المنتج أبدًا.',
            'سعر مرتفع جدًا مقابل جودة متدنية.',
            'خدمة عملاء سيئة.',
            'تم إرجاع المنتج لعدم مطابقته للمواصفات.',
            'تصميم سيء وجودة رديئة.',
            'لن أشتري من هذا المتجر مرة أخرى.',
            'خاب ظني في هذا المنتج.',
        ];

        // إنشاء تقييمات للمنتجات
        foreach ($products as $product) {
            // عدد عشوائي من التقييمات لكل منتج (0-5)
            $count = rand(0, 5);

            // اختيار مستخدمين عشوائيين
            $randomUsers = $users->random(min($count, $users->count()));

            foreach ($randomUsers as $user) {
                // تخطي إذا كان المستخدم قد قام بتقييم هذا المنتج من قبل
                if (Review::where('product_id', $product->id)->where('user_id', $user->id)->exists()) {
                    continue;
                }

                // تقييم عشوائي (1-5)
                $rating = rand(1, 5);

                // اختيار تعليق بناءً على التقييم
                if ($rating >= 4) {
                    $comment = $positiveComments[array_rand($positiveComments)];
                } elseif ($rating >= 2) {
                    $comment = $neutralComments[array_rand($neutralComments)];
                } else {
                    $comment = $negativeComments[array_rand($negativeComments)];
                }

                try {
                    Review::create([
                        'product_id' => $product->id,
                        'user_id' => $user->id,
                        'rating' => $rating,
                        'comment' => $comment,
                        'is_approved' => true,
                        'created_at' => now()->subDays(rand(0, 60)),
                    ]);
                } catch (\Exception $e) {
                    // تجاهل الأخطاء المتعلقة بتكرار التقييمات
                    continue;
                }
            }
        }

        $this->command->info('تم إنشاء ' . Review::count() . ' تقييم تجريبي بنجاح!');
    }
}
