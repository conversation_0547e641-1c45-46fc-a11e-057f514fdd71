<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class NotificationSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود الإشعارات مسبقاً
        $existingNotification = DB::table('notifications')->count();
        if ($existingNotification >= 200) {
            $this->command->info("الإشعارات موجودة مسبقاً ({$existingNotification} سجل)، تم تخطي الإنشاء.");
            return;
        }

        $this->command->info('جاري إنشاء الإشعارات التجريبية...');

        // التأكد من وجود مستخدمين
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->warn('لا يوجد مستخدمين لإنشاء إشعارات لهم!');
            return;
        }

        // أنواع الإشعارات
        $types = ['info', 'success', 'warning', 'error'];

        // عناوين الإشعارات
        $titles = [
            'طلب جديد',
            'تم شحن طلبك',
            'تم تأكيد موعدك',
            'تخفيضات جديدة',
            'منتجات جديدة',
            'تحديث حالة الطلب',
            'تم استلام طلبك',
            'تم إلغاء طلبك',
            'تم تغيير موعدك',
            'تم إلغاء موعدك',
        ];

        // رسائل الإشعارات
        $messages = [
            'تم تقديم طلبك بنجاح وهو قيد المعالجة الآن.',
            'تم شحن طلبك وسيصل إليك خلال 3-5 أيام عمل.',
            'تم تأكيد موعدك ليوم الأحد القادم الساعة 10:00 صباحًا.',
            'تخفيضات كبيرة على جميع المنتجات بمناسبة العيد!',
            'تم إضافة منتجات جديدة إلى المتجر، تفضل بزيارتنا!',
            'تم تحديث حالة طلبك إلى "قيد التحضير".',
            'تم استلام طلبك بنجاح، شكرًا لتسوقك معنا!',
            'تم إلغاء طلبك بناءً على طلبك.',
            'تم تغيير موعدك إلى يوم الاثنين القادم الساعة 2:00 ظهرًا.',
            'تم إلغاء موعدك بناءً على طلبك.',
        ];

        // روابط الإشعارات
        $links = [
            '/orders/123',
            '/orders/456',
            '/appointments/789',
            '/products/sale',
            '/products/new',
            '/orders/321',
            '/orders/654',
            '/orders/987',
            '/appointments/456',
            '/appointments/123',
        ];

        // إنشاء إشعارات لكل مستخدم
        foreach ($users as $user) {
            // عدد عشوائي من الإشعارات لكل مستخدم (1-10)
            $count = rand(1, 10);

            for ($i = 0; $i < $count; $i++) {
                $titleIndex = array_rand($titles);
                $messageIndex = array_rand($messages);
                $linkIndex = array_rand($links);

                // إنشاء بيانات الإشعار
                $notificationType = $types[array_rand($types)];
                $notificationData = [
                    'title' => $titles[$titleIndex],
                    'message' => $messages[$messageIndex],
                    'type' => $notificationType,
                    'link' => $links[$linkIndex],
                ];

                // إنشاء الإشعار باستخدام هيكل Laravel الافتراضي
                DB::table('notifications')->insert([
                    'id' => \Illuminate\Support\Str::uuid(),
                    'type' => 'App\\Notifications\\SystemNotification',
                    'notifiable_type' => 'App\\Models\\User',
                    'notifiable_id' => $user->id,
                    'data' => json_encode($notificationData),
                    'read_at' => rand(0, 1) ? null : now()->subDays(rand(0, 15)),
                    'created_at' => now()->subDays(rand(0, 30))->subHours(rand(0, 23))->subMinutes(rand(0, 59)),
                    'updated_at' => now(),
                ]);
            }
        }

        $notificationCount = DB::table('notifications')->count();
        $this->command->info('تم إنشاء ' . $notificationCount . ' إشعار تجريبي بنجاح!');
    }
}
