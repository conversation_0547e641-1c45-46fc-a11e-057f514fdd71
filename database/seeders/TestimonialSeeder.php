<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // التحقق من وجود شهادات مسبقاً
        if (Testimonial::count() > 0) {
            $this->command->info('الشهادات موجودة مسبقاً، تم تخطي الإنشاء.');
            return;
        }
        $this->command->info('جاري إنشاء آراء العملاء التجريبية...');

        $testimonials = [
            [
                'client_name_ar' => 'سارة أحمد',
                'client_name_en' => '<PERSON>',
                'location_ar' => 'القاهرة',
                'location_en' => 'Cairo',
                'content_ar' => 'جودة المجوهرات ممتازة والتصاميم فريدة من نوعها. أنا سعيدة جدًا بخاتم الخطوبة الذي اشتريته من مكة جولد.',
                'content_en' => 'The quality of the jewelry is excellent and the designs are unique. I am very happy with the engagement ring I bought from Makkah Gold.',
                'image' => null,
                'rating' => 5,
                'order' => 1,
                'is_active' => true,
            ],
            [
                'client_name_ar' => 'محمد علي',
                'client_name_en' => 'Mohamed Ali',
                'location_ar' => 'الإسكندرية',
                'location_en' => 'Alexandria',
                'content_ar' => 'خدمة العملاء ممتازة والمنتجات ذات جودة عالية. اشتريت سلسلة ذهبية وكانت أفضل مما توقعت. سأعود بالتأكيد للشراء مرة أخرى.',
                'content_en' => 'Customer service is excellent and the products are of high quality. I bought a gold chain and it was better than I expected. I will definitely come back to buy again.',
                'image' => null,
                'rating' => 5,
                'order' => 2,
                'is_active' => true,
            ],
            [
                'client_name_ar' => 'أحمد محمود',
                'client_name_en' => 'Ahmed Mahmoud',
                'location_ar' => 'الجيزة',
                'location_en' => 'Giza',
                'content_ar' => 'أسعار معقولة مقارنة بالجودة العالية. اشتريت أقراطًا لزوجتي وكانت سعيدة جدًا بها. التوصيل كان سريعًا والتغليف كان ممتازًا.',
                'content_en' => 'Reasonable prices compared to the high quality. I bought earrings for my wife and she was very happy with them. Delivery was fast and packaging was excellent.',
                'image' => null,
                'rating' => 4,
                'order' => 3,
                'is_active' => true,
            ],
            [
                'client_name_ar' => 'نورا حسن',
                'client_name_en' => 'Noura Hassan',
                'location_ar' => 'الرياض',
                'location_en' => 'Riyadh',
                'content_ar' => 'تجربة تسوق رائعة! المجوهرات جميلة جدًا والأسعار معقولة. سأوصي بهذا المتجر لجميع أصدقائي.',
                'content_en' => 'Amazing shopping experience! The jewelry is very beautiful and the prices are reasonable. I will recommend this store to all my friends.',
                'image' => null,
                'rating' => 5,
                'order' => 4,
                'is_active' => true,
            ],
            [
                'client_name_ar' => 'خالد عبدالله',
                'client_name_en' => 'Khaled Abdullah',
                'location_ar' => 'جدة',
                'location_en' => 'Jeddah',
                'content_ar' => 'اشتريت خاتم زواج من هذا المتجر وكان رائعًا. الجودة ممتازة والسعر معقول. أنصح الجميع بالتسوق من هنا.',
                'content_en' => 'I bought a wedding ring from this store and it was amazing. The quality is excellent and the price is reasonable. I recommend everyone to shop from here.',
                'image' => null,
                'rating' => 5,
                'order' => 5,
                'is_active' => true,
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }

        $this->command->info('تم إنشاء ' . count($testimonials) . ' آراء عملاء بنجاح!');
    }
}
