<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\User;
use App\Models\Wishlist;
use Illuminate\Database\Seeder;

class WishlistSeeder extends Seeder
{
    /**
     * تشغيل بذرة قاعدة البيانات.
     */
    public function run(): void
    {
        // التحقق من وجود قوائم الرغبات مسبقاً
        $existingWishlist = Wishlist::count();
        if ($existingWishlist >= 100) {
            $this->command->info("قوائم الرغبات موجودة مسبقاً ({$existingWishlist} سجل)، تم تخطي الإنشاء.");
            return;
        }

        $this->command->info('جاري إنشاء قوائم الرغبات التجريبية...');

        // التأكد من وجود مستخدمين ومنتجات
        $users = User::all();
        $products = Product::all();

        if ($users->isEmpty()) {
            $this->command->warn('لا يوجد مستخدمين لإنشاء قوائم رغبات لهم!');
            return;
        }

        if ($products->isEmpty()) {
            $this->command->warn('لا يوجد منتجات لإضافتها إلى قوائم الرغبات!');
            return;
        }

        // إنشاء قوائم رغبات لكل مستخدم
        foreach ($users as $user) {
            // عدد عشوائي من المنتجات لكل مستخدم (1-5)
            $count = rand(1, 5);

            // اختيار منتجات عشوائية
            $randomProducts = $products->random($count);

            foreach ($randomProducts as $product) {
                // تخطي إذا كان المنتج موجود بالفعل في قائمة رغبات المستخدم
                if (Wishlist::where('user_id', $user->id)->where('product_id', $product->id)->exists()) {
                    continue;
                }

                try {
                    Wishlist::create([
                        'user_id' => $user->id,
                        'product_id' => $product->id,
                        'created_at' => now()->subDays(rand(0, 30)),
                    ]);
                } catch (\Exception $e) {
                    // تجاهل الأخطاء المتعلقة بتكرار المنتجات في قائمة الرغبات
                    continue;
                }
            }
        }

        $this->command->info('تم إنشاء ' . Wishlist::count() . ' عنصر في قوائم الرغبات بنجاح!');
    }
}
