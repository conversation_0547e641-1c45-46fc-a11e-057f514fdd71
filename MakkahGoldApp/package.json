{"name": "makkah-gold-group-jewelry", "version": "2.0.0", "main": "expo-router/entry", "license": "MIT", "scripts": {"start": "npx expo start", "android": "npx expo start --android", "ios": "npx expo start --ios", "web": "npx expo start --web", "test-updates": "node scripts/test-updates.js", "build:production": "eas build --platform android --profile production", "update:production": "eas update --branch production", "publish-update": "./scripts/publish-update.sh"}, "dependencies": {"@expo-google-fonts/cairo": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "axios": "^1.9.0", "expo": "53.0.20", "expo-blur": "~14.1.5", "expo-constants": "~17.1.6", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-updates": "~0.28.17", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "expo-image": "~2.4.0"}, "devDependencies": {}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-chart-kit"], "listUnknownPackages": false}}}}