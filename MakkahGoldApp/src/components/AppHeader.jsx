// مكون الهيدر المشترك - Shared App Header Component
import React from "react";
import {
  View,
  TouchableOpacity,
  Image,
  StyleSheet,
  Platform,
  I18nManager,
  Text,
} from "react-native";
import { router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../contexts/ThemeContext";
import { SPACING, FONTS } from "../constants";
import ThemedText from "./ThemedText";

/**
 * مكون الهيدر المشترك للتطبيق
 * @param {Object} props - خصائص المكون
 * @param {string} props.title - عنوان الصفحة
 * @param {string} [props.subtitle] - العنوان الفرعي (اختياري)
 * @param {string} [props.icon] - أيقونة الصفحة (اختيارية)
 * @param {boolean} [props.showBackButton=true] - إظهار زر الرجوع
 * @param {boolean} [props.showLogo=false] - إظهار اللوجو
 * @param {boolean} [props.showThemeToggle=false] - إظهار زر تغيير الثيم
 * @param {boolean} [props.useGradient=true] - استخدام خلفية متدرجة
 * @param {Function} [props.onBackPress] - دالة مخصصة للرجوع
 * @param {Function} [props.onThemeToggle] - دالة تغيير الثيم
 * @param {Object} [props.style] - أنماط إضافية
 */
const AppHeader = ({
  title,
  subtitle,
  icon,
  showBackButton = true,
  showLogo = false,
  showThemeToggle = false,
  useGradient = true,
  onBackPress,
  onThemeToggle,
  style,
}) => {
  const { colors, isDarkMode } = useTheme();
  const insets = useSafeAreaInsets();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  const styles = createStyles(colors, isDarkMode, insets);

  // إذا كان useGradient = true، استخدم نمط صفحة الأقسام
  if (useGradient) {
    return (
      <LinearGradient
        colors={[colors.primary, colors.primaryDark || colors.primary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={[styles.gradientHeader, { paddingTop: insets.top + 20 }, style]}
      >
        {showBackButton && (
          <TouchableOpacity
            onPress={handleBackPress}
            style={styles.backButtonGradient}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name={I18nManager.isRTL ? "arrow-forward" : "arrow-back"}
              size={24}
              color={colors.surface}
            />
          </TouchableOpacity>
        )}

        {icon && <MaterialIcons name={icon} size={32} color={colors.surface} />}

        <Text style={[styles.gradientTitle, { color: colors.surface }]}>
          {title}
        </Text>

        {subtitle && (
          <Text
            style={[
              styles.gradientSubtitle,
              { color: colors.surface, opacity: 0.9 },
            ]}
          >
            {subtitle}
          </Text>
        )}
      </LinearGradient>
    );
  }

  // النمط العادي (نمط الصفحة الرئيسية)
  return (
    <View style={[styles.header, { backgroundColor: colors.surface }, style]}>
      {/* الجانب الأيسر */}
      <View style={styles.leftSection}>
        {showBackButton && (
          <TouchableOpacity
            onPress={handleBackPress}
            style={styles.actionButton}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name={I18nManager.isRTL ? "arrow-forward" : "arrow-back"}
              size={24}
              color={colors.textPrimary}
            />
          </TouchableOpacity>
        )}
        {showThemeToggle && (
          <TouchableOpacity
            style={styles.themeToggleButton}
            onPress={onThemeToggle}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name={isDarkMode ? "light-mode" : "dark-mode"}
              size={24}
              color={colors.primary}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* المنطقة الوسطى */}
      <View style={styles.centerSection}>
        <View style={styles.titleContainer}>
          {icon && (
            <MaterialIcons
              name={icon}
              size={24}
              color={colors.primary}
              style={styles.titleIcon}
            />
          )}
          <ThemedText
            variant="primary"
            size="lg"
            weight="bold"
            style={styles.title}
          >
            {title}
          </ThemedText>
        </View>
      </View>

      {/* الجانب الأيمن */}
      <View style={styles.rightSection}>
        {showLogo && (
          <View style={styles.logoContainer}>
            <Image
              source={require("../../assets/logo.png")}
              style={styles.logo}
              resizeMode="contain"
            />
          </View>
        )}
      </View>
    </View>
  );
};

const createStyles = (colors, isDarkMode, insets) =>
  StyleSheet.create({
    // نمط الهيدر العادي
    header: {
      flexDirection: I18nManager.isRTL ? "row-reverse" : "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      paddingTop: Platform.OS === "ios" ? insets.top + SPACING.sm : SPACING.md,
      paddingBottom: SPACING.sm,
      elevation: Platform.OS === "android" ? 4 : 0,
      shadowColor: colors.shadow || "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      minHeight: Platform.OS === "ios" ? 44 + insets.top : 60,
      marginTop: Platform.OS === "ios" ? SPACING.sm : SPACING.lg,
    },
    // نمط الهيدر المتدرج (مثل صفحة الأقسام)
    gradientHeader: {
      padding: SPACING.lg,
      paddingBottom: SPACING.xl,
      alignItems: "center",
      elevation: 8,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      position: "relative",
    },
    gradientTitle: {
      fontSize: FONTS.sizes.xl,
      fontFamily: FONTS.families.bold,
      marginBottom: SPACING.sm,
      marginTop: SPACING.sm,
      textAlign: "center",
    },
    gradientSubtitle: {
      fontSize: FONTS.sizes.md,
      fontFamily: FONTS.families.regular,
      textAlign: "center",
      lineHeight: 22,
    },
    backButtonGradient: {
      position: "absolute",
      top: insets.top + 20,
      left: I18nManager.isRTL ? undefined : SPACING.md,
      right: I18nManager.isRTL ? SPACING.md : undefined,
      padding: SPACING.sm,
      borderRadius: 8,
      backgroundColor: "rgba(255, 255, 255, 0.2)",
      zIndex: 1,
    },
    leftSection: {
      flexDirection: "row",
      alignItems: "center",
      width: 56,
      justifyContent: "flex-start",
    },
    centerSection: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: SPACING.sm,
    },
    rightSection: {
      flexDirection: "row",
      alignItems: "center",
      width: 56,
      justifyContent: "flex-end",
    },
    titleContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
    },
    titleIcon: {
      marginRight: I18nManager.isRTL ? 0 : SPACING.xs,
      marginLeft: I18nManager.isRTL ? SPACING.xs : 0,
    },
    title: {
      textAlign: "center",
    },
    actionButton: {
      padding: SPACING.sm,
      borderRadius: 8,
      alignItems: "center",
      justifyContent: "center",
      width: 40,
      height: 40,
    },
    themeToggleButton: {
      padding: SPACING.sm,
      borderRadius: 8,
      backgroundColor: "rgba(212, 175, 55, 0.1)",
      width: 56,
      height: 40,
      alignItems: "center",
      justifyContent: "center",
    },
    logoContainer: {
      alignItems: "center",
      justifyContent: "center",
      width: 56,
      height: 56,
    },
    logo: {
      width: 52,
      height: 52,
      borderRadius: 26,
      backgroundColor: "transparent",
      shadowColor: colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.4 : 0.15,
      shadowRadius: 3,
      elevation: 2,
      ...(Platform.OS === "ios" && {
        shadowPath: undefined,
        shouldRasterizeIOS: true,
        rasterizationScale: 2,
      }),
    },
  });

export default AppHeader;
