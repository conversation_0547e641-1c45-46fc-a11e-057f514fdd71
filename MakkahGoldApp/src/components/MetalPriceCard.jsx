// مكون بطاقة أسعار المعادن - Metal Price Card Component

import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, I18nManager } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, FONTS, SPACING } from "../constants";

const MetalPriceCard = ({ metalGroup, onPricePress }) => {
  const getMetalIcon = (metalType) => {
    switch (metalType) {
      case "gold":
        return "star";
      case "silver":
        return "circle";
      case "platinum":
        return "diamond";
      default:
        return "monetization-on";
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat("ar-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(price);
  };

  const formatTime = (timeString) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString("ar-EG", {
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return timeString;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <MaterialIcons
            name={getMetalIcon(metalGroup.metal_type)}
            size={24}
            color={COLORS.primary}
          />
          <Text style={styles.title}>{metalGroup.metal_name}</Text>
        </View>
        <Text style={styles.lastUpdate}>
          آخر تحديث: {formatTime(metalGroup.prices[0]?.last_updated || "")}
        </Text>
      </View>

      {/* Prices List */}
      <View style={styles.pricesList}>
        {metalGroup.prices.map((price, index) => (
          <TouchableOpacity
            key={price.id}
            style={[
              styles.priceItem,
              index === metalGroup.prices.length - 1 && styles.lastPriceItem,
            ]}
            onPress={() => onPricePress(metalGroup.metal_type, price.purity)}
          >
            <View style={styles.priceInfo}>
              <Text style={styles.purityName}>{price.purity_name}</Text>
              <Text style={styles.purity}>{price.purity}</Text>
            </View>

            <View style={styles.priceValue}>
              <Text style={styles.price}>
                {formatPrice(price.price_per_gram)}
              </Text>
              <Text style={styles.unit}>للجرام</Text>
            </View>

            <MaterialIcons
              name={I18nManager.isRTL ? "chevron-right" : "chevron-left"} // RTL support
              size={20}
              color={COLORS.textSecondary}
            />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    marginHorizontal: SPACING.md,
    marginVertical: SPACING.sm,
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  titleContainer: {
    flexDirection: I18nManager.isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    marginBottom: SPACING.xs,
  },
  title: {
    marginLeft: I18nManager.isRTL ? 0 : SPACING.sm, // RTL support
    marginRight: I18nManager.isRTL ? SPACING.sm : 0, // RTL support
    fontSize: FONTS.sizes.lg,
    fontFamily: "Cairo-Bold",
    color: COLORS.textPrimary,
  },
  lastUpdate: {
    fontSize: FONTS.sizes.sm,
    color: COLORS.textSecondary,
    fontFamily: "Cairo-Regular",
  },
  pricesList: {
    padding: SPACING.md,
  },
  priceItem: {
    flexDirection: I18nManager.isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  lastPriceItem: {
    borderBottomWidth: 0,
  },
  priceInfo: {
    flex: 1,
  },
  purityName: {
    fontSize: FONTS.sizes.md,
    fontFamily: "Cairo-Medium",
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs / 2,
  },
  purity: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Regular",
    color: COLORS.textSecondary,
  },
  priceValue: {
    alignItems: "flex-end",
    marginRight: SPACING.md,
  },
  price: {
    fontSize: FONTS.sizes.lg,
    fontFamily: "Cairo-Bold",
    color: COLORS.primary,
    marginBottom: SPACING.xs / 2,
  },
  unit: {
    fontSize: FONTS.sizes.xs,
    fontFamily: "Cairo-Regular",
    color: COLORS.textSecondary,
  },
});

export default MetalPriceCard;
