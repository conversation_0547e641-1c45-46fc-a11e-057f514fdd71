// بطاقة متكيفة مع الثيم - Themed Card Component
import React from "react";
import { View, StyleSheet, ViewStyle } from "react-native";
import { useTheme } from "../contexts/ThemeContext";
import { SPACING } from "../constants";

interface ThemedCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  padding?: number;
  margin?: number;
  borderRadius?: number;
  elevation?: number;
}

const ThemedCard = ({
  children,
  style,
  padding = SPACING.md,
  margin = 0,
  borderRadius = 12,
  elevation = 3,
}) => {
  const { colors } = useTheme();

  const cardStyle = [
    styles.card,
    {
      backgroundColor: colors.surface,
      padding,
      margin,
      borderRadius,
      shadowColor: "#000",
      elevation,
    },
    style,
  ];

  return <View style={cardStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  card: {
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
});

export default ThemedCard;
