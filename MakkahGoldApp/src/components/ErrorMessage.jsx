// مكون رسالة الخطأ - Error Message Component

import React from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, FONTS, SPACING } from "../constants";
import { useTheme } from "../contexts/ThemeContext";
import ThemedText from "./ThemedText";

const ErrorMessage = ({ message, onRetry }) => {
  const { colors } = useTheme();

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <MaterialIcons
        name="error-outline"
        size={48}
        color={colors.error || "#F44336"}
      />
      <ThemedText variant="secondary" size="md" style={styles.message}>
        {message}
      </ThemedText>
      {onRetry && (
        <TouchableOpacity
          style={[styles.retryButton, { backgroundColor: colors.primary }]}
          onPress={onRetry}
        >
          <MaterialIcons name="refresh" size={20} color={colors.surface} />
          <ThemedText
            variant="surface"
            size="md"
            weight="bold"
            style={styles.retryText}
          >
            إعادة المحاولة
          </ThemedText>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: SPACING.lg,
  },
  message: {
    marginTop: SPACING.md,
    marginBottom: SPACING.lg,
    textAlign: "center",
    lineHeight: 24,
  },
  retryButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 8,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  retryText: {
    marginLeft: SPACING.sm,
  },
});

export default ErrorMessage;
