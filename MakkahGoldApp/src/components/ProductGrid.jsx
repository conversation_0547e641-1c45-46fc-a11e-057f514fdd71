// شبكة المنتجات - Product Grid Component
import React from "react";
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
  Linking,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "../contexts/ThemeContext";
import { useAppSettings } from "../utils/hooks";
import { FONTS, SPACING } from "../constants";
import ThemedText from "./ThemedText";
import ThemedCard from "./ThemedCard";

const { width } = Dimensions.get("window");
const itemWidth = (width - SPACING.md * 3) / 2;

interface ProductGridProps {
  products: Product[];
  onProductPress: (productId: number) => void;
  onFavoritePress?: (productId: number) => void;
  favorites?: number[];
  numColumns?: number;
  showFavorites?: boolean;
}

const ProductGrid = ({
  products,
  onProductPress,
  onFavoritePress,
  favorites = [],
  numColumns = 2,
  showFavorites = true,
}) => {
  const { colors } = useTheme();

  // جلب إعدادات التطبيق للحصول على رقم الواتساب
  const { contactInfo } = useAppSettings();

  // التحقق من صحة البيانات
  if (!products || !Array.isArray(products)) {
    return null;
  }

  // تنظيف البيانات
  const cleanProducts = products.filter(
    (product) => product && typeof product === "object" && product.id
  );

  const formatPrice = (price: number) => {
    if (!price || isNaN(price) || price <= 0) {
      return "السعر غير متاح";
    }
    try {
      return `${price.toLocaleString("en-US")} جنيه`;
    } catch (error) {
      return "السعر غير متاح";
    }
  };

  const calculateDiscount = (price: number, oldPrice?: number) => {
    if (!oldPrice || oldPrice <= price) return null;
    return Math.round(((oldPrice - price) / oldPrice) * 100);
  };

  const openWhatsApp = (productName: string) => {
    // التحقق من وجود رقم الواتساب من API
    if (!contactInfo?.whatsapp) {
      console.warn("رقم الواتساب غير متاح في إعدادات التطبيق");
      return;
    }

    const safeName = String(productName || "منتج غير محدد");
    const message = `مرحباً، أريد الاستفسار عن سعر منتج: ${safeName}`;
    const phoneNumber = contactInfo.whatsapp; // رقم الواتساب من API
    const cleanPhoneNumber = phoneNumber.replace(/[^0-9]/g, ""); // إزالة الرموز
    const url = `whatsapp://send?phone=${cleanPhoneNumber}&text=${encodeURIComponent(
      message
    )}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // فتح واتساب ويب كبديل
          const webUrl = `https://wa.me/${cleanPhoneNumber}?text=${encodeURIComponent(
            message
          )}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => console.error("خطأ في فتح واتساب:", err));
  };

  const renderProduct = ({ item }: { item: Product }) => {
    // التحقق من صحة البيانات
    if (!item || typeof item !== "object" || !item.id) {
      return null;
    }

    // استخدام البيانات العربية مباشرة من API
    const productName = item.name || "منتج غير محدد";
    const productPrice = Number(item.price) || 0;
    const productOldPrice = item.old_price ? Number(item.old_price) : undefined;
    const showPrice = item.show_price !== false;

    const isFavorite = favorites.includes(item.id);
    const discount =
      item.discount_percentage ||
      calculateDiscount(productPrice, productOldPrice);

    return (
      <TouchableOpacity
        style={[styles.productItem, { width: itemWidth }]}
        onPress={() => onProductPress(item.id)}
        activeOpacity={0.8}
      >
        <ThemedCard style={styles.productCard}>
          {/* صورة المنتج */}
          <View style={styles.imageContainer}>
            {item.image ? (
              <Image source={{ uri: item.image }} style={styles.productImage} />
            ) : (
              <View
                style={[
                  styles.placeholderImage,
                  { backgroundColor: colors.border },
                ]}
              >
                <MaterialIcons
                  name="diamond"
                  size={40}
                  color={colors.textSecondary}
                />
              </View>
            )}

            {/* علامة الخصم */}
            {discount && (
              <View
                style={[
                  styles.discountBadge,
                  { backgroundColor: colors.accent },
                ]}
              >
                <ThemedText variant="inverse" size="xs" weight="bold">
                  {`-${discount}%`}
                </ThemedText>
              </View>
            )}

            {/* زر المفضلة */}
            {showFavorites && onFavoritePress && (
              <TouchableOpacity
                style={[
                  styles.favoriteButton,
                  { backgroundColor: colors.surface },
                ]}
                onPress={() => onFavoritePress(item.id)}
                activeOpacity={0.7}
              >
                <MaterialIcons
                  name={isFavorite ? "favorite" : "favorite-border"}
                  size={20}
                  color={isFavorite ? colors.accent : colors.textSecondary}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* معلومات المنتج */}
          <View style={styles.productInfo}>
            <ThemedText
              variant="primary"
              size="sm"
              weight="regular"
              numberOfLines={2}
              style={styles.productName}
            >
              {productName}
            </ThemedText>

            {/* السعر أو زر تواصل للسعر */}
            <View style={styles.priceContainer}>
              {showPrice ? (
                <>
                  <ThemedText variant="primary" size="md" weight="bold">
                    {formatPrice(productPrice)}
                  </ThemedText>
                  {productOldPrice && productOldPrice > productPrice && (
                    <ThemedText
                      variant="secondary"
                      size="sm"
                      style={styles.oldPrice}
                    >
                      {formatPrice(productOldPrice)}
                    </ThemedText>
                  )}
                </>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.contactButton,
                    { backgroundColor: colors.accent },
                  ]}
                  onPress={() => openWhatsApp(productName)}
                  activeOpacity={0.8}
                >
                  <MaterialIcons
                    name="chat"
                    size={16}
                    color={colors.textInverse}
                  />
                  <ThemedText
                    variant="inverse"
                    size="sm"
                    weight="bold"
                    style={styles.contactButtonText}
                  >
                    تواصل للسعر
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>

            {/* معلومات المعدن والعيار - البيانات الجديدة من API */}
            {(item.metal?.name_ar || item.metal_purity_info?.name_ar) && (
              <ThemedText variant="secondary" size="xs" numberOfLines={1}>
                {(() => {
                  const metalName = item.metal?.name_ar?.trim();
                  const purityName = item.metal_purity_info?.name_ar?.trim();

                  if (metalName && purityName) {
                    return `${metalName} - ${purityName}`;
                  } else if (metalName) {
                    return metalName;
                  } else if (purityName) {
                    return purityName;
                  }
                  return "";
                })()}
              </ThemedText>
            )}
          </View>
        </ThemedCard>
      </TouchableOpacity>
    );
  };

  return (
    <FlatList
      data={cleanProducts}
      renderItem={renderProduct}
      keyExtractor={(item) => String(item?.id || Math.random())}
      numColumns={numColumns}
      contentContainerStyle={styles.container}
      columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
      showsVerticalScrollIndicator={false}
      scrollEnabled={false}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: SPACING.md,
  },
  row: {
    justifyContent: "space-between",
  },
  productItem: {
    marginBottom: SPACING.md,
  },
  productCard: {
    padding: 0,
    overflow: "hidden",
  },
  imageContainer: {
    position: "relative",
    width: "100%",
    height: itemWidth * 0.8,
  },
  productImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  discountBadge: {
    position: "absolute",
    top: SPACING.sm,
    right: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
  favoriteButton: {
    position: "absolute",
    bottom: SPACING.sm,
    left: SPACING.sm,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  productInfo: {
    padding: SPACING.sm,
  },
  productName: {
    marginBottom: SPACING.xs,
    textAlign: "right",
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginBottom: SPACING.xs,
    gap: SPACING.xs,
  },
  oldPrice: {
    textDecorationLine: "line-through",
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
    gap: SPACING.xs,
    marginTop: SPACING.xs,
  },
  contactButtonText: {
    fontSize: 12,
  },
});

export default ProductGrid;
