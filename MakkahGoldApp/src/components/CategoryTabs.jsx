// تبويبات الفئات - Category Tabs Component
import React from "react";
import { View, ScrollView, TouchableOpacity, StyleSheet, I18nManager } from "react-native";
import { useTheme } from "../contexts/ThemeContext";
import { FONTS, SPACING } from "../constants";
import ThemedText from "./ThemedText";

const CategoryTabs = ({
  categories,
  activeCategory,
  onCategoryPress,
  showCounts = false,
}) => {
  const { colors } = useTheme();

  // إضافة تبويب "الكل" في البداية
  const allCategories = [{ id: "all", name: "الكل", count: 0 }, ...categories];

  const renderTab = (category) => {
    const isActive = category.id === activeCategory;

    return (
      <TouchableOpacity
        key={category.id}
        style={[
          styles.tab,
          {
            backgroundColor: isActive ? colors.primary : colors.surface,
            borderColor: isActive ? colors.primary : colors.border,
          },
        ]}
        onPress={() => onCategoryPress(category.id)}
        activeOpacity={0.7}
      >
        <ThemedText
          variant={isActive ? "inverse" : "primary"}
          size="sm"
          weight="regular"
          style={styles.tabText}
        >
          {category.name}
        </ThemedText>
        {showCounts && category.count !== undefined && category.count > 0 && (
          <View
            style={[
              styles.countBadge,
              {
                backgroundColor: isActive ? colors.surface : colors.primary,
              },
            ]}
          >
            <ThemedText
              variant={isActive ? "primary" : "inverse"}
              size="xs"
              weight="regular"
            >
              {category.count}
            </ThemedText>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        style={styles.scrollView}
      >
        {allCategories.map(renderTab)}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: SPACING.sm,
  },
  scrollView: {
    flexGrow: 0,
  },
  scrollContent: {
    paddingHorizontal: SPACING.md,
    gap: SPACING.sm,
  },
  tab: {
    flexDirection: I18nManager.isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 20,
    borderWidth: 1,
    minHeight: 36,
    gap: SPACING.xs,
  },
  tabText: {
    textAlign: "center",
  },
  countBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: SPACING.xs,
  },
});

export default CategoryTabs;
