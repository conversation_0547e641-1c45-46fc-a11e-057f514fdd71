// اختبار الخطوط على Android - Font Testing Component
import React from "react";
import { View, Text, StyleSheet, Platform } from "react-native";
import { getFontFamily, getSafeFontFamily } from "../utils/fontHelper";

const FontTest = () => {
  const testFonts = [
    { label: "Cairo-Regular (Android)", fontFamily: "Cairo-Regular" },
    { label: "Cairo-Bold (Android)", fontFamily: "Cairo-Bold" },
    { label: "Cairo_400Regular (iOS)", fontFamily: "Cairo_400Regular" },
    { label: "Cairo_700Bold (iOS)", fontFamily: "Cairo_700Bold" },
    { label: "getFontFamily regular", fontFamily: getFontFamily("regular") },
    { label: "getFontFamily bold", fontFamily: getFontFamily("bold") },
    {
      label: "getSafeFontFamily regular",
      fontFamily: getSafeFontFamily("regular"),
    },
    { label: "getSafeFontFamily bold", fontFamily: getSafeFontFamily("bold") },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>اختبار الخطوط على {Platform.OS}</Text>
      {testFonts.map((font, index) => (
        <Text
          key={index}
          style={[styles.testText, { fontFamily: font.fontFamily }]}
        >
          {font.label}: مرحباً بكم في مجوهرات مكة
        </Text>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: "#fff",
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  testText: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: "right",
    backgroundColor: "#f0f0f0",
    padding: 8,
    borderRadius: 4,
  },
});

export default FontTest;
