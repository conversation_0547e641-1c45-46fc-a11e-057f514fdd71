/**
 * زر تبويب مخصص مع تأثيرات جميلة - Custom Tab Button with Beautiful Effects
 * يدعم الخطوط العربية والتصميم المتجاوب
 */

import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Platform,
  StyleSheet,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { FONTS } from "../constants";
import { getSafeFontFamily } from "../utils/fontHelper";
import { useTheme } from "../contexts/ThemeContext";
import {
  responsiveFontSize,
  responsiveSpacing,
  getIconSize,
  isSmallScreen,
  RESPONSIVE_SPACING,
} from "../utils/responsive";
import RTLManager from "../utils/RTLManager";

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

const CustomTabButton = ({
  iconName,
  label,
  focused,
  onPress,
  index,
  totalTabs = 5,
}) => {
  const { colors } = useTheme();
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const opacityValue = React.useRef(new Animated.Value(0.7)).current;
  const translateY = React.useRef(new Animated.Value(0)).current;

  // تأثيرات الضغط والتركيز
  React.useEffect(() => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: focused ? 1.1 : 1,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: focused ? 1 : 0.7,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(translateY, {
        toValue: focused ? -2 : 0,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
    ]).start();
  }, [focused]);

  const handlePressIn = () => {
    Animated.spring(scaleValue, {
      toValue: 0.95,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleValue, {
      toValue: focused ? 1.1 : 1,
      tension: 300,
      friction: 10,
      useNativeDriver: true,
    }).start();
  };

  // حساب عرض الزر بناءً على عدد التبويبات
  const buttonWidth = `${100 / totalTabs}%`;

  // حجم الأيقونة والخط المتجاوب
  const iconSize = getIconSize(isSmallScreen() ? "md" : "lg");
  const fontSize = responsiveFontSize(isSmallScreen() ? 10 : 12);

  return (
    <AnimatedTouchableOpacity
      style={[
        styles.tabButton,
        {
          width: buttonWidth,
          transform: [{ scale: scaleValue }, { translateY: translateY }],
          opacity: opacityValue,
        },
      ]}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.8}
    >
      {/* خلفية متدرجة للتبويب النشط */}
      {focused && (
        <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.activeBackground}
        />
      )}

      {/* حاوي الأيقونة مع تأثير الهالة */}
      <View
        style={[
          styles.iconContainer,
          focused && {
            backgroundColor: colors.surface,
            ...styles.iconContainerActive,
          },
        ]}
      >
        <MaterialIcons
          name={iconName}
          size={iconSize}
          color={focused ? colors.primary : colors.textSecondary}
          style={[focused && styles.iconActive]}
        />
      </View>

      {/* النص مع الخط العربي المحسن للعمل على جميع الأنظمة */}
      <Text
        style={[
          styles.tabLabel,
          {
            color: focused ? colors.surface : colors.textSecondary,
            fontSize: fontSize,
            fontFamily: getSafeFontFamily("bold"),
            writingDirection: RTLManager.getTextDirection(),
            textAlign: RTLManager.getTextAlign("center"),
          },
          focused && styles.tabLabelActive,
        ]}
        numberOfLines={1}
        adjustsFontSizeToFit={true}
      >
        {label}
      </Text>

      {/* نقطة مؤشر صغيرة للتبويب النشط */}
      {focused && (
        <View
          style={[styles.activeIndicator, { backgroundColor: colors.surface }]}
        />
      )}
    </AnimatedTouchableOpacity>
  );
};

const styles = StyleSheet.create({
  tabButton: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: RESPONSIVE_SPACING.sm,
    paddingHorizontal: RESPONSIVE_SPACING.xs,
    borderRadius: responsiveSpacing(12),
    marginHorizontal: responsiveSpacing(2),
    position: "relative",
    minHeight: responsiveSpacing(60),
  },
  activeBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: responsiveSpacing(12),
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  iconContainer: {
    marginBottom: RESPONSIVE_SPACING.xs,
    padding: responsiveSpacing(4),
    borderRadius: responsiveSpacing(8),
    minWidth: responsiveSpacing(32),
    minHeight: responsiveSpacing(32),
    alignItems: "center",
    justifyContent: "center",
  },
  iconContainerActive: {
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
  },
  iconActive: {
    textShadowColor: "rgba(0,0,0,0.1)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  tabLabel: {
    marginTop: responsiveSpacing(2),
    lineHeight: responsiveFontSize(14),
    letterSpacing: 0.5,
  },
  tabLabelActive: {
    fontWeight: "bold",
    textShadowColor: "rgba(0,0,0,0.1)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },
  activeIndicator: {
    position: "absolute",
    bottom: responsiveSpacing(4),
    width: responsiveSpacing(6),
    height: responsiveSpacing(6),
    borderRadius: responsiveSpacing(3),
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
});

export default CustomTabButton;
