/**
 * مكون تأثيرات بصرية إضافية للـ splash screen
 * Additional Visual Effects Component for Splash Screen
 */

import React, { useEffect, useRef } from "react";
import { View, Animated, StyleSheet, Dimensions } from "react-native";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

// مكون النجوم المتحركة
export const AnimatedStars = () => {
  const stars = useRef(
    Array.from({ length: 50 }, (_, i) => ({
      id: i,
      opacity: new Animated.Value(0),
      translateY: new Animated.Value(Math.random() * SCREEN_HEIGHT),
      translateX: new Animated.Value(Math.random() * SCREEN_WIDTH),
      scale: new Animated.Value(0),
    }))
  ).current;

  useEffect(() => {
    const animateStars = () => {
      stars.forEach((star, index) => {
        setTimeout(() => {
          Animated.loop(
            Animated.sequence([
              Animated.parallel([
                Animated.timing(star.opacity, {
                  toValue: 1,
                  duration: 2000,
                  useNativeDriver: true,
                }),
                Animated.timing(star.scale, {
                  toValue: 1,
                  duration: 2000,
                  useNativeDriver: true,
                }),
              ]),
              Animated.parallel([
                Animated.timing(star.opacity, {
                  toValue: 0,
                  duration: 2000,
                  useNativeDriver: true,
                }),
                Animated.timing(star.scale, {
                  toValue: 0,
                  duration: 2000,
                  useNativeDriver: true,
                }),
              ]),
            ])
          ).start();
        }, index * 100);
      });
    };

    animateStars();
  }, []);

  return (
    <View style={styles.starsContainer}>
      {stars.map((star) => (
        <Animated.View
          key={star.id}
          style={[
            styles.star,
            {
              opacity: star.opacity,
              transform: [
                { translateX: star.translateX },
                { translateY: star.translateY },
                { scale: star.scale },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// مكون الموجات المتحركة
export const AnimatedWaves = () => {
  const wave1 = useRef(new Animated.Value(0)).current;
  const wave2 = useRef(new Animated.Value(0)).current;
  const wave3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateWaves = () => {
      Animated.loop(
        Animated.stagger(500, [
          Animated.timing(wave1, {
            toValue: 1,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(wave2, {
            toValue: 1,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(wave3, {
            toValue: 1,
            duration: 3000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    animateWaves();
  }, []);

  const getWaveScale = (wave) =>
    wave.interpolate({
      inputRange: [0, 1],
      outputRange: [0, 2],
    });

  const getWaveOpacity = (wave) =>
    wave.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0.8, 0.3, 0],
    });

  return (
    <View style={styles.wavesContainer}>
      <Animated.View
        style={[
          styles.wave,
          {
            transform: [{ scale: getWaveScale(wave1) }],
            opacity: getWaveOpacity(wave1),
          },
        ]}
      />
      <Animated.View
        style={[
          styles.wave,
          {
            transform: [{ scale: getWaveScale(wave2) }],
            opacity: getWaveOpacity(wave2),
          },
        ]}
      />
      <Animated.View
        style={[
          styles.wave,
          {
            transform: [{ scale: getWaveScale(wave3) }],
            opacity: getWaveOpacity(wave3),
          },
        ]}
      />
    </View>
  );
};

// مكون الجسيمات المتطايرة
export const FloatingParticles = () => {
  const particles = useRef(
    Array.from({ length: 20 }, (_, i) => ({
      id: i,
      translateY: new Animated.Value(SCREEN_HEIGHT + 100),
      translateX: new Animated.Value(Math.random() * SCREEN_WIDTH),
      rotation: new Animated.Value(0),
      opacity: new Animated.Value(0),
    }))
  ).current;

  useEffect(() => {
    const animateParticles = () => {
      particles.forEach((particle, index) => {
        setTimeout(() => {
          Animated.loop(
            Animated.parallel([
              Animated.timing(particle.translateY, {
                toValue: -100,
                duration: 8000 + Math.random() * 2000,
                useNativeDriver: true,
              }),
              Animated.timing(particle.rotation, {
                toValue: 1,
                duration: 8000 + Math.random() * 2000,
                useNativeDriver: true,
              }),
              Animated.sequence([
                Animated.timing(particle.opacity, {
                  toValue: 0.6,
                  duration: 1000,
                  useNativeDriver: true,
                }),
                Animated.timing(particle.opacity, {
                  toValue: 0,
                  duration: 7000,
                  useNativeDriver: true,
                }),
              ]),
            ])
          ).start(() => {
            // إعادة تعيين الموضع
            particle.translateY.setValue(SCREEN_HEIGHT + 100);
            particle.rotation.setValue(0);
            particle.opacity.setValue(0);
          });
        }, index * 300);
      });
    };

    setTimeout(animateParticles, 1000);
  }, []);

  return (
    <View style={styles.particlesContainer}>
      {particles.map((particle) => {
        const rotationInterpolate = particle.rotation.interpolate({
          inputRange: [0, 1],
          outputRange: ["0deg", "360deg"],
        });

        return (
          <Animated.View
            key={particle.id}
            style={[
              styles.particle,
              {
                opacity: particle.opacity,
                transform: [
                  { translateX: particle.translateX },
                  { translateY: particle.translateY },
                  { rotate: rotationInterpolate },
                ],
              },
            ]}
          />
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  starsContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  star: {
    position: "absolute",
    width: 2,
    height: 2,
    backgroundColor: "#FFFFFF",
    borderRadius: 1,
  },
  wavesContainer: {
    position: "absolute",
    top: "50%",
    left: "50%",
    marginTop: -100,
    marginLeft: -100,
  },
  wave: {
    position: "absolute",
    width: 200,
    height: 200,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: "#FFFFFF",
  },
  particlesContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  particle: {
    position: "absolute",
    width: 4,
    height: 4,
    backgroundColor: "#FFFFFF",
    borderRadius: 2,
  },
});

export default {
  AnimatedStars,
  AnimatedWaves,
  FloatingParticles,
};
