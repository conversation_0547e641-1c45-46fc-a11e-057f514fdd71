// شريط البحث المحسن - Fixed Search Bar Component
import React, { useState } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  I18nManager,
  Image,
  Platform,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "../contexts/ThemeContext";
import { useSearch } from "../utils/hooks";
import { FONTS, SPACING } from "../constants";
import { router } from "expo-router";
import ThemedText from "./ThemedText";

const SearchBar = ({
  placeholder = "البحث عن المجوهرات...",
  onFilterPress,
  showFilter = true,
}) => {
  const { colors } = useTheme();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const {
    query,
    setQuery,
    results,
    loading,
    error,
    isSearching,
    clearSearch,
    performSearch,
    hasResults,
    hasQuery,
    isQueryValid,
  } = useSearch();
  const handleSearch = (text) => {
    setQuery(text);

    // إظهار النتائج تلقائياً عند وجود حرفين أو أكثر
    if (text.length >= 2) {
      setIsModalVisible(true);
    } else {
      setIsModalVisible(false);
    }
  };

  const handleProductSelect = (productId) => {
    setIsModalVisible(false);
    clearSearch();
    router.push(`/product/${productId}`);
  };

  const handleSubmit = () => {
    if (isQueryValid) {
      setIsModalVisible(false);
      router.push(`/search?q=${encodeURIComponent(query)}`);
    }
  };

  const renderSearchResult = ({ item }) => (
    <TouchableOpacity
      key={item.id}
      style={[styles.resultItem, { borderBottomColor: colors.border }]}
      onPress={() => handleProductSelect(item.id)}
    >
      <View style={styles.productImageContainer}>
        {item.image ? (
          <Image
            source={{ uri: item.image }}
            style={[styles.productImage, { borderColor: colors.border }]}
            resizeMode="cover"
          />
        ) : (
          <View
            style={[
              styles.placeholderImage,
              { backgroundColor: colors.surface, borderColor: colors.border },
            ]}
          >
            <MaterialIcons name="diamond" size={16} color={colors.primary} />
          </View>
        )}
      </View>
      <View style={styles.resultInfo}>
        <ThemedText align="left" variant="primary" size="sm" weight="regular">
          {item.name}
        </ThemedText>
        <ThemedText align="left" variant="secondary" size="xs">
          {item.show_price && item.price > 0
            ? `${item.price.toLocaleString("en-US")} جنيه`
            : "تواصل للسعر"}
        </ThemedText>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.surface }]}>
      <View style={[styles.searchContainer, { borderColor: colors.border }]}>
        <TouchableOpacity
          onPress={performSearch}
          style={styles.searchIconButton}
          disabled={!isQueryValid}
        >
          <MaterialIcons
            name="search"
            size={20}
            color={isQueryValid ? colors.primary : colors.textSecondary}
            style={styles.searchIcon}
          />
        </TouchableOpacity>
        <TextInput
          style={[
            styles.searchInput,
            {
              color: colors.textPrimary,
              fontFamily: FONTS.families.regular,
            },
          ]}
          placeholder={placeholder}
          placeholderTextColor={colors.textSecondary}
          value={query}
          onChangeText={handleSearch}
          onSubmitEditing={handleSubmit}
          returnKeyType="search"
          textAlign="right"
          autoCorrect={false}
          autoCapitalize="none"
        />
        {query.length > 0 && (
          <TouchableOpacity
            onPress={() => {
              clearSearch();
              setIsModalVisible(false);
            }}
            style={styles.clearButton}
          >
            <MaterialIcons
              name="clear"
              size={20}
              color={colors.textSecondary}
            />
          </TouchableOpacity>
        )}
      </View>

      {showFilter && (
        <TouchableOpacity
          style={[styles.filterButton, { backgroundColor: colors.primary }]}
          onPress={onFilterPress}
        >
          <MaterialIcons name="tune" size={20} color={colors.textInverse} />
        </TouchableOpacity>
      )}

      {/* نتائج البحث كـ dropdown */}
      {isModalVisible && (
        <View style={styles.dropdownContainer}>
          <TouchableOpacity
            style={styles.dropdownOverlay}
            activeOpacity={1}
            onPress={() => setIsModalVisible(false)}
          />
          <View
            style={[
              styles.resultsContainer,
              { backgroundColor: colors.surface },
            ]}
          >
            {loading || isSearching ? (
              <View style={styles.loadingContainer}>
                <ThemedText variant="secondary" size="sm">
                  جاري البحث...
                </ThemedText>
              </View>
            ) : error ? (
              <View style={styles.noResultsContainer}>
                <ThemedText
                  variant="secondary"
                  size="sm"
                  style={{ color: colors.error }}
                >
                  {error}
                </ThemedText>
              </View>
            ) : hasResults ? (
              <ScrollView
                style={styles.resultsList}
                showsVerticalScrollIndicator={false}
                nestedScrollEnabled={true}
              >
                {results.map((item) => renderSearchResult({ item }))}
              </ScrollView>
            ) : isQueryValid ? (
              <View style={styles.noResultsContainer}>
                <ThemedText variant="secondary" size="sm">
                  لا توجد نتائج للبحث "{query}"
                </ThemedText>
              </View>
            ) : hasQuery ? (
              <View style={styles.noResultsContainer}>
                <ThemedText variant="secondary" size="sm">
                  اكتب حرفين على الأقل للبحث
                </ThemedText>
              </View>
            ) : null}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    gap: SPACING.sm,
  },
  searchContainer: {
    flex: 1,
    flexDirection: I18nManager.isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: SPACING.md,
    paddingVertical: Platform.OS === "android" ? 1 : SPACING.sm, // تقليل الـ padding على أندرويد
    minHeight: Platform.OS === "android" ? 30 : 48, // تقليل الارتفاع الأدنى على أندرويد
  },
  searchIconButton: {
    padding: SPACING.xs,
    marginLeft: I18nManager.isRTL ? 0 : SPACING.xs, // RTL support
    marginRight: I18nManager.isRTL ? SPACING.xs : 0, // RTL support
    borderRadius: 20,
  },
  searchIcon: {
    marginHorizontal: SPACING.xs,
  },
  searchInput: {
    flex: 1,
    fontSize: FONTS.sizes.md,
    textAlign: I18nManager.isRTL ? "right" : "left", // RTL support
  },
  clearButton: {
    padding: SPACING.xs,
  },
  filterButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: "center",
    alignItems: "center",
  },
  dropdownContainer: {
    position: "absolute",
    top: "100%",
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  dropdownOverlay: {
    position: "absolute",
    top: 0,
    left: -1000,
    right: -1000,
    bottom: -1000,
    zIndex: -1,
  },
  resultsContainer: {
    borderRadius: 12,
    maxHeight: 300,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    marginTop: SPACING.xs,
    marginHorizontal: SPACING.md,
  },
  resultsList: {
    maxHeight: 250,
  },
  resultItem: {
    flexDirection: "row-reverse",
    alignItems: "center",
    padding: SPACING.md,
    borderBottomWidth: 1,
  },
  productImageContainer: {
    marginLeft: SPACING.sm,
  },
  productImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
  },
  placeholderImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    borderWidth: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  resultInfo: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  loadingContainer: {
    padding: SPACING.lg,
    alignItems: "center",
  },
  noResultsContainer: {
    padding: SPACING.lg,
    alignItems: "center",
  },
});

export default SearchBar;
