// مكون تبديل الوضع الليلي - Theme Toggle Component
import React from "react";
import { TouchableOpacity, StyleSheet, Animated } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "../contexts/ThemeContext";
import { SPACING } from "../constants";

interface ThemeToggleProps {
  size?: number;
  style?: any;
}

const ThemeToggle = ({ size = 24, style }) => {
  const { colors, isDarkMode, toggleTheme } = useTheme();

  const handleToggle = async () => {
    await toggleTheme();
  };

  return (
    <TouchableOpacity
      style={[styles.container, { backgroundColor: colors.surface }, style]}
      onPress={handleToggle}
      activeOpacity={0.7}
    >
      <MaterialIcons
        name={isDarkMode ? "light-mode" : "dark-mode"}
        size={size}
        color={colors.primary}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: SPACING.sm,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
});

export default ThemeToggle;
