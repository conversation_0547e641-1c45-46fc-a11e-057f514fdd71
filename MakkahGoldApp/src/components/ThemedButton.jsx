// زر متكيف مع الثيم - Themed Button Component
import React from "react";
import {
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  View,
} from "react-native";
import { useTheme } from "../contexts/ThemeContext";
import { SPACING, FONTS } from "../constants";
import ThemedText from "./ThemedText";

const ThemedButton = ({
  title,
  onPress,
  disabled = false,
  loading = false,
  variant = "primary", // primary, secondary, outline, danger
  size = "md", // sm, md, lg
  icon,
  iconPosition = "left", // left, right
  style,
  textStyle,
  fullWidth = false,
  ...props
}) => {
  const { colors } = useTheme();

  // تحديد ألوان الزر بناءً على النوع
  const getButtonColors = () => {
    switch (variant) {
      case "primary":
        return {
          backgroundColor: disabled ? colors.border : colors.primary,
          textColor: colors.surface,
          borderColor: disabled ? colors.border : colors.primary,
        };
      case "secondary":
        return {
          backgroundColor: disabled ? colors.border : colors.secondary,
          textColor: colors.surface,
          borderColor: disabled ? colors.border : colors.secondary,
        };
      case "outline":
        return {
          backgroundColor: "transparent",
          textColor: disabled ? colors.textSecondary : colors.primary,
          borderColor: disabled ? colors.border : colors.primary,
        };
      case "danger":
        return {
          backgroundColor: disabled ? colors.border : colors.error,
          textColor: colors.surface,
          borderColor: disabled ? colors.border : colors.error,
        };
      default:
        return {
          backgroundColor: disabled ? colors.border : colors.primary,
          textColor: colors.surface,
          borderColor: disabled ? colors.border : colors.primary,
        };
    }
  };

  // تحديد أحجام الزر
  const getButtonSize = () => {
    switch (size) {
      case "sm":
        return {
          paddingVertical: SPACING.sm,
          paddingHorizontal: SPACING.md,
          borderRadius: 8,
          fontSize: FONTS.sizes.sm,
        };
      case "lg":
        return {
          paddingVertical: SPACING.lg,
          paddingHorizontal: SPACING.xl,
          borderRadius: 12,
          fontSize: FONTS.sizes.lg,
        };
      default: // md
        return {
          paddingVertical: SPACING.md,
          paddingHorizontal: SPACING.lg,
          borderRadius: 10,
          fontSize: FONTS.sizes.md,
        };
    }
  };

  const buttonColors = getButtonColors();
  const buttonSize = getButtonSize();

  const buttonStyle = [
    styles.button,
    {
      backgroundColor: buttonColors.backgroundColor,
      borderColor: buttonColors.borderColor,
      paddingVertical: buttonSize.paddingVertical,
      paddingHorizontal: buttonSize.paddingHorizontal,
      borderRadius: buttonSize.borderRadius,
      opacity: disabled ? 0.6 : 1,
      width: fullWidth ? "100%" : "auto",
    },
    style,
  ];

  const renderContent = () => {
    if (loading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="small"
            color={buttonColors.textColor}
            style={{ marginLeft: 8 }}
          />
          <ThemedText
            style={[
              {
                color: buttonColors.textColor,
                fontSize: buttonSize.fontSize,
                fontFamily: FONTS.families.bold,
              },
              textStyle,
            ]}
          >
            {title}
          </ThemedText>
        </View>
      );
    }

    return (
      <View style={styles.contentContainer}>
        {icon && iconPosition === "left" && (
          <View style={styles.iconLeft}>{icon}</View>
        )}
        
        <ThemedText
          style={[
            {
              color: buttonColors.textColor,
              fontSize: buttonSize.fontSize,
              fontFamily: FONTS.families.bold,
            },
            textStyle,
          ]}
        >
          {title}
        </ThemedText>
        
        {icon && iconPosition === "right" && (
          <View style={styles.iconRight}>{icon}</View>
        )}
      </View>
    );
  };

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  contentContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  loadingContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  iconLeft: {
    marginRight: SPACING.sm,
  },
  iconRight: {
    marginLeft: SPACING.sm,
  },
});

export default ThemedButton;
