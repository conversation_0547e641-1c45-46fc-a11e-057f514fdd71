/**
 * شاشة البداية المخصصة مع الخطوط العربية والتأثيرات
 * Custom Splash Screen with Arabic Fonts and Animations
 */

import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  Animated,
  Dimensions,
  Image,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { LinearGradient } from "expo-linear-gradient";
import {
  useFonts,
  Cairo_400Regular,
  Cairo_700Bold,
} from "@expo-google-fonts/cairo";
import { COLORS } from "../constants";
import {
  responsiveFontSize,
  RESPONSIVE_SPACING,
  wp,
  hp,
} from "../utils/responsive";
import {
  AnimatedStars,
  AnimatedWaves,
  FloatingParticles,
} from "./SplashEffects";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

const AnimatedSplashScreen = ({ onFinish, isAppReady = false }) => {
  // تحميل الخطوط مع أسماء متعددة لضمان التوافق
  const [fontsLoaded] = useFonts({
    // أسماء لـ iOS
    Cairo_400Regular: Cairo_400Regular,
    Cairo_700Bold: Cairo_700Bold,
    // أسماء لـ Android
    "Cairo-Regular": Cairo_400Regular,
    "Cairo-Bold": Cairo_700Bold,
    // أسماء بديلة للتأكد
    CairoRegular: Cairo_400Regular,
    CairoBold: Cairo_700Bold,
  });

  // متغيرات الحركة
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoRotation = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const titleTranslateY = useRef(new Animated.Value(50)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleTranslateY = useRef(new Animated.Value(30)).current;
  const shimmerOpacity = useRef(new Animated.Value(0)).current;
  const backgroundScale = useRef(new Animated.Value(1.2)).current;

  useEffect(() => {
    if (fontsLoaded) {
      startAnimations();
    }
  }, [fontsLoaded]);

  useEffect(() => {
    // إنهاء شاشة البداية عندما يكون التطبيق جاهزاً
    if (isAppReady && fontsLoaded) {
      const timer = setTimeout(() => {
        if (onFinish) {
          onFinish();
        }
      }, 3000); // 3 ثواني كحد أدنى لعرض شاشة البداية

      return () => clearTimeout(timer);
    }
  }, [isAppReady, fontsLoaded, onFinish]);

  const startAnimations = () => {
    // تأثير تدرج الخلفية
    Animated.timing(backgroundScale, {
      toValue: 1,
      duration: 2000,
      useNativeDriver: true,
    }).start();

    // حركة ظهور وتدوير الشعار
    Animated.parallel([
      Animated.spring(logoScale, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
      Animated.timing(logoRotation, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: true,
      }),
    ]).start();

    // حركة ظهور العنوان الرئيسي
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(titleTranslateY, {
          toValue: 0,
          tension: 80,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    }, 500);

    // حركة ظهور العنوان الفرعي
    setTimeout(() => {
      Animated.parallel([
        Animated.timing(subtitleOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(subtitleTranslateY, {
          toValue: 0,
          tension: 80,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    }, 1000);

    // تأثير اللمعان
    setTimeout(() => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerOpacity, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerOpacity, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }, 1500);

    // إزالة المهلة الزمنية الثابتة - ستكون معتمدة على useEffect
  };

  // تحديد دوران الشعار
  const logoRotationInterpolate = logoRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  if (!fontsLoaded) {
    return null;
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" translucent />

      {/* خلفية متدرجة متحركة */}
      <Animated.View
        style={[
          styles.backgroundContainer,
          {
            transform: [{ scale: backgroundScale }],
          },
        ]}
      >
        <LinearGradient
          colors={["#000000", "#1a1a1a", "#000000", "#000000"]}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      {/* طبقة اللمعان */}
      <Animated.View
        style={[
          styles.shimmerOverlay,
          {
            opacity: shimmerOpacity,
          },
        ]}
      >
        <LinearGradient
          colors={["transparent", "rgba(255, 255, 255, 0.3)", "transparent"]}
          style={styles.shimmerGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      {/* تأثيرات بصرية إضافية */}
      <AnimatedStars />
      <FloatingParticles />
      <AnimatedWaves />

      {/* المحتوى الرئيسي */}
      <View style={styles.content}>
        {/* الشعار المتحرك */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              transform: [
                { scale: logoScale },
                { rotate: logoRotationInterpolate },
              ],
            },
          ]}
        >
          <Image
            source={require("../../assets/splashscreen_logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </Animated.View>

        {/* العنوان الرئيسي */}
        <Animated.View
          style={[
            styles.titleContainer,
            {
              opacity: titleOpacity,
              transform: [{ translateY: titleTranslateY }],
            },
          ]}
        >
          <Text style={styles.mainTitle}>مجوهرات مكة جولد جروب</Text>
        </Animated.View>

        {/* العنوان الفرعي */}
        <Animated.View
          style={[
            styles.subtitleContainer,
            {
              opacity: subtitleOpacity,
              transform: [{ translateY: subtitleTranslateY }],
            },
          ]}
        >
          <Text style={styles.subtitle}>
            شركة رائدة في مجال المجوهرات والمعادن الثمينة
          </Text>
          <View style={styles.versionContainer}>
            {/* <Text style={styles.version}>الإصدار 2.0.0</Text> */}
            <Text style={styles.buildInfo}>Built with ❤️ in Egypt</Text>
          </View>
        </Animated.View>

        {/* نقاط التحميل المتحركة */}
        <View style={styles.loadingContainer}>
          <LoadingDots />
        </View>
      </View>
    </View>
  );
};

// مكون نقاط التحميل المتحركة
const LoadingDots = () => {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animateDots = () => {
      Animated.loop(
        Animated.stagger(200, [
          Animated.sequence([
            Animated.timing(dot1, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot1, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
          Animated.sequence([
            Animated.timing(dot2, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot2, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
          Animated.sequence([
            Animated.timing(dot3, {
              toValue: 1,
              duration: 400,
              useNativeDriver: true,
            }),
            Animated.timing(dot3, {
              toValue: 0,
              duration: 400,
              useNativeDriver: true,
            }),
          ]),
        ])
      ).start();
    };

    setTimeout(animateDots, 2000);
  }, []);

  return (
    <View style={styles.dotsContainer}>
      <Animated.View style={[styles.dot, { opacity: dot1 }]} />
      <Animated.View style={[styles.dot, { opacity: dot2 }]} />
      <Animated.View style={[styles.dot, { opacity: dot3 }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  backgroundContainer: {
    position: "absolute",
    top: -SCREEN_HEIGHT * 0.1,
    left: -SCREEN_WIDTH * 0.1,
    right: -SCREEN_WIDTH * 0.1,
    bottom: -SCREEN_HEIGHT * 0.1,
  },
  gradient: {
    flex: 1,
  },
  shimmerOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  shimmerGradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: RESPONSIVE_SPACING.xl,
  },
  logoContainer: {
    marginBottom: hp(5),
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  logo: {
    width: wp(35),
    height: wp(35),
  },
  titleContainer: {
    marginBottom: hp(2),
    alignItems: "center",
  },
  mainTitle: {
    fontSize: responsiveFontSize(28),
    fontFamily: "Cairo_700Bold",
    color: "#FFFFFF",
    textAlign: "center",
    textShadowColor: "rgba(0, 0, 0, 0.5)",
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 1,
  },
  subtitleContainer: {
    alignItems: "center",
    marginBottom: hp(5),
  },
  subtitle: {
    fontSize: responsiveFontSize(16),
    fontFamily: "Cairo_400Regular",
    color: "#FFFFFF",
    textAlign: "center",
    opacity: 0.9,
    lineHeight: responsiveFontSize(24),
    marginBottom: hp(1),
  },
  version: {
    fontSize: responsiveFontSize(14),
    fontFamily: "Cairo_400Regular",
    color: "#FFFFFF",
    opacity: 0.7,
  },
  versionContainer: {
    alignItems: "center",
    marginTop: hp(1),
  },
  buildInfo: {
    fontSize: responsiveFontSize(12),
    fontFamily: "Cairo_400Regular",
    color: "#FFFFFF",
    opacity: 0.6,
    marginTop: hp(0.5),
  },
  loadingContainer: {
    position: "absolute",
    bottom: hp(8),
  },
  dotsContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#FFFFFF",
    marginHorizontal: 4,
  },
});

export default AnimatedSplashScreen;
