// نص متكيف مع الثيم والشاشات المختلفة - Responsive Themed Text Component
import React from "react";
import { Text, I18nManager } from "react-native";
import { useTheme } from "../contexts/ThemeContext";
import { FONTS } from "../constants";
import RTLManager from "../utils/RTLManager";
import { RESPONSIVE_FONTS, responsiveFontSize } from "../utils/responsive";

const ThemedText = ({
  children,
  style,
  variant = "primary",
  size = "md",
  weight = "regular",
  align = "auto", // افتراضي تلقائي حسب RTL
  numberOfLines,
  onPress,
  responsive = true, // تفعيل التصميم المتجاوب
}) => {
  const { colors } = useTheme();

  // تحديد اتجاه النص تلقائياً حسب RTL مع دعم أقوى للـ Expo Go
  const getTextAlign = () => {
    if (align === "center") return "center";
    if (align === "left" || align === "right") return align;

    // استخدام RTLManager للتوافق مع Expo Go
    return RTLManager.getTextAlign("auto");
  };

  // تحديد لون النص بناءً على النوع
  const getTextColor = () => {
    switch (variant) {
      case "primary":
        return colors.textPrimary;
      case "secondary":
        return colors.textSecondary;
      case "light":
        return colors.textLight;
      case "inverse":
        return colors.textInverse;
      case "surface":
        return colors.surface;
      default:
        return colors.textPrimary;
    }
  };

  // تحديد حجم الخط مع دعم التصميم المتجاوب
  const getFontSize = () => {
    if (responsive) {
      // استخدام الأحجام المتجاوبة
      return RESPONSIVE_FONTS[size] || responsiveFontSize(FONTS.sizes[size]);
    }
    return FONTS.sizes[size];
  };

  const textStyle = [
    {
      color: getTextColor(),
      fontSize: getFontSize(),
      fontFamily:
        weight === "bold" ? FONTS.families.bold : FONTS.families.regular,
      textAlign: getTextAlign(),
      writingDirection: RTLManager.getTextDirection(),
      // إضافة دعم قوي للعربية
      textAlignVertical: "center",
      includeFontPadding: false, // لـ Android
    },
    style,
  ];

  return (
    <Text style={textStyle} numberOfLines={numberOfLines} onPress={onPress}>
      {children}
    </Text>
  );
};

export default ThemedText;
