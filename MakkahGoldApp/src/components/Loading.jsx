// مكون التحميل - Loading Component

import React from "react";
import { View, ActivityIndicator, Text, StyleSheet } from "react-native";
import { COLORS, FONTS, SPACING } from "../constants";

const Loading = ({
  size = "large",
  color = COLORS.primary,
  text = "جاري التحميل...",
}) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size={size} color={color} />
      {text && <Text style={styles.text}>{text}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: SPACING.lg,
  },
  text: {
    marginTop: SPACING.md,
    fontSize: FONTS.sizes.md,
    color: COLORS.textSecondary,
    textAlign: "center",
    fontFamily: "Cairo-Regular",
  },
});

export default Loading;
