/**
 * زر لتفعيل RTL يدوياً في Expo Go
 * Manual RTL Toggle Button for Expo Go
 */

import React, { useState, useEffect } from "react";
import { View, TouchableOpacity, Alert } from "react-native";
import ThemedText from "./ThemedText";
import RTLManager from "../utils/RTLManager";

const ExpoGoRTLButton = ({ style }) => {
  const [isRTLActive, setIsRTLActive] = useState(false);

  useEffect(() => {
    setIsRTLActive(RTLManager.isRTLActive());
  }, []);

  const toggleRTL = () => {
    if (!RTLManager.isExpoGo) {
      Alert.alert(
        "معلومة",
        "هذا الزر مخصص لـ Expo Go فقط. التطبيق المبني يدعم RTL تلقائياً.",
        [{ text: "حسناً" }]
      );
      return;
    }

    if (isRTLActive) {
      RTLManager.disableManualRTL();
      setIsRTLActive(false);
      Alert.alert("RTL", "تم إلغاء RTL. أعد تشغيل التطبيق لرؤية التأثير.", [
        { text: "حسناً" },
      ]);
    } else {
      RTLManager.enableManualRTL();
      setIsRTLActive(true);
      Alert.alert("RTL", "تم تفعيل RTL. أعد تشغيل التطبيق لرؤية التأثير.", [
        { text: "حسناً" },
      ]);
    }
  };

  // إخفاء الزر إذا لم يكن في Expo Go
  if (!RTLManager.isExpoGo) {
    return null;
  }

  return (
    <View style={[{ padding: 10 }, style]}>
      <TouchableOpacity
        onPress={toggleRTL}
        style={{
          backgroundColor: isRTLActive ? "#4CAF50" : "#FF9800",
          paddingHorizontal: 20,
          paddingVertical: 10,
          borderRadius: 8,
          alignItems: "center",
        }}
      >
        <ThemedText style={{ color: "white", fontWeight: "bold" }} size="sm">
          {isRTLActive ? "✅ RTL نشط" : "🔄 تفعيل RTL"}
        </ThemedText>
        <ThemedText style={{ color: "white", fontSize: 10, marginTop: 2 }}>
          (Expo Go فقط)
        </ThemedText>
      </TouchableOpacity>
    </View>
  );
};

export default ExpoGoRTLButton;
