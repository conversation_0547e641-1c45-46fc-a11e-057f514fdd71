// البانر الإعلاني - Promo Banner Component
import React, { useRef, useEffect, useState } from "react";
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
  ImageBackground,
  I18nManager,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useTheme } from "../contexts/ThemeContext";
import { FONTS, SPACING } from "../constants";
import { useResponsive } from "../utils/hooks";
import ThemedText from "./ThemedText";

const { width } = Dimensions.get("window");

const PromoBanner = ({
  sliders,
  onBannerPress,
  autoPlay = true,
  autoPlayInterval = 4000,
}) => {
  const { colors, isDarkMode } = useTheme();
  const { wp, hp } = useResponsive();
  const flatListRef = useRef(null);
  const [activeIndex, setActiveIndex] = useState(0);

  // Auto-scroll للبانر
  useEffect(() => {
    if (autoPlay && sliders.length > 1) {
      const interval = setInterval(() => {
        setActiveIndex((prevIndex) => {
          const nextIndex = (prevIndex + 1) % sliders.length;
          flatListRef.current?.scrollToIndex({
            index: nextIndex,
            animated: true,
          });
          return nextIndex;
        });
      }, autoPlayInterval);

      return () => clearInterval(interval);
    }
  }, [autoPlay, sliders.length, autoPlayInterval]);

  const handleScroll = (event) => {
    const slideIndex = Math.round(event.nativeEvent.contentOffset.x / width);
    setActiveIndex(slideIndex);
  };

  const renderSlider = ({ item }) => (
    <TouchableOpacity
      style={styles.bannerContainer}
      onPress={() => onBannerPress?.(item)}
      activeOpacity={0.9}
    >
      {item.image ? (
        <ImageBackground
          source={{ uri: item.image }}
          style={styles.bannerImage}
          imageStyle={styles.bannerImageStyle}
        >
          {/* تدرج للنص */}
          <LinearGradient
            colors={
              isDarkMode
                ? ["rgba(0,0,0,0.1)", "rgba(0,0,0,0.7)"]
                : ["rgba(0,0,0,0.1)", "rgba(0,0,0,0.6)"]
            }
            style={styles.bannerOverlay}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          >
            <View style={styles.bannerContent}>
              {/* العنوان */}
              <ThemedText
                variant="inverse"
                size="xl"
                weight="bold"
                style={styles.bannerTitle}
                numberOfLines={2}
              >
                {item.title}
              </ThemedText>

              {/* الوصف */}
              {item.description && (
                <ThemedText
                  variant="inverse"
                  size="md"
                  weight="regular"
                  style={styles.bannerDescription}
                  numberOfLines={3}
                >
                  {item.description}
                </ThemedText>
              )}

              {/* زر العمل */}
              {/* {item.button_text && (
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    { backgroundColor: colors.accent },
                  ]}
                  onPress={() => onBannerPress?.(item)}
                >
                  <ThemedText
                    variant="inverse"
                    size="sm"
                    weight="bold"
                    style={styles.buttonText}
                  >
                    {item.button_text}
                  </ThemedText>
                </TouchableOpacity>
              )} */}
            </View>
          </LinearGradient>
        </ImageBackground>
      ) : (
        <LinearGradient
          colors={[colors.primary, colors.primaryDark]}
          style={styles.bannerImage}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.bannerContent}>
            {/* العنوان */}
            <ThemedText
              variant="inverse"
              size="xl"
              weight="bold"
              style={styles.bannerTitle}
              numberOfLines={2}
            >
              {item.title}
            </ThemedText>

            {/* الوصف */}
            {item.description && (
              <ThemedText
                variant="inverse"
                size="md"
                weight="regular"
                style={styles.bannerDescription}
                numberOfLines={3}
              >
                {item.description}
              </ThemedText>
            )}

            {/* زر العمل */}
            {item.button_text && (
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  { backgroundColor: colors.accent },
                ]}
                onPress={() => onBannerPress?.(item)}
              >
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.buttonText}
                >
                  {item.button_text}
                </ThemedText>
              </TouchableOpacity>
            )}
          </View>
        </LinearGradient>
      )}
    </TouchableOpacity>
  );

  const renderPaginationDot = (index) => (
    <View
      key={index}
      style={[
        styles.paginationDot,
        {
          backgroundColor:
            index === activeIndex ? colors.accent : colors.textLight,
          width: index === activeIndex ? 24 : 8,
        },
      ]}
    />
  );

  if (!sliders || sliders.length === 0) {
    return null;
  }

  // إنشاء الستايلات باستخدام wp و hp
  const styles = StyleSheet.create({
    container: {
      marginBottom: wp(3),
      marginTop: wp(2),
    },
    bannerContainer: {
      width: width,
      height: hp(22), // استخدام hp بدلاً من قيمة ثابتة
      paddingHorizontal: wp(4),
    },
    bannerImage: {
      flex: 1,
      borderRadius: wp(3),
      overflow: "hidden",
      justifyContent: "center",
      alignItems: "center",
    },
    bannerImageStyle: {
      borderRadius: wp(3),
      resizeMode: "cover",
      width: "100%",
      height: "100%",
    },
    bannerOverlay: {
      flex: 1,
      justifyContent: "flex-end",
      alignItems: I18nManager.isRTL ? "flex-end" : "flex-start", // RTL support
      padding: wp(4),
      backgroundColor: "rgba(0, 0, 0, 0.2)",
    },
    bannerContent: {
      alignItems: I18nManager.isRTL ? "flex-start" : "flex-end", // RTL support
      width: "100%",
    },
    bannerTitle: {
      textAlign: I18nManager.isRTL ? "right" : "left", // RTL support
      marginBottom: hp(0.8),
      textShadowColor: "rgba(0, 0, 0, 0.8)",
      textShadowOffset: { width: 2, height: 2 },
      textShadowRadius: 4,
      color: "white",
      fontSize: wp(4.5),
      fontWeight: "bold",
    },
    bannerDescription: {
      textAlign: I18nManager.isRTL ? "left" : "right", // RTL support
      marginBottom: hp(1),
      opacity: 0.95,
      textShadowColor: "rgba(0, 0, 0, 0.8)",
      textShadowOffset: { width: 2, height: 2 },
      textShadowRadius: 4,
      color: "white",
      fontSize: wp(3.5),
    },
    actionButton: {
      paddingHorizontal: wp(4),
      paddingVertical: hp(0.8),
      borderRadius: wp(5),
      elevation: 2,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
    },
    buttonText: {
      textAlign: "center",
    },
    pagination: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      marginTop: wp(2),
      gap: wp(1),
    },
    paginationDot: {
      height: wp(1.5),
      borderRadius: wp(0.75),
    },
  });

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={sliders}
        renderItem={renderSlider}
        keyExtractor={(item) => item.id.toString()}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={handleScroll}
        getItemLayout={(data, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
      />

      {/* مؤشر النقاط */}
      {sliders.length > 1 && (
        <View style={styles.pagination}>
          {sliders.map((_, index) => renderPaginationDot(index))}
        </View>
      )}
    </View>
  );
};

export default PromoBanner;
