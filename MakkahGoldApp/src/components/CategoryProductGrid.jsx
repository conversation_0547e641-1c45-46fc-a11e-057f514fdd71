// مكون عرض منتجات الفئات للصفحة الرئيسية - Category Products Grid for Home Screen
import React from "react";
import {
  View,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Linking,
  I18nManager,
} from "react-native";
import { Image } from "expo-image";

import { MaterialIcons } from "@expo/vector-icons";
import { SPACING } from "../constants";
import { useTheme } from "../contexts/ThemeContext";
import { useAppSettings } from "../utils/hooks";
import ThemedCard from "./ThemedCard";
import ThemedText from "./ThemedText";

const { width } = Dimensions.get("window");
const itemWidth = (width - SPACING.md * 3) / 2;

const CategoryProductGrid = ({
  categories,
  onProductPress,
  onFavoritePress,
  favorites = [],
  numColumns = 2,
  showFavorites = true,
}) => {
  const { colors } = useTheme();

  // جلب إعدادات التطبيق للحصول على رقم الواتساب
  const { contactInfo } = useAppSettings();

  // التحقق من صحة البيانات
  if (!categories || !Array.isArray(categories)) {
    return null;
  }

  // الحصول على جميع المنتجات من جميع الفئات
  const allProducts = categories.flatMap((category) =>
    (category.products || []).map((product) => ({
      ...product,
      // استخدام categoryShowPrice إذا كان موجود، وإلا استخدم show_price من الفئة
      categoryShowPrice:
        product.categoryShowPrice !== undefined
          ? product.categoryShowPrice
          : category.show_price,
    }))
  );

  const formatPrice = (price) => {
    if (!price || isNaN(price) || price <= 0) {
      return "السعر غير متاح";
    }
    try {
      return `${price.toLocaleString("en-US")} جنيه`;
    } catch (error) {
      return "السعر غير متاح";
    }
  };

  const calculateDiscount = (price, oldPrice) => {
    if (!oldPrice || oldPrice <= price) return null;
    return Math.round(((oldPrice - price) / oldPrice) * 100);
  };

  const openWhatsApp = (productName) => {
    // التحقق من وجود رقم الواتساب من API
    if (!contactInfo?.whatsapp) {
      console.warn("رقم الواتساب غير متاح في إعدادات التطبيق");
      return;
    }

    const safeName = String(productName || "منتج غير محدد");
    const message = `مرحباً، أريد الاستفسار عن سعر منتج: ${safeName}`;
    const phoneNumber = contactInfo.whatsapp; // رقم الواتساب من API
    const cleanPhoneNumber = phoneNumber.replace(/[^0-9]/g, ""); // إزالة الرموز
    const url = `whatsapp://send?phone=${cleanPhoneNumber}&text=${encodeURIComponent(
      message
    )}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // فتح واتساب ويب كبديل
          const webUrl = `https://wa.me/${cleanPhoneNumber}?text=${encodeURIComponent(
            message
          )}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => console.error("خطأ في فتح واتساب:", err));
  };

  const renderProduct = ({ item }) => {
    // التحقق من صحة البيانات
    if (!item || typeof item !== "object" || !item.id) {
      return null;
    }

    // استخدام البيانات العربية مباشرة من API
    const productName = item.name || "منتج غير محدد";
    const productPrice = Number(item.price) || 0;
    const productOldPrice = item.old_price ? Number(item.old_price) : undefined;
    // استخدام show_price من المنتج مباشرة
    const showPrice = item.show_price !== false;

    const isFavorite = favorites.includes(item.id);
    const discount =
      item.discount_percentage ||
      calculateDiscount(productPrice, productOldPrice);

    return (
      <TouchableOpacity
        style={[styles.productItem, { width: itemWidth }]}
        onPress={() => onProductPress(item.id)}
        activeOpacity={0.8}
      >
        <ThemedCard style={styles.productCard}>
          {/* صورة المنتج */}
          <View style={styles.imageContainer}>
            {item.image ? (
              <Image source={{ uri: item.image }} style={styles.productImage} />
            ) : (
              <View
                style={[
                  styles.placeholderImage,
                  { backgroundColor: colors.border },
                ]}
              >
                <MaterialIcons
                  name="diamond"
                  size={40}
                  color={colors.textSecondary}
                />
              </View>
            )}

            {/* علامة الخصم */}
            {discount && showPrice && (
              <View
                style={[
                  styles.discountBadge,
                  { backgroundColor: colors.accent },
                ]}
              >
                <ThemedText variant="inverse" size="xs" weight="bold">
                  {`-${discount}%`}
                </ThemedText>
              </View>
            )}

            {/* زر المفضلة */}
            {showFavorites && onFavoritePress && (
              <TouchableOpacity
                style={[
                  styles.favoriteButton,
                  { backgroundColor: colors.surface },
                ]}
                onPress={() => onFavoritePress(item.id)}
                activeOpacity={0.7}
              >
                <MaterialIcons
                  name={isFavorite ? "favorite" : "favorite-border"}
                  size={20}
                  color={isFavorite ? colors.accent : colors.textSecondary}
                />
              </TouchableOpacity>
            )}
          </View>

          {/* معلومات المنتج */}
          <View style={styles.productInfo}>
            <ThemedText
              variant="primary"
              size="sm"
              weight="regular"
              numberOfLines={2}
              style={styles.productName}
            >
              {productName}
            </ThemedText>

            {/* السعر أو زر تواصل للسعر - حسب إعدادات الفئة */}
            <View style={styles.priceContainer}>
              {showPrice ? (
                <>
                  <ThemedText variant="primary" size="md" weight="bold">
                    {formatPrice(productPrice)}
                  </ThemedText>
                  {productOldPrice && productOldPrice > productPrice && (
                    <ThemedText
                      variant="secondary"
                      size="sm"
                      style={styles.oldPrice}
                    >
                      {formatPrice(productOldPrice)}
                    </ThemedText>
                  )}
                </>
              ) : (
                <TouchableOpacity
                  style={[
                    styles.contactButton,
                    { backgroundColor: colors.accent },
                  ]}
                  onPress={() => openWhatsApp(productName)}
                  activeOpacity={0.8}
                >
                  <MaterialIcons
                    name="chat"
                    size={16}
                    color={colors.textInverse}
                  />
                  <ThemedText
                    variant="inverse"
                    size="sm"
                    weight="bold"
                    style={styles.contactButtonText}
                  >
                    تواصل للسعر
                  </ThemedText>
                </TouchableOpacity>
              )}
            </View>

            {/* معلومات المعدن والعيار - البيانات تأتي جاهزة من API */}
            {(item.metal?.name_ar || item.metal_purity_info?.name_ar) && (
              <ThemedText
                variant="secondary"
                size="xs"
                numberOfLines={1}
                style={styles.materialInfo}
              >
                {(() => {
                  const metalName = item.metal?.name_ar?.trim();
                  const purityName = item.metal_purity_info?.name_ar?.trim();

                  if (metalName && purityName) {
                    return `${metalName} - ${purityName}`;
                  } else if (metalName) {
                    return metalName;
                  } else if (purityName) {
                    return purityName;
                  }
                  return "";
                })()}
              </ThemedText>
            )}
          </View>
        </ThemedCard>
      </TouchableOpacity>
    );
  };

  return (
    <FlatList
      data={allProducts}
      renderItem={renderProduct}
      keyExtractor={(item) => String(item?.id || Math.random())}
      numColumns={numColumns}
      contentContainerStyle={styles.container}
      columnWrapperStyle={numColumns > 1 ? styles.row : undefined}
      showsVerticalScrollIndicator={false}
      scrollEnabled={false}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: SPACING.md,
  },
  row: {
    justifyContent: "space-between",
  },
  productItem: {
    marginBottom: SPACING.md,
  },
  productCard: {
    padding: 0,
    overflow: "hidden",
  },
  imageContainer: {
    position: "relative",
    width: "100%",
    height: itemWidth * 0.8,
  },
  productImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  discountBadge: {
    position: "absolute",
    top: SPACING.sm,
    right: I18nManager.isRTL ? SPACING.sm : "auto", // RTL support
    left: I18nManager.isRTL ? "auto" : SPACING.sm, // RTL support
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
  favoriteButton: {
    position: "absolute",
    bottom: SPACING.sm,
    left: I18nManager.isRTL ? "auto" : SPACING.sm, // RTL support
    right: I18nManager.isRTL ? SPACING.sm : "auto", // RTL support
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  productInfo: {
    padding: SPACING.sm,
  },
  productName: {
    marginBottom: SPACING.xs,
    textAlign: I18nManager.isRTL ? "left" : "right", // RTL support
  },
  priceContainer: {
    flexDirection: I18nManager.isRTL ? "row" : "row-reverse", // RTL support
    alignItems: "center",
    justifyContent: I18nManager.isRTL ? "flex-end" : "flex-start", // RTL support
    marginBottom: SPACING.xs,
    gap: SPACING.xs,
  },
  oldPrice: {
    textDecorationLine: "line-through",
  },
  contactButton: {
    flexDirection: I18nManager.isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
    gap: SPACING.xs,
    marginTop: SPACING.xs,
  },
  contactButtonText: {
    fontSize: 12,
  },
  materialInfo: {
    textAlign: "center",
    marginTop: SPACING.xs,
    opacity: 0.8,
  },
});

export default CategoryProductGrid;
