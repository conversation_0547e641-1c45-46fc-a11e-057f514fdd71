// شريط الحالة المتكيف مع الثيم - Themed Status Bar Component (Edge-to-Edge Compatible)
// يتبع أفضل الممارسات من توثيق Expo لدعم edge-to-edge على Android
// https://docs.expo.dev/versions/latest/sdk/status-bar/
import React, { useEffect } from "react";
import { View, Platform } from "react-native";
import { StatusBar } from "expo-status-bar";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../contexts/ThemeContext";
import { COLORS } from "../constants";

/**
 * @typedef {Object} ThemedStatusBarProps
 * @property {string} [backgroundColor] - لون خلفية شريط الحالة (يعمل مع edge-to-edge)
 * @property {"auto" | "inverted" | "light" | "dark"} [style] - نمط شريط الحالة
 * @property {boolean} [showBackground] - عرض خلفية ملونة (للتوافق مع edge-to-edge)
 */

const ThemedStatusBar = ({
  backgroundColor,
  style = "auto",
  showBackground = true, // تفعيل افتراضي للتوافق مع edge-to-edge
}) => {
  // محاولة استخدام الثيم مع fallback
  let themeData = null;
  try {
    themeData = useTheme();
  } catch (error) {
    // إذا لم يكن ThemeProvider متاح، استخدم قيم افتراضية
    themeData = {
      colors: COLORS,
      isDarkMode: false,
    };
  }

  const { colors, isDarkMode } = themeData;
  const insets = useSafeAreaInsets();

  // تحديد نمط النص تلقائياً بناءً على الوضع والخلفية
  const getStatusBarStyle = () => {
    if (style === "auto") {
      // إذا كان هناك خلفية مخصصة، حدد النمط بناءً على لونها
      if (backgroundColor) {
        const isBackgroundDark = isColorDark(backgroundColor);
        return isBackgroundDark ? "light" : "dark";
      }

      // منطق محسن للأندرويد
      if (Platform.OS === "android") {
        // في Light Mode: الخلفية بيضاء (#FFFFFF) - نحتاج نص داكن
        // في Dark Mode: الخلفية داكنة (#2C2C2C) - نحتاج نص فاتح
        return isDarkMode ? "light" : "dark";
      }

      // لـ iOS: نفس المنطق
      return isDarkMode ? "light" : "dark";
    }
    return style;
  };

  // دالة لتحديد إذا كان اللون داكن أم فاتح
  const isColorDark = (color) => {
    // إزالة # إذا كانت موجودة
    const hex = color.replace("#", "");

    // تحويل إلى RGB
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // حساب السطوع (brightness)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // إذا كان السطوع أقل من 128 فهو داكن
    return brightness < 128;
  };

  // تحديث شريط الحالة عند تغيير الثيم
  useEffect(() => {
    // تم حذف console.log للتنظيف
  }, [isDarkMode, backgroundColor, style]);

  const statusBarBgColor =
    backgroundColor || (isDarkMode ? colors.background : colors.card);
  const statusBarStyle = getStatusBarStyle();

  return (
    <>
      {/* شريط الحالة للتوافق مع edge-to-edge حسب توثيق Expo */}
      <StatusBar
        style={statusBarStyle}
        translucent={true} // مطلوب لـ edge-to-edge على جميع المنصات
        animated={true}
        key={`statusbar-${statusBarStyle}-${isDarkMode}`}
      />

      {/* خلفية منفصلة تحت شريط الحالة - الحل الموصى به من Expo لـ edge-to-edge */}
      {showBackground && insets.top > 0 && (
        <View
          style={{
            height: insets.top,
            backgroundColor: statusBarBgColor,
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            zIndex: 1000,
          }}
        />
      )}
    </>
  );
};

export default ThemedStatusBar;
