// التنقل الرئيسي للتطبيق - Enhanced App Navigator with Modern Tab Bar

import React from "react";
import { View, Text } from "react-native";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, FONTS, ROUTES } from "../constants";
import { useTheme } from "../contexts/ThemeContext";
import { ModernTabBar } from "../components";
import { RESPONSIVE_SPACING, responsiveFontSize } from "../utils/responsive";
import RTLManager from "../utils/RTLManager";

// Import Screens
import HomeScreen from "../screens/HomeScreen";
import PricesScreen from "../screens/PricesScreen";
// import CalculatorsScreen from '../screens/CalculatorsScreen';
// import ProductsScreen from '../screens/ProductsScreen';
// import MoreScreen from '../screens/MoreScreen';
// import PriceHistoryScreen from '../screens/PriceHistoryScreen';
// import ZakatCalculatorScreen from '../screens/ZakatCalculatorScreen';
// import JewelryCalculatorScreen from '../screens/JewelryCalculatorScreen';
// import ProductDetailsScreen from '../screens/ProductDetailsScreen';
// import CategoryProductsScreen from '../screens/CategoryProductsScreen';
// import SettingsScreen from '../screens/SettingsScreen';
// import AboutScreen from '../screens/AboutScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Placeholder screens for development
const PlaceholderScreen = ({ route }) => {
  const { colors } = useTheme();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: colors.background,
        padding: RESPONSIVE_SPACING.lg,
      }}
    >
      <MaterialIcons
        name="construction"
        size={responsiveFontSize(48)}
        color={colors.primary}
      />
      <Text
        style={{
          marginTop: RESPONSIVE_SPACING.md,
          fontSize: responsiveFontSize(18),
          color: colors.textPrimary,
          fontFamily: FONTS.families.bold,
          textAlign: RTLManager.getTextAlign("center"),
          writingDirection: RTLManager.getTextDirection(),
        }}
      >
        {route.name} - قيد التطوير
      </Text>
      <Text
        style={{
          marginTop: RESPONSIVE_SPACING.sm,
          fontSize: responsiveFontSize(14),
          color: colors.textSecondary,
          fontFamily: FONTS.families.regular,
          textAlign: RTLManager.getTextAlign("center"),
          writingDirection: RTLManager.getTextDirection(),
        }}
      >
        ستكون متاحة في التحديث القادم
      </Text>
    </View>
  );
};

// Bottom Tab Navigator with Modern Design
function MainTabs() {
  const { colors } = useTheme();

  return (
    <Tab.Navigator
      tabBar={(props) => <ModernTabBar {...props} />}
      screenOptions={{
        headerStyle: {
          backgroundColor: colors.primary,
          elevation: 8,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        },
        headerTintColor: colors.surface,
        headerTitleStyle: {
          fontFamily: FONTS.families.bold,
          fontSize: responsiveFontSize(FONTS.sizes.lg),
          writingDirection: RTLManager.getTextDirection(),
        },
        headerTitleAlign: "center",
      }}
    >
      <Tab.Screen
        name="index"
        component={HomeScreen}
        options={{
          title: "الرئيسية",
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="prices"
        component={PricesScreen}
        options={{
          title: "الأسعار",
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="calculators"
        component={PlaceholderScreen}
        options={{
          title: "الحاسبات",
        }}
      />
      <Tab.Screen
        name="categories"
        component={PlaceholderScreen}
        options={{
          title: "الأقسام",
        }}
      />
      <Tab.Screen
        name="settings"
        component={PlaceholderScreen}
        options={{
          title: "الإعدادات",
        }}
      />
    </Tab.Navigator>
  );
}

// Main Stack Navigator
function AppNavigator() {
  const { colors } = useTheme();

  return (
    <NavigationContainer>
      <Stack.Navigator
        screenOptions={{
          headerStyle: {
            backgroundColor: colors.primary,
            elevation: 8,
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
          },
          headerTintColor: colors.surface,
          headerTitleStyle: {
            fontFamily: FONTS.families.bold,
            fontSize: responsiveFontSize(FONTS.sizes.lg),
            writingDirection: RTLManager.getTextDirection(),
          },
          headerTitleAlign: "center",
          headerBackTitleVisible: false,
          animation: RTLManager.isRTLActive()
            ? "slide_from_left"
            : "slide_from_right",
        }}
      >
        <Stack.Screen
          name="MainTabs"
          component={MainTabs}
          options={{ headerShown: false }}
        />

        <Stack.Screen
          name={ROUTES.PRICE_HISTORY}
          component={PlaceholderScreen}
          options={({ route }) => ({
            title: `تاريخ أسعار ${route.params?.metalName || "المعدن"}`,
          })}
        />

        <Stack.Screen
          name={ROUTES.ZAKAT_CALCULATOR}
          component={PlaceholderScreen}
          options={({ route }) => ({
            title: `حاسبة زكاة ${
              route.params?.metalType === "gold" ? "الذهب" : "الفضة"
            }`,
          })}
        />

        <Stack.Screen
          name={ROUTES.JEWELRY_CALCULATOR}
          component={PlaceholderScreen}
          options={{
            title: "حاسبة قيمة المجوهرات",
          }}
        />

        <Stack.Screen
          name={ROUTES.PRODUCT_DETAILS}
          component={PlaceholderScreen}
          options={{
            title: "تفاصيل المنتج",
          }}
        />

        <Stack.Screen
          name={ROUTES.CATEGORY_PRODUCTS}
          component={PlaceholderScreen}
          options={({ route }) => ({
            title: route.params?.categoryName || "منتجات الفئة",
          })}
        />

        <Stack.Screen
          name={ROUTES.ABOUT}
          component={PlaceholderScreen}
          options={{
            title: "حول التطبيق",
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

export default AppNavigator;
