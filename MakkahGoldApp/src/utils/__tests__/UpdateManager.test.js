/**
 * اختبارات مدير التحديثات - UpdateManager Tests
 */

import UpdateManager from '../UpdateManager';
import AndroidCompatibility from '../AndroidCompatibility';
import * as Updates from 'expo-updates';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock dependencies
jest.mock('expo-updates');
jest.mock('@react-native-async-storage/async-storage');
jest.mock('../AndroidCompatibility');

describe('UpdateManager', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Reset UpdateManager state
    UpdateManager.isInitialized = false;
    UpdateManager.updateCheckInterval = null;
    UpdateManager.lastUpdateCheck = null;
    UpdateManager.updateSettings = null;

    // Default mock implementations
    Updates.isEnabled = true;
    Updates.channel = 'production';
    Updates.runtimeVersion = '2.0.0';
    
    AndroidCompatibility.initialize = jest.fn();
    AndroidCompatibility.isFeatureSupported = jest.fn().mockReturnValue(true);
    AndroidCompatibility.canRunBackgroundUpdates = jest.fn().mockReturnValue(true);
    AndroidCompatibility.getCompatibleUpdateSettings = jest.fn().mockReturnValue({
      autoCheck: true,
      autoDownload: true,
      wifiOnly: false,
      checkInterval: 30 * 60 * 1000,
    });
    AndroidCompatibility.getCompatibilityMessage = jest.fn().mockReturnValue(null);
    AndroidCompatibility.getSystemInfo = jest.fn().mockReturnValue({
      platform: 'android',
      apiLevel: 28,
      supportedFeatures: {
        easUpdates: true,
        backgroundUpdates: true,
      },
    });

    AsyncStorage.getItem = jest.fn().mockResolvedValue(null);
    AsyncStorage.setItem = jest.fn().mockResolvedValue();
  });

  describe('initialize', () => {
    it('should initialize successfully with compatible Android version', async () => {
      await UpdateManager.initialize();

      expect(AndroidCompatibility.initialize).toHaveBeenCalled();
      expect(AndroidCompatibility.isFeatureSupported).toHaveBeenCalledWith('eas_updates');
      expect(UpdateManager.isInitialized).toBe(true);
    });

    it('should not initialize if Updates are not enabled', async () => {
      Updates.isEnabled = false;

      await UpdateManager.initialize();

      expect(UpdateManager.isInitialized).toBe(false);
    });

    it('should not initialize if EAS Updates are not supported', async () => {
      AndroidCompatibility.isFeatureSupported.mockReturnValue(false);

      await UpdateManager.initialize();

      expect(UpdateManager.isInitialized).toBe(false);
    });

    it('should not start periodic updates if background updates are not supported', async () => {
      AndroidCompatibility.canRunBackgroundUpdates.mockReturnValue(false);
      const startPeriodicSpy = jest.spyOn(UpdateManager, 'startPeriodicUpdateCheck');

      await UpdateManager.initialize();

      expect(startPeriodicSpy).not.toHaveBeenCalled();
    });

    it('should show compatibility message if needed', async () => {
      const compatibilityMessage = {
        type: 'warning',
        title: 'إصدار Android قديم',
        message: 'قد تكون بعض ميزات التحديث محدودة',
      };
      AndroidCompatibility.getCompatibilityMessage.mockReturnValue(compatibilityMessage);

      // Mock setTimeout to execute immediately
      jest.spyOn(global, 'setTimeout').mockImplementation((fn) => fn());

      await UpdateManager.initialize();

      expect(setTimeout).toHaveBeenCalled();
    });
  });

  describe('loadUpdateSettings', () => {
    it('should load existing settings from storage', async () => {
      const existingSettings = {
        autoCheck: false,
        autoDownload: false,
        wifiOnly: true,
        checkInterval: 60 * 60 * 1000,
      };
      AsyncStorage.getItem.mockResolvedValueOnce(JSON.stringify(existingSettings));

      await UpdateManager.loadUpdateSettings();

      expect(UpdateManager.updateSettings).toEqual(existingSettings);
    });

    it('should use compatible default settings if no settings exist', async () => {
      const compatibleSettings = {
        autoCheck: true,
        autoDownload: false,
        wifiOnly: true,
        checkInterval: 45 * 60 * 1000,
      };
      AndroidCompatibility.getCompatibleUpdateSettings.mockReturnValue(compatibleSettings);

      await UpdateManager.loadUpdateSettings();

      expect(AndroidCompatibility.getCompatibleUpdateSettings).toHaveBeenCalled();
      expect(UpdateManager.updateSettings).toEqual(compatibleSettings);
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });
  });

  describe('checkForUpdates', () => {
    beforeEach(() => {
      Updates.checkForUpdateAsync = jest.fn();
      AsyncStorage.setItem = jest.fn().mockResolvedValue();
    });

    it('should check for updates successfully', async () => {
      Updates.checkForUpdateAsync.mockResolvedValue({
        isAvailable: false,
      });

      await UpdateManager.checkForUpdates(false);

      expect(Updates.checkForUpdateAsync).toHaveBeenCalled();
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        UpdateManager.STORAGE_KEYS.LAST_UPDATE_CHECK,
        expect.any(String)
      );
    });

    it('should handle update available scenario', async () => {
      const updateInfo = {
        isAvailable: true,
        manifest: { id: 'test-update' },
      };
      Updates.checkForUpdateAsync.mockResolvedValue(updateInfo);
      UpdateManager.updateSettings = { autoDownload: false };

      await UpdateManager.checkForUpdates(false);

      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        UpdateManager.STORAGE_KEYS.PENDING_UPDATE,
        JSON.stringify(updateInfo)
      );
    });

    it('should auto-download if enabled', async () => {
      const updateInfo = {
        isAvailable: true,
        manifest: { id: 'test-update' },
      };
      Updates.checkForUpdateAsync.mockResolvedValue(updateInfo);
      UpdateManager.updateSettings = { autoDownload: true };
      
      const downloadSpy = jest.spyOn(UpdateManager, 'downloadAndInstallUpdate').mockResolvedValue();

      await UpdateManager.checkForUpdates(false);

      expect(downloadSpy).toHaveBeenCalledWith(updateInfo);
    });

    it('should handle errors gracefully', async () => {
      Updates.checkForUpdateAsync.mockRejectedValue(new Error('Network error'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      await UpdateManager.checkForUpdates(false);

      expect(consoleSpy).toHaveBeenCalledWith(
        'خطأ في التحقق من التحديثات:',
        expect.any(Error)
      );
    });
  });

  describe('downloadAndInstallUpdate', () => {
    beforeEach(() => {
      Updates.fetchUpdateAsync = jest.fn();
      AsyncStorage.removeItem = jest.fn().mockResolvedValue();
    });

    it('should download and install update successfully', async () => {
      Updates.fetchUpdateAsync.mockResolvedValue({
        isNew: true,
      });

      const updateInfo = { manifest: { id: 'test-update' } };
      await UpdateManager.downloadAndInstallUpdate(updateInfo);

      expect(Updates.fetchUpdateAsync).toHaveBeenCalled();
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(
        UpdateManager.STORAGE_KEYS.PENDING_UPDATE
      );
    });

    it('should handle download errors', async () => {
      Updates.fetchUpdateAsync.mockRejectedValue(new Error('Download failed'));
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      const updateInfo = { manifest: { id: 'test-update' } };
      await UpdateManager.downloadAndInstallUpdate(updateInfo);

      expect(consoleSpy).toHaveBeenCalledWith(
        'خطأ في تحميل التحديث:',
        expect.any(Error)
      );
    });
  });

  describe('getUpdateInfo', () => {
    it('should return complete update information', () => {
      UpdateManager.updateSettings = { autoCheck: true };
      UpdateManager.lastUpdateCheck = new Date();

      const info = UpdateManager.getUpdateInfo();

      expect(info).toEqual({
        isEnabled: Updates.isEnabled,
        channel: Updates.channel,
        runtimeVersion: Updates.runtimeVersion,
        lastUpdateCheck: UpdateManager.lastUpdateCheck,
        settings: UpdateManager.updateSettings,
        compatibility: expect.any(Object),
      });
      expect(AndroidCompatibility.getSystemInfo).toHaveBeenCalled();
    });
  });

  describe('updateSettings', () => {
    beforeEach(() => {
      UpdateManager.updateSettings = {
        autoCheck: true,
        autoDownload: true,
        wifiOnly: false,
        checkInterval: 30 * 60 * 1000,
      };
    });

    it('should update settings and save to storage', async () => {
      const newSettings = { autoCheck: false };

      await UpdateManager.updateSettings(newSettings);

      expect(UpdateManager.updateSettings).toEqual({
        autoCheck: false,
        autoDownload: true,
        wifiOnly: false,
        checkInterval: 30 * 60 * 1000,
      });
      expect(AsyncStorage.setItem).toHaveBeenCalled();
    });

    it('should restart periodic check when relevant settings change', async () => {
      const startPeriodicSpy = jest.spyOn(UpdateManager, 'startPeriodicUpdateCheck');
      
      await UpdateManager.updateSettings({ checkInterval: 60 * 60 * 1000 });

      expect(startPeriodicSpy).toHaveBeenCalled();
    });
  });

  describe('cleanup', () => {
    it('should clean up resources', () => {
      UpdateManager.updateCheckInterval = setInterval(() => {}, 1000);
      UpdateManager.isInitialized = true;

      UpdateManager.cleanup();

      expect(UpdateManager.isInitialized).toBe(false);
      expect(UpdateManager.updateCheckInterval).toBeNull();
    });
  });
});

describe('Integration with AndroidCompatibility', () => {
  it('should use compatible settings for old Android versions', async () => {
    const oldAndroidSettings = {
      autoCheck: false,
      autoDownload: false,
      wifiOnly: true,
      checkInterval: 60 * 60 * 1000,
    };
    
    AndroidCompatibility.getCompatibleUpdateSettings.mockReturnValue(oldAndroidSettings);
    AndroidCompatibility.canRunBackgroundUpdates.mockReturnValue(false);

    await UpdateManager.initialize();

    expect(UpdateManager.updateSettings).toEqual(oldAndroidSettings);
  });
});
