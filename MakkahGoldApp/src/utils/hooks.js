// Hooks مخصصة - Custom Hooks

import React, { useState, useEffect, useCallback } from "react";
import { apiService } from "../services/api";

// Hook عام لاستخدام API
export function useApi(apiCall, dependencies = []) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall();
      setData(result || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "حدث خطأ غير متوقع");
      console.error("API Error:", err);
      setData([]); // تعيين array فارغ في حالة الخطأ
    } finally {
      setLoading(false);
    }
  }, dependencies);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(async () => {
    await fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
}

// Hook لجلب أسعار المعادن الحالية
export function useCurrentPrices() {
  return useApi(() => apiService.getCurrentPrices());
}

// Hook لجلب الفئات
export function useCategories() {
  return useApi(() => apiService.getCategories());
}

// Hook لجلب الشرائح الرئيسية
export function useHomeSliders() {
  return useApi(() => apiService.getHomeSliders());
}

// Hook لجلب منتج محدد
export function useProduct(productId) {
  return useApi(() => apiService.getProduct(productId), [productId]);
}

// Hook لجلب المنتجات المشابهة
export function useRelatedProducts(productId) {
  return useApi(() => apiService.getRelatedProducts(productId), [productId]);
}

// Hook للبحث التفاعلي المستمر
export function useSearch(initialQuery = "") {
  const [query, setQuery] = useState(initialQuery);
  const [debouncedQuery, setDebouncedQuery] = useState(initialQuery);
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isSearching, setIsSearching] = useState(false);

  // Debounce للبحث - تأخير مناسب لتجربة تفاعلية سلسة
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 500); // تأخير متوسط للتوازن بين السرعة والأداء

    return () => clearTimeout(timer);
  }, [query]);

  // تنفيذ البحث التلقائي عند تغيير debouncedQuery
  useEffect(() => {
    const trimmedQuery = debouncedQuery.trim();

    if (trimmedQuery.length >= 2) {
      searchProducts(trimmedQuery);
    } else {
      // مسح النتائج عند عدم وجود نص كافي
      setResults([]);
      setError(null);
      setLoading(false);
      setIsSearching(false);
    }
  }, [debouncedQuery]);

  // دالة البحث الرئيسية
  const searchProducts = async (searchQuery) => {
    try {
      setLoading(true);
      setIsSearching(true);
      setError(null);

      // استخدام Laravel API للبحث
      const response = await apiService.searchProducts(searchQuery, 1);

      // تنسيق النتائج للعرض
      const formattedResults = (response.data || [])
        .slice(0, 8)
        .map((product) => ({
          id: product.id,
          name: product.name || product.name_ar || "منتج غير محدد",
          price: product.price || 0,
          show_price: product.show_price || false,
          image: product.image || product.images?.[0]?.url,
        }));

      setResults(formattedResults);
    } catch (err) {
      setError(err instanceof Error ? err.message : "خطأ في البحث");
      setResults([]);
      console.error("Search Error:", err);
    } finally {
      setLoading(false);
      setIsSearching(false);
    }
  };

  // مسح البحث والعودة للحالة الأولية
  const clearSearch = () => {
    setQuery("");
    setDebouncedQuery("");
    setResults([]);
    setError(null);
    setLoading(false);
    setIsSearching(false);
  };

  // البحث اليدوي (عند الضغط على زر البحث)
  const performSearch = () => {
    const trimmedQuery = query.trim();
    if (trimmedQuery.length >= 2) {
      searchProducts(trimmedQuery);
    }
  };

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    isSearching,
    clearSearch,
    performSearch,
    hasResults: results.length > 0,
    hasQuery: query.trim().length > 0,
    isQueryValid: query.trim().length >= 2,
  };
}

// Hook لجلب معادلات الزكاة
export const useZakatFormulas = () => {
  return useApi(() => apiService.getZakatFormulas());
};

// Hook لجلب أنواع المعادن
export const useMetalTypes = () => {
  return useApi(() => apiService.getMetalTypes());
};

// Hook لجلب عيارات معدن محدد
export const useMetalPurities = (metalType) => {
  return useApi(() => apiService.getMetalPurities(metalType), [metalType]);
};

// Hook لجلب تاريخ أسعار المعادن
export const useMetalPriceHistory = (params = {}) => {
  return useApi(
    () => apiService.getMetalPriceHistory(params),
    [
      params.metal_type,
      params.purity,
      params.period,
      params.date_from,
      params.date_to,
      params.page,
      params.per_page,
    ]
  );
};

// Hook لجلب إعدادات التطبيق
export const useAppSettings = () => {
  const { data, loading, error, refetch } = useApi(() =>
    apiService.getAppSettings()
  );

  // تنظيم البيانات للوصول السهل
  const organizedData = React.useMemo(() => {
    if (!data) return {};

    return {
      // معلومات التطبيق
      appInfo: {
        name: data.site_name || "مجوهرات مكة جولد جروب",
        version: "2.0.0",
        description:
          data.site_description ||
          data.meta_description ||
          "شركة رائدة في مجال المجوهرات والمعادن الثمينة",
      },

      // معلومات الشركة
      companyInfo: {
        name: data.site_name || "مكة جولد جروب",
        description:
          data.site_description ||
          "شركة رائدة في مجال المجوهرات والمعادن الثمينة",
        copyright:
          data.footer_text || "جميع الحقوق محفوظة © 2024 مكة جولد جروب",
      },

      // معلومات الاتصال
      contactInfo: {
        email: data.contact_info?.email || data.contact_email,
        phone: data.contact_info?.phone || data.contact_phone,
        whatsapp: data.contact_info?.whatsapp || data.whatsapp_number,
        address: data.contact_info?.address || data.address,
      },

      // ساعات العمل
      workingHours: data.working_hours ||
        data.business_hours || {
          weekdays: {
            days: "السبت - الخميس",
            hours: "9:00 ص - 10:00 م",
          },
          friday: {
            day: "الجمعة",
            hours: "2:00 م - 10:00 م",
          },
        },

      // روابط وسائل التواصل الاجتماعي
      socialLinks: {
        facebook: data.social_links?.facebook || data.facebook_url,
        instagram: data.social_links?.instagram || data.instagram_url,
        twitter: data.social_links?.twitter || data.twitter_url,
        youtube: data.social_links?.youtube || data.youtube_url,
        tiktok: data.social_links?.tiktok || data.tiktok_url,
        linkedin: data.social_links?.linkedin || data.linkedin_url,
      },

      // إعدادات المحتوى القانوني
      privacySettings: {
        privacy_policy: data.privacy_policy,
        terms_conditions: data.terms_conditions,
        return_policy: data.return_policy,
      },

      // روابط المتاجر
      storeLinks: {
        app_store_url: data.app_store_url,
        play_store_url: data.play_store_url,
      },

      // إعدادات العرض
      displaySettings: {
        show_featured_products: data.show_featured_products,
        show_new_arrivals: data.show_new_arrivals,
        show_categories: data.show_categories,
        show_gold_prices: data.show_gold_prices,
        show_features: data.show_features,
        show_testimonials: data.show_testimonials,
        show_newsletter: data.show_newsletter,
      },
    };
  }, [data]);

  return {
    data,
    loading,
    error,
    refetch,
    // البيانات المنظمة
    ...organizedData,
  };
};

// Hook لجلب معلومات الاتصال
export const useContactInfo = () => {
  return useApi(() => apiService.getContactInfo());
};

// Hook لجلب سياسة الخصوصية
export const usePrivacyPolicy = () => {
  return useApi(() => apiService.getPrivacyPolicy());
};

// Hook لجلب إحصائيات أسعار المعادن
export const useMetalPriceStatistics = (
  metalType,
  purity,
  period = "month"
) => {
  return useApi(
    () => apiService.getMetalPriceStatistics(metalType, purity, period),
    [metalType, purity, period]
  );
};

// Hook للفروع
export const useStores = () => {
  return useApi(() => apiService.getStores(), [], {
    staleTime: 5 * 60 * 1000, // 5 دقائق
    cacheTime: 10 * 60 * 1000, // 10 دقائق
  });
};

// Hook لفرع محدد
export const useStore = (id) => {
  return useApi(() => apiService.getStore(id), [id], {
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    cacheTime: 10 * 60 * 1000,
  });
};

// Hook للفروع القريبة
export const useNearbyStores = (latitude, longitude, radius = 50) => {
  return useApi(
    () => apiService.getNearbyStores(latitude, longitude, radius),
    [latitude, longitude, radius],
    {
      enabled: !!(latitude && longitude),
      staleTime: 2 * 60 * 1000, // 2 دقائق
      cacheTime: 5 * 60 * 1000, // 5 دقائق
    }
  );
};

// تصدير useResponsive
export { useResponsive } from "./hooks/useResponsive";

// تصدير useBarsCoinsData
export { useBarsCoinsData } from "./hooks/useBarsCoinsData";
