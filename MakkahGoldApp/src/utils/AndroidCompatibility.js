/**
 * Android Compatibility Manager
 * مدير التوافق مع Android API level 28 وما دون
 */

import { Platform } from "react-native";
import Constants from "expo-constants";

class AndroidCompatibility {
  static apiLevel = null;
  static isInitialized = false;

  /**
   * تهيئة مدير التوافق
   */
  static initialize() {
    if (AndroidCompatibility.isInitialized) {
      return;
    }

    try {
      if (Platform.OS === "android") {
        // الحصول على API level من Constants
        AndroidCompatibility.apiLevel =
          Constants.platform?.android?.apiLevel || 28;
        // تم حذف console.log للتنظيف
      }

      AndroidCompatibility.isInitialized = true;
    } catch (error) {
      // خطأ في تهيئة مدير التوافق - تم حذف الرسالة للتنظيف
      // افتراض API level 28 في حالة الخطأ
      AndroidCompatibility.apiLevel = 28;
      AndroidCompatibility.isInitialized = true;
    }
  }

  /**
   * التحقق من دعم ميزة معينة بناءً على API level
   */
  static isFeatureSupported(feature) {
    if (Platform.OS !== "android") {
      return true; // iOS يدعم جميع الميزات
    }

    AndroidCompatibility.initialize();

    const featureRequirements = {
      // ميزات التحديثات
      eas_updates: 21, // الحد الأدنى لـ EAS Updates
      background_updates: 23, // التحديثات في الخلفية
      notification_channels: 26, // قنوات الإشعارات
      adaptive_icons: 26, // الأيقونات التكيفية
      edge_to_edge: 29, // Edge to Edge
      scoped_storage: 29, // التخزين المحدود النطاق
      biometric_auth: 23, // المصادقة البيومترية
      dark_mode: 29, // الوضع المظلم
      gesture_navigation: 29, // التنقل بالإيماءات
      rtl_support: 17, // دعم RTL
      network_security_config: 24, // تكوين أمان الشبكة
    };

    const requiredLevel = featureRequirements[feature];
    if (requiredLevel === undefined) {
      console.warn(`ميزة غير معروفة: ${feature}`);
      return true;
    }

    return AndroidCompatibility.apiLevel >= requiredLevel;
  }

  /**
   * الحصول على إعدادات التحديثات المتوافقة مع API level
   */
  static getCompatibleUpdateSettings() {
    AndroidCompatibility.initialize();

    const baseSettings = {
      autoCheck: true,
      autoDownload: true,
      wifiOnly: false,
      checkInterval: 30 * 60 * 1000, // 30 دقيقة
    };

    if (Platform.OS !== "android") {
      return baseSettings;
    }

    // تعديل الإعدادات بناءً على API level
    if (AndroidCompatibility.apiLevel < 23) {
      // API level أقل من 23 - تقييد التحديثات في الخلفية
      return {
        ...baseSettings,
        autoCheck: false, // إيقاف التحقق التلقائي
        checkInterval: 60 * 60 * 1000, // ساعة واحدة
        wifiOnly: true, // Wi-Fi فقط لتوفير البيانات
      };
    }

    if (AndroidCompatibility.apiLevel < 26) {
      // API level أقل من 26 - تقييد بعض الميزات
      return {
        ...baseSettings,
        checkInterval: 45 * 60 * 1000, // 45 دقيقة
        wifiOnly: true, // Wi-Fi فقط
      };
    }

    if (AndroidCompatibility.apiLevel < 29) {
      // API level أقل من 29 - إعدادات محافظة
      return {
        ...baseSettings,
        checkInterval: 30 * 60 * 1000, // 30 دقيقة
      };
    }

    return baseSettings;
  }

  /**
   * الحصول على إعدادات الشبكة المتوافقة
   */
  static getCompatibleNetworkSettings() {
    AndroidCompatibility.initialize();

    const baseSettings = {
      timeout: 10000,
      retries: 3,
      useHttps: true,
    };

    if (Platform.OS !== "android") {
      return baseSettings;
    }

    // تعديل إعدادات الشبكة بناءً على API level
    if (AndroidCompatibility.apiLevel < 24) {
      // API level أقل من 24 - إعدادات شبكة محافظة
      return {
        ...baseSettings,
        timeout: 15000, // مهلة أطول
        retries: 2, // محاولات أقل
      };
    }

    return baseSettings;
  }

  /**
   * الحصول على إعدادات التخزين المتوافقة
   */
  static getCompatibleStorageSettings() {
    AndroidCompatibility.initialize();

    const baseSettings = {
      useEncryption: true,
      cacheSize: 50 * 1024 * 1024, // 50 MB
    };

    if (Platform.OS !== "android") {
      return baseSettings;
    }

    // تعديل إعدادات التخزين بناءً على API level
    if (AndroidCompatibility.apiLevel < 23) {
      // API level أقل من 23 - تقييد التشفير
      return {
        ...baseSettings,
        useEncryption: false, // إيقاف التشفير
        cacheSize: 25 * 1024 * 1024, // 25 MB
      };
    }

    if (AndroidCompatibility.apiLevel < 29) {
      // API level أقل من 29 - إعدادات محافظة
      return {
        ...baseSettings,
        cacheSize: 30 * 1024 * 1024, // 30 MB
      };
    }

    return baseSettings;
  }

  /**
   * التحقق من إمكانية تشغيل التحديثات في الخلفية
   */
  static canRunBackgroundUpdates() {
    return AndroidCompatibility.isFeatureSupported("background_updates");
  }

  /**
   * التحقق من دعم الإشعارات المتقدمة
   */
  static supportsAdvancedNotifications() {
    return AndroidCompatibility.isFeatureSupported("notification_channels");
  }

  /**
   * الحصول على استراتيجية التحديث المناسبة
   */
  static getUpdateStrategy() {
    AndroidCompatibility.initialize();

    if (Platform.OS !== "android") {
      return "aggressive"; // iOS يدعم التحديثات القوية
    }

    if (AndroidCompatibility.apiLevel >= 29) {
      return "aggressive"; // تحديثات قوية
    }

    if (AndroidCompatibility.apiLevel >= 26) {
      return "moderate"; // تحديثات متوسطة
    }

    if (AndroidCompatibility.apiLevel >= 23) {
      return "conservative"; // تحديثات محافظة
    }

    return "minimal"; // تحديثات أساسية فقط
  }

  /**
   * الحصول على رسالة التوافق للمستخدم
   */
  static getCompatibilityMessage() {
    AndroidCompatibility.initialize();

    if (Platform.OS !== "android") {
      return null;
    }

    if (AndroidCompatibility.apiLevel < 23) {
      return {
        type: "warning",
        title: "إصدار Android قديم",
        message: "قد تكون بعض ميزات التحديث محدودة على هذا الإصدار من Android.",
      };
    }

    if (AndroidCompatibility.apiLevel < 26) {
      return {
        type: "info",
        title: "تحسين الأداء",
        message: "للحصول على أفضل تجربة، يُنصح بتحديث Android إلى إصدار أحدث.",
      };
    }

    return null; // لا توجد رسائل للإصدارات الحديثة
  }

  /**
   * تطبيق إعدادات التوافق على كائن الإعدادات
   */
  static applyCompatibilitySettings(settings) {
    AndroidCompatibility.initialize();

    const compatibleUpdateSettings =
      AndroidCompatibility.getCompatibleUpdateSettings();
    const compatibleNetworkSettings =
      AndroidCompatibility.getCompatibleNetworkSettings();
    const compatibleStorageSettings =
      AndroidCompatibility.getCompatibleStorageSettings();

    return {
      ...settings,
      updates: {
        ...settings.updates,
        ...compatibleUpdateSettings,
      },
      network: {
        ...settings.network,
        ...compatibleNetworkSettings,
      },
      storage: {
        ...settings.storage,
        ...compatibleStorageSettings,
      },
      compatibility: {
        apiLevel: AndroidCompatibility.apiLevel,
        strategy: AndroidCompatibility.getUpdateStrategy(),
        backgroundUpdates: AndroidCompatibility.canRunBackgroundUpdates(),
        advancedNotifications:
          AndroidCompatibility.supportsAdvancedNotifications(),
      },
    };
  }

  /**
   * الحصول على معلومات النظام
   */
  static getSystemInfo() {
    AndroidCompatibility.initialize();

    return {
      platform: Platform.OS,
      version: Platform.Version,
      apiLevel: AndroidCompatibility.apiLevel,
      isTablet:
        Platform.isPad ||
        (Platform.OS === "android" && Constants.deviceType === 2),
      supportedFeatures: {
        easUpdates: AndroidCompatibility.isFeatureSupported("eas_updates"),
        backgroundUpdates:
          AndroidCompatibility.isFeatureSupported("background_updates"),
        notificationChannels: AndroidCompatibility.isFeatureSupported(
          "notification_channels"
        ),
        adaptiveIcons:
          AndroidCompatibility.isFeatureSupported("adaptive_icons"),
        edgeToEdge: AndroidCompatibility.isFeatureSupported("edge_to_edge"),
        rtlSupport: AndroidCompatibility.isFeatureSupported("rtl_support"),
      },
    };
  }
}

export default AndroidCompatibility;
