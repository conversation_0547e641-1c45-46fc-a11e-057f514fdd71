// مساعد الخطوط للعمل بشكل صحيح على Android و iOS
import { Platform } from "react-native";

/**
 * دالة للحصول على اسم الخط الصحيح بناءً على النظام
 * @param {string} fontType - نوع الخط (regular, bold, medium)
 * @returns {string} اسم الخط المناسب للنظام
 */
export const getFontFamily = (fontType = "regular") => {
  const fontMaps = {
    ios: {
      regular: "Cairo_400Regular",
      bold: "Cairo_700Bold",
      medium: "Cairo_400Regular",
    },
    android: {
      regular: "Cairo-Regular",
      bold: "Cairo-Bold",
      medium: "Cairo-Regular",
    },
    default: {
      regular: Platform.select({
        ios: "System",
        android: "Roboto",
        default: "System",
      }),
      bold: Platform.select({
        ios: "System",
        android: "Roboto",
        default: "System",
      }),
      medium: Platform.select({
        ios: "System",
        android: "Roboto",
        default: "System",
      }),
    },
  };

  const platformFonts = fontMaps[Platform.OS] || fontMaps.default;
  return platformFonts[fontType] || platformFonts.regular;
};

/**
 * كائن الخطوط المحدث للعمل على جميع الأنظمة
 */
export const FONT_FAMILIES = {
  regular: getFontFamily("regular"),
  bold: getFontFamily("bold"),
  medium: getFontFamily("medium"),
};

/**
 * دالة للتحقق من تحميل الخطوط
 */
export const isFontSupported = () => {
  return Platform.OS === "ios" || Platform.OS === "android";
};

/**
 * دالة للحصول على خط آمن (fallback)
 */
export const getSafeFontFamily = (fontType = "regular") => {
  if (!isFontSupported()) {
    return Platform.select({
      ios: "System",
      android: "Roboto",
      default: "System",
    });
  }
  return getFontFamily(fontType);
};
