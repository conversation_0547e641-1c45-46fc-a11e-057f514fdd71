/**
 * أدوات التصميم المتجاوب - Responsive Design Utils
 * للتعامل مع جميع أحجام الشاشات والهواتف
 */

import { Dimensions, PixelRatio, Platform } from "react-native";

// الحصول على أبعاد الشاشة الحالية
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get("window");

// تصنيف أنواع الأجهزة
export const DEVICE_TYPES = {
  PHONE_SMALL: "phone_small", // < 375px
  PHONE_MEDIUM: "phone_medium", // 375-414px
  PHONE_LARGE: "phone_large", // 414-480px
  TABLET_SMALL: "tablet_small", // 480-768px
  TABLET_LARGE: "tablet_large", // > 768px
};

// تصنيف نسب العرض إلى الارتفاع
export const ASPECT_RATIOS = {
  CLASSIC: "classic", // 16:9 - 1.77
  WIDE: "wide", // 18:9, 19:9 - 2.0+
  EXTRA_WIDE: "extra_wide", // 20:9+ - 2.2+
};

/**
 * تحديد نوع الجهاز الحالي
 */
export const getDeviceType = () => {
  if (SCREEN_WIDTH < 375) return DEVICE_TYPES.PHONE_SMALL;
  if (SCREEN_WIDTH < 414) return DEVICE_TYPES.PHONE_MEDIUM;
  if (SCREEN_WIDTH < 480) return DEVICE_TYPES.PHONE_LARGE;
  if (SCREEN_WIDTH < 768) return DEVICE_TYPES.TABLET_SMALL;
  return DEVICE_TYPES.TABLET_LARGE;
};

/**
 * تحديد نسبة العرض إلى الارتفاع
 */
export const getAspectRatio = () => {
  const ratio = SCREEN_HEIGHT / SCREEN_WIDTH;
  if (ratio >= 2.2) return ASPECT_RATIOS.EXTRA_WIDE;
  if (ratio >= 2.0) return ASPECT_RATIOS.WIDE;
  return ASPECT_RATIOS.CLASSIC;
};

/**
 * التحقق من نوع الجهاز
 */
export const isPhone = () => {
  const deviceType = getDeviceType();
  return [
    DEVICE_TYPES.PHONE_SMALL,
    DEVICE_TYPES.PHONE_MEDIUM,
    DEVICE_TYPES.PHONE_LARGE,
  ].includes(deviceType);
};

export const isTablet = () => {
  const deviceType = getDeviceType();
  return [DEVICE_TYPES.TABLET_SMALL, DEVICE_TYPES.TABLET_LARGE].includes(
    deviceType
  );
};

export const isSmallScreen = () => {
  return getDeviceType() === DEVICE_TYPES.PHONE_SMALL;
};

export const isLargeScreen = () => {
  const deviceType = getDeviceType();
  return [DEVICE_TYPES.TABLET_SMALL, DEVICE_TYPES.TABLET_LARGE].includes(
    deviceType
  );
};

/**
 * تحويل الأبعاد بناءً على حجم الشاشة
 */
const baseWidth = 375; // iPhone X width as base
const baseHeight = 812; // iPhone X height as base

export const wp = (percentage) => {
  const value = (percentage * SCREEN_WIDTH) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

export const hp = (percentage) => {
  const value = (percentage * SCREEN_HEIGHT) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

/**
 * تحويل حجم الخط بناءً على حجم الشاشة
 */
export const responsiveFontSize = (size) => {
  const scale = SCREEN_WIDTH / baseWidth;
  const newSize = size * scale;

  // تطبيق حدود دنيا وعليا
  const minSize = size * 0.8;
  const maxSize = size * 1.3;

  return Math.max(minSize, Math.min(maxSize, newSize));
};

/**
 * تحويل المسافات بناءً على حجم الشاشة
 */
export const responsiveSpacing = (spacing) => {
  const deviceType = getDeviceType();

  switch (deviceType) {
    case DEVICE_TYPES.PHONE_SMALL:
      return spacing * 0.8;
    case DEVICE_TYPES.PHONE_MEDIUM:
      return spacing;
    case DEVICE_TYPES.PHONE_LARGE:
      return spacing * 1.1;
    case DEVICE_TYPES.TABLET_SMALL:
      return spacing * 1.3;
    case DEVICE_TYPES.TABLET_LARGE:
      return spacing * 1.5;
    default:
      return spacing;
  }
};

/**
 * الحصول على عدد الأعمدة المناسب للشبكة
 */
export const getGridColumns = (minItemWidth = 150) => {
  const availableWidth = SCREEN_WIDTH - 32; // مع padding
  const columns = Math.floor(availableWidth / minItemWidth);
  return Math.max(1, columns);
};

/**
 * الحصول على عرض العنصر في الشبكة
 */
export const getGridItemWidth = (columns, spacing = 16) => {
  const totalSpacing = spacing * (columns - 1);
  const availableWidth = SCREEN_WIDTH - 32 - totalSpacing; // مع padding
  return availableWidth / columns;
};

/**
 * إعدادات متجاوبة للخطوط
 */
export const RESPONSIVE_FONTS = {
  xs: responsiveFontSize(12),
  sm: responsiveFontSize(14),
  md: responsiveFontSize(16),
  lg: responsiveFontSize(18),
  xl: responsiveFontSize(20),
  xxl: responsiveFontSize(24),
  xxxl: responsiveFontSize(32),
};

/**
 * إعدادات متجاوبة للمسافات
 */
export const RESPONSIVE_SPACING = {
  xs: responsiveSpacing(4),
  sm: responsiveSpacing(8),
  md: responsiveSpacing(16),
  lg: responsiveSpacing(24),
  xl: responsiveSpacing(32),
  xxl: responsiveSpacing(48),
  xxxl: responsiveSpacing(64),
};

/**
 * إعدادات Safe Area بناءً على الجهاز
 */
export const getSafeAreaPadding = () => {
  if (Platform.OS === "ios") {
    const ratio = SCREEN_HEIGHT / SCREEN_WIDTH;
    // للهواتف الحديثة مع notch
    if (ratio >= 2.0) {
      return {
        top: hp(5),
        bottom: hp(3),
      };
    }
  }

  return {
    top: hp(2),
    bottom: hp(1),
  };
};

/**
 * حجم الأيقونات المتجاوب
 */
export const getIconSize = (size = "md") => {
  const sizes = {
    xs: 16,
    sm: 20,
    md: 24,
    lg: 28,
    xl: 32,
    xxl: 40,
  };

  const baseSize = sizes[size] || sizes.md;
  return responsiveFontSize(baseSize);
};

/**
 * معلومات الشاشة والجهاز
 */
export const getScreenInfo = () => {
  return {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    deviceType: getDeviceType(),
    aspectRatio: getAspectRatio(),
    isPhone: isPhone(),
    isTablet: isTablet(),
    isSmallScreen: isSmallScreen(),
    isLargeScreen: isLargeScreen(),
    pixelRatio: PixelRatio.get(),
    fontScale: PixelRatio.getFontScale(),
  };
};

// تصدير أبعاد الشاشة
export { SCREEN_WIDTH, SCREEN_HEIGHT };
