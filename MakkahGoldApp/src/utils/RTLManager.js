/**
 * React Native RTL Configuration
 * تكوين React Native لدعم RTL القوي
 */

import { I18nManager, Platform } from "react-native";
import * as Localization from "expo-localization";
import Constants from "expo-constants";

class RTLManager {
  static isInitialized = false;
  static isExpoGo = Constants.appOwnership === "expo";
  static manualRTL = false; // للتتبع اليدوي في Expo Go

  /**
   * تهيئة RTL بقوة عند بدء التطبيق
   */
  static initialize() {
    if (RTLManager.isInitialized) {
      return;
    }

    try {
      // بدء تهيئة RTL (تم حذف الرسائل للتنظيف)

      // للتحقق من بيئة التشغيل
      if (RTLManager.isExpoGo) {
        // يعمل في Expo Go - سيتم استخدام RTL محاكي
      }

      // محاولة تفعيل RTL الحقيقي
      if (!RTLManager.isExpoGo) {
        // في standalone app
        I18nManager.allowRTL(true);
        I18nManager.forceRTL(true);

        // إعدادات خاصة بـ Android
        if (Platform.OS === "android") {
          I18nManager.swapLeftAndRightInRTL(true);
        }
      } else {
        // في Expo Go - استخدام حل بديل
        I18nManager.allowRTL(true);
        RTLManager.manualRTL = true; // تفعيل RTL اليدوي
      }

      // التحقق من نجاح التطبيق
      if (I18nManager.isRTL) {
        // تم تفعيل RTL الأصلي بنجاح
      } else if (RTLManager.isExpoGo) {
        // تم تفعيل RTL في Expo Go (وضع محاكي)
      } else {
        // سيتم استخدام RTL يدوي
      }

      RTLManager.isInitialized = true;
    } catch (error) {
      // خطأ في تهيئة RTL - تم حذف الرسالة للتنظيف
      // حتى لو فشل، سنستخدم RTL يدوي
      RTLManager.isInitialized = true;
    }
  }

  /**
   * التحقق من حالة RTL
   */
  static checkRTLStatus() {
    const locales = Localization.getLocales();
    const isArabicLocale = locales.some(
      (locale) => locale.languageCode === "ar"
    );

    return {
      isRTL: I18nManager.isRTL,
      isArabicLocale,
      allowRTL: true,
      forceRTL: true,
      swapLeftAndRight: Platform.OS === "android",
      needsRestart: Platform.OS === "android" && !I18nManager.isRTL,
      platform: Platform.OS,
      locales: locales.map((l) => l.languageCode),
    };
  }

  /**
   * إجبار إعادة تطبيق RTL (في حالة الفشل)
   */
  static forceRTL() {
    try {
      I18nManager.allowRTL(true);

      // في Expo Go، forceRTL قد لا يعمل
      if (!RTLManager.isExpoGo) {
        I18nManager.forceRTL(true);

        if (Platform.OS === "android") {
          I18nManager.swapLeftAndRightInRTL(true);
        }
      }

      return RTLManager.isRTLActive();
    } catch (error) {
      // خطأ في إجبار RTL - تم حذف الرسالة للتنظيف
      return true; // نعتبره active للـ manual RTL
    }
  }

  /**
   * تفعيل RTL يدوياً (خاص بـ Expo Go)
   */
  static enableManualRTL() {
    if (RTLManager.isExpoGo) {
      RTLManager.manualRTL = true;
      console.log("✅ تم تفعيل RTL يدوياً في Expo Go");
      return true;
    }
    return false;
  }

  /**
   * إلغاء RTL اليدوي (خاص بـ Expo Go)
   */
  static disableManualRTL() {
    if (RTLManager.isExpoGo) {
      RTLManager.manualRTL = false;
      console.log("❌ تم إلغاء RTL اليدوي في Expo Go");
      return true;
    }
    return false;
  }

  /**
   * تحديد ما إذا كان RTL نشطاً (يعمل في Expo Go وغيره)
   */
  static isRTLActive() {
    // في Expo Go، نستخدم RTL يدوي للعربية
    if (RTLManager.isExpoGo) {
      // التحقق من اللغة العربية أو التفعيل اليدوي
      const locales = Localization.getLocales();
      const hasArabic = locales.some((locale) => locale.languageCode === "ar");
      return RTLManager.manualRTL || hasArabic || I18nManager.isRTL;
    }

    return I18nManager.isRTL;
  }

  /**
   * الحصول على اتجاه النص المناسب
   */
  static getTextDirection() {
    return RTLManager.isRTLActive() ? "rtl" : "ltr";
  }

  /**
   * الحصول على محاذاة النص المناسبة
   */
  static getTextAlign(defaultAlign = "auto") {
    if (defaultAlign === "center") return "center";
    if (defaultAlign === "left" || defaultAlign === "right")
      return defaultAlign;
    return RTLManager.isRTLActive() ? "right" : "left";
  }

  /**
   * الحصول على اتجاه flex المناسب
   */
  static getFlexDirection(direction = "row") {
    if (!RTLManager.isRTLActive()) return direction;

    switch (direction) {
      case "row":
        return "row-reverse";
      case "row-reverse":
        return "row";
      default:
        return direction;
    }
  }

  /**
   * تحويل المسافات للـ RTL
   */
  static transformSpacing(spacing) {
    if (!RTLManager.isRTLActive()) return spacing;

    const transformed = { ...spacing };

    // تبديل اليسار واليمين
    if (spacing.marginLeft !== undefined || spacing.marginRight !== undefined) {
      transformed.marginLeft = spacing.marginRight;
      transformed.marginRight = spacing.marginLeft;
    }

    if (
      spacing.paddingLeft !== undefined ||
      spacing.paddingRight !== undefined
    ) {
      transformed.paddingLeft = spacing.paddingRight;
      transformed.paddingRight = spacing.paddingLeft;
    }

    if (spacing.left !== undefined || spacing.right !== undefined) {
      transformed.left = spacing.right;
      transformed.right = spacing.left;
    }

    return transformed;
  }
}

// تهيئة تلقائية عند استيراد الملف
RTLManager.initialize();

export default RTLManager;
