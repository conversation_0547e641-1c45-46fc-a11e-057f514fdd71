/**
 * إعدادات شاشة البداية المخصصة
 * Custom Splash Screen Configuration
 */

import * as SplashScreen from "expo-splash-screen";

// إعدادات لإخفاء شاشة البداية الافتراضية فوراً
export const configureSplashScreen = async () => {
  try {
    // منع الإخفاء التلقائي أولاً
    await SplashScreen.preventAutoHideAsync();

    // ثم إخفاء الشاشة فوراً لتجنب الشاشة البيضاء
    setTimeout(async () => {
      try {
        await SplashScreen.hideAsync();
      } catch (error) {
        console.warn("خطأ في إخفاء شاشة البداية:", error);
      }
    }, 10); // إخفاء بعد 10ms فقط
  } catch (error) {
    console.warn("خطأ في إعداد شاشة البداية:", error);
    // في حالة الخطأ، حاول الإخفاء مباشرة
    try {
      await SplashScreen.hideAsync();
    } catch (hideError) {
      console.warn("خطأ في الإخفاء الاضطراري:", hideError);
    }
  }
};

export default configureSplashScreen;
