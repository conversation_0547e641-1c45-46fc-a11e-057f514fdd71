// Hook لإدارة بيانات السبائك والعملات
import { useState, useEffect, useCallback } from "react";
import { apiService } from "../../services/api";

export const useBarsCoinsData = (selectedType = "سبيكة") => {
  const [companies, setCompanies] = useState([]);
  const [productTypes, setProductTypes] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [calculatedPrices, setCalculatedPrices] = useState(null);
  const [currentPrices, setCurrentPrices] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل الشركات
  const loadCompanies = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const companies = await apiService.getBarsCoinsCompanies(selectedType);
      setCompanies(companies || []);
    } catch (err) {
      console.error("Error loading companies:", err);
      setError(err.message || "حدث خطأ في تحميل قائمة الشركات");
      setCompanies([]);
    } finally {
      setLoading(false);
    }
  }, [selectedType]);

  // تحميل أنواع المنتجات
  const loadProductTypes = useCallback(
    async (companyId = null) => {
      try {
        setError(null);
        const productTypes = await apiService.getBarsCoinsProductTypes(
          selectedType,
          companyId
        );
        setProductTypes(productTypes || []);
      } catch (err) {
        console.error("Error loading product types:", err);
        setError(err.message || "حدث خطأ في تحميل أنواع المنتجات");
        setProductTypes([]);
      }
    },
    [selectedType]
  );

  // تحميل تفاصيل المنتج المحدد
  const loadSelectedProduct = useCallback(async (companyId, productTypeId) => {
    try {
      setError(null);
      const selectedProduct = await apiService.getBarsCoinsCompanyProduct(
        companyId,
        productTypeId
      );
      setSelectedProduct(selectedProduct || null);
    } catch (err) {
      console.error("Error loading selected product:", err);
      setError(err.message || "حدث خطأ في تحميل تفاصيل المنتج");
      setSelectedProduct(null);
    }
  }, []);

  // حساب أسعار المنتج
  const calculatePrices = useCallback(async (companyId, productTypeId) => {
    try {
      setError(null);
      const calculatedPrices = await apiService.calculateBarsCoinsProductPrices(
        companyId,
        productTypeId
      );
      setCalculatedPrices(calculatedPrices || null);
    } catch (err) {
      console.error("Error calculating prices:", err);
      setError(err.message || "حدث خطأ في حساب أسعار المنتج");
      setCalculatedPrices(null);
    }
  }, []);

  // تحميل أسعار المعادن الحالية
  const loadCurrentPrices = useCallback(async () => {
    try {
      setError(null);
      const currentPrices = await apiService.getBarsCoinsCurrentPrices();
      setCurrentPrices(currentPrices || null);
    } catch (err) {
      console.error("Error loading current prices:", err);
      setError(err.message || "حدث خطأ في تحميل أسعار المعادن");
      setCurrentPrices(null);
    }
  }, []);

  // إعادة تحميل جميع البيانات
  const refetch = useCallback(async () => {
    await Promise.all([
      loadCompanies(),
      loadProductTypes(),
      loadCurrentPrices(),
    ]);
  }, [loadCompanies, loadProductTypes, loadCurrentPrices]);

  // تحميل البيانات الأولية
  useEffect(() => {
    const initializeData = async () => {
      await Promise.all([
        loadCompanies(),
        loadProductTypes(),
        loadCurrentPrices(),
      ]);
    };

    initializeData();
  }, [selectedType, loadCompanies, loadProductTypes, loadCurrentPrices]);

  return {
    companies,
    productTypes,
    selectedProduct,
    calculatedPrices,
    currentPrices,
    loading,
    error,
    refetch,
    loadProductTypes,
    loadSelectedProduct,
    calculatePrices,
    loadCurrentPrices,
  };
};
