import { useMemo } from "react";
import { Dimensions, PixelRatio } from "react-native";

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

export const useResponsive = () => {
  const responsive = useMemo(() => {
    // دالة لحساب عرض نسبي
    const wp = (percentage) => {
      const value = (percentage * screenWidth) / 100;
      return Math.round(PixelRatio.roundToNearestPixel(value));
    };

    // دالة لحساب ارتفاع نسبي
    const hp = (percentage) => {
      const value = (percentage * screenHeight) / 100;
      return Math.round(PixelRatio.roundToNearestPixel(value));
    };

    // تحديد نوع الجهاز
    const isSmallDevice = screenWidth < 375;
    const isMediumDevice = screenWidth >= 375 && screenWidth < 414;
    const isLargeDevice = screenWidth >= 414;
    const isTablet = screenWidth >= 768;

    return {
      wp,
      hp,
      screenWidth,
      screenHeight,
      isSmallDevice,
      isMediumDevice,
      isLargeDevice,
      isTablet,
    };
  }, []);

  return responsive;
};
