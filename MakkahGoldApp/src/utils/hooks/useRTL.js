// Hook للتحكم في RTL - RTL Hook
import { useEffect, useState } from "react";
import { I18nManager, Platform } from "react-native";
import * as Updates from "expo-updates";

const useRTL = () => {
  const [isRTL, setIsRTL] = useState(I18nManager.isRTL);
  const [needsRestart, setNeedsRestart] = useState(false);

  const forceRTL = async () => {
    try {
      if (!I18nManager.isRTL) {
        I18nManager.allowRTL(true);
        I18nManager.forceRTL(true);

        if (Platform.OS === "android") {
          I18nManager.swapLeftAndRightInRTL(true);
        }

        // تحديد ما إذا كان يحتاج إعادة تشغيل
        setNeedsRestart(Platform.OS === "android");
        setIsRTL(true);

        return true;
      }
      return false;
    } catch (error) {
      console.error("خطأ في تفعيل RTL:", error);
      return false;
    }
  };

  const restartApp = async () => {
    try {
      if (needsRestart && Platform.OS === "android") {
        await Updates.reloadAsync();
      }
    } catch (error) {
      console.error("خطأ في إعادة تشغيل التطبيق:", error);
    }
  };

  useEffect(() => {
    // التأكد من RTL عند تحميل التطبيق
    if (!I18nManager.isRTL) {
      forceRTL();
    }
  }, []);

  return {
    isRTL,
    forceRTL,
    needsRestart,
    restartApp,
    // دوال مساعدة
    getFlexDirection: (direction = "row") => {
      if (!isRTL) return direction;
      switch (direction) {
        case "row":
          return "row-reverse";
        case "row-reverse":
          return "row";
        default:
          return direction;
      }
    },
    getTextAlign: (align = "auto") => {
      if (align === "center") return "center";
      if (align === "left" || align === "right") return align;
      return isRTL ? "right" : "left";
    },
    getWritingDirection: () => (isRTL ? "rtl" : "ltr"),
  };
};

export default useRTL;
