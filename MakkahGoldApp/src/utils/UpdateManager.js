/**
 * مدير التحديثات التلقائية - EAS Updates Manager
 * يدعم Android API level 28 وما دون
 */

import * as Updates from "expo-updates";
import { Platform, Alert } from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Constants from "expo-constants";
import AndroidCompatibility from "./AndroidCompatibility";

class UpdateManager {
  static isInitialized = false;
  static updateCheckInterval = null;
  static lastUpdateCheck = null;
  static isExpoGo = Constants.appOwnership === "expo";

  // مفاتيح التخزين المحلي
  static STORAGE_KEYS = {
    LAST_UPDATE_CHECK: "@makkah_gold_last_update_check",
    UPDATE_SETTINGS: "@makkah_gold_update_settings",
    PENDING_UPDATE: "@makkah_gold_pending_update",
  };

  /**
   * تهيئة مدير التحديثات
   */
  static async initialize() {
    if (UpdateManager.isInitialized) {
      return;
    }

    try {
      // تهيئة مدير التوافق مع Android
      AndroidCompatibility.initialize();

      // التحقق من بيئة التشغيل
      if (UpdateManager.isExpoGo) {
        // EAS Updates غير مدعوم في Expo Go - يحتاج إلى بناء مستقل
        return;
      }

      // التحقق من بيئة التطوير
      if (__DEV__) {
        // EAS Updates غير مدعوم في development builds
        return;
      }

      // التحقق من دعم التحديثات
      if (!Updates.isEnabled) {
        // EAS Updates غير مفعل في هذا البناء
        return;
      }

      // التحقق من التوافق مع Android API level
      if (!AndroidCompatibility.isFeatureSupported("eas_updates")) {
        // EAS Updates غير مدعوم على هذا الإصدار من Android
        return;
      }

      // تحميل إعدادات التحديث المحفوظة
      await UpdateManager.loadUpdateSettings();

      // التحقق من التحديثات عند بدء التطبيق
      await UpdateManager.checkForUpdatesOnStartup();

      // بدء التحقق الدوري من التحديثات (إذا كان مدعوماً)
      if (AndroidCompatibility.canRunBackgroundUpdates()) {
        UpdateManager.startPeriodicUpdateCheck();
      }

      UpdateManager.isInitialized = true;

      if (__DEV__) {
        console.log(
          "مدير التحديثات: تم التهيئة (وضع التطوير - تم تعطيل التحديثات)"
        );
      } else {
        console.log("تم تهيئة مدير التحديثات بنجاح");
      }

      // عرض رسالة التوافق إذا لزم الأمر
      const compatibilityMessage =
        AndroidCompatibility.getCompatibilityMessage();
      if (compatibilityMessage) {
        setTimeout(() => {
          Alert.alert(
            compatibilityMessage.title,
            compatibilityMessage.message,
            [{ text: "حسناً" }]
          );
        }, 2000);
      }
    } catch (error) {
      console.error("خطأ في تهيئة مدير التحديثات:", error);
    }
  }

  /**
   * تحميل إعدادات التحديث من التخزين المحلي
   */
  static async loadUpdateSettings() {
    try {
      const settings = await AsyncStorage.getItem(
        UpdateManager.STORAGE_KEYS.UPDATE_SETTINGS
      );
      const lastCheck = await AsyncStorage.getItem(
        UpdateManager.STORAGE_KEYS.LAST_UPDATE_CHECK
      );

      if (settings) {
        UpdateManager.updateSettings = JSON.parse(settings);
      } else {
        // الإعدادات الافتراضية المتوافقة مع Android API level
        UpdateManager.updateSettings =
          AndroidCompatibility.getCompatibleUpdateSettings();
        await UpdateManager.saveUpdateSettings();
      }

      if (lastCheck) {
        UpdateManager.lastUpdateCheck = new Date(lastCheck);
      }
    } catch (error) {
      console.error("خطأ في تحميل إعدادات التحديث:", error);
    }
  }

  /**
   * حفظ إعدادات التحديث
   */
  static async saveUpdateSettings() {
    try {
      await AsyncStorage.setItem(
        UpdateManager.STORAGE_KEYS.UPDATE_SETTINGS,
        JSON.stringify(UpdateManager.updateSettings)
      );
    } catch (error) {
      console.error("خطأ في حفظ إعدادات التحديث:", error);
    }
  }

  /**
   * التحقق من التحديثات عند بدء التطبيق
   */
  static async checkForUpdatesOnStartup() {
    try {
      // تجاهل في بيئة التطوير
      if (__DEV__) {
        return;
      }

      if (!UpdateManager.updateSettings?.autoCheck) {
        return;
      }

      // التحقق من وجود تحديث معلق
      const pendingUpdate = await AsyncStorage.getItem(
        UpdateManager.STORAGE_KEYS.PENDING_UPDATE
      );
      if (pendingUpdate) {
        const updateInfo = JSON.parse(pendingUpdate);
        UpdateManager.showUpdateAvailableDialog(updateInfo);
        return;
      }

      // التحقق من التحديثات الجديدة
      await UpdateManager.checkForUpdates(false);
    } catch (error) {
      console.error("خطأ في التحقق من التحديثات عند البدء:", error);
    }
  }

  /**
   * التحقق من التحديثات
   * @param {boolean} showNoUpdateMessage - عرض رسالة عدم وجود تحديثات
   */
  static async checkForUpdates(showNoUpdateMessage = true) {
    try {
      // التحقق من بيئة التشغيل
      if (UpdateManager.isExpoGo) {
        if (showNoUpdateMessage) {
          Alert.alert(
            "تنبيه",
            "التحديثات غير متاحة في Expo Go. يرجى استخدام البناء المستقل (APK) لاختبار التحديثات.",
            [{ text: "حسناً" }]
          );
        }
        return;
      }

      // التحقق من بيئة التطوير
      if (__DEV__) {
        if (showNoUpdateMessage) {
          // رسالة مخفية في التطوير لتجنب الإزعاج
        }
        return;
      }

      if (!Updates.isEnabled) {
        if (showNoUpdateMessage) {
          Alert.alert("تنبيه", "التحديثات غير متاحة في هذا الإصدار");
        }
        return;
      }

      console.log("جاري التحقق من التحديثات...");

      let update;
      try {
        update = await Updates.checkForUpdateAsync();
      } catch (checkError) {
        // معالجة خطأ التحقق من التحديثات
        if (checkError.message?.includes("development builds")) {
          // هذا خطأ متوقع في development builds
          return;
        }
        throw checkError; // رمي الخطأ مرة أخرى إذا لم يكن متوقعاً
      }

      // تحديث وقت آخر فحص
      UpdateManager.lastUpdateCheck = new Date();
      await AsyncStorage.setItem(
        UpdateManager.STORAGE_KEYS.LAST_UPDATE_CHECK,
        UpdateManager.lastUpdateCheck.toISOString()
      );

      if (update.isAvailable) {
        console.log("تحديث متاح:", update);

        if (UpdateManager.updateSettings?.autoDownload) {
          await UpdateManager.downloadAndInstallUpdate(update);
        } else {
          // حفظ معلومات التحديث وعرض حوار
          await AsyncStorage.setItem(
            UpdateManager.STORAGE_KEYS.PENDING_UPDATE,
            JSON.stringify(update)
          );
          UpdateManager.showUpdateAvailableDialog(update);
        }
      } else {
        console.log("لا توجد تحديثات متاحة");
        if (showNoUpdateMessage) {
          Alert.alert("التحديثات", "تطبيقك محدث إلى أحدث إصدار");
        }
      }
    } catch (error) {
      // معالجة محسنة للأخطاء
      if (error.message?.includes("development builds")) {
        // هذا خطأ متوقع في development builds - لا نعرض رسالة
        return;
      }

      console.error("خطأ في التحقق من التحديثات:", error);
      if (showNoUpdateMessage && !__DEV__) {
        // عرض رسالة الخطأ فقط في production
        Alert.alert("خطأ", "حدث خطأ أثناء التحقق من التحديثات");
      }
    }
  }

  /**
   * تحميل وتثبيت التحديث
   */
  static async downloadAndInstallUpdate(updateInfo) {
    try {
      console.log("جاري تحميل التحديث...");

      // عرض مؤشر التحميل
      Alert.alert("تحديث التطبيق", "جاري تحميل التحديث، يرجى الانتظار...", [], {
        cancelable: false,
      });

      const downloadResult = await Updates.fetchUpdateAsync();

      if (downloadResult.isNew) {
        console.log("تم تحميل التحديث بنجاح");

        // إزالة التحديث المعلق
        await AsyncStorage.removeItem(
          UpdateManager.STORAGE_KEYS.PENDING_UPDATE
        );

        // عرض حوار إعادة التشغيل
        Alert.alert(
          "تحديث جاهز",
          "تم تحميل التحديث بنجاح. هل تريد إعادة تشغيل التطبيق الآن؟",
          [
            {
              text: "لاحقاً",
              style: "cancel",
            },
            {
              text: "إعادة التشغيل",
              onPress: () => Updates.reloadAsync(),
            },
          ]
        );
      }
    } catch (error) {
      console.error("خطأ في تحميل التحديث:", error);
      Alert.alert("خطأ", "حدث خطأ أثناء تحميل التحديث");
    }
  }

  /**
   * عرض حوار التحديث المتاح
   */
  static showUpdateAvailableDialog(updateInfo) {
    Alert.alert(
      "تحديث متاح",
      "يتوفر تحديث جديد للتطبيق. هل تريد تحميله الآن؟",
      [
        {
          text: "لاحقاً",
          style: "cancel",
        },
        {
          text: "تحميل",
          onPress: () => UpdateManager.downloadAndInstallUpdate(updateInfo),
        },
      ]
    );
  }

  /**
   * بدء التحقق الدوري من التحديثات
   */
  static startPeriodicUpdateCheck() {
    // تجاهل في بيئة التطوير
    if (__DEV__) {
      return;
    }

    if (UpdateManager.updateCheckInterval) {
      clearInterval(UpdateManager.updateCheckInterval);
    }

    if (!UpdateManager.updateSettings?.autoCheck) {
      return;
    }

    const interval =
      UpdateManager.updateSettings.checkInterval || 30 * 60 * 1000;

    UpdateManager.updateCheckInterval = setInterval(() => {
      UpdateManager.checkForUpdates(false);
    }, interval);

    console.log(
      `تم بدء التحقق الدوري من التحديثات كل ${interval / 1000 / 60} دقيقة`
    );
  }

  /**
   * إيقاف التحقق الدوري من التحديثات
   */
  static stopPeriodicUpdateCheck() {
    if (UpdateManager.updateCheckInterval) {
      clearInterval(UpdateManager.updateCheckInterval);
      UpdateManager.updateCheckInterval = null;
      console.log("تم إيقاف التحقق الدوري من التحديثات");
    }
  }

  /**
   * تحديث إعدادات التحديث
   */
  static async updateSettings(newSettings) {
    try {
      UpdateManager.updateSettings = {
        ...UpdateManager.updateSettings,
        ...newSettings,
      };
      await UpdateManager.saveUpdateSettings();

      // إعادة تشغيل التحقق الدوري إذا تغيرت الإعدادات
      if (
        newSettings.autoCheck !== undefined ||
        newSettings.checkInterval !== undefined
      ) {
        UpdateManager.startPeriodicUpdateCheck();
      }
    } catch (error) {
      console.error("خطأ في تحديث إعدادات التحديث:", error);
    }
  }

  /**
   * الحصول على معلومات التحديث الحالية
   */
  static getUpdateInfo() {
    return {
      isEnabled: Updates.isEnabled && !UpdateManager.isExpoGo,
      channel: Updates.channel,
      runtimeVersion: Updates.runtimeVersion,
      lastUpdateCheck: UpdateManager.lastUpdateCheck,
      settings: UpdateManager.updateSettings,
      compatibility: AndroidCompatibility.getSystemInfo(),
      environment: {
        isExpoGo: UpdateManager.isExpoGo,
        appOwnership: Constants.appOwnership,
        supportsUpdates: Updates.isEnabled && !UpdateManager.isExpoGo,
      },
    };
  }

  /**
   * فرض التحقق من التحديثات
   */
  static async forceCheckForUpdates() {
    return await UpdateManager.checkForUpdates(true);
  }

  /**
   * تنظيف الموارد
   */
  static cleanup() {
    UpdateManager.stopPeriodicUpdateCheck();
    UpdateManager.isInitialized = false;
  }
}

export default UpdateManager;
