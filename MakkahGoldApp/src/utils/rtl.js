// مساعدات RTL - RTL Helper Functions
import { I18nManager } from 'react-native';

/**
 * تحديد اتجاه flexDirection حسب RTL
 * @param {string} direction - الاتجاه المطلوب ('row', 'row-reverse', 'column', 'column-reverse')
 * @returns {string} الاتجاه المناسب حسب RTL
 */
export const getFlexDirection = (direction = 'row') => {
  if (!I18nManager.isRTL) return direction;
  
  switch (direction) {
    case 'row':
      return 'row-reverse';
    case 'row-reverse':
      return 'row';
    default:
      return direction;
  }
};

/**
 * تحديد اتجاه النص حسب RTL
 * @param {string} align - الاتجاه المطلوب ('left', 'right', 'center')
 * @returns {string} الاتجاه المناسب حسب RTL
 */
export const getTextAlign = (align) => {
  if (align === 'center') return 'center';
  if (!I18nManager.isRTL) return align || 'left';
  
  switch (align) {
    case 'left':
      return 'right';
    case 'right':
      return 'left';
    default:
      return 'right'; // افتراضي للعربية
  }
};

/**
 * تحديد المسافات حسب RTL
 * @param {object} spacing - كائن المسافات {left, right, top, bottom}
 * @returns {object} المسافات المناسبة حسب RTL
 */
export const getRTLSpacing = (spacing) => {
  if (!I18nManager.isRTL) return spacing;
  
  return {
    ...spacing,
    marginLeft: spacing.marginRight,
    marginRight: spacing.marginLeft,
    paddingLeft: spacing.paddingRight,
    paddingRight: spacing.paddingLeft,
  };
};

/**
 * تحديد موضع العنصر حسب RTL
 * @param {object} position - كائن الموضع {left, right, top, bottom}
 * @returns {object} الموضع المناسب حسب RTL
 */
export const getRTLPosition = (position) => {
  if (!I18nManager.isRTL) return position;
  
  return {
    ...position,
    left: position.right,
    right: position.left,
  };
};

/**
 * تحديد اتجاه الأيقونة حسب RTL
 * @param {string} iconName - اسم الأيقونة
 * @returns {string} اسم الأيقونة المناسب حسب RTL
 */
export const getRTLIcon = (iconName) => {
  if (!I18nManager.isRTL) return iconName;
  
  const rtlIconMap = {
    'arrow-forward': 'arrow-back',
    'arrow-back': 'arrow-forward',
    'chevron-left': 'chevron-right',
    'chevron-right': 'chevron-left',
    'keyboard-arrow-left': 'keyboard-arrow-right',
    'keyboard-arrow-right': 'keyboard-arrow-left',
    'navigate-next': 'navigate-before',
    'navigate-before': 'navigate-next',
  };
  
  return rtlIconMap[iconName] || iconName;
};

/**
 * إنشاء styles متوافقة مع RTL
 * @param {object} styles - كائن الـ styles
 * @returns {object} الـ styles المناسبة حسب RTL
 */
export const createRTLStyles = (styles) => {
  if (!I18nManager.isRTL) return styles;
  
  const rtlStyles = {};
  
  Object.keys(styles).forEach(key => {
    const style = styles[key];
    rtlStyles[key] = {
      ...style,
      ...getRTLSpacing(style),
      ...getRTLPosition(style),
      flexDirection: style.flexDirection ? getFlexDirection(style.flexDirection) : undefined,
      textAlign: style.textAlign ? getTextAlign(style.textAlign) : undefined,
    };
  });
  
  return rtlStyles;
};

/**
 * تحديد اتجاه التمرير حسب RTL
 * @param {string} direction - اتجاه التمرير ('horizontal', 'vertical')
 * @returns {object} إعدادات التمرير المناسبة حسب RTL
 */
export const getScrollDirection = (direction = 'vertical') => {
  return {
    horizontal: direction === 'horizontal',
    showsHorizontalScrollIndicator: direction === 'horizontal',
    showsVerticalScrollIndicator: direction === 'vertical',
    // عكس اتجاه التمرير الأفقي في RTL
    inverted: I18nManager.isRTL && direction === 'horizontal',
  };
};

/**
 * تحديد إعدادات الـ animation حسب RTL
 * @param {string} animationType - نوع الحركة
 * @returns {string} نوع الحركة المناسب حسب RTL
 */
export const getRTLAnimation = (animationType) => {
  if (!I18nManager.isRTL) return animationType;
  
  const rtlAnimationMap = {
    'slide_from_right': 'slide_from_left',
    'slide_from_left': 'slide_from_right',
    'slide_from_bottom': 'slide_from_bottom',
    'slide_from_top': 'slide_from_top',
  };
  
  return rtlAnimationMap[animationType] || animationType;
};

/**
 * التحقق من حالة RTL
 * @returns {boolean} true إذا كان RTL مفعل
 */
export const isRTL = () => I18nManager.isRTL;

/**
 * تحديد اتجاه الكتابة
 * @returns {string} 'rtl' أو 'ltr'
 */
export const getWritingDirection = () => I18nManager.isRTL ? 'rtl' : 'ltr';

/**
 * تحديد اللغة الافتراضية
 * @returns {string} كود اللغة
 */
export const getDefaultLanguage = () => I18nManager.isRTL ? 'ar' : 'en';
