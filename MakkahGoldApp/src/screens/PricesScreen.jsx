// شاشة أسعار المعادن - Prices Screen

import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  I18nManager,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, FONTS, ROUTES } from "../constants";
import { RESPONSIVE_SPACING } from "../utils/responsive";
import { useCurrentPrices } from "../utils/hooks";
import Loading from "../components/Loading";
import ErrorMessage from "../components/ErrorMessage";
import MetalPriceCard from "../components/MetalPriceCard";

const PricesScreen = ({ navigation }) => {
  const { data: prices, loading, error, refetch } = useCurrentPrices();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedMetal, setSelectedMetal] = useState(null);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const handlePricePress = (metalType, purity) => {
    const metalGroup = prices?.find((group) => group.metal_type === metalType);
    const priceData = metalGroup?.prices.find((p) => p.purity === purity);

    if (metalGroup && priceData) {
      navigation.navigate(ROUTES.PRICE_HISTORY, {
        metalType,
        purity,
        metalName: metalGroup.metal_name,
        purityName: priceData.purity_name,
      });
    }
  };

  const getLastUpdateTime = () => {
    if (!prices || prices.length === 0) return "";

    const firstPrice = prices[0]?.prices[0];
    if (!firstPrice) return "";

    try {
      const date = new Date(firstPrice.last_updated);
      return date.toLocaleString("ar-EG", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return firstPrice.last_updated_human || "";
    }
  };

  const filteredPrices = selectedMetal
    ? prices?.filter((group) => group.metal_type === selectedMetal)
    : prices;

  if (loading) {
    return <Loading text="جاري تحميل أسعار المعادن..." />;
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={refetch} />;
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>أسعار المعادن</Text>
        <Text style={styles.lastUpdate}>آخر تحديث: {getLastUpdateTime()}</Text>
      </View>

      {/* Metal Filter */}
      <View style={styles.filterContainer}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              selectedMetal === null && styles.filterButtonActive,
            ]}
            onPress={() => setSelectedMetal(null)}
          >
            <Text
              style={[
                styles.filterText,
                selectedMetal === null && styles.filterTextActive,
              ]}
            >
              جميع المعادن
            </Text>
          </TouchableOpacity>

          {prices?.map((metalGroup) => (
            <TouchableOpacity
              key={metalGroup.metal_type}
              style={[
                styles.filterButton,
                selectedMetal === metalGroup.metal_type &&
                  styles.filterButtonActive,
              ]}
              onPress={() => setSelectedMetal(metalGroup.metal_type)}
            >
              <MaterialIcons
                name={metalGroup.metal_type === "gold" ? "star" : "circle"}
                size={16}
                color={
                  selectedMetal === metalGroup.metal_type
                    ? COLORS.white
                    : COLORS.primary
                }
                style={styles.filterIcon}
              />
              <Text
                style={[
                  styles.filterText,
                  selectedMetal === metalGroup.metal_type &&
                    styles.filterTextActive,
                ]}
              >
                {metalGroup.metal_name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Prices List */}
      <ScrollView
        style={styles.pricesList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {filteredPrices?.map((metalGroup) => (
          <MetalPriceCard
            key={metalGroup.metal_type}
            metalGroup={metalGroup}
            onPricePress={handlePricePress}
          />
        ))}

        {/* Info Card */}
        <View style={styles.infoCard}>
          <MaterialIcons name="info" size={24} color={COLORS.info} />
          <View style={styles.infoContent}>
            <Text style={styles.infoTitle}>معلومات مهمة</Text>
            <Text style={styles.infoText}>
              • الأسعار محدثة كل 15 دقيقة{"\n"}• جميع الأسعار بالجنيه المصري
              {"\n"}• اضغط على أي سعر لعرض التاريخ والرسم البياني{"\n"}• الأسعار
              قابلة للتغيير حسب السوق
            </Text>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActionsCard}>
          <Text style={styles.quickActionsTitle}>حاسبات سريعة</Text>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() =>
              navigation.navigate(ROUTES.ZAKAT_CALCULATOR, {
                metalType: "gold",
              })
            }
          >
            <MaterialIcons name="calculate" size={20} color={COLORS.primary} />
            <Text style={styles.quickActionText}>حاسبة زكاة الذهب</Text>
            <MaterialIcons
              name="chevron-left"
              size={20}
              color={COLORS.textSecondary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionButton}
            onPress={() =>
              navigation.navigate(ROUTES.ZAKAT_CALCULATOR, {
                metalType: "silver",
              })
            }
          >
            <MaterialIcons name="calculate" size={20} color={COLORS.gray} />
            <Text style={styles.quickActionText}>حاسبة زكاة الفضة</Text>
            <MaterialIcons
              name="chevron-left"
              size={20}
              color={COLORS.textSecondary}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.quickActionButton, styles.lastQuickAction]}
            onPress={() => navigation.navigate(ROUTES.JEWELRY_CALCULATOR)}
          >
            <MaterialIcons name="diamond" size={20} color={COLORS.accent} />
            <Text style={styles.quickActionText}>حاسبة قيمة المجوهرات</Text>
            <MaterialIcons
              name="chevron-left"
              size={20}
              color={COLORS.textSecondary}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    backgroundColor: COLORS.primary,
    padding: RESPONSIVE_SPACING.lg,
    alignItems: "center",
  },
  title: {
    fontSize: FONTS.sizes.xxl,
    fontFamily: "Cairo-Bold",
    color: COLORS.white,
    marginBottom: RESPONSIVE_SPACING.xs,
  },
  lastUpdate: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Regular",
    color: COLORS.white,
    opacity: 0.9,
  },
  filterContainer: {
    backgroundColor: COLORS.white,
    paddingVertical: RESPONSIVE_SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: RESPONSIVE_SPACING.md,
    paddingVertical: RESPONSIVE_SPACING.sm,
    marginHorizontal: RESPONSIVE_SPACING.xs,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  filterButtonActive: {
    backgroundColor: COLORS.primary,
  },
  filterIcon: {
    marginRight: RESPONSIVE_SPACING.xs,
  },
  filterText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Medium",
    color: COLORS.primary,
  },
  filterTextActive: {
    color: COLORS.white,
  },
  pricesList: {
    flex: 1,
  },
  infoCard: {
    flexDirection: "row",
    backgroundColor: COLORS.white,
    margin: RESPONSIVE_SPACING.md,
    padding: RESPONSIVE_SPACING.md,
    borderRadius: 12,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoContent: {
    flex: 1,
    marginLeft: RESPONSIVE_SPACING.md,
  },
  infoTitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: "Cairo-Bold",
    color: COLORS.textPrimary,
    marginBottom: RESPONSIVE_SPACING.sm,
  },
  infoText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Regular",
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  quickActionsCard: {
    backgroundColor: COLORS.white,
    margin: RESPONSIVE_SPACING.md,
    borderRadius: 12,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionsTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: "Cairo-Bold",
    color: COLORS.textPrimary,
    padding: RESPONSIVE_SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  quickActionButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: RESPONSIVE_SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  lastQuickAction: {
    borderBottomWidth: 0,
  },
  quickActionText: {
    flex: 1,
    fontSize: FONTS.sizes.md,
    fontFamily: "Cairo-Medium",
    color: COLORS.textPrimary,
    marginLeft: RESPONSIVE_SPACING.md,
  },
  bottomSpacing: {
    height: RESPONSIVE_SPACING.xl,
  },
});

export default PricesScreen;
