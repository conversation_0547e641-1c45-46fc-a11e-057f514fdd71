// الشاشة الرئيسية - Home Screen

import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { COLORS, FONTS, SPACING, ROUTES } from "../constants";
import { useCurrentPrices, useCategories, useNisabInfo } from "../utils/hooks";
import Loading from "../components/Loading";
import ErrorMessage from "../components/ErrorMessage";
import MetalPriceCard from "../components/MetalPriceCard";

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen = ({ navigation }) => {
  const {
    data: prices,
    loading: pricesLoading,
    error: pricesError,
    refetch: refetchPrices,
  } = useCurrentPrices();

  const {
    data: categories,
    loading: categoriesLoading,
    error: categoriesError,
    refetch: refetchCategories,
  } = useCategories();

  const {
    data: nisabInfo,
    loading: nisabLoading,
    error: nisabError,
    refetch: refetchNisab,
  } = useNisabInfo();

  const [refreshing, setRefreshing] = React.useState(false);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await Promise.all([refetchPrices(), refetchCategories(), refetchNisab()]);
    setRefreshing(false);
  }, [refetchPrices, refetchCategories, refetchNisab]);

  const handlePricePress = (metalType: string, purity: string) => {
    const metalGroup = prices?.find((group) => group.metal_type === metalType);
    const priceData = metalGroup?.prices.find((p) => p.purity === purity);

    if (metalGroup && priceData) {
      navigation.navigate(ROUTES.PRICE_HISTORY, {
        metalType,
        purity,
        metalName: metalGroup.metal_name,
        purityName: priceData.purity_name,
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("ar-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (pricesLoading && categoriesLoading && nisabLoading) {
    return <Loading text="جاري تحميل البيانات..." />;
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.welcomeText}>مرحباً بك في</Text>
        <Text style={styles.appTitle}>مكة الذهبية</Text>
        <Text style={styles.subtitle}>أسعار المعادن الثمينة والحاسبات</Text>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>الحاسبات</Text>
        <View style={styles.actionsGrid}>
          <TouchableOpacity
            style={styles.actionCard}
            onPress={() =>
              navigation.navigate(ROUTES.ZAKAT_CALCULATOR, {
                metalType: "gold",
              })
            }
          >
            <MaterialIcons name="calculate" size={32} color={COLORS.primary} />
            <Text style={styles.actionTitle}>حاسبة الزكاة</Text>
            <Text style={styles.actionSubtitle}>احسب زكاة الذهب والفضة</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionCard}
            onPress={() => navigation.navigate(ROUTES.JEWELRY_CALCULATOR)}
          >
            <MaterialIcons name="diamond" size={32} color={COLORS.primary} />
            <Text style={styles.actionTitle}>قيمة المجوهرات</Text>
            <Text style={styles.actionSubtitle}>احسب قيمة مجوهراتك</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Nisab Info */}
      {nisabInfo && (
        <View style={styles.nisabSection}>
          <Text style={styles.sectionTitle}>نصاب الزكاة</Text>
          <View style={styles.nisabCard}>
            <View style={styles.nisabItem}>
              <MaterialIcons name="star" size={20} color={COLORS.primary} />
              <View style={styles.nisabInfo}>
                <Text style={styles.nisabLabel}>نصاب الذهب</Text>
                <Text style={styles.nisabValue}>
                  {formatCurrency(nisabInfo.gold_nisab.current_value)}
                </Text>
                <Text style={styles.nisabWeight}>
                  ({nisabInfo.gold_nisab.weight_grams} جرام)
                </Text>
              </View>
            </View>

            <View style={styles.nisabItem}>
              <MaterialIcons name="circle" size={20} color={COLORS.gray} />
              <View style={styles.nisabInfo}>
                <Text style={styles.nisabLabel}>نصاب الفضة</Text>
                <Text style={styles.nisabValue}>
                  {formatCurrency(nisabInfo.silver_nisab.current_value)}
                </Text>
                <Text style={styles.nisabWeight}>
                  ({nisabInfo.silver_nisab.weight_grams} جرام)
                </Text>
              </View>
            </View>
          </View>
        </View>
      )}

      {/* Current Prices */}
      <View style={styles.pricesSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>أسعار المعادن الحالية</Text>
          <TouchableOpacity onPress={() => navigation.navigate(ROUTES.PRICES)}>
            <Text style={styles.viewAllText}>عرض الكل</Text>
          </TouchableOpacity>
        </View>

        {pricesError ? (
          <ErrorMessage message={pricesError} onRetry={refetchPrices} />
        ) : prices ? (
          prices.map((metalGroup) => (
            <MetalPriceCard
              key={metalGroup.metal_type}
              metalGroup={metalGroup}
              onPricePress={handlePricePress}
            />
          ))
        ) : (
          <Loading text="جاري تحميل الأسعار..." />
        )}
      </View>

      {/* Featured Products */}
      <View style={styles.productsSection}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>الأقسام والمنتجات</Text>
          <TouchableOpacity
            onPress={() => navigation.navigate(ROUTES.PRODUCTS)}
          >
            <Text style={styles.viewAllText}>عرض الكل</Text>
          </TouchableOpacity>
        </View>

        {categoriesError ? (
          <ErrorMessage message={categoriesError} onRetry={refetchCategories} />
        ) : categories ? (
          categories.slice(0, 3).map((category) => (
            <View key={category.id} style={styles.categorySection}>
              <TouchableOpacity
                style={styles.categoryHeader}
                onPress={() =>
                  navigation.navigate(ROUTES.CATEGORY_PRODUCTS, {
                    categoryId: category.id,
                  })
                }
              >
                <Text style={styles.categoryTitle}>
                  {category.name || category.name_ar}
                </Text>
                <MaterialIcons
                  name="arrow-forward-ios"
                  size={16}
                  color={COLORS.primary}
                />
              </TouchableOpacity>

              {category.products && category.products.length > 0 ? (
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={styles.productsScroll}
                >
                  {category.products.slice(0, 4).map((product) => (
                    <TouchableOpacity
                      key={product.id}
                      style={styles.productCard}
                      onPress={() =>
                        navigation.navigate(ROUTES.PRODUCT_DETAILS, {
                          productId: product.id,
                        })
                      }
                    >
                      <View style={styles.productImagePlaceholder}>
                        <MaterialIcons
                          name="diamond"
                          size={40}
                          color={COLORS.primary}
                        />
                      </View>
                      <Text style={styles.productName} numberOfLines={2}>
                        {product.name || product.name_ar || "منتج"}
                      </Text>
                      {product.show_price ? (
                        <Text style={styles.productPrice}>
                          {formatCurrency(product.price || 0)}
                        </Text>
                      ) : (
                        <Text style={styles.contactPrice}>تواصل للسعر</Text>
                      )}
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              ) : (
                <Text style={styles.noProductsText}>
                  لا توجد منتجات في هذا القسم
                </Text>
              )}
            </View>
          ))
        ) : (
          <Loading text="جاري تحميل الأقسام..." />
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    backgroundColor: COLORS.primary,
    alignItems: "center",
  },
  welcomeText: {
    fontSize: FONTS.sizes.md,
    color: COLORS.white,
    fontFamily: "Cairo-Regular",
  },
  appTitle: {
    fontSize: FONTS.sizes.xxxl,
    color: COLORS.white,
    fontFamily: "Cairo-Bold",
    marginVertical: SPACING.xs,
  },
  subtitle: {
    fontSize: FONTS.sizes.sm,
    color: COLORS.white,
    fontFamily: "Cairo-Regular",
    opacity: 0.9,
  },
  quickActions: {
    padding: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: "Cairo-Bold",
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  actionsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  actionCard: {
    flex: 1,
    backgroundColor: COLORS.white,
    padding: SPACING.md,
    borderRadius: 12,
    alignItems: "center",
    marginHorizontal: SPACING.xs,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionTitle: {
    fontSize: FONTS.sizes.md,
    fontFamily: "Cairo-Bold",
    color: COLORS.textPrimary,
    marginTop: SPACING.sm,
    textAlign: "center",
  },
  actionSubtitle: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Regular",
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
    textAlign: "center",
  },
  nisabSection: {
    padding: SPACING.md,
  },
  nisabCard: {
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  nisabItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: SPACING.md,
  },
  nisabInfo: {
    marginLeft: SPACING.md,
    flex: 1,
  },
  nisabLabel: {
    fontSize: FONTS.sizes.md,
    fontFamily: "Cairo-Medium",
    color: COLORS.textPrimary,
  },
  nisabValue: {
    fontSize: FONTS.sizes.lg,
    fontFamily: "Cairo-Bold",
    color: COLORS.primary,
    marginTop: SPACING.xs,
  },
  nisabWeight: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Regular",
    color: COLORS.textSecondary,
  },
  pricesSection: {
    marginBottom: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: SPACING.md,
    marginBottom: SPACING.sm,
  },
  viewAllText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Medium",
    color: COLORS.primary,
  },
  productsSection: {
    marginBottom: SPACING.lg,
  },
  productCard: {
    width: 140,
    backgroundColor: COLORS.white,
    borderRadius: 12,
    padding: SPACING.md,
    marginLeft: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  productImagePlaceholder: {
    height: 80,
    backgroundColor: COLORS.lightGray,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: SPACING.sm,
  },
  productName: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Medium",
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    textAlign: "center",
  },
  productPrice: {
    fontSize: FONTS.sizes.md,
    fontFamily: "Cairo-Bold",
    color: COLORS.primary,
    textAlign: "center",
  },
  categorySection: {
    marginBottom: SPACING.lg,
    paddingHorizontal: SPACING.md,
  },
  categoryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  categoryTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: "Cairo-Bold",
    color: COLORS.textPrimary,
  },
  productsScroll: {
    paddingVertical: SPACING.xs,
  },
  contactPrice: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Medium",
    color: COLORS.secondary,
    textAlign: "center",
  },
  noProductsText: {
    fontSize: FONTS.sizes.sm,
    fontFamily: "Cairo-Regular",
    color: COLORS.textSecondary,
    textAlign: "center",
    paddingVertical: SPACING.lg,
  },
});

export default HomeScreen;
