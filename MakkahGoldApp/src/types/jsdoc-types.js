// أنواع البيانات - Makkah Gold App Types (JSDoc Format)

/**
 * @typedef {Object} ApiResponse
 * @property {boolean} success
 * @property {string} message
 * @property {*} [data]
 * @property {string} [error]
 */

/**
 * @typedef {Object} PaginatedResponse
 * @property {boolean} success
 * @property {string} message
 * @property {Array} data
 * @property {Object} pagination
 * @property {number} pagination.current_page
 * @property {number} pagination.last_page
 * @property {number} pagination.per_page
 * @property {number} pagination.total
 * @property {number|null} pagination.from
 * @property {number|null} pagination.to
 */

/**
 * @typedef {Object} MetalPrice
 * @property {number} id
 * @property {string} purity
 * @property {string} purity_name
 * @property {number} price_per_gram
 * @property {number} price_per_ounce
 * @property {number} price_per_piece
 * @property {string} currency
 * @property {string} last_updated
 * @property {string} last_updated_human
 */

/**
 * @typedef {Object} MetalPriceGroup
 * @property {string} metal_type
 * @property {string} metal_name
 * @property {MetalPrice[]} prices
 */

/**
 * @typedef {Object} PriceHistory
 * @property {string} date
 * @property {number} price_per_gram
 * @property {number} price_per_ounce
 * @property {number} price_per_piece
 */

/**
 * @typedef {Object} PriceStatistics
 * @property {number} highest
 * @property {number} lowest
 * @property {number} average
 * @property {number} current
 * @property {number} change
 * @property {number} change_percentage
 */

/**
 * @typedef {Object} PriceHistoryResponse
 * @property {string} metal_type
 * @property {string} metal_name
 * @property {string} purity
 * @property {string} purity_name
 * @property {number} period_days
 * @property {PriceStatistics} statistics
 * @property {PriceHistory[]} history
 */

/**
 * @typedef {Object} JewelryCalculationRequest
 * @property {string} metal_type
 * @property {string} purity
 * @property {number} weight
 * @property {string} [unit]
 */

/**
 * @typedef {Object} JewelryCalculationResponse
 * @property {string} metal_type
 * @property {string} metal_name
 * @property {string} purity
 * @property {string} purity_name
 * @property {number} weight
 * @property {string} unit
 * @property {number} price_per_unit
 * @property {number} total_value
 * @property {string} currency
 * @property {string} calculation_date
 */

/**
 * @typedef {Object} ZakatItem
 * @property {string} [name]
 * @property {number} weight
 * @property {string} purity
 */

/**
 * @typedef {Object} ZakatItemDetails
 * @property {string} [name]
 * @property {number} weight
 * @property {string} purity
 * @property {string} purity_name
 * @property {number} purity_percentage
 * @property {number} [pure_gold_weight]
 * @property {number} [pure_silver_weight]
 * @property {number} value
 * @property {number} price_per_gram
 */

/**
 * @typedef {Object} ZakatSummary
 * @property {number} total_weight
 * @property {number} total_value
 * @property {number} nisab_weight
 * @property {number} nisab_value
 * @property {boolean} is_zakat_due
 * @property {string} zakat_rate
 * @property {number} zakat_weight
 * @property {number} zakat_value
 */

/**
 * @typedef {Object} ZakatCalculationRequest
 * @property {ZakatItem[]} items
 */

/**
 * @typedef {Object} ZakatCalculationResponse
 * @property {ZakatItemDetails[]} items
 * @property {ZakatSummary} summary
 * @property {string} currency
 * @property {string} calculation_date
 * @property {string[]} notes
 */

/**
 * @typedef {Object} NisabInfo
 * @property {Object} gold_nisab
 * @property {number} gold_nisab.weight_grams
 * @property {number} gold_nisab.current_value
 * @property {number} gold_nisab.price_per_gram
 * @property {string} gold_nisab.last_updated
 * @property {Object} silver_nisab
 * @property {number} silver_nisab.weight_grams
 * @property {number} silver_nisab.current_value
 * @property {number} silver_nisab.price_per_gram
 * @property {string} silver_nisab.last_updated
 * @property {string} zakat_rate
 * @property {string} currency
 * @property {string[]} general_info
 */

/**
 * @typedef {Object} Category
 * @property {number} id
 * @property {string} name - الاسم العربي كحقل أساسي
 * @property {string} [name_ar]
 * @property {string} [name_en]
 * @property {string} [description] - الوصف العربي كحقل أساسي
 * @property {string} [description_ar]
 * @property {string} slug
 * @property {string|null} [image]
 * @property {boolean} show_price - حقل إجباري لتحديد عرض الأسعار
 * @property {Product[]} products - آخر 4 منتجات من API
 */

/**
 * @typedef {Object} Product
 * @property {number} id
 * @property {string} name - الاسم العربي كحقل أساسي
 * @property {string} [name_ar]
 * @property {string} [name_en]
 * @property {string} [description] - الوصف العربي كحقل أساسي
 * @property {string} [description_ar]
 * @property {string} slug
 * @property {number} price
 * @property {number|null} [old_price]
 * @property {number|null} [discount_percentage]
 * @property {number|null} [weight]
 * @property {string|null} [metal_purity] - للتوافق مع النظام القديم
 * @property {string|null} [material_type] - للتوافق مع النظام القديم
 * @property {boolean} is_featured
 * @property {boolean} [is_active]
 * @property {boolean} show_price - تم نقل show_price إلى المنتج (مطلوب الآن)
 * @property {string|null} [image]
 * @property {string} [created_at] - تاريخ الإنشاء للترتيب
 * @property {Object|null} [metal] - معلومات المعادن الجديدة من الجداول المرجعية
 * @property {number} metal.id
 * @property {string} metal.name
 * @property {string} [metal.name_ar]
 * @property {string} [metal.name_en]
 * @property {Object|null} [metal_purity_info]
 * @property {number} metal_purity_info.id
 * @property {string} metal_purity_info.name
 * @property {string} [metal_purity_info.name_ar]
 * @property {string} [metal_purity_info.name_en]
 * @property {Object|null} [category]
 * @property {number} category.id
 * @property {string} category.name - الاسم العربي كحقل أساسي
 * @property {string} [category.name_ar]
 * @property {string} [category.name_en]
 * @property {string} category.slug
 */

/**
 * @typedef {Object} ProductImage
 * @property {number} id
 * @property {string} url
 * @property {string} [alt_text_ar]
 * @property {string} [alt_text_en]
 * @property {boolean} is_primary
 * @property {number} sort_order
 */

/**
 * @typedef {Object} ProductDetails
 * @property {number} id
 * @property {string} name
 * @property {string} [name_ar]
 * @property {string} [name_en]
 * @property {string} [description]
 * @property {string} [description_ar]
 * @property {string} description_en
 * @property {string} slug
 * @property {number} price
 * @property {number|null} [old_price]
 * @property {number|null} [discount_percentage]
 * @property {number|null} [weight]
 * @property {string|null} [metal_purity]
 * @property {string|null} [material_type]
 * @property {boolean} is_featured
 * @property {boolean} [is_active]
 * @property {boolean} show_price
 * @property {string|null} [image]
 * @property {string} [created_at]
 * @property {string} sku
 * @property {string} dimensions
 * @property {string[]} gallery
 * @property {ProductImage[]} [images] - الصور الجديدة من النظام المحدث
 * @property {Object|null} [metal]
 * @property {Object|null} [metal_purity_info]
 * @property {Object|null} [category]
 */

/**
 * @typedef {Object} HomeSlider
 * @property {number} id
 * @property {string} title - العنوان العربي كحقل أساسي
 * @property {string} [title_ar]
 * @property {string} [title_en]
 * @property {string} [description] - الوصف العربي كحقل أساسي
 * @property {string} [description_ar]
 * @property {string} [description_en]
 * @property {string} [button_text] - نص الزر العربي كحقل أساسي
 * @property {string} [button_text_ar]
 * @property {string} [button_text_en]
 * @property {string} [button_link]
 * @property {string} [image]
 * @property {number} order
 */

/**
 * @typedef {Object} UseApiResult
 * @property {*|null} data
 * @property {boolean} loading
 * @property {string|null} error
 * @property {Function} refetch
 */

/**
 * @typedef {Object} StorageData
 * @property {string} language
 * @property {string} theme
 * @property {number[]} favorites
 * @property {PriceAlert[]} priceAlerts
 * @property {string} lastPricesUpdate
 */

/**
 * @typedef {Object} PriceAlert
 * @property {string} id
 * @property {string} metalType
 * @property {string} purity
 * @property {number} targetPrice
 * @property {"above"|"below"} condition
 * @property {boolean} isActive
 * @property {string} createdAt
 */

/**
 * @typedef {Object} Theme
 * @property {Object} colors
 * @property {string} colors.primary
 * @property {string} colors.primaryDark
 * @property {string} colors.secondary
 * @property {string} colors.accent
 * @property {string} colors.background
 * @property {string} colors.surface
 * @property {string} colors.text
 * @property {string} colors.textSecondary
 * @property {string} colors.border
 * @property {string} colors.success
 * @property {string} colors.error
 * @property {string} colors.warning
 * @property {string} colors.info
 * @property {Object} spacing
 * @property {number} spacing.xs
 * @property {number} spacing.sm
 * @property {number} spacing.md
 * @property {number} spacing.lg
 * @property {number} spacing.xl
 * @property {Object} typography
 * @property {Object} typography.sizes
 * @property {number} typography.sizes.xs
 * @property {number} typography.sizes.sm
 * @property {number} typography.sizes.md
 * @property {number} typography.sizes.lg
 * @property {number} typography.sizes.xl
 * @property {number} typography.sizes.xxl
 * @property {Object} typography.weights
 * @property {string} typography.weights.light
 * @property {string} typography.weights.regular
 * @property {string} typography.weights.medium
 * @property {string} typography.weights.semiBold
 * @property {string} typography.weights.bold
 */

// Component Props Types

/**
 * @typedef {Object} MetalPriceCardProps
 * @property {MetalPriceGroup} metalGroup
 * @property {Function} onPricePress - (metalType: string, purity: string) => void
 */

/**
 * @typedef {Object} ProductCardProps
 * @property {Product} product
 * @property {Function} onPress - (productId: number) => void
 */

/**
 * @typedef {Object} CategoryCardProps
 * @property {Category} category
 * @property {Function} onPress - (categoryId: number) => void
 */

/**
 * @typedef {Object} LoadingProps
 * @property {"small"|"large"} [size]
 * @property {string} [color]
 * @property {string} [text]
 */

/**
 * @typedef {Object} ErrorProps
 * @property {string} message
 * @property {Function} [onRetry]
 */

// Export empty object to make this a module
export {};
