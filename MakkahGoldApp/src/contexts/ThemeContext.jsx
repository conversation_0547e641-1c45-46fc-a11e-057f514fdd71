// سياق الوضع الليلي - Theme Context
import React, { createContext, useContext, useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  getColors,
  setDarkMode,
  getDarkMode,
  toggleDarkMode,
} from "../constants";

/**
 * @typedef {Object} ThemeContextType
 * @property {boolean} isDarkMode
 * @property {Object} colors
 * @property {Function} toggleTheme
 * @property {Function} setTheme
 */

const ThemeContext = createContext(undefined);

const THEME_STORAGE_KEY = "@makkah_gold_theme";

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(true); // الوضع المظلم افتراضياً
  const [colors, setColors] = useState(getColors());

  // تحميل الوضع المحفوظ عند بدء التطبيق
  useEffect(() => {
    loadSavedTheme();
  }, []);

  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
      if (savedTheme !== null) {
        const isDark = JSON.parse(savedTheme);
        setDarkMode(isDark);
        setIsDarkMode(isDark);
        setColors(getColors());
      } else {
        // إذا لم يكن هناك إعداد محفوظ، استخدم الوضع المظلم كافتراضي
        setDarkMode(true);
        setIsDarkMode(true);
        setColors(getColors());
      }
    } catch (error) {
      console.log("خطأ في تحميل الوضع المحفوظ:", error);
      // في حالة حدوث خطأ، استخدم الوضع المظلم كافتراضي
      setDarkMode(true);
      setIsDarkMode(true);
      setColors(getColors());
    }
  };

  const saveTheme = async (isDark) => {
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, JSON.stringify(isDark));
    } catch (error) {
      console.log("خطأ في حفظ الوضع:", error);
    }
  };

  const toggleTheme = async () => {
    const newMode = toggleDarkMode();
    setIsDarkMode(newMode);
    setColors(getColors());
    await saveTheme(newMode);
  };

  const setTheme = async (isDark) => {
    setDarkMode(isDark);
    setIsDarkMode(isDark);
    setColors(getColors());
    await saveTheme(isDark);
  };

  const value = {
    isDarkMode,
    colors,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
};

// Hook لاستخدام السياق
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }
  return context;
};

export default ThemeContext;
