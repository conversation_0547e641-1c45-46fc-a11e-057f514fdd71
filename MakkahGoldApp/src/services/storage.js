// خدمة التخزين المحلي - Storage Service

import AsyncStorage from "@react-native-async-storage/async-storage";

// مفاتيح التخزين
const STORAGE_KEYS = {
  FAVORITES: "@makkah_gold_favorites",
  APP_SETTINGS: "@makkah_gold_settings",
  PRICE_ALERTS: "@makkah_gold_price_alerts",
  SEARCH_HISTORY: "@makkah_gold_search_history",
};

class StorageService {
  // المفضلة
  async getFavorites() {
    try {
      const favorites = await AsyncStorage.getItem(STORAGE_KEYS.FAVORITES);
      return favorites ? JSON.parse(favorites) : [];
    } catch (error) {
      console.error("Error getting favorites:", error);
      return [];
    }
  }

  async addToFavorites(productId) {
    try {
      const favorites = await this.getFavorites();
      if (!favorites.includes(productId)) {
        favorites.push(productId);
        await AsyncStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(favorites));
      }
    } catch (error) {
      console.error("Error adding to favorites:", error);
    }
  }

  async removeFromFavorites(productId) {
    try {
      const favorites = await this.getFavorites();
      const updatedFavorites = favorites.filter((id) => id !== productId);
      await AsyncStorage.setItem(STORAGE_KEYS.FAVORITES, JSON.stringify(updatedFavorites));
    } catch (error) {
      console.error("Error removing from favorites:", error);
    }
  }

  // إعدادات التطبيق
  async getAppSettings() {
    try {
      const settings = await AsyncStorage.getItem(STORAGE_KEYS.APP_SETTINGS);
      return settings ? JSON.parse(settings) : {
        language: "ar",
        theme: "light",
        notifications: true,
        priceAlerts: true,
      };
    } catch (error) {
      console.error("Error getting app settings:", error);
      return {
        language: "ar",
        theme: "light",
        notifications: true,
        priceAlerts: true,
      };
    }
  }

  async updateAppSettings(newSettings) {
    try {
      const currentSettings = await this.getAppSettings();
      const updatedSettings = { ...currentSettings, ...newSettings };
      await AsyncStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(updatedSettings));
    } catch (error) {
      console.error("Error updating app settings:", error);
    }
  }
}

// إنشاء instance واحد من الخدمة
export const storageService = new StorageService();

// تصدير الخدمة كـ default
export default storageService;
