// خدمة API - API Service

import axios from "axios";

// إعدادات API
const API_CONFIG = {
  //   BASE_URL: "https://9746-156-199-206-18.ngrok-free.app/api/app/v1",
  BASE_URL: "https://makkahgold.com/api/app/v1",
  TIMEOUT: 10000,
  HEADERS: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "ngrok-skip-browser-warning": "true",
  },
};

// نقاط النهاية
const ENDPOINTS = {
  CURRENT_PRICES: "/metal-prices/current",
  PRICE_HISTORY: "/metal-prices/history",
  PRICE_STATISTICS: "/metal-prices/statistics",
  CATEGORIES: "/categories",
  FEATURED_PRODUCTS: "/products/featured",
  HOME_SLIDERS: "/sliders",
  PRODUCTS: "/products",
  PRODUCT_DETAILS: "/products",
  RELATED_PRODUCTS: "/products",
  // <PERSON>akat Calculator endpoints
  ZAKAT_FORMULAS: "/zakat/formulas",
  METAL_TYPES: "/metals/types",
  METAL_PURITIES: "/metals/purities",
  // App Settings endpoints
  APP_SETTINGS: "/settings",
  CONTACT_INFO: "/settings/contact",
  PRIVACY_POLICY: "/settings/privacy",
  // Stores endpoints
  STORES: "/stores",

  // Bars & Coins endpoints
  BARS_COINS_COMPANIES: "/bars-coins/companies",
  BARS_COINS_PRODUCT_TYPES: "/bars-coins/product-types",
  BARS_COINS_COMPANY_PRODUCT: "/bars-coins/company-product",
  BARS_COINS_CURRENT_PRICES: "/bars-coins/current-prices",
  BARS_COINS_CALCULATE_PRICES: "/bars-coins/calculate-prices",
  BARS_COINS_SITE_SETTINGS: "/bars-coins/site-settings",
};

class ApiService {
  constructor() {
    this.api = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: API_CONFIG.HEADERS,
    });

    // إضافة interceptor للأخطاء
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error("API Error:", {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });
        return Promise.reject(error);
      }
    );
  }

  // Metal Prices APIs
  async getCurrentPrices() {
    const response = await this.api.get(ENDPOINTS.CURRENT_PRICES);
    return response.data.data || [];
  }

  // Categories APIs
  async getCategories() {
    const response = await this.api.get(ENDPOINTS.CATEGORIES);
    return response.data.data || [];
  }

  // Home Sliders
  async getHomeSliders() {
    const response = await this.api.get(ENDPOINTS.HOME_SLIDERS);
    return response.data.data || [];
  }

  // Products API with filters
  async getProducts(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.category_id)
      queryParams.append("category_id", params.category_id);
    if (params.page) queryParams.append("page", params.page);
    if (params.per_page) queryParams.append("per_page", params.per_page);
    if (params.search) queryParams.append("search", params.search);

    const response = await this.api.get(
      `${ENDPOINTS.PRODUCTS}?${queryParams.toString()}`
    );
    return response.data;
  }

  // Product Details API
  async getProduct(productId) {
    const response = await this.api.get(
      `${ENDPOINTS.PRODUCT_DETAILS}/${productId}`
    );
    return response.data.data || null;
  }

  // Related Products API
  async getRelatedProducts(productId) {
    const response = await this.api.get(
      `${ENDPOINTS.RELATED_PRODUCTS}/${productId}/related`
    );
    return response.data.data || [];
  }

  // Search Products
  async searchProducts(query, page = 1) {
    const response = await this.api.get(
      `${ENDPOINTS.PRODUCTS}?search=${query}&page=${page}&per_page=20`
    );
    return response.data;
  }

  // Zakat Calculator APIs
  async getZakatFormulas() {
    try {
      const response = await this.api.get(ENDPOINTS.ZAKAT_FORMULAS);
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching zakat formulas:", error);
      throw new Error("فشل في جلب معادلات الزكاة");
    }
  }

  async getMetalTypes() {
    try {
      const response = await this.api.get(ENDPOINTS.METAL_TYPES);
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching metal types:", error);
      throw new Error("فشل في جلب أنواع المعادن");
    }
  }

  async getMetalPurities(metalType) {
    if (!metalType) return [];
    try {
      const response = await this.api.get(
        `${ENDPOINTS.METAL_PURITIES}/${metalType}`
      );
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching metal purities:", error);
      throw new Error("فشل في جلب عيارات المعدن");
    }
  }

  // Metal Price History APIs
  async getMetalPriceHistory(params = {}) {
    try {
      const queryParams = new URLSearchParams();

      if (params.metal_type)
        queryParams.append("metal_type", params.metal_type);
      if (params.purity) queryParams.append("purity", params.purity);
      if (params.period) queryParams.append("period", params.period);
      if (params.date_from) queryParams.append("date_from", params.date_from);
      if (params.date_to) queryParams.append("date_to", params.date_to);
      if (params.page) queryParams.append("page", params.page);
      if (params.per_page) queryParams.append("per_page", params.per_page);

      const url = `${ENDPOINTS.PRICE_HISTORY}?${queryParams.toString()}`;
      const response = await this.api.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching metal price history:", error);
      throw new Error("فشل في جلب تاريخ أسعار المعادن");
    }
  }

  async getMetalPriceStatistics(metalType, purity, period = "month") {
    try {
      const queryParams = new URLSearchParams({
        metal_type: metalType,
        purity: purity,
        period: period,
      });

      const url = `${ENDPOINTS.PRICE_STATISTICS}?${queryParams.toString()}`;
      const response = await this.api.get(url);
      return response.data;
    } catch (error) {
      console.error("Error fetching metal price statistics:", error);
      throw new Error("فشل في جلب إحصائيات أسعار المعادن");
    }
  }

  // App Settings APIs
  async getAppSettings() {
    try {
      const response = await this.api.get(ENDPOINTS.APP_SETTINGS);
      return response.data.data || {};
    } catch (error) {
      console.error("Error fetching app settings:", error);
      throw new Error("فشل في جلب إعدادات التطبيق");
    }
  }

  async getContactInfo() {
    try {
      const response = await this.api.get(ENDPOINTS.CONTACT_INFO);
      return response.data.data || {};
    } catch (error) {
      console.error("Error fetching contact info:", error);
      throw new Error("فشل في جلب معلومات الاتصال");
    }
  }

  async getPrivacyPolicy() {
    try {
      const response = await this.api.get(ENDPOINTS.PRIVACY_POLICY);
      return response.data.data || {};
    } catch (error) {
      console.error("Error fetching privacy policy:", error);
      throw new Error("فشل في جلب سياسة الخصوصية");
    }
  }

  async updateAppSettings(settings) {
    try {
      const response = await this.api.put(ENDPOINTS.APP_SETTINGS, settings);
      return response.data;
    } catch (error) {
      console.error("Error updating app settings:", error);
      throw new Error("فشل في تحديث إعدادات التطبيق");
    }
  }

  // Store APIs
  async getStores() {
    try {
      const response = await this.api.get(ENDPOINTS.STORES);
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching stores:", error);
      throw new Error("فشل في جلب الفروع");
    }
  }

  async getStore(id) {
    try {
      const response = await this.api.get(`${ENDPOINTS.STORES}/${id}`);
      return response.data.data || null;
    } catch (error) {
      console.error("Error fetching store:", error);
      throw new Error("فشل في جلب بيانات الفرع");
    }
  }

  async getNearbyStores(latitude, longitude, radius = 50) {
    try {
      const response = await this.api.post(`${ENDPOINTS.STORES}/nearby`, {
        latitude,
        longitude,
        radius,
      });
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching nearby stores:", error);
      throw new Error("فشل في جلب الفروع القريبة");
    }
  }
  // Bars & Coins APIs

  // جلب قائمة الشركات حسب النوع
  async getBarsCoinsCompanies(type) {
    try {
      const response = await this.api.get(ENDPOINTS.BARS_COINS_COMPANIES, {
        params: { type },
      });
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching bars coins companies:", error);
      throw new Error("فشل في جلب قائمة الشركات");
    }
  }

  // جلب أنواع المنتجات لشركة معينة
  async getBarsCoinsProductTypes(type, companyId) {
    try {
      const response = await this.api.get(ENDPOINTS.BARS_COINS_PRODUCT_TYPES, {
        params: {
          type,
          company_id: companyId,
        },
      });
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching bars coins product types:", error);
      throw new Error("فشل في جلب أنواع المنتجات");
    }
  }

  // جلب تفاصيل منتج شركة معينة
  async getBarsCoinsCompanyProduct(companyId, productTypeId) {
    try {
      if (!companyId || !productTypeId) {
        throw new Error("يجب تحديد الشركة ونوع المنتج");
      }

      const response = await this.api.get(
        ENDPOINTS.BARS_COINS_COMPANY_PRODUCT,
        {
          params: {
            company_id: companyId,
            product_type_id: productTypeId,
          },
        }
      );
      return response.data.data || null;
    } catch (error) {
      console.error("Error fetching bars coins company product:", error);
      throw new Error("فشل في جلب تفاصيل المنتج");
    }
  }

  // جلب أسعار المعادن الحالية للسبائك والعملات
  async getBarsCoinsCurrentPrices() {
    try {
      const response = await this.api.get(ENDPOINTS.BARS_COINS_CURRENT_PRICES);
      return response.data.data || null;
    } catch (error) {
      console.error("Error fetching bars coins current prices:", error);
      throw new Error("فشل في جلب أسعار المعادن الحالية");
    }
  }

  // حساب أسعار منتج معين
  async calculateBarsCoinsProductPrices(companyId, productTypeId) {
    try {
      if (!companyId || !productTypeId) {
        throw new Error("يجب تحديد الشركة ونوع المنتج");
      }

      const response = await this.api.post(
        ENDPOINTS.BARS_COINS_CALCULATE_PRICES,
        {
          company_id: companyId,
          product_type_id: productTypeId,
        }
      );
      return response.data.data || null;
    } catch (error) {
      console.error("Error calculating bars coins product prices:", error);
      throw new Error("فشل في حساب أسعار المنتج");
    }
  }

  // جلب إعدادات الموقع
  async getBarsCoinsSettings() {
    try {
      const response = await this.api.get(ENDPOINTS.BARS_COINS_SITE_SETTINGS);
      return response.data.data || null;
    } catch (error) {
      console.error("Error fetching bars coins site settings:", error);
      throw new Error("فشل في جلب إعدادات الموقع");
    }
  }
}

// إنشاء instance واحد من الخدمة
export const apiService = new ApiService();

// تصدير الخدمة كـ default
export default apiService;
