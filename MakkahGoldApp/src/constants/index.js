// ثوابت التطبيق - <PERSON><PERSON><PERSON> App Constants
import { I18nManager, Platform } from "react-native";
import { getFontFamily } from "../utils/fontHelper";

// API Configuration
export const API_CONFIG = {
  // BASE_URL: "https://makkah-gold-jewelry.test/api/app/v1",
  BASE_URL: "https://9746-156-199-206-18.ngrok-free.app/api/app/v1",
  //   BASE_URL: "http://********:8000/api/app/v1",
  // BASE_URL: "http://*************:8000/api/app/v1",
  TIMEOUT: 10000,
  HEADERS: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "ngrok-skip-browser-warning": "true",
  },
};

// نظام الألوان مع دعم الوضع الليلي - Color System with Dark Mode Support

/**
 * @typedef {Object} ColorScheme
 * @property {string} primary - ذهبي
 * @property {string} primaryDark - ذهبي داكن
 * @property {string} primaryLight - ذهبي فاتح
 * @property {string} secondary - فضي
 * @property {string} secondaryDark - فضي داكن
 * @property {string} secondaryLight - فضي فاتح
 * @property {string} accent - لون مميز
 * @property {string} textPrimary - نص أساسي
 * @property {string} textSecondary - نص ثانوي
 * @property {string} textLight - نص فاتح
 * @property {string} textInverse - نص معكوس
 * @property {string} background - خلفية أساسية
 * @property {string} surface - سطح البطاقات
 * @property {string} card - خلفية البطاقات
 * @property {string} overlay - طبقة علوية
 * @property {string} success - نجاح
 * @property {string} error - خطأ
 * @property {string} warning - تحذير
 * @property {string} info - معلومات
 * @property {string} white - أبيض
 * @property {string} black - أسود
 * @property {string} gray - رمادي
 * @property {string} lightGray - رمادي فاتح
 * @property {string} darkGray - رمادي داكن
 * @property {string} border - حدود
 * @property {string} divider - فاصل
 * @property {string} shadow - ظل
 */

// الوضع النهاري - Light Mode
const lightColors = {
  // الألوان الأساسية - مستوحاة من الذهب والفضة
  primary: "#D4AF37", // ذهبي كلاسيكي
  primaryDark: "#B8941F", // ذهبي داكن
  primaryLight: "#E6C55A", // ذهبي فاتح
  secondary: "#C0C0C0", // فضي
  secondaryDark: "#A0A0A0", // فضي داكن
  secondaryLight: "#E8E8E8", // فضي فاتح
  accent: "#FFD700", // ذهبي مشرق

  // ألوان النص
  textPrimary: "#1A1A1A", // أسود داكن
  textSecondary: "#666666", // رمادي متوسط
  textLight: "#999999", // رمادي فاتح
  textInverse: "#FFFFFF", // أبيض

  // ألوان الخلفية
  background: "#FAFAFA", // أبيض مائل للرمادي
  surface: "#FFFFFF", // أبيض نقي
  card: "#FFFFFF", // أبيض للبطاقات
  overlay: "rgba(0,0,0,0.5)", // طبقة شفافة

  // ألوان الحالة
  success: "#2E7D32", // أخضر داكن
  error: "#C62828", // أحمر داكن
  warning: "#F57C00", // برتقالي
  info: "#1976D2", // أزرق

  // ألوان إضافية
  white: "#FFFFFF",
  black: "#000000",
  gray: "#757575",
  lightGray: "#F5F5F5",
  darkGray: "#424242",

  // حدود وفواصل
  border: "#E0E0E0",
  divider: "#EEEEEE",
  shadow: "rgba(0,0,0,0.1)",
};

// الوضع الليلي - Dark Mode
const darkColors = {
  // الألوان الأساسية (أكثر إشراقاً في الوضع الليلي)
  primary: "#FFD700", // ذهبي مشرق
  primaryDark: "#E6C200", // ذهبي داكن
  primaryLight: "#FFED4E", // ذهبي فاتح جداً
  secondary: "#E0E0E0", // فضي فاتح
  secondaryDark: "#BDBDBD", // فضي متوسط
  secondaryLight: "#F5F5F5", // فضي فاتح جداً
  accent: "#FFF176", // أصفر ذهبي

  // ألوان النص (معكوسة)
  textPrimary: "#FFFFFF", // أبيض
  textSecondary: "#B0B0B0", // رمادي فاتح
  textLight: "#808080", // رمادي متوسط
  textInverse: "#000000", // أسود

  // ألوان الخلفية (داكنة)
  background: "#121212", // أسود مائل للرمادي
  surface: "#1E1E1E", // رمادي داكن جداً
  card: "#2C2C2C", // رمادي داكن للبطاقات
  overlay: "rgba(0,0,0,0.7)", // طبقة شفافة داكنة

  // ألوان الحالة (أكثر إشراقاً)
  success: "#4CAF50", // أخضر مشرق
  error: "#F44336", // أحمر مشرق
  warning: "#FF9800", // برتقالي مشرق
  info: "#2196F3", // أزرق مشرق

  // ألوان إضافية
  white: "#FFFFFF",
  black: "#000000",
  gray: "#9E9E9E",
  lightGray: "#424242",
  darkGray: "#212121",

  // حدود وفواصل
  border: "#404040",
  divider: "#303030",
  shadow: "rgba(0,0,0,0.3)",
};

// متغير لتحديد الوضع الحالي (الوضع المظلم افتراضياً)
let isDarkMode = true;

// دالة للحصول على الألوان الحالية
export const getColors = () => {
  return isDarkMode ? darkColors : lightColors;
};

// دالة لتبديل الوضع
export const toggleDarkMode = () => {
  isDarkMode = !isDarkMode;
  return isDarkMode;
};

// دالة لتعيين الوضع
export const setDarkMode = (enabled) => {
  isDarkMode = enabled;
};

// دالة للحصول على حالة الوضع الليلي
export const getDarkMode = () => {
  return isDarkMode;
};

// تصدير الألوان الافتراضية (الوضع المظلم) للتوافق مع الكود الموجود
export const COLORS = darkColors;

// Typography - الخطوط
export const FONTS = {
  // عائلات الخطوط مع دعم متقدم للأنظمة المختلفة
  families: {
    regular: getFontFamily("regular"),
    bold: getFontFamily("bold"),
    medium: getFontFamily("medium"),
  },

  // أحجام الخطوط
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
  },

  // أوزان الخطوط
  weights: {
    light: "300",
    regular: "400",
    medium: "500",
    semiBold: "600",
    bold: "700",
  },
};

// Spacing - المسافات
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Metal Types - أنواع المعادن
export const METAL_TYPES = {
  GOLD: "gold",
  SILVER: "silver",
  PLATINUM: "platinum",
};

// Metal Purities - عيارات المعادن
export const METAL_PURITIES = {
  GOLD: ["24K", "22K", "21K", "18K", "14K", "12K", "9K"],
  SILVER: ["999", "925", "900", "800", "600"],
  PLATINUM: ["950"],
};

// Zakat Constants - ثوابت الزكاة
export const ZAKAT = {
  GOLD_NISAB_GRAMS: 85, // نصاب الذهب بالجرام
  SILVER_NISAB_GRAMS: 595, // نصاب الفضة بالجرام
  RATE: 0.025, // معدل الزكاة 2.5%
  RATE_PERCENTAGE: "2.5%", // معدل الزكاة كنص
};

// Navigation Routes - مسارات التنقل
export const ROUTES = {
  // Bottom Tabs
  HOME: "Home",
  PRICES: "Prices",
  CALCULATORS: "Calculators",
  PRODUCTS: "Products",
  MORE: "More",

  // Stack Screens
  PRICE_HISTORY: "PriceHistory",
  ZAKAT_CALCULATOR: "ZakatCalculator",
  JEWELRY_CALCULATOR: "JewelryCalculator",
  PRODUCT_DETAILS: "ProductDetails",
  CATEGORY_PRODUCTS: "CategoryProducts",
  SETTINGS: "Settings",
  ABOUT: "About",
};

// API Endpoints - نقاط النهاية للـ API
export const ENDPOINTS = {
  // Metal Prices
  CURRENT_PRICES: "/metal-prices/current",
  METAL_PRICES: (metalType) => `/metal-prices/${metalType}`,
  PRICE_HISTORY: (metalType, purity) =>
    `/metal-prices/${metalType}/${purity}/history`,
  CALCULATE_JEWELRY_VALUE: "/metal-prices/calculate-jewelry-value",

  // Zakat Calculator
  ZAKAT_GOLD: "/zakat/gold",
  ZAKAT_SILVER: "/zakat/silver",
  NISAB_INFO: "/zakat/nisab-info",

  // Categories & Products
  CATEGORIES: "/categories",
  CATEGORY_PRODUCTS: (id) => `/categories/${id}/products`,
  PRODUCTS: "/products",
  PRODUCT_DETAILS: (id) => `/products/${id}`,
  FEATURED_PRODUCTS: "/products/featured",

  // Home Sliders
  HOME_SLIDERS: "/sliders",

  // App Settings
  APP_SETTINGS: "/settings",
  CONTACT_INFO: "/settings/contact",
  PRIVACY_POLICY: "/settings/privacy",

  // Stores
  STORES: "/stores",

  // Search
  SEARCH_PRODUCTS: "/search",
};

// Error Messages - رسائل الخطأ
export const ERROR_MESSAGES = {
  NETWORK_ERROR: "خطأ في الاتصال بالشبكة",
  SERVER_ERROR: "خطأ في الخادم",
  UNKNOWN_ERROR: "حدث خطأ غير متوقع",
  NO_DATA: "لا توجد بيانات متاحة",
  LOADING_ERROR: "خطأ في تحميل البيانات",
};

// RTL Configuration - إعدادات RTL
export const RTL_CONFIG = {
  IS_RTL: I18nManager.isRTL,
  WRITING_DIRECTION: I18nManager.isRTL ? "rtl" : "ltr",
  DEFAULT_TEXT_ALIGN: I18nManager.isRTL ? "right" : "left",
  FLEX_DIRECTION: I18nManager.isRTL ? "row-reverse" : "row",

  // اتجاهات الحركة
  ANIMATIONS: {
    SLIDE_FROM_RIGHT: I18nManager.isRTL
      ? "slide_from_left"
      : "slide_from_right",
    SLIDE_FROM_LEFT: I18nManager.isRTL ? "slide_from_right" : "slide_from_left",
  },

  // أيقونات الاتجاه
  ICONS: {
    ARROW_FORWARD: I18nManager.isRTL ? "arrow-back" : "arrow-forward",
    ARROW_BACK: I18nManager.isRTL ? "arrow-forward" : "arrow-back",
    CHEVRON_LEFT: I18nManager.isRTL ? "chevron-right" : "chevron-left",
    CHEVRON_RIGHT: I18nManager.isRTL ? "chevron-left" : "chevron-right",
  },
};
