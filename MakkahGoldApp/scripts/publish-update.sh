#!/bin/bash

# سكريبت نشر التحديثات - EAS Updates Publishing Script
# يستخدم هذا السكريبت لنشر التحديثات بعد اكتمال البناء

echo "🚀 بدء نشر تحديث EAS Updates..."

# التحقق من وجود EAS CLI
if ! command -v eas &> /dev/null; then
    echo "❌ EAS CLI غير مثبت. يرجى تثبيته باستخدام: npm install -g @expo/eas-cli"
    exit 1
fi

# التحقق من تسجيل الدخول
if ! eas whoami &> /dev/null; then
    echo "❌ غير مسجل دخول في EAS. يرجى تسجيل الدخول باستخدام: eas login"
    exit 1
fi

# قراءة رسالة التحديث
if [ -z "$1" ]; then
    echo "📝 يرجى إدخال رسالة التحديث:"
    read -r UPDATE_MESSAGE
else
    UPDATE_MESSAGE="$1"
fi

# التحقق من وجود رسالة
if [ -z "$UPDATE_MESSAGE" ]; then
    UPDATE_MESSAGE="تحديث جديد - $(date '+%Y-%m-%d %H:%M')"
fi

echo "📦 نشر التحديث مع الرسالة: $UPDATE_MESSAGE"

# نشر التحديث للإنتاج
echo "🔄 نشر التحديث لقناة الإنتاج..."
eas update --branch production --message "$UPDATE_MESSAGE"

if [ $? -eq 0 ]; then
    echo "✅ تم نشر التحديث بنجاح!"
    echo ""
    echo "📱 لاختبار التحديث:"
    echo "1. تأكد من أن التطبيق مثبت من APK (وليس Expo Go)"
    echo "2. افتح التطبيق"
    echo "3. اذهب إلى الإعدادات > التحديثات"
    echo "4. اضغط على 'التحقق من التحديثات الآن'"
    echo ""
    echo "🔗 يمكنك متابعة التحديثات في لوحة تحكم Expo:"
    echo "https://expo.dev/accounts/algmaal/projects/makkah-gold-group-jewelry/updates"
else
    echo "❌ فشل في نشر التحديث"
    exit 1
fi
