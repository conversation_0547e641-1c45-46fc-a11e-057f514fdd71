// حاسبة قيمة المجوهرات - Jewelry Value Calculator
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  I18nManager,
  Alert,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { LinearGradient } from "expo-linear-gradient";
import { SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  useCurrentPrices,
  useMetalTypes,
  useMetalPurities,
} from "../../src/utils/hooks";
import Loading from "../../src/components/Loading";
import ThemedText from "../../src/components/ThemedText";
import ThemedCard from "../../src/components/ThemedCard";
import ThemedStatusBar from "../../src/components/ThemedStatusBar";

// تفعيل RTL - صفحة الحاسبات تستخدم اتجاه RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const JewelryCalculatorScreen = () => {
  const { colors } = useTheme();

  // State للقطع المجوهرات
  const [jewelryItems, setJewelryItems] = useState([]);
  const [totalValue, setTotalValue] = useState(0);

  // State لعرض السعر الحالي
  const [currentPrice, setCurrentPrice] = useState(null);

  // State للنموذج الموحد
  const [formData, setFormData] = useState({
    name: "",
    metalType: "gold",
    purity: "21K",
    weight: "",
  });
  const [formErrors, setFormErrors] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);
  const [selectedMetalType, setSelectedMetalType] = useState("");

  // مرجع للتحكم في ScrollView الخاص بأزرار العيار
  const purityScrollRef = useRef(null);

  // مرجع للتحكم في ScrollView الخاص بأزرار نوع المعدن
  const metalTypeScrollRef = React.useRef(null);

  // جلب البيانات من API
  const {
    data: prices,
    loading: pricesLoading,
    error: pricesError,
  } = useCurrentPrices();
  const {
    data: metalTypes,
    loading: metalTypesLoading,
    error: metalTypesError,
  } = useMetalTypes();
  const {
    data: metalPurities,
    loading: puritiesLoading,
    error: puritiesError,
  } = useMetalPurities(selectedMetalType);

  // تحديث selectedMetalType عند تغيير formData.metalType
  useEffect(() => {
    if (formData.metalType !== selectedMetalType) {
      setSelectedMetalType(formData.metalType);
    }
  }, [formData.metalType, selectedMetalType]);

  // تحديث السعر الحالي عند تغيير نوع المعدن أو العيار
  useEffect(() => {
    if (formData.metalType && formData.purity && prices) {
      const metalGroup = prices.find(
        (group) => group.metal_type === formData.metalType
      );
      if (metalGroup) {
        const priceInfo = metalGroup.prices.find(
          (p) => p.purity === formData.purity
        );
        setCurrentPrice(priceInfo ? priceInfo.price_per_gram : null);
      } else {
        setCurrentPrice(null);
      }
    }
  }, [formData.metalType, formData.purity, prices]);

  // تعيين القيم الافتراضية من البيانات الحقيقية عند تحميلها
  useEffect(() => {
    if (metalTypes && metalTypes.length > 0 && !formData.metalType) {
      const defaultMetalType = metalTypes[0].name_en
        ? metalTypes[0].name_en
        : "gold";
      const defaultPurity = defaultMetalType === "gold" ? "21K" : "999K";
      setFormData((prev) => ({
        ...prev,
        metalType: defaultMetalType,
        purity: defaultPurity,
      }));
      setSelectedMetalType(defaultMetalType);
    }
  }, [metalTypes, formData.metalType]);

  // تعيين العيار الافتراضي عند تحميل العيارات (عيار 21 للذهب)
  useEffect(() => {
    if (prices && prices.length > 0 && formData.metalType && !formData.purity) {
      const defaultPurity = formData.metalType === "gold" ? "21K" : "999";
      const purityOptions = getPurityOptions(formData.metalType);

      // التحقق من وجود العيار المطلوب، وإلا استخدم الأول المتاح
      const finalPurity = purityOptions.includes(defaultPurity)
        ? defaultPurity
        : purityOptions[0];

      if (finalPurity) {
        setFormData((prev) => ({
          ...prev,
          purity: finalPurity,
        }));
      }
    }
  }, [prices, formData.metalType]);

  // وظائف النموذج الموحد
  const validateForm = () => {
    const errors = {};

    // اسم القطعة اختياري - لا نحتاج للتحقق منه

    if (!formData.metalType) {
      errors.metalType = "يرجى اختيار نوع المعدن";
    }

    if (!formData.purity) {
      errors.purity = "يرجى اختيار عيار المعدن";
    }

    const weight = parseFloat(formData.weight);
    if (!formData.weight || isNaN(weight) || weight <= 0) {
      errors.weight = "يرجى إدخال وزن صحيح أكبر من صفر";
    }

    setFormErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    setIsFormValid(isValid);
    return isValid;
  };

  const updateFormField = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // إزالة رسالة الخطأ عند التعديل
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: "" }));
    }

    // إذا كان التحديث للنقاء، قم بالتمرير التلقائي للخيار المحدد
    if (field === "purity") {
      scrollToCenterPurity(value);
    }
  };

  // وظيفة للتمرير التلقائي إلى الخيار المحدد في منتصف الشاشة
  const scrollToCenterPurity = (selectedPurity) => {
    if (!purityScrollRef.current) return;

    setTimeout(() => {
      const purities = getPurityOptions(formData.metalType, selectedPurity);
      const selectedIndex = purities.findIndex((p) => p === selectedPurity);

      if (selectedIndex !== -1) {
        // حساب موقع التمرير لوضع الخيار المحدد في المنتصف
        const buttonWidth = 80; // عرض تقريبي لكل زر
        const spacing = 8; // المسافة بين الأزرار
        const scrollPosition = Math.max(
          0,
          selectedIndex * (buttonWidth + spacing) - buttonWidth * 1.5
        );

        purityScrollRef.current.scrollTo({
          x: scrollPosition,
          animated: true,
        });
      }
    }, 100); // تأخير قصير للسماح بإعادة الرندر
  };

  const resetForm = () => {
    // استخدام أول نوع معدن متاح من البيانات الحقيقية
    const defaultMetalType =
      metalTypes && metalTypes.length > 0 ? metalTypes[0].name_en : "gold";

    // استخدام العيار الافتراضي (عيار 21 للذهب)
    const defaultPurity = defaultMetalType === "gold" ? "21K" : "999K";

    setFormData({
      name: "",
      metalType: defaultMetalType,
      purity: defaultPurity,
      weight: "",
    });
    setFormErrors({});
    setIsFormValid(false);
  };

  // حساب قيمة قطعة مجوهرات واحدة (القيمة الأساسية فقط بدون رسوم)
  const calculateItemValue = (item) => {
    const weight = parseFloat(item.weight);
    if (isNaN(weight) || weight <= 0 || !prices) return 0;

    // البحث عن مجموعة المعدن
    const metalGroup = prices.find(
      (group) => group.metal_type === item.metalType
    );

    if (!metalGroup) return 0;

    // البحث عن السعر المناسب داخل مجموعة المعدن
    const priceInfo = metalGroup.prices.find((p) => p.purity === item.purity);
    if (!priceInfo) return 0;

    const pricePerGram = priceInfo.price_per_gram;
    // حساب القيمة الأساسية فقط: الوزن × السعر
    const metalValue = weight * pricePerGram;

    return metalValue;
  };

  const addJewelryItemFromForm = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const weight = parseFloat(formData.weight);
      const value = calculateItemValue({
        metalType: formData.metalType,
        purity: formData.purity,
        weight: weight,
      });

      const newItem = {
        id: Date.now().toString(),
        name: formData.name.trim() || `قطعة ${jewelryItems.length + 1}`,
        metalType: formData.metalType,
        purity: formData.purity,
        weight: weight,
        value: value,
      };

      const updatedItems = [...jewelryItems, newItem];
      setJewelryItems(updatedItems);

      // حفظ تلقائي في AsyncStorage عند إضافة قطعة جديدة (مفتاح منفصل عن حاسبة الزكاة)
      await AsyncStorage.setItem(
        "jewelryValueCalculatorItems",
        JSON.stringify(updatedItems)
      );

      resetForm();
      console.log("تم إضافة القطعة وحفظها تلقائياً");
    } catch (error) {
      console.error("خطأ في إضافة القطعة:", error);
      Alert.alert(
        "خطأ",
        "حدث خطأ أثناء إضافة القطعة. يرجى المحاولة مرة أخرى.",
        [{ text: "موافق" }]
      );
    }
  };

  // تحديث قيم القطع المحفوظة عند تغيير الأسعار
  const updateSavedItemsValues = async () => {
    if (jewelryItems.length > 0 && prices) {
      const updatedItems = jewelryItems.map((item) => {
        const newValue = calculateItemValue(item);
        return {
          ...item,
          value: newValue,
        };
      });

      // التحقق من أن القيم تغيرت فعلاً قبل التحديث
      const hasChanged = updatedItems.some(
        (item, index) => item.value !== jewelryItems[index]?.value
      );

      if (hasChanged) {
        setJewelryItems(updatedItems);

        // حفظ القيم المحدثة في AsyncStorage
        try {
          await AsyncStorage.setItem(
            "jewelryValueCalculatorItems",
            JSON.stringify(updatedItems)
          );
        } catch (error) {
          console.error("خطأ في تحديث القيم المحفوظة:", error);
        }
      }
    }
  };

  // تحديث القيم عند تغيير الأسعار (مع تجنب الحلقة اللا نهائية)
  useEffect(() => {
    if (prices && jewelryItems.length > 0) {
      updateSavedItemsValues();
    }
  }, [prices]);

  // حساب إجمالي القيمة
  useEffect(() => {
    const total = jewelryItems.reduce(
      (sum, item) => sum + (item.value || 0),
      0
    );
    setTotalValue(total);
  }, [jewelryItems]);

  // التحقق من صحة النموذج عند تغيير البيانات
  useEffect(() => {
    validateForm();
  }, [formData]);

  // التمرير التلقائي للخيار المحدد عند تحميل الصفحة أو تغيير نوع المعدن أو النقاء
  useEffect(() => {
    if (formData.purity && prices && prices.length > 0) {
      // تأخير أطول للتأكد من أن ScrollView تم رندره بالكامل
      setTimeout(() => {
        scrollToCenterPurity(formData.purity);
      }, 300);
    }
  }, [formData.metalType, formData.purity, prices]);

  // التمرير التلقائي عند انتهاء التحميل لأول مرة
  useEffect(() => {
    if (!pricesLoading && !metalTypesLoading && formData.purity && prices) {
      // تأخير إضافي للتأكد من أن جميع العناصر تم رندرها
      setTimeout(() => {
        scrollToCenterPurity(formData.purity);
      }, 500);
    }
  }, [pricesLoading, metalTypesLoading]);

  // وظائف إدارة القطع

  const confirmDeleteItem = (id) => {
    Alert.alert(
      "تأكيد الحذف",
      "هل أنت متأكد من حذف هذه القطعة؟ لا يمكن التراجع عن هذا الإجراء.",
      [
        {
          text: "إلغاء",
          style: "cancel",
        },
        {
          text: "حذف",
          style: "destructive",
          onPress: () => deleteJewelryItem(id),
        },
      ]
    );
  };

  const deleteJewelryItem = async (id) => {
    try {
      // تحديث الـ state المحلي
      const updatedItems = jewelryItems.filter((item) => item.id !== id);
      setJewelryItems(updatedItems);

      // حفظ التغييرات في AsyncStorage فوراً (مفتاح منفصل عن حاسبة الزكاة)
      await AsyncStorage.setItem(
        "jewelryValueCalculatorItems",
        JSON.stringify(updatedItems)
      );

      console.log("تم حذف القطعة وحفظ التغييرات بنجاح");
    } catch (error) {
      console.error("خطأ في حذف القطعة:", error);
      // في حالة فشل الحفظ، يمكن إظهار رسالة خطأ للمستخدم
      Alert.alert("خطأ", "حدث خطأ أثناء حذف القطعة. يرجى المحاولة مرة أخرى.", [
        { text: "موافق" },
      ]);
    }
  };

  // وظائف الحفظ والتحميل (منفصلة عن حاسبة الزكاة)
  const loadSavedItems = async () => {
    try {
      const saved = await AsyncStorage.getItem("jewelryValueCalculatorItems");
      if (saved) {
        setJewelryItems(JSON.parse(saved));
      }
    } catch (error) {
      console.error("خطأ في تحميل القطع المحفوظة:", error);
    }
  };

  // تحميل القطع المحفوظة عند بدء التطبيق
  useEffect(() => {
    loadSavedItems();
  }, []);

  // وظائف مساعدة
  const formatCurrency = (amount) => {
    return `${amount.toLocaleString("en-US")} جنيه`;
  };

  // دالة لتصفية النص وإزالة الأرقام العربية
  const filterEnglishNumbers = (text) => {
    // إزالة الأرقام العربية واستبدالها بالإنجليزية
    const arabicToEnglish = {
      "٠": "0",
      "١": "1",
      "٢": "2",
      "٣": "3",
      "٤": "4",
      "٥": "5",
      "٦": "6",
      "٧": "7",
      "٨": "8",
      "٩": "9",
    };

    let filteredText = text;
    Object.keys(arabicToEnglish).forEach((arabic) => {
      filteredText = filteredText.replace(
        new RegExp(arabic, "g"),
        arabicToEnglish[arabic]
      );
    });

    // إزالة أي أحرف غير مسموحة (فقط الأرقام الإنجليزية والنقطة)
    filteredText = filteredText.replace(/[^0-9.]/g, "");

    // التأكد من وجود نقطة واحدة فقط
    const parts = filteredText.split(".");
    if (parts.length > 2) {
      filteredText = parts[0] + "." + parts.slice(1).join("");
    }

    return filteredText;
  };

  // الحصول على عيارات المعدن من API مع ترتيب الخيار المحدد في المنتصف
  const getPurityOptions = (metalType, selectedPurity = null) => {
    if (!prices) return [];

    const metalGroup = prices.find((p) => p.metal_type === metalType);
    if (!metalGroup) return [];

    const allPurities = metalGroup.prices.map((p) => p.purity);

    // إذا لم يكن هناك خيار محدد، نعرض الخيارات كما هي مع وضع الخيار الافتراضي في المنتصف
    if (!selectedPurity) {
      const defaultPurity = metalType === "gold" ? "21K" : "999";
      return arrangePurityOptionsWithCenter(allPurities, defaultPurity);
    }

    // ترتيب الخيارات بحيث يكون الخيار المحدد في المنتصف
    return arrangePurityOptionsWithCenter(allPurities, selectedPurity);
  };

  // وظيفة لترتيب خيارات النقاء بحيث يكون الخيار المحدد في المنتصف
  const arrangePurityOptionsWithCenter = (purities, centerPurity) => {
    if (!purities || purities.length === 0) return [];

    // إذا كان الخيار المحدد غير موجود في القائمة، نعيد القائمة كما هي
    if (!purities.includes(centerPurity)) return purities;

    // إذا كان هناك خيار واحد فقط، نعيده
    if (purities.length === 1) return purities;

    // إزالة الخيار المحدد من القائمة
    const otherPurities = purities.filter((p) => p !== centerPurity);

    // تقسيم الخيارات الأخرى إلى نصفين
    const halfLength = Math.floor(otherPurities.length / 2);
    const leftSide = otherPurities.slice(0, halfLength);
    const rightSide = otherPurities.slice(halfLength);

    // ترتيب الخيارات: النصف الأول + الخيار المحدد + النصف الثاني
    return [...leftSide, centerPurity, ...rightSide];
  };

  // الحصول على اسم العيار بالعربية من API
  const getPurityName = (purity) => {
    if (!metalPurities) {
      // Fallback للأسماء الافتراضية
      if (purity.includes("K")) {
        return `عيار ${purity}`;
      } else {
        return `فضة ${purity}`;
      }
    }

    // استخدام الأسماء من API
    const purityInfo = metalPurities.find((p) => p.purity === purity);
    return purityInfo ? purityInfo.name_ar : purity;
  };

  // الحصول على اسم المعدن بالعربية من API
  const getMetalTypeName = (metalType) => {
    if (!metalTypes) {
      // Fallback للأسماء الافتراضية
      return metalType === "gold" ? "الذهب" : "الفضة";
    }

    const metalInfo = metalTypes.find((m) => m.name_en === metalType);
    return metalInfo ? metalInfo.name_ar : metalType;
  };

  // عرض حالة التحميل
  if (pricesLoading || metalTypesLoading || puritiesLoading) {
    return <Loading text="جاري تحميل بيانات المعادن..." />;
  }

  // عرض حالة الخطأ
  if (pricesError || metalTypesError || puritiesError) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedStatusBar />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <ThemedText
            variant="primary"
            size="lg"
            weight="bold"
            style={styles.errorTitle}
          >
            خطأ في تحميل البيانات
          </ThemedText>
          <ThemedText variant="secondary" size="md" style={styles.errorMessage}>
            تعذر تحميل بيانات المعادن والأسعار. يرجى المحاولة مرة أخرى.
          </ThemedText>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedStatusBar />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* النموذج المضغوط لإضافة المجوهرات */}
        <ThemedCard
          style={[
            styles.compactJewelryCard,
            { backgroundColor: colors.surface },
          ]}
        >
          <View style={styles.compactCardHeader}>
            <MaterialIcons name="add-circle" size={20} color={colors.primary} />
            <ThemedText variant="primary" size="md" weight="bold">
              إضافة قطعة مجوهرات
            </ThemedText>
          </View>

          <View style={styles.compactFormContainer}>
            {/* اسم القطعة */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                اسم القطعة (اختياري)
              </ThemedText>
              <TextInput
                style={[
                  styles.compactInput,
                  {
                    color: colors.textPrimary,
                    borderColor: formErrors.name ? colors.error : colors.border,
                    backgroundColor: colors.surface,
                  },
                ]}
                placeholder="مثال: خاتم ذهب"
                placeholderTextColor={colors.textSecondary}
                value={formData.name}
                onChangeText={(text) => updateFormField("name", text)}
              />
              {formErrors.name && (
                <ThemedText variant="error" size="xs">
                  {formErrors.name}
                </ThemedText>
              )}
            </View>

            {/* نوع المعدن */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                نوع المعدن{" "}
                <ThemedText style={{ color: colors.error }}>*</ThemedText>
              </ThemedText>
              <View style={styles.compactMetalTypeButtons}>
                {metalTypes &&
                  metalTypes.map((metalType) => (
                    <TouchableOpacity
                      key={metalType.id}
                      style={[
                        styles.compactMetalButton,
                        {
                          borderColor: colors.border,
                          backgroundColor: colors.surface,
                        },
                        formData.metalType === metalType.name_en && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary,
                        },
                      ]}
                      onPress={() => {
                        updateFormField("metalType", metalType.name_en);
                        setSelectedMetalType(metalType.name_en);
                        // إعادة تعيين العيار عند تغيير نوع المعدن (عيار 21 للذهب)
                        updateFormField(
                          "purity",
                          metalType.name_en === "gold" ? "21K" : "999"
                        );
                      }}
                    >
                      <ThemedText
                        variant={
                          formData.metalType === metalType.name_en
                            ? "inverse"
                            : "primary"
                        }
                        size="xs"
                        weight={
                          formData.metalType === metalType.name_en
                            ? "bold"
                            : "regular"
                        }
                      >
                        {metalType.name_ar}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
              </View>
              {formErrors.metalType && (
                <ThemedText variant="error" size="xs">
                  {formErrors.metalType}
                </ThemedText>
              )}
            </View>

            {/* العيار */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                العيار{" "}
                <ThemedText style={{ color: colors.error }}>*</ThemedText>
              </ThemedText>
              <ScrollView
                ref={purityScrollRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.compactPurityScroll}
              >
                {getPurityOptions(formData.metalType, formData.purity).map(
                  (purity) => (
                    <TouchableOpacity
                      key={purity}
                      style={[
                        styles.compactPurityButton,
                        {
                          borderColor: colors.border,
                          backgroundColor: colors.surface,
                        },
                        formData.purity === purity && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary,
                        },
                      ]}
                      onPress={() => updateFormField("purity", purity)}
                    >
                      <ThemedText
                        variant={
                          formData.purity === purity ? "inverse" : "primary"
                        }
                        size="xs"
                        weight={formData.purity === purity ? "bold" : "regular"}
                        style={{ textAlign: "center" }}
                      >
                        {getPurityName(purity)}
                      </ThemedText>
                    </TouchableOpacity>
                  )
                )}
              </ScrollView>
              {formErrors.purity && (
                <ThemedText variant="error" size="xs">
                  {formErrors.purity}
                </ThemedText>
              )}
            </View>

            {/* عرض السعر الحالي المضغوط */}
            {currentPrice && (
              <LinearGradient
                colors={[colors.primary + "20", colors.primary + "10"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.compactCurrentPriceContainer}
              >
                <MaterialIcons
                  name="trending-up"
                  size={16}
                  color={colors.primary}
                />
                <View style={styles.compactPriceInfo}>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="primary"
                    size="xs"
                    weight="bold"
                  >
                    السعر: {currentPrice.toLocaleString("en-US")} ج/جم
                  </ThemedText>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="secondary"
                    size="xs"
                  >
                    {getMetalTypeName(formData.metalType)} -{" "}
                    {getPurityName(formData.purity)}
                  </ThemedText>
                </View>
              </LinearGradient>
            )}

            {/* الوزن */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                الوزن (جرام){" "}
                <ThemedText style={{ color: colors.error }}>*</ThemedText>
              </ThemedText>
              <TextInput
                style={[
                  styles.compactInput,
                  {
                    color: colors.textPrimary,
                    borderColor: formErrors.weight
                      ? colors.error
                      : colors.border,
                    backgroundColor: colors.surface,
                  },
                ]}
                placeholder="0.00"
                placeholderTextColor={colors.textSecondary}
                value={formData.weight}
                onChangeText={(text) => {
                  const filteredText = filterEnglishNumbers(text);
                  updateFormField("weight", filteredText);
                }}
                keyboardType="numeric"
              />
              {formErrors.weight && (
                <ThemedText
                  style={{ textAlign: "left" }}
                  variant="error"
                  size="xs"
                >
                  {formErrors.weight}
                </ThemedText>
              )}
            </View>

            {/* زر الإضافة المضغوط */}
            <TouchableOpacity
              style={[
                styles.compactAddPieceButton,
                {
                  backgroundColor: isFormValid
                    ? colors.primary
                    : colors.textSecondary,
                  opacity: isFormValid ? 1 : 0.6,
                },
              ]}
              onPress={addJewelryItemFromForm}
              disabled={!isFormValid}
            >
              <MaterialIcons name="add" size={16} color={colors.surface} />
              <ThemedText variant="inverse" size="sm" weight="bold">
                إضافة قطعة
              </ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedCard>

        {/* جدول إدارة القطع المحفوظة */}
        {jewelryItems.length > 0 && (
          <ThemedCard
            style={[styles.resultCard, { backgroundColor: colors.surface }]}
          >
            <View style={styles.cardHeader}>
              <MaterialIcons name="list" size={24} color={colors.primary} />
              <ThemedText variant="primary" size="lg" weight="bold">
                القطع المضافة ({jewelryItems.length})
              </ThemedText>
            </View>

            {/* رأس الجدول */}
            <View
              style={[styles.tableHeader, { backgroundColor: colors.primary }]}
            >
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                #
              </ThemedText>
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                الاسم
              </ThemedText>
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                المعدن
              </ThemedText>
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                العيار
              </ThemedText>
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                الوزن
              </ThemedText>
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                القيمة
              </ThemedText>
              <ThemedText
                variant="inverse"
                size="xs"
                weight="bold"
                style={styles.tableHeaderText}
              >
                إجراءات
              </ThemedText>
            </View>

            {/* صفوف الجدول */}
            {jewelryItems.map((item, index) => (
              <View
                key={item.id}
                style={[
                  styles.tableRow,
                  {
                    backgroundColor:
                      index % 2 === 0 ? colors.background : colors.surface,
                  },
                ]}
              >
                {/* الرقم */}
                <ThemedText
                  variant="primary"
                  size="xs"
                  style={styles.tableCellText}
                >
                  {index + 1}
                </ThemedText>

                {/* الاسم */}
                <ThemedText
                  variant="primary"
                  size="xs"
                  style={styles.tableCellText}
                >
                  {item.name}
                </ThemedText>

                {/* نوع المعدن */}
                <ThemedText
                  variant="primary"
                  size="xs"
                  style={styles.tableCellText}
                >
                  {getMetalTypeName(item.metalType)}
                </ThemedText>

                {/* العيار */}
                <ThemedText
                  variant="primary"
                  size="xs"
                  style={styles.tableCellText}
                >
                  {getPurityName(item.purity)}
                </ThemedText>

                {/* الوزن */}
                <ThemedText
                  variant="primary"
                  size="xs"
                  style={styles.tableCellText}
                >
                  {item.weight} جم
                </ThemedText>

                {/* القيمة */}
                <ThemedText
                  variant="primary"
                  size="xs"
                  weight="bold"
                  style={styles.tableCellText}
                >
                  {formatCurrency(item.value)}
                </ThemedText>

                {/* الإجراءات */}
                <View style={styles.tableActions}>
                  <TouchableOpacity
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.error },
                    ]}
                    onPress={() => confirmDeleteItem(item.id)}
                  >
                    <MaterialIcons
                      name="delete"
                      size={16}
                      color={colors.surface}
                    />
                  </TouchableOpacity>
                </View>
              </View>
            ))}

            {/* إجمالي القيمة المحسن */}
            <LinearGradient
              colors={[colors.primary, colors.primaryDark || colors.primary]}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
              style={styles.enhancedTotalRow}
            >
              <View style={styles.totalIconContainer}>
                <MaterialIcons
                  name="account-balance-wallet"
                  size={32}
                  color={colors.surface}
                />
              </View>
              <View style={styles.totalTextContainer}>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  style={styles.totalLabel}
                >
                  إجمالي القيمة التقديرية
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="xl"
                  weight="bold"
                  style={styles.totalValue}
                >
                  {formatCurrency(totalValue)}
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="xs"
                  style={styles.totalNote}
                >
                  القيمة الأساسية للمعدن
                </ThemedText>
              </View>
            </LinearGradient>
          </ThemedCard>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SPACING.xl + 80, // إضافة مساحة إضافية لمنع اختفاء العناصر أسفل التاب بار
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: SPACING.xl,
  },
  errorTitle: {
    marginTop: SPACING.md,
    textAlign: "center",
  },
  errorMessage: {
    marginTop: SPACING.sm,
    textAlign: "center",
  },
  heroCard: {
    margin: SPACING.md,
    borderRadius: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  heroContent: {
    alignItems: "center",
    padding: SPACING.xl,
  },
  heroTitle: {
    marginTop: SPACING.md,
    textAlign: "center",
  },
  heroSubtitle: {
    marginTop: SPACING.sm,
    textAlign: "center",
    opacity: 0.9,
  },
  craftingCard: {
    margin: SPACING.md,
    borderRadius: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardHeader: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
    gap: SPACING.sm,
  },
  inputGroup: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.lg,
    gap: SPACING.xs,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: SPACING.sm,
    fontSize: 16,
    textAlign: "right", // RTL: محاذاة النص لليمين
  },
  inputHint: {
    textAlign: "right", // RTL: محاذاة النص لليمين
    opacity: 0.7,
  },
  // Compact Form Styles
  compactJewelryCard: {
    margin: SPACING.sm,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  compactCardHeader: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    padding: SPACING.sm,
    paddingBottom: SPACING.xs,
    gap: SPACING.xs,
  },
  compactFormContainer: {
    padding: SPACING.sm,
    gap: SPACING.xs,
  },
  compactInputGroup: {
    gap: 2,
  },
  compactInput: {
    borderWidth: 1,
    borderRadius: 6,
    padding: SPACING.xs,
    fontSize: 14,
    textAlign: "right", // RTL: محاذاة النص لليمين
    height: 36,
  },
  // Compact Metal Type Buttons
  compactMetalTypeButtons: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    gap: SPACING.xs,
  },
  compactMetalButton: {
    flex: 1,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: "center",
    height: 32,
  },
  // Compact Purity Buttons
  compactPurityScroll: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
  },
  compactPurityButton: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    borderWidth: 1,
    marginRight: SPACING.xs, // RTL: تباعد من اليمين
    alignItems: "center",
    justifyContent: "center", // Center content both horizontally and vertically
    minWidth: 70, // زيادة العرض قليلاً لتمركز أفضل
    height: 32,
  },
  metalTypeButtons: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    gap: SPACING.sm,
  },
  metalButton: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
  },
  purityScroll: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
  },
  purityButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: SPACING.sm, // RTL: تباعد من اليمين
    alignItems: "center",
    minWidth: 80,
  },
  // Compact Add Button
  compactAddPieceButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    gap: SPACING.xs,
    marginTop: SPACING.xs,
    height: 36,
  },
  // Compact Current Price Display
  compactCurrentPriceContainer: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    alignItems: "center",
    padding: SPACING.xs,
    borderRadius: 8,
    gap: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  compactPriceInfo: {
    flex: 1,
  },
  addPieceButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 12,
    gap: SPACING.sm,
    marginTop: SPACING.sm,
  },
  // Current Price Display Styles
  currentPriceContainer: {
    flexDirection: "row-reverse", // LTR: من اليسار إلى اليمين
    alignItems: "center",
    padding: SPACING.md,
    borderRadius: 12,
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  priceInfo: {
    flex: 1,
  },
  // Styles للجدول
  resultCard: {
    margin: SPACING.md,
    borderRadius: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  saveButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    gap: SPACING.xs,
  },
  tableHeader: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  tableHeaderText: {
    flex: 1,
    textAlign: "center",
    fontSize: 10,
  },
  tableRow: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "rgba(0,0,0,0.1)",
  },
  tableCell: {
    flex: 1,
    alignItems: "center",
  },
  tableCellText: {
    flex: 1,
    textAlign: "center",
    fontSize: 10,
  },
  tableInput: {
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    fontSize: 10,
    textAlign: "center",
    minWidth: 60,
  },
  tableActions: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    gap: SPACING.xs,
    flex: 1,
    justifyContent: "center",
  },
  actionButton: {
    padding: SPACING.xs,
    borderRadius: 4,
    alignItems: "center",
    justifyContent: "center",
  },
  // Enhanced Total Row Styles
  enhancedTotalRow: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    alignItems: "center",
    padding: SPACING.lg,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
    gap: SPACING.md,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
  },
  totalIconContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  totalTextContainer: {
    flex: 1,
    alignItems: "flex-start", // RTL: محاذاة لليمين
  },
  totalLabel: {
    textAlign: "right", // RTL: محاذاة النص لليمين
    opacity: 0.9,
  },
  totalValue: {
    textAlign: "right", // RTL: محاذاة النص لليمين
    marginTop: SPACING.xs,
  },
  totalNote: {
    textAlign: "right", // RTL: محاذاة النص لليمين
    opacity: 0.8,
    marginTop: SPACING.xs,
  },
});

export default JewelryCalculatorScreen;
