// حاسبة الزكاة - Zakat Calculator
import React, { useState, useEffect, useRef } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  I18nManager,
  Alert,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  useCurrentPrices,
  useZakatFormulas,
  useMetalTypes,
  useMetalPurities,
} from "../../src/utils/hooks";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  Loading,
} from "../../src/components";
import AsyncStorage from "@react-native-async-storage/async-storage";

// تفعيل RTL - صفحة الحاسبات تستخدم اتجاه RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const ZakatCalculatorScreen = () => {
  const { colors } = useTheme();

  // State للمجوهرات والحسابات
  const [jewelryItems, setJewelryItems] = useState([]);
  const [totalValue, setTotalValue] = useState(0);
  const [totalGoldWeight, setTotalGoldWeight] = useState(0);
  const [totalSilverWeight, setTotalSilverWeight] = useState(0);
  const [zakatAmount, setZakatAmount] = useState(0);
  const [isZakatDue, setIsZakatDue] = useState(false);
  const [nisabInfo, setNisabInfo] = useState(null);
  const [calculationInfo, setCalculationInfo] = useState(null);
  const [selectedMetalType, setSelectedMetalType] = useState("gold");

  // State لعرض السعر الحالي
  const [currentPrice, setCurrentPrice] = useState(null);

  // State للنموذج الموحد
  const [formData, setFormData] = useState({
    name: "",
    metalType: "gold",
    purity: "21K",
    weight: "",
  });
  const [formErrors, setFormErrors] = useState({});
  const [isFormValid, setIsFormValid] = useState(false);

  // مرجع للـ ScrollView الخاص بخيارات النقاء
  const purityScrollRef = useRef(null);

  // جلب البيانات من API باستخدام الـ hooks الجديدة
  const {
    data: prices,
    loading: pricesLoading,
    error: pricesError,
  } = useCurrentPrices();

  const {
    data: zakatFormulas,
    loading: formulasLoading,
    error: formulasError,
    refetch: refetchFormulas,
  } = useZakatFormulas();

  const {
    data: metalTypes,
    loading: metalTypesLoading,
    error: metalTypesError,
    refetch: refetchMetalTypes,
  } = useMetalTypes();

  const { data: metalPurities } = useMetalPurities(selectedMetalType);

  // حساب النصاب من المعادلات والأسعار الحالية (API-First)
  useEffect(() => {
    if (
      prices &&
      prices.length > 0 &&
      zakatFormulas &&
      zakatFormulas.length > 0
    ) {
      const goldPrices = prices.find((p) => p.metal_type === "gold");
      const silverPrices = prices.find((p) => p.metal_type === "silver");

      const gold24Price =
        goldPrices?.prices?.find((p) => p.purity === "24K")?.price_per_gram ||
        0;
      const silver999Price =
        silverPrices?.prices?.find((p) => p.purity === "999")?.price_per_gram ||
        0;

      // الحصول على قيم النصاب من المعادلات (بدلاً من القيم الثابتة)
      const goldNisabWeight =
        zakatFormulas.find((f) => f.name === "نصاب الذهب")?.value || 85;
      const silverNisabWeight =
        zakatFormulas.find((f) => f.name === "نصاب الفضة")?.value || 595;

      const goldNisabValue = goldNisabWeight * gold24Price;
      const silverNisabValue = silverNisabWeight * silver999Price;

      setNisabInfo({
        gold: {
          weight_grams: goldNisabWeight,
          price_per_gram: gold24Price,
          value: goldNisabValue,
        },
        silver: {
          weight_grams: silverNisabWeight,
          price_per_gram: silver999Price,
          value: silverNisabValue,
        },
        applicable: {
          type: "gold", // دائماً نصاب الذهب (الطريقة الشرعية الصحيحة)
          value: goldNisabValue,
          weight_grams: goldNisabWeight,
        },
      });
    }
  }, [prices, zakatFormulas]);

  // تحديث السعر الحالي عند تغيير نوع المعدن أو العيار
  useEffect(() => {
    if (formData.metalType && formData.purity && prices) {
      const metalGroup = prices.find(
        (group) => group.metal_type === formData.metalType
      );
      if (metalGroup) {
        const priceInfo = metalGroup.prices.find(
          (p) => p.purity === formData.purity
        );
        setCurrentPrice(priceInfo ? priceInfo.price_per_gram : null);
      } else {
        setCurrentPrice(null);
      }
    }
  }, [formData.metalType, formData.purity, prices]);

  // وظائف النموذج الموحد
  const validateForm = () => {
    const errors = {};

    // اسم القطعة اختياري - لا نحتاج للتحقق منه

    if (!formData.metalType) {
      errors.metalType = "يرجى اختيار نوع المعدن";
    }

    if (!formData.purity) {
      errors.purity = "يرجى اختيار عيار المعدن";
    }

    const weight = parseFloat(formData.weight);
    if (!formData.weight || isNaN(weight) || weight <= 0) {
      errors.weight = "يرجى إدخال وزن صحيح أكبر من صفر";
    }

    setFormErrors(errors);
    const isValid = Object.keys(errors).length === 0;
    setIsFormValid(isValid);
    return isValid;
  };

  const updateFormField = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // إزالة رسالة الخطأ عند التعديل
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: "" }));
    }

    // إذا كان التحديث للنقاء، قم بالتمرير التلقائي للخيار المحدد
    if (field === "purity") {
      scrollToCenterPurity(value);
    }
  };

  // وظيفة للتمرير التلقائي إلى الخيار المحدد في منتصف الشاشة
  const scrollToCenterPurity = (selectedPurity) => {
    if (!purityScrollRef.current) return;

    setTimeout(() => {
      const purities = getPurityOptions(formData.metalType, selectedPurity);
      const selectedIndex = purities.findIndex((p) => p === selectedPurity);

      if (selectedIndex !== -1) {
        // حساب موقع التمرير لوضع الخيار المحدد في المنتصف
        const buttonWidth = 80; // عرض تقريبي لكل زر
        const spacing = 8; // المسافة بين الأزرار
        const scrollPosition = Math.max(
          0,
          selectedIndex * (buttonWidth + spacing) - buttonWidth * 1.5
        );

        purityScrollRef.current.scrollTo({
          x: scrollPosition,
          animated: true,
        });
      }
    }, 100); // تأخير قصير للسماح بإعادة الرندر
  };

  const resetForm = () => {
    setFormData({
      name: "",
      metalType: "gold",
      purity: "21K",
      weight: "",
    });
    setFormErrors({});
    setIsFormValid(false);
  };

  const addJewelryItemFromForm = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const weight = parseFloat(formData.weight);
      const value = calculateItemValue({
        metalType: formData.metalType,
        purity: formData.purity,
        weight: weight,
      });

      const newItem = {
        id: Date.now().toString(),
        name: formData.name.trim() || `قطعة ${jewelryItems.length + 1}`,
        metalType: formData.metalType,
        purity: formData.purity,
        weight: weight,
        value: value,
      };

      const updatedItems = [...jewelryItems, newItem];
      setJewelryItems(updatedItems);

      // حفظ تلقائي في AsyncStorage عند إضافة قطعة جديدة
      await AsyncStorage.setItem(
        "savedJewelryItems",
        JSON.stringify(updatedItems)
      );

      resetForm();
      console.log("تم إضافة القطعة وحفظها تلقائياً");
    } catch (error) {
      console.error("خطأ في إضافة القطعة:", error);
      Alert.alert(
        "خطأ",
        "حدث خطأ أثناء إضافة القطعة. يرجى المحاولة مرة أخرى.",
        [{ text: "موافق" }]
      );
    }
  };

  // حساب قيمة قطعة مجوهرات واحدة باستخدام البيانات الحقيقية من API
  const calculateItemValue = (item) => {
    const weight = parseFloat(item.weight);
    if (isNaN(weight) || weight <= 0 || !prices) return 0;

    const metalGroup = prices.find(
      (group) => group.metal_type === item.metalType
    );

    if (!metalGroup) return 0;

    const priceInfo = metalGroup.prices.find((p) => p.purity === item.purity);
    if (!priceInfo) return 0;

    return weight * priceInfo.price_per_gram;
  };

  // تحديث قيم القطع المحفوظة عند تغيير الأسعار
  const updateSavedItemsValues = async () => {
    if (jewelryItems.length > 0 && prices) {
      const updatedItems = jewelryItems.map((item) => {
        const newValue = calculateItemValue(item);
        return {
          ...item,
          value: newValue,
        };
      });

      // التحقق من أن القيم تغيرت فعلاً قبل التحديث
      const hasChanged = updatedItems.some(
        (item, index) => item.value !== jewelryItems[index]?.value
      );

      if (hasChanged) {
        setJewelryItems(updatedItems);

        // حفظ القيم المحدثة في AsyncStorage
        try {
          await AsyncStorage.setItem(
            "savedJewelryItems",
            JSON.stringify(updatedItems)
          );
        } catch (error) {
          console.error("خطأ في تحديث القيم المحفوظة:", error);
        }
      }
    }
  };

  // تحديث القيم عند تغيير الأسعار (مع تجنب الحلقة اللا نهائية)
  useEffect(() => {
    if (prices && jewelryItems.length > 0) {
      updateSavedItemsValues();
    }
  }, [prices]);

  // حساب الإجماليات والزكاة بالطريقة الشرعية الصحيحة
  const calculateTotals = () => {
    let newTotalValue = 0;
    let newTotalGoldWeight = 0;
    let newTotalSilverWeight = 0;

    jewelryItems.forEach((item) => {
      newTotalValue += item.value;
      const weight = parseFloat(item.weight) || 0;

      if (item.metalType === "gold") {
        newTotalGoldWeight += weight;
      } else if (item.metalType === "silver") {
        newTotalSilverWeight += weight;
      }
    });

    setTotalValue(newTotalValue);
    setTotalGoldWeight(newTotalGoldWeight);
    setTotalSilverWeight(newTotalSilverWeight);

    // حساب الزكاة بالطريقة الشرعية الصحيحة
    calculateZakatCorrectly(newTotalValue);
  };

  // حساب الزكاة بالطريقة الشرعية الصحيحة
  const calculateZakatCorrectly = (totalValue) => {
    if (!nisabInfo || !prices || !zakatFormulas) {
      setIsZakatDue(false);
      setZakatAmount(0);
      return;
    }

    const goldPrice24K = nisabInfo.gold.price_per_gram;
    const goldNisabWeight = nisabInfo.applicable.weight_grams || 85;

    if (goldPrice24K <= 0) {
      setIsZakatDue(false);
      setZakatAmount(0);
      return;
    }

    // تحويل جميع المعادن إلى وزن ذهب 24 عيار مكافئ
    let totalGoldEquivalentWeight = 0;

    jewelryItems.forEach((item) => {
      if (item.metalType === "gold") {
        // تحويل الذهب إلى 24 عيار
        const goldEquivalentWeight = convertGoldTo24K(item.weight, item.purity);
        totalGoldEquivalentWeight += goldEquivalentWeight;
      } else if (item.metalType === "silver") {
        // تحويل الفضة إلى قيمة ذهب 24 عيار
        const silverValue = item.value;
        const goldEquivalentWeight = silverValue / goldPrice24K;
        totalGoldEquivalentWeight += goldEquivalentWeight;
      }
    });

    // فحص النصاب: هل المجموع يساوي أو يزيد عن 85 جرام ذهب 24 عيار؟
    if (totalGoldEquivalentWeight >= goldNisabWeight) {
      setIsZakatDue(true);
      // حساب الزكاة على كامل القيمة (2.5%)
      const zakatPercentage =
        zakatFormulas.find((f) => f.name === "نسبة الزكاة")?.value || 2.5;
      setZakatAmount(totalValue * (zakatPercentage / 100));
    } else {
      setIsZakatDue(false);
      setZakatAmount(0);
    }

    // حفظ معلومات إضافية للعرض
    setCalculationInfo({
      total_gold_equivalent_weight: totalGoldEquivalentWeight,
      formatted_gold_equivalent: totalGoldEquivalentWeight.toFixed(3),
      nisab_weight: goldNisabWeight,
      nisab_reached: totalGoldEquivalentWeight >= goldNisabWeight,
      shortage:
        totalGoldEquivalentWeight >= goldNisabWeight
          ? 0
          : goldNisabWeight - totalGoldEquivalentWeight,
      formatted_shortage:
        totalGoldEquivalentWeight >= goldNisabWeight
          ? "0"
          : (goldNisabWeight - totalGoldEquivalentWeight).toFixed(3),
    });
  };

  // تحويل وزن الذهب إلى 24 عيار
  const convertGoldTo24K = (weight, purity) => {
    // استخراج الرقم من العيار (مثل 21 من 21K)
    const purityNumber = parseFloat(purity.replace("K", ""));

    // تحويل إلى 24 عيار: (الوزن × العيار الحالي) ÷ 24
    return (weight * purityNumber) / 24;
  };

  // حساب الإجماليات عند تغيير البيانات
  useEffect(() => {
    calculateTotals();
  }, [jewelryItems, nisabInfo]);

  const formatCurrency = (amount) => {
    return `${amount.toLocaleString("en-US")} جنيه`;
  };

  // دالة لتصفية النص وإزالة الأرقام العربية
  const filterEnglishNumbers = (text) => {
    // إزالة الأرقام العربية واستبدالها بالإنجليزية
    const arabicToEnglish = {
      "٠": "0",
      "١": "1",
      "٢": "2",
      "٣": "3",
      "٤": "4",
      "٥": "5",
      "٦": "6",
      "٧": "7",
      "٨": "8",
      "٩": "9",
    };

    let filteredText = text;
    Object.keys(arabicToEnglish).forEach((arabic) => {
      filteredText = filteredText.replace(
        new RegExp(arabic, "g"),
        arabicToEnglish[arabic]
      );
    });

    // إزالة أي أحرف غير مسموحة (فقط الأرقام الإنجليزية والنقطة)
    filteredText = filteredText.replace(/[^0-9.]/g, "");

    // التأكد من وجود نقطة واحدة فقط
    const parts = filteredText.split(".");
    if (parts.length > 2) {
      filteredText = parts[0] + "." + parts.slice(1).join("");
    }

    return filteredText;
  };

  // الحصول على عيارات المعدن من API مع ترتيب الخيار المحدد في المنتصف
  const getPurityOptions = (metalType, selectedPurity = null) => {
    if (!prices) return [];

    const metalGroup = prices.find((p) => p.metal_type === metalType);
    if (!metalGroup) return [];

    const allPurities = metalGroup.prices.map((p) => p.purity);

    // إذا لم يكن هناك خيار محدد، نعرض الخيارات كما هي مع وضع الخيار الافتراضي في المنتصف
    if (!selectedPurity) {
      const defaultPurity = metalType === "gold" ? "21K" : "999";
      return arrangePurityOptionsWithCenter(allPurities, defaultPurity);
    }

    // ترتيب الخيارات بحيث يكون الخيار المحدد في المنتصف
    return arrangePurityOptionsWithCenter(allPurities, selectedPurity);
  };

  // وظيفة لترتيب خيارات النقاء بحيث يكون الخيار المحدد في المنتصف
  const arrangePurityOptionsWithCenter = (purities, centerPurity) => {
    if (!purities || purities.length === 0) return [];

    // إذا كان الخيار المحدد غير موجود في القائمة، نعيد القائمة كما هي
    if (!purities.includes(centerPurity)) return purities;

    // إذا كان هناك خيار واحد فقط، نعيده
    if (purities.length === 1) return purities;

    // إزالة الخيار المحدد من القائمة
    const otherPurities = purities.filter((p) => p !== centerPurity);

    // تقسيم الخيارات الأخرى إلى نصفين
    const halfLength = Math.floor(otherPurities.length / 2);
    const leftSide = otherPurities.slice(0, halfLength);
    const rightSide = otherPurities.slice(halfLength);

    // ترتيب الخيارات: النصف الأول + الخيار المحدد + النصف الثاني
    return [...leftSide, centerPurity, ...rightSide];
  };

  // الحصول على اسم العيار بالعربية من API
  const getPurityName = (purity) => {
    if (!metalPurities) {
      // Fallback للأسماء الافتراضية
      if (purity.includes("K")) {
        return `عيار ${purity}`;
      } else {
        return `فضة ${purity}`;
      }
    }

    // استخدام الأسماء من API
    const purityInfo = metalPurities.find((p) => p.purity === purity);
    return purityInfo ? purityInfo.name_ar : purity;
  };

  // الحصول على اسم المعدن بالعربية من API
  const getMetalTypeName = (metalType) => {
    if (!metalTypes) {
      // Fallback للأسماء الافتراضية
      return metalType === "gold" ? "الذهب" : "الفضة";
    }

    const metalInfo = metalTypes.find((m) => m.name_en === metalType);
    return metalInfo ? metalInfo.name_ar : metalType;
  };

  // وظائف إدارة القطع المحفوظة

  const loadSavedItems = async () => {
    try {
      const saved = await AsyncStorage.getItem("savedJewelryItems");
      if (saved) {
        const loadedItems = JSON.parse(saved);
        setJewelryItems(loadedItems);
      }
    } catch (error) {
      console.error("خطأ في تحميل القطع المحفوظة:", error);
    }
  };

  const confirmDeleteItem = (id) => {
    Alert.alert(
      "تأكيد الحذف",
      "هل أنت متأكد من حذف هذه القطعة؟ لا يمكن التراجع عن هذا الإجراء.",
      [
        {
          text: "إلغاء",
          style: "cancel",
        },
        {
          text: "حذف",
          style: "destructive",
          onPress: () => deleteJewelryItem(id),
        },
      ]
    );
  };

  const deleteJewelryItem = async (id) => {
    try {
      // تحديث الـ state المحلي
      const updatedItems = jewelryItems.filter((item) => item.id !== id);
      setJewelryItems(updatedItems);

      // حفظ التغييرات في AsyncStorage فوراً
      await AsyncStorage.setItem(
        "savedJewelryItems",
        JSON.stringify(updatedItems)
      );

      console.log("تم حذف القطعة وحفظ التغييرات بنجاح");
    } catch (error) {
      console.error("خطأ في حذف القطعة:", error);
      // في حالة فشل الحفظ، يمكن إظهار رسالة خطأ للمستخدم
      Alert.alert("خطأ", "حدث خطأ أثناء حذف القطعة. يرجى المحاولة مرة أخرى.", [
        { text: "موافق" },
      ]);
    }
  };

  const resetCalculator = () => {
    setJewelryItems([]);
  };

  // تحميل القطع المحفوظة عند بدء التطبيق
  useEffect(() => {
    loadSavedItems();
  }, []);

  // التحقق من صحة النموذج عند تغيير البيانات
  useEffect(() => {
    validateForm();
  }, [formData]);

  // التمرير التلقائي للخيار المحدد عند تحميل الصفحة أو تغيير نوع المعدن أو النقاء
  useEffect(() => {
    if (formData.purity && prices && prices.length > 0) {
      // تأخير أطول للتأكد من أن ScrollView تم رندره بالكامل
      setTimeout(() => {
        scrollToCenterPurity(formData.purity);
      }, 300);
    }
  }, [formData.metalType, formData.purity, prices]);

  // التمرير التلقائي عند انتهاء التحميل لأول مرة
  useEffect(() => {
    if (
      !pricesLoading &&
      !formulasLoading &&
      !metalTypesLoading &&
      formData.purity &&
      prices
    ) {
      // تأخير إضافي للتأكد من أن جميع العناصر تم رندرها
      setTimeout(() => {
        scrollToCenterPurity(formData.purity);
      }, 500);
    }
  }, [pricesLoading, formulasLoading, metalTypesLoading]);

  // عرض حالة التحميل (شامل لجميع البيانات المطلوبة)
  if (pricesLoading || formulasLoading || metalTypesLoading) {
    return <Loading text="جاري تحميل بيانات الزكاة..." />;
  }

  // عرض حالة الخطأ (شامل لجميع الأخطاء المحتملة)
  if (pricesError || formulasError || metalTypesError) {
    const errorMessage = pricesError || formulasError || metalTypesError;
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedStatusBar />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <ThemedText variant="primary" size="lg" style={styles.errorTitle}>
            خطأ في تحميل البيانات
          </ThemedText>
          <ThemedText variant="secondary" size="md" style={styles.errorMessage}>
            {errorMessage}
          </ThemedText>
          <TouchableOpacity
            style={[styles.resetButton, { backgroundColor: colors.primary }]}
            onPress={() => {
              if (pricesError) refetchFormulas();
              if (formulasError) refetchFormulas();
              if (metalTypesError) refetchMetalTypes();
            }}
          >
            <MaterialIcons name="refresh" size={20} color={colors.surface} />
            <ThemedText variant="inverse" size="sm" weight="bold">
              إعادة المحاولة
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedStatusBar />

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* معلومات النصاب المضغوطة */}
        {/* {nisabInfo && (
          <ThemedCard
            style={[
              styles.compactNisabCard,
              { backgroundColor: colors.surface },
            ]}
          >
            <View style={styles.compactNisabContent}>
              <MaterialIcons name="info" size={16} color={colors.primary} />
              <ThemedText variant="primary" size="xs" weight="bold">
                النصاب المطبق: {formatCurrency(nisabInfo.applicable.value)} (
                {nisabInfo.applicable.type === "gold" ? "ذهب" : "فضة"})
              </ThemedText>
            </View>
          </ThemedCard>
        )} */}

        {/* النموذج المضغوط لإضافة المجوهرات */}
        <ThemedCard
          style={[
            styles.compactJewelryCard,
            { backgroundColor: colors.surface },
          ]}
        >
          <View style={styles.compactCardHeader}>
            <MaterialIcons name="add-circle" size={20} color={colors.primary} />
            <ThemedText variant="primary" size="md" weight="bold">
              إضافة قطعة مجوهرات
            </ThemedText>
          </View>

          <View style={styles.compactFormContainer}>
            {/* اسم القطعة */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                اسم القطعة (اختياري)
              </ThemedText>
              <TextInput
                style={[
                  styles.compactInput,
                  {
                    color: colors.textPrimary,
                    borderColor: formErrors.name ? colors.error : colors.border,
                    backgroundColor: colors.surface,
                  },
                ]}
                placeholder="مثال: خاتم ذهب"
                placeholderTextColor={colors.textSecondary}
                value={formData.name}
                onChangeText={(text) => updateFormField("name", text)}
              />
              {formErrors.name && (
                <ThemedText variant="error" size="xs">
                  {formErrors.name}
                </ThemedText>
              )}
            </View>

            {/* نوع المعدن */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                نوع المعدن{" "}
                <ThemedText style={{ color: colors.error }}>*</ThemedText>
              </ThemedText>
              <View style={styles.compactMetalTypeButtons}>
                {metalTypes &&
                  metalTypes.map((metalType) => (
                    <TouchableOpacity
                      key={metalType.id}
                      style={[
                        styles.compactMetalButton,
                        {
                          borderColor: colors.border,
                          backgroundColor: colors.surface,
                        },
                        formData.metalType === metalType.name_en && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary,
                        },
                      ]}
                      onPress={() => {
                        updateFormField("metalType", metalType.name_en);
                        setSelectedMetalType(metalType.name_en);
                        // إعادة تعيين العيار عند تغيير نوع المعدن (عيار 21 للذهب)
                        updateFormField(
                          "purity",
                          metalType.name_en === "gold" ? "21K" : "999"
                        );
                      }}
                    >
                      <ThemedText
                        variant={
                          formData.metalType === metalType.name_en
                            ? "inverse"
                            : "primary"
                        }
                        size="xs"
                        weight={
                          formData.metalType === metalType.name_en
                            ? "bold"
                            : "regular"
                        }
                      >
                        {metalType.name_ar}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
              </View>
              {formErrors.metalType && (
                <ThemedText variant="error" size="xs">
                  {formErrors.metalType}
                </ThemedText>
              )}
            </View>

            {/* العيار */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                العيار{" "}
                <ThemedText style={{ color: colors.error }}>*</ThemedText>
              </ThemedText>
              <ScrollView
                ref={purityScrollRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.compactPurityScroll}
              >
                {getPurityOptions(formData.metalType, formData.purity).map(
                  (purity) => (
                    <TouchableOpacity
                      key={purity}
                      style={[
                        styles.compactPurityButton,
                        {
                          borderColor: colors.border,
                          backgroundColor: colors.surface,
                        },
                        formData.purity === purity && {
                          backgroundColor: colors.primary,
                          borderColor: colors.primary,
                        },
                      ]}
                      onPress={() => updateFormField("purity", purity)}
                    >
                      <ThemedText
                        variant={
                          formData.purity === purity ? "inverse" : "primary"
                        }
                        size="xs"
                        weight={formData.purity === purity ? "bold" : "regular"}
                      >
                        {getPurityName(purity)}
                      </ThemedText>
                    </TouchableOpacity>
                  )
                )}
              </ScrollView>
              {formErrors.purity && (
                <ThemedText variant="error" size="xs">
                  {formErrors.purity}
                </ThemedText>
              )}
            </View>

            {/* عرض السعر الحالي المضغوط */}
            {currentPrice && (
              <LinearGradient
                colors={[colors.primary + "20", colors.primary + "10"]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.compactCurrentPriceContainer}
              >
                <MaterialIcons
                  name="trending-up"
                  size={16}
                  color={colors.primary}
                />
                <View style={styles.compactPriceInfo}>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="primary"
                    size="xs"
                    weight="bold"
                  >
                    السعر: {currentPrice.toLocaleString("en-US")} ج/جم
                  </ThemedText>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="secondary"
                    size="xs"
                  >
                    {getMetalTypeName(formData.metalType)} -{" "}
                    {getPurityName(formData.purity)}
                  </ThemedText>
                </View>
              </LinearGradient>
            )}

            {/* الوزن */}
            <View style={styles.compactInputGroup}>
              <ThemedText
                style={{ textAlign: "left" }}
                variant="primary"
                size="xs"
                weight="bold"
              >
                الوزن (جرام){" "}
                <ThemedText style={{ color: colors.error }}>*</ThemedText>
              </ThemedText>
              <TextInput
                style={[
                  styles.compactInput,
                  {
                    color: colors.textPrimary,
                    borderColor: formErrors.weight
                      ? colors.error
                      : colors.border,
                    backgroundColor: colors.surface,
                  },
                ]}
                placeholder="0.00"
                placeholderTextColor={colors.textSecondary}
                value={formData.weight}
                onChangeText={(text) => {
                  const filteredText = filterEnglishNumbers(text);
                  updateFormField("weight", filteredText);
                }}
                keyboardType="numeric"
              />
              {formErrors.weight && (
                <ThemedText
                  style={{ textAlign: "left" }}
                  variant="error"
                  size="xs"
                >
                  {formErrors.weight}
                </ThemedText>
              )}
            </View>

            {/* زر الإضافة المضغوط */}
            <TouchableOpacity
              style={[
                styles.compactAddPieceButton,
                {
                  backgroundColor: isFormValid
                    ? colors.primary
                    : colors.textSecondary,
                  opacity: isFormValid ? 1 : 0.6,
                },
              ]}
              onPress={addJewelryItemFromForm}
              disabled={!isFormValid}
            >
              <MaterialIcons name="add" size={16} color={colors.surface} />
              <ThemedText variant="inverse" size="sm" weight="bold">
                إضافة قطعة
              </ThemedText>
            </TouchableOpacity>
          </View>
        </ThemedCard>

        {/* جدول إدارة القطع المحفوظة */}
        {jewelryItems.length > 0 && (
          <ThemedCard
            style={[styles.resultCard, { backgroundColor: colors.surface }]}
          >
            <View style={styles.cardHeader}>
              <MaterialIcons name="list" size={24} color={colors.primary} />
              <ThemedText variant="primary" size="lg" weight="bold">
                قائمة القطع المضافة
              </ThemedText>
              <View style={{ flex: 1 }} />
            </View>

            <View style={styles.tableContainer}>
              {/* رأس الجدول */}
              <View
                style={[
                  styles.tableHeader,
                  { backgroundColor: colors.primary },
                ]}
              >
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCell}
                >
                  #
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCell}
                >
                  اسم القطعة
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCell}
                >
                  المعدن
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCell}
                >
                  العيار
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCell}
                >
                  الوزن
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCell}
                >
                  القيمة
                </ThemedText>
                <ThemedText
                  variant="inverse"
                  size="sm"
                  weight="bold"
                  style={styles.tableCellActions}
                >
                  الإجراءات
                </ThemedText>
              </View>

              {/* صفوف الجدول */}
              {jewelryItems.map((item, index) => (
                <View
                  key={item.id}
                  style={[
                    styles.tableRow,
                    {
                      backgroundColor:
                        index % 2 === 0 ? colors.background : colors.surface,
                    },
                  ]}
                >
                  <ThemedText
                    variant="primary"
                    size="sm"
                    style={styles.tableCell}
                  >
                    {index + 1}
                  </ThemedText>

                  <ThemedText
                    variant="primary"
                    size="sm"
                    style={styles.tableCell}
                  >
                    {item.name || `قطعة ${index + 1}`}
                  </ThemedText>

                  {/* نوع المعدن */}
                  <ThemedText
                    variant="primary"
                    size="sm"
                    style={styles.tableCell}
                  >
                    {getMetalTypeName(item.metalType)}
                  </ThemedText>

                  {/* العيار */}
                  <ThemedText
                    variant="primary"
                    size="sm"
                    style={styles.tableCell}
                  >
                    {getPurityName(item.purity)}
                  </ThemedText>

                  {/* الوزن */}
                  <ThemedText
                    variant="primary"
                    size="sm"
                    style={styles.tableCell}
                  >
                    {item.weight} جم
                  </ThemedText>

                  <ThemedText
                    variant="primary"
                    size="sm"
                    style={styles.tableCell}
                  >
                    {formatCurrency(item.value)}
                  </ThemedText>

                  <View style={styles.tableCellActions}>
                    <View style={styles.editActions}>
                      <TouchableOpacity
                        style={[
                          styles.actionButton,
                          { backgroundColor: colors.error },
                        ]}
                        onPress={() => confirmDeleteItem(item.id)}
                      >
                        <MaterialIcons
                          name="delete"
                          size={16}
                          color={colors.surface}
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </ThemedCard>
        )}

        {/* نتائج الحساب */}
        {jewelryItems.length > 0 && (
          <ThemedCard
            style={[styles.resultCard, { backgroundColor: colors.surface }]}
          >
            <View style={styles.cardHeader}>
              <MaterialIcons
                name="calculate"
                size={24}
                color={colors.primary}
              />
              <ThemedText variant="primary" size="lg" weight="bold">
                نتائج حساب الزكاة
              </ThemedText>
            </View>

            <View style={styles.resultContent}>
              {/* الإجماليات */}
              <View
                style={[
                  styles.summarySection,
                  { backgroundColor: colors.background },
                ]}
              >
                <View style={styles.summaryRow}>
                  <ThemedText variant="secondary" size="sm">
                    إجمالي القيمة:
                  </ThemedText>
                  <ThemedText variant="primary" size="md" weight="bold">
                    {formatCurrency(totalValue)}
                  </ThemedText>
                </View>
                <View style={styles.summaryRow}>
                  <ThemedText variant="secondary" size="sm">
                    إجمالي وزن الذهب:
                  </ThemedText>
                  <ThemedText variant="primary" size="md" weight="bold">
                    {totalGoldWeight.toFixed(2)} جرام
                  </ThemedText>
                </View>
                <View style={styles.summaryRow}>
                  <ThemedText variant="secondary" size="sm">
                    إجمالي وزن الفضة:
                  </ThemedText>
                  <ThemedText variant="primary" size="md" weight="bold">
                    {totalSilverWeight.toFixed(2)} جرام
                  </ThemedText>
                </View>
              </View>

              {/* معلومات الحساب الشرعي */}
              {calculationInfo && (
                <View
                  style={[
                    styles.calculationSection,
                    { backgroundColor: colors.background },
                  ]}
                >
                  <View style={styles.calculationHeader}>
                    <MaterialIcons
                      name="balance"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText variant="primary" size="md" weight="bold">
                      تفاصيل الحساب الشرعي
                    </ThemedText>
                  </View>

                  <View style={styles.calculationGrid}>
                    <View style={styles.calculationItem}>
                      <ThemedText
                        align="center"
                        variant="primary"
                        size="lg"
                        weight="bold"
                      >
                        {calculationInfo.formatted_gold_equivalent}
                      </ThemedText>
                      <ThemedText align="center" variant="secondary" size="xs">
                        جرام ذهب 24 عيار مكافئ
                      </ThemedText>
                      <ThemedText align="center" variant="secondary" size="xs">
                        (مجموع جميع المعادن محولة)
                      </ThemedText>
                    </View>

                    <View style={styles.calculationItem}>
                      <ThemedText
                        align="center"
                        variant="primary"
                        size="lg"
                        weight="bold"
                      >
                        {calculationInfo.nisab_weight}
                      </ThemedText>
                      <ThemedText align="center" variant="secondary" size="xs">
                        جرام ذهب 24 عيار
                      </ThemedText>
                      <ThemedText align="center" variant="secondary" size="xs">
                        (النصاب المطلوب شرعياً)
                      </ThemedText>
                    </View>

                    <View style={styles.calculationItem}>
                      {calculationInfo.nisab_reached ? (
                        <>
                          <MaterialIcons
                            name="check-circle"
                            size={24}
                            color={colors.success}
                          />
                          <ThemedText
                            align="center"
                            variant="success"
                            size="sm"
                            weight="bold"
                          >
                            تم بلوغ النصاب
                          </ThemedText>
                          <ThemedText
                            align="center"
                            variant="secondary"
                            size="xs"
                          >
                            الزكاة واجبة
                          </ThemedText>
                        </>
                      ) : (
                        <>
                          <MaterialIcons
                            name="cancel"
                            size={24}
                            color={colors.warning}
                          />
                          <ThemedText
                            align="center"
                            variant="warning"
                            size="sm"
                            weight="bold"
                          >
                            لم يبلغ النصاب
                          </ThemedText>
                          <ThemedText
                            align="center"
                            variant="secondary"
                            size="xs"
                          >
                            نقص: {calculationInfo.formatted_shortage} جرام
                          </ThemedText>
                        </>
                      )}
                    </View>
                  </View>

                  <View
                    style={[
                      styles.islamicNote,
                      { backgroundColor: colors.primary + "20" },
                    ]}
                  >
                    <ThemedText variant="primary" size="xs">
                      <ThemedText
                        align="left"
                        variant="primary"
                        size="xs"
                        weight="bold"
                      >
                        طريقة الحساب الشرعية:
                      </ThemedText>{" "}
                      يتم تحويل جميع المعادن (ذهب وفضة) إلى قيمة الذهب 24 عيار،
                      ثم مقارنة المجموع بنصاب الذهب (85 جرام). إذا بلغ المجموع
                      النصاب أو زاد عنه، تجب الزكاة على كامل القيمة بنسبة 2.5%.
                    </ThemedText>
                  </View>
                </View>
              )}

              {/* نتيجة الزكاة */}
              <View
                style={[
                  styles.zakatResult,
                  {
                    backgroundColor: isZakatDue
                      ? colors.success
                      : colors.warning,
                  },
                ]}
              >
                <MaterialIcons
                  name={isZakatDue ? "check-circle" : "info"}
                  size={32}
                  color={colors.surface}
                />
                <View style={styles.zakatInfo}>
                  <ThemedText
                    align="left"
                    variant="inverse"
                    size="lg"
                    weight="bold"
                  >
                    {isZakatDue ? "تجب عليك الزكاة" : "لا تجب عليك الزكاة"}
                  </ThemedText>
                  {isZakatDue ? (
                    <>
                      <ThemedText
                        align="left"
                        variant="inverse"
                        size="xl"
                        weight="bold"
                      >
                        {formatCurrency(zakatAmount)}
                      </ThemedText>
                      <ThemedText align="left" variant="inverse" size="sm">
                        مقدار الزكاة الواجبة (2.5%)
                      </ThemedText>
                    </>
                  ) : (
                    <>
                      <ThemedText align="left" variant="inverse" size="md">
                        لم تبلغ مجوهراتك النصاب المقرر شرعياً
                      </ThemedText>
                      {calculationInfo && (
                        <ThemedText align="left" variant="inverse" size="sm">
                          النقص: {calculationInfo.formatted_shortage} جرام ذهب
                          24 عيار
                        </ThemedText>
                      )}
                    </>
                  )}
                </View>
              </View>

              {/* معلومات إضافية */}
              <View
                style={[
                  styles.infoSection,
                  { backgroundColor: colors.background },
                ]}
              >
                <MaterialIcons name="info" size={20} color={colors.primary} />
                <View style={styles.infoContent}>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="primary"
                    size="sm"
                    weight="bold"
                  >
                    ملاحظات مهمة:
                  </ThemedText>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="secondary"
                    size="xs"
                  >
                    • يجب أن تكون المجوهرات في حوزتك لمدة عام هجري كامل
                  </ThemedText>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="secondary"
                    size="xs"
                  >
                    • الحساب يعتمد على أسعار اليوم الحالية
                  </ThemedText>
                  <ThemedText
                    style={{ textAlign: "left" }}
                    variant="secondary"
                    size="xs"
                  >
                    • يُنصح بمراجعة عالم شرعي للتأكد من الحساب
                  </ThemedText>
                </View>
              </View>

              {/* أزرار التحكم */}
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={[
                    styles.resetButton,
                    { backgroundColor: colors.error },
                  ]}
                  onPress={resetCalculator}
                >
                  <MaterialIcons
                    name="refresh"
                    size={20}
                    color={colors.surface}
                  />
                  <ThemedText variant="inverse" size="sm" weight="bold">
                    إعادة تعيين
                  </ThemedText>
                </TouchableOpacity>
              </View>
            </View>
          </ThemedCard>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: SPACING.xl + 80, // إضافة مساحة إضافية لمنع اختفاء العناصر أسفل التاب بار
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: SPACING.xl,
  },
  errorTitle: {
    marginTop: SPACING.md,
    textAlign: "center",
  },
  errorMessage: {
    marginTop: SPACING.sm,
    textAlign: "center",
  },
  heroCard: {
    margin: SPACING.md,
    borderRadius: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  heroContent: {
    alignItems: "center",
    padding: SPACING.xl,
  },
  heroTitle: {
    marginTop: SPACING.md,
    textAlign: "center",
  },
  heroSubtitle: {
    marginTop: SPACING.sm,
    textAlign: "center",
    opacity: 0.9,
  },
  nisabCard: {
    margin: SPACING.md,
    borderRadius: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardHeader: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    padding: SPACING.lg,
    paddingBottom: SPACING.md,
    gap: SPACING.sm,
  },
  nisabGrid: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  nisabItem: {
    flex: 1,
    padding: SPACING.md,
    borderRadius: 12,
    alignItems: "center",
    gap: SPACING.xs,
  },
  nisabDetails: {
    alignItems: "center",
    gap: SPACING.xs,
  },
  applicableNisab: {
    margin: SPACING.lg,
    marginTop: SPACING.md,
    padding: SPACING.md,
    borderRadius: 12,
    alignItems: "center",
    gap: SPACING.xs,
  },
  jewelryCard: {
    margin: SPACING.md,
    borderRadius: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyState: {
    alignItems: "center",
    padding: SPACING.xl,
    gap: SPACING.md,
  },
  jewelryList: {
    padding: SPACING.lg,
    gap: SPACING.md,
  },
  jewelryItem: {
    borderRadius: 12,
    padding: SPACING.md,
    gap: SPACING.md,
  },
  jewelryHeader: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    alignItems: "center",
    gap: SPACING.sm,
  },
  jewelryNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#D4AF37",
    justifyContent: "center",
    alignItems: "center",
  },
  nameInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    borderBottomWidth: 1,
    textAlign: "right", // RTL: محاذاة النص لليمين
  },
  removeButton: {
    padding: SPACING.xs,
  },
  jewelryInputs: {
    gap: SPACING.md,
  },
  inputGroup: {
    gap: SPACING.xs,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: SPACING.sm,
    fontSize: 16,
    textAlign: "right", // RTL: محاذاة النص لليمين
  },
  metalTypeButtons: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    gap: SPACING.sm,
  },
  metalButton: {
    flex: 1,
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
  },
  purityScroll: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
  },
  purityButton: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    marginRight: SPACING.sm, // RTL: تباعد من اليمين
    alignItems: "center",
    minWidth: 60,
  },
  jewelryValue: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: SPACING.md,
    borderTopWidth: 1,
  },
  resultCard: {
    // margin: SPACING.sm,
    borderRadius: 16,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  resultContent: {
    // padding: SPACING.sm,
    gap: SPACING.md,
  },
  summarySection: {
    borderRadius: 12,
    padding: SPACING.md,
    gap: SPACING.sm,
  },
  summaryRow: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    justifyContent: "space-between",
    alignItems: "center",
  },
  zakatResult: {
    borderRadius: 12,
    padding: SPACING.lg,
    flexDirection: "row-reverse", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    gap: SPACING.md,
  },
  zakatInfo: {
    flex: 1,
    gap: SPACING.xs,
  },
  infoSection: {
    borderRadius: 12,
    padding: SPACING.md,
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    gap: SPACING.sm,
  },
  infoContent: {
    flex: 1,
    gap: SPACING.xs,
  },
  actionButtons: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    justifyContent: "center",
  },
  resetButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    borderRadius: 8,
    gap: SPACING.xs,
  },
  // Styles للجدول
  tableContainer: {
    // margin: SPACING.md,
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  tableHeader: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    paddingVertical: SPACING.sm,
    // paddingHorizontal: SPACING.xs,
  },
  tableRow: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    paddingVertical: SPACING.sm,
    // paddingHorizontal: SPACING.xs,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  tableCell: {
    flex: 1,
    textAlign: "center",
    fontSize: 10,
    // paddingHorizontal: SPACING.xs,
  },
  tableCellActions: {
    width: 80,
    textAlign: "center",
    // paddingHorizontal: SPACING.xs,
  },
  tableInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 4,
    paddingHorizontal: SPACING.xs,
    paddingVertical: 4,
    textAlign: "center",
    fontSize: 12,
  },
  editActions: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    justifyContent: "center",
    gap: SPACING.xs,
  },
  actionButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
  },
  saveButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    gap: SPACING.xs,
  },
  successMessage: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    justifyContent: "center",
    margin: SPACING.lg,
    marginTop: 0,
    padding: SPACING.sm,
    borderRadius: 8,
    gap: SPACING.xs,
  },
  // Compact Form Styles
  compactJewelryCard: {
    margin: SPACING.sm,
    borderRadius: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  compactCardHeader: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    padding: SPACING.sm,
    paddingBottom: SPACING.xs,
    gap: SPACING.xs,
  },
  compactFormContainer: {
    padding: SPACING.sm,
    gap: SPACING.xs,
  },
  compactInputGroup: {
    gap: 2,
  },
  compactInput: {
    borderWidth: 1,
    borderRadius: 6,
    padding: SPACING.xs,
    fontSize: 14,
    textAlign: "right", // RTL: محاذاة النص لليمين
    height: 36,
  },
  // Compact Metal Type Buttons
  compactMetalTypeButtons: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    gap: SPACING.xs,
  },
  compactMetalButton: {
    flex: 1,
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    borderWidth: 1,
    alignItems: "center",
    height: 32,
  },
  // Compact Purity Buttons
  compactPurityScroll: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
  },
  compactPurityButton: {
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.sm,
    borderRadius: 6,
    borderWidth: 1,
    marginRight: SPACING.xs, // RTL: تباعد من اليمين
    alignItems: "center",
    minWidth: 60,
    height: 32,
  },
  // Compact Add Button
  compactAddPieceButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.xs,
    paddingHorizontal: SPACING.md,
    borderRadius: 8,
    gap: SPACING.xs,
    marginTop: SPACING.xs,
    height: 36,
  },
  // Compact Nisab Card
  compactNisabCard: {
    margin: SPACING.sm,
    borderRadius: 8,
    elevation: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  compactNisabContent: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    padding: SPACING.xs,
    gap: SPACING.xs,
  },
  // Compact Current Price Display
  compactCurrentPriceContainer: {
    flexDirection: "row", // LTR: من اليسار إلى اليمين
    alignItems: "center",
    padding: SPACING.xs,
    borderRadius: 8,
    gap: SPACING.xs,
    marginBottom: SPACING.xs,
  },
  compactPriceInfo: {
    flex: 1,
  },
  // Styles للحساب الشرعي
  calculationSection: {
    borderRadius: 12,
    padding: SPACING.md,
    gap: SPACING.sm,
  },
  calculationHeader: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    gap: SPACING.xs,
    marginBottom: SPACING.sm,
  },
  calculationGrid: {
    flexDirection: "row-reverse", // RTL: من اليمين إلى اليسار
    gap: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  calculationItem: {
    flex: 1,
    alignItems: "center",
    gap: SPACING.xs,
    padding: SPACING.sm,
    borderRadius: 8,
    backgroundColor: "rgba(255,255,255,0.5)",
  },
  islamicNote: {
    padding: SPACING.sm,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.1)",
  },
  // Styles للنموذج الموحد
  formContainer: {
    padding: SPACING.lg,
    gap: SPACING.md,
  },
  addPieceButton: {
    flexDirection: "row", // LTR: الأيقونة يسار والنص يمين
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 12,
    gap: SPACING.sm,
    marginTop: SPACING.sm,
  },
});

export default ZakatCalculatorScreen;
