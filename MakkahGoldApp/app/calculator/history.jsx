// تاريخ أسعار المعادن - Price History Screen
import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  RefreshControl,
  ActivityIndicator,
  Pressable,
  I18nManager,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { useMetalPriceHistory } from "../../src/utils/hooks";

// تفعيل RTL - صفحة الحاسبات تستخدم اتجاه RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const MetalPriceHistoryScreen = () => {
  const { colors } = useTheme();

  // State للفلاتر
  const [selectedMetalType, setSelectedMetalType] = useState("gold");
  const [selectedPurity, setSelectedPurity] = useState("21K");

  // دالة تغيير نوع المعدن مع تحديث العيار المناسب
  const handleMetalTypeChange = (metalType) => {
    setSelectedMetalType(metalType);
    // تحديث العيار حسب نوع المعدن
    if (metalType === "gold") {
      // إذا كان العيار الحالي ليس من عيارات الذهب، غيره إلى 21K
      const goldPurities = ["24K", "21K", "18K", "14K", "12K"];
      if (!goldPurities.includes(selectedPurity)) {
        setSelectedPurity("21K");
      }
    } else if (metalType === "silver") {
      // إذا كان العيار الحالي ليس من عيارات الفضة، غيره إلى 925
      const silverPurities = ["999", "925", "900"];
      if (!silverPurities.includes(selectedPurity)) {
        setSelectedPurity("925");
      }
    }
  };

  // جلب تاريخ الأسعار باستخدام النمط المعياري
  const {
    data: historyResponse,
    loading: historyLoading,
    error: historyError,
    refetch: refetchHistory,
  } = useMetalPriceHistory({
    metal_type: selectedMetalType,
    purity: selectedPurity,
    per_page: 20,
  });

  // استخراج البيانات من الاستجابة
  const historyData = historyResponse?.data || [];

  // دالة إعادة التحميل
  const handleRefresh = () => {
    refetchHistory();
  };

  // دالة تنسيق العملة
  const formatCurrency = (amount) => {
    return `${amount.toLocaleString("en-US")} جنيه`;
  };

  // دالة تنسيق التاريخ
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("ar-EG");
  };

  // دالة تنسيق الوقت
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("ar-EG", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // عرض حالة التحميل
  if (historyLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>جاري تحميل تاريخ الأسعار...</Text>
      </View>
    );
  }

  // عرض حالة الخطأ
  if (historyError && !historyData.length) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            خطأ في تحميل البيانات
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {historyError}
          </Text>
          <Pressable
            style={({ pressed }) => [
              styles.retryButton,
              pressed && { transform: [{ scale: 0.95 }] },
            ]}
            onPress={handleRefresh}
            android_ripple={{ color: "rgba(255,255,255,0.2)" }}
          >
            <MaterialIcons name="refresh" size={20} color={colors.surface} />
            <Text style={styles.retryText}>إعادة المحاولة</Text>
          </Pressable>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={historyLoading}
            onRefresh={handleRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
      >
        {/* Enhanced Filters Card */}
        <LinearGradient
          colors={[colors.surface, colors.surfaceVariant || colors.surface]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.filtersCard}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialIcons name="tune" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              الفلاتر
            </Text>
          </View>

          {/* نوع المعدن */}
          <View style={styles.filterGroup}>
            <View style={styles.filterLabelContainer}>
              <MaterialIcons
                name="category"
                size={16}
                color={colors.textSecondary}
              />
              <Text style={[styles.filterLabel, { color: colors.textPrimary }]}>
                نوع المعدن
              </Text>
            </View>
            <View style={styles.filterButtons}>
              <Pressable
                style={({ pressed }) => [
                  styles.filterButton,
                  selectedMetalType === "gold" && styles.filterButtonActive,
                  pressed && styles.filterButtonPressed,
                  {
                    backgroundColor:
                      selectedMetalType === "gold"
                        ? colors.primary
                        : colors.surface,
                  },
                ]}
                onPress={() => handleMetalTypeChange("gold")}
                android_ripple={{ color: colors.primary + "20" }}
              >
                <MaterialIcons
                  name="star"
                  size={16}
                  color={
                    selectedMetalType === "gold"
                      ? colors.surface
                      : colors.primary
                  }
                />
                <Text
                  style={[
                    styles.filterButtonText,
                    {
                      color:
                        selectedMetalType === "gold"
                          ? colors.surface
                          : colors.textPrimary,
                    },
                  ]}
                >
                  الذهب
                </Text>
              </Pressable>
              <Pressable
                style={({ pressed }) => [
                  styles.filterButton,
                  selectedMetalType === "silver" && styles.filterButtonActive,
                  pressed && styles.filterButtonPressed,
                  {
                    backgroundColor:
                      selectedMetalType === "silver"
                        ? colors.primary
                        : colors.surface,
                  },
                ]}
                onPress={() => handleMetalTypeChange("silver")}
                android_ripple={{ color: colors.primary + "20" }}
              >
                <MaterialIcons
                  name="circle"
                  size={16}
                  color={
                    selectedMetalType === "silver"
                      ? colors.surface
                      : colors.textSecondary
                  }
                />
                <Text
                  style={[
                    styles.filterButtonText,
                    {
                      color:
                        selectedMetalType === "silver"
                          ? colors.surface
                          : colors.textPrimary,
                    },
                  ]}
                >
                  الفضة
                </Text>
              </Pressable>
            </View>
          </View>

          {/* العيار */}
          <View style={styles.filterGroup}>
            <View style={styles.filterLabelContainer}>
              <MaterialIcons
                name="grade"
                size={16}
                color={colors.textSecondary}
              />
              <Text style={[styles.filterLabel, { color: colors.textPrimary }]}>
                العيار
              </Text>
            </View>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {selectedMetalType === "gold" ? (
                <>
                  {["24K", "21K", "18K", "14K", "12K"].map((purity) => (
                    <Pressable
                      key={purity}
                      style={({ pressed }) => [
                        styles.filterButton,
                        selectedPurity === purity && styles.filterButtonActive,
                        pressed && styles.filterButtonPressed,
                        {
                          backgroundColor:
                            selectedPurity === purity
                              ? colors.primary
                              : colors.surface,
                        },
                      ]}
                      onPress={() => setSelectedPurity(purity)}
                      android_ripple={{ color: colors.primary + "20" }}
                    >
                      <MaterialIcons
                        name="star-border"
                        size={14}
                        color={
                          selectedPurity === purity
                            ? colors.surface
                            : colors.primary
                        }
                      />
                      <Text
                        style={[
                          styles.filterButtonText,
                          {
                            color:
                              selectedPurity === purity
                                ? colors.surface
                                : colors.textPrimary,
                          },
                        ]}
                      >
                        عيار {purity}
                      </Text>
                    </Pressable>
                  ))}
                </>
              ) : (
                <>
                  {["999", "925", "900"].map((purity) => (
                    <Pressable
                      key={purity}
                      style={({ pressed }) => [
                        styles.filterButton,
                        selectedPurity === purity && styles.filterButtonActive,
                        pressed && styles.filterButtonPressed,
                        {
                          backgroundColor:
                            selectedPurity === purity
                              ? colors.primary
                              : colors.surface,
                        },
                      ]}
                      onPress={() => setSelectedPurity(purity)}
                      android_ripple={{ color: colors.primary + "20" }}
                    >
                      <MaterialIcons
                        name="fiber-manual-record"
                        size={14}
                        color={
                          selectedPurity === purity
                            ? colors.surface
                            : colors.textSecondary
                        }
                      />
                      <Text
                        style={[
                          styles.filterButtonText,
                          {
                            color:
                              selectedPurity === purity
                                ? colors.surface
                                : colors.textPrimary,
                          },
                        ]}
                      >
                        فضة {purity}
                      </Text>
                    </Pressable>
                  ))}
                </>
              )}
            </ScrollView>
          </View>
        </LinearGradient>

        {/* Enhanced History Card */}
        <LinearGradient
          colors={[colors.surface, colors.surfaceVariant || colors.surface]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.historyCard}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialIcons name="timeline" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              تاريخ الأسعار
            </Text>
          </View>

          {!historyLoading && historyData.length === 0 && (
            <View style={styles.emptyContainer}>
              <MaterialIcons
                name="history"
                size={64}
                color={colors.textSecondary}
              />
              <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
                لا توجد بيانات تاريخية للفترة المحددة
              </Text>
            </View>
          )}

          {!historyLoading && historyData.length > 0 && (
            <FlatList
              data={historyData}
              keyExtractor={(item) => item.id.toString()}
              renderItem={({ item, index }) => (
                <Pressable
                  style={({ pressed }) => [
                    styles.historyItem,
                    pressed && styles.historyItemPressed,
                    {
                      backgroundColor: pressed
                        ? colors.primaryLight + "10"
                        : "transparent",
                    },
                  ]}
                  android_ripple={{ color: colors.primary + "10" }}
                >
                  {/* Price Indicator Line */}
                  <View
                    style={[
                      styles.priceIndicator,
                      { backgroundColor: colors.primary },
                    ]}
                  />

                  <View style={styles.historyItemContent}>
                    <View style={styles.historyItemHeader}>
                      <View style={styles.priceContainer}>
                        <MaterialIcons
                          name="attach-money"
                          size={18}
                          color={colors.primary}
                        />
                        <Text
                          style={[
                            styles.priceText,
                            { color: colors.textPrimary },
                          ]}
                        >
                          {formatCurrency(parseFloat(item.price_per_gram))}
                        </Text>
                      </View>
                      <View style={styles.dateContainer}>
                        <View style={styles.dateRow}>
                          <MaterialIcons
                            name="calendar-today"
                            size={14}
                            color={colors.textSecondary}
                          />
                          <Text
                            style={[
                              styles.dateText,
                              { color: colors.textSecondary },
                            ]}
                          >
                            {formatDate(item.datetime)}
                          </Text>
                        </View>
                        <View style={styles.timeRow}>
                          <MaterialIcons
                            name="schedule"
                            size={12}
                            color={colors.textSecondary}
                          />
                          <Text
                            style={[
                              styles.timeText,
                              { color: colors.textSecondary },
                            ]}
                          >
                            {formatTime(item.datetime)}
                          </Text>
                        </View>
                      </View>
                    </View>

                    {/* Item Number */}
                    {/* <View style={styles.itemNumber}>
                      <Text
                        style={[
                          styles.itemNumberText,
                          { color: colors.textSecondary },
                        ]}
                      >
                        #{index + 1}
                      </Text>
                    </View> */}
                  </View>
                </Pressable>
              )}
              scrollEnabled={false}
              ItemSeparatorComponent={() => (
                <View
                  style={[styles.separator, { backgroundColor: colors.border }]}
                />
              )}
            />
          )}
        </LinearGradient>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: COLORS.textPrimary,
    textAlign: "center",
    fontFamily: FONTS.medium,
  },
  content: {
    flex: 1,
    padding: SPACING.sm,
    paddingBottom: SPACING.lg + 80, // إضافة مساحة إضافية لمنع اختفاء العناصر أسفل التاب بار
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorTitle: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: "bold",
    color: COLORS.textPrimary,
    textAlign: "center",
    fontFamily: FONTS.bold,
  },
  errorMessage: {
    marginTop: 8,
    fontSize: 14,
    color: COLORS.textSecondary,
    textAlign: "center",
    fontFamily: FONTS.regular,
  },
  retryButton: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 20,
    backgroundColor: COLORS.primary,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  retryText: {
    color: COLORS.surface,
    fontWeight: "bold",
    fontFamily: FONTS.bold,
  },
  filtersCard: {
    borderRadius: 16,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  sectionTitleContainer: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontSize: 17,
    fontWeight: "bold",
    color: COLORS.textPrimary,
    fontFamily: FONTS.bold,
  },
  filterGroup: {
    marginBottom: SPACING.lg,
  },
  filterLabelContainer: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    marginBottom: 12,
    gap: 6,
  },
  filterLabel: {
    fontSize: 15,
    fontWeight: "600",
    color: COLORS.textPrimary,
    fontFamily: FONTS.medium,
  },
  filterButtons: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    gap: 10,
    flexWrap: "wrap",
  },
  filterButton: {
    flexDirection: "row", // RTL: الأيقونة يمين والنص يسار
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 25,
    borderWidth: 1.5,
    borderColor: COLORS.border,
    backgroundColor: COLORS.surface,
    marginRight: 8, // RTL: تباعد من اليمين
    marginBottom: 8,
    gap: 6,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  filterButtonActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
    elevation: 4,
    shadowOpacity: 0.2,
  },
  filterButtonPressed: {
    transform: [{ scale: 0.95 }],
    elevation: 1,
  },
  filterButtonText: {
    fontSize: 12,
    color: COLORS.textPrimary,
    fontFamily: FONTS.medium,
    fontWeight: "500",
  },
  historyCard: {
    borderRadius: 16,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  emptyContainer: {
    alignItems: "center",
    padding: 40,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 15,
    color: COLORS.textSecondary,
    textAlign: "center",
    fontFamily: FONTS.regular,
  },
  historyItem: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderRadius: 12,
    marginVertical: 2,
  },
  historyItemPressed: {
    transform: [{ scale: 0.98 }],
  },
  historyItemContent: {
    flex: 1,
    marginRight: 12, // RTL: تباعد من اليمين
  },
  historyItemHeader: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    alignItems: "center",
    gap: 6,
  },
  priceText: {
    fontSize: 17,
    fontWeight: "bold",
    color: COLORS.textPrimary,
    fontFamily: FONTS.bold,
  },
  dateContainer: {
    alignItems: "flex-end", // RTL: محاذاة لليمين
    gap: 4,
  },
  dateRow: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    alignItems: "center",
    gap: 4,
  },
  timeRow: {
    flexDirection: "row", // RTL: من اليمين إلى اليسار
    alignItems: "center",
    gap: 3,
    marginTop: 2,
  },
  dateText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontFamily: FONTS.medium,
    fontWeight: "500",
  },
  timeText: {
    fontSize: 13,
    color: COLORS.textSecondary,
    fontFamily: FONTS.regular,
    opacity: 0.8,
  },
  priceIndicator: {
    width: 4,
    height: "100%",
    borderRadius: 2,
    marginLeft: 8, // RTL: تباعد من اليسار
  },
  itemNumber: {
    alignSelf: "flex-end", // RTL: محاذاة لليمين
  },
  itemNumberText: {
    fontSize: 13,
    color: COLORS.textSecondary,
    fontFamily: FONTS.light,
    opacity: 0.7,
  },
  separator: {
    height: 1,
    marginHorizontal: 16,
    opacity: 0.3,
  },
});

export default MetalPriceHistoryScreen;
