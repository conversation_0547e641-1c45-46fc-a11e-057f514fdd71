// التخطيط الرئيسي للتطبيق - Root Layout (محسن للتصميم المتجاوب)
import React, { useCallback, useEffect, useState } from "react";
import { Stack } from "expo-router";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { View } from "react-native";
import * as SplashScreen from "expo-splash-screen";
import {
  useFonts,
  Cairo_400Regular,
  Cairo_700Bold,
} from "@expo-google-fonts/cairo";
import { COLORS, RTL_CONFIG } from "../src/constants";
import { ThemeProvider } from "../src/contexts/ThemeContext";
import { ThemedStatusBar, AnimatedSplashScreen } from "../src/components";
import RTLManager from "../src/utils/RTLManager";
import UpdateManager from "../src/utils/UpdateManager";
import configureSplashScreen from "../src/utils/splashConfig";

// تكوين شاشة البداية لإخفاء الشاشة الافتراضية فوراً
configureSplashScreen();

export default function RootLayout() {
  const [showCustomSplash, setShowCustomSplash] = useState(true);
  const [appIsReady, setAppIsReady] = useState(false);

  // تحميل الخطوط مع أسماء متعددة لضمان التوافق
  const [fontsLoaded] = useFonts({
    // أسماء لـ iOS
    Cairo_400Regular,
    Cairo_700Bold,
    // أسماء لـ Android
    "Cairo-Regular": Cairo_400Regular,
    "Cairo-Bold": Cairo_700Bold,
    // أسماء بديلة للتأكد
    CairoRegular: Cairo_400Regular,
    CairoBold: Cairo_700Bold,
  });

  // تحميل موارد التطبيق
  useEffect(() => {
    async function prepare() {
      try {
        // تهيئة RTL Manager
        RTLManager.initialize();

        // تهيئة Update Manager للتحديثات التلقائية
        await UpdateManager.initialize();

        if (fontsLoaded) {
          // جميع الموارد محملة، التطبيق جاهز
          setAppIsReady(true);
        }
      } catch (e) {
        console.warn("خطأ في تحميل موارد التطبيق:", e);
        // تعيين التطبيق كجاهز حتى في حالة الخطأ
        setAppIsReady(true);
      }
    }

    prepare();
  }, [fontsLoaded]);

  const handleSplashFinish = useCallback(() => {
    setShowCustomSplash(false);
  }, []);

  // عرض شاشة البداية المخصصة حتى يتم تحميل جميع الموارد
  if (showCustomSplash || !appIsReady) {
    return (
      <ThemeProvider>
        <SafeAreaProvider>
          <AnimatedSplashScreen
            onFinish={handleSplashFinish}
            isAppReady={appIsReady}
          />
        </SafeAreaProvider>
      </ThemeProvider>
    );
  }

  return (
    <View style={{ flex: 1 }}>
      <ThemeProvider>
        <SafeAreaProvider>
          <ThemedStatusBar style="auto" showBackground={true} />
          <Stack
            screenOptions={{
              headerStyle: {
                backgroundColor: COLORS.primary,
              },
              headerTintColor: COLORS.white,
              headerTitleStyle: {
                fontFamily: "Cairo_700Bold",
                fontSize: 18,
              },
              headerTitleAlign: "center",
              animation: RTL_CONFIG.ANIMATIONS.SLIDE_FROM_RIGHT, // RTL animation
            }}
          >
            <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
            <Stack.Screen
              name="product/[id]"
              options={{
                title: "تفاصيل المنتج",
              }}
            />
            <Stack.Screen
              name="category/[id]"
              options={{
                headerShown: false,
                title: "منتجات الفئة",
              }}
            />
            <Stack.Screen
              name="products/category"
              options={{
                title: "منتجات القسم",
              }}
            />
            <Stack.Screen
              name="calculator/zakat"
              options={{
                title: "حاسبة الزكاة",
              }}
            />
            <Stack.Screen
              name="calculator/jewelry"
              options={{
                title: "حاسبة قيمة المجوهرات",
              }}
            />
            <Stack.Screen
              name="calculator/history"
              options={{
                title: "تاريخ أسعار المعادن",
              }}
            />
            <Stack.Screen
              name="search"
              options={{
                title: "نتائج البحث",
              }}
            />
            <Stack.Screen
              name="stores/index"
              options={{
                title: "فروعنا",
              }}
            />
            <Stack.Screen
              name="bars-coins"
              options={{
                title: "السبائك والعملات",
              }}
            />
          </Stack>
        </SafeAreaProvider>
      </ThemeProvider>
    </View>
  );
}
