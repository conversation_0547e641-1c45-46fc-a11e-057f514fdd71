// صفحة إعدادات المظهر - Theme Settings Screen
import React from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  I18nManager,
  Pressable,
  Switch,
} from "react-native";
import { Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { ThemedStatusBar, ThemedText, ThemedCard } from "../../src/components";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const ThemeScreen = () => {
  const { colors, isDarkMode, toggleTheme } = useTheme();
  const insets = useSafeAreaInsets();

  const themeOptions = [
    {
      id: "light",
      title: "الوضع الفاتح",
      description: "مظهر فاتح ومريح للعينين في النهار",
      icon: "light-mode",
      active: !isDarkMode,
      colors: ["#FFFFFF", "#F5F5F5"],
    },
    {
      id: "dark",
      title: "الوضع المظلم",
      description: "مظهر مظلم مريح للعينين في الليل",
      icon: "dark-mode",
      active: isDarkMode,
      colors: ["#1A1A1A", "#2D2D2D"],
    },
  ];

  return (
    <>
      <Stack.Screen
        options={{
          title: "المظهر",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: colors.white,
          headerTitleStyle: { fontFamily: "Cairo-Bold" },
        }}
      />
      <ThemedStatusBar />

      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        {/* <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="brightness-6" size={48} color={colors.surface} />
          <ThemedText
            variant="surface"
            size="xl"
            weight="bold"
            style={styles.headerTitle}
          >
            إعدادات المظهر
          </ThemedText>
          <ThemedText variant="surface" size="md" style={styles.headerSubtitle}>
            اختر المظهر المناسب لك
          </ThemedText>
        </LinearGradient> */}

        {/* التبديل السريع */}
        {/* <ThemedCard style={styles.section}>
          <View style={styles.quickToggle}>
            <View style={styles.toggleInfo}>
              <MaterialIcons
                name={isDarkMode ? "dark-mode" : "light-mode"}
                size={24}
                color={colors.primary}
              />
              <View style={styles.toggleText}>
                <ThemedText variant="primary" size="lg" weight="bold">
                  {isDarkMode ? "الوضع المظلم" : "الوضع الفاتح"}
                </ThemedText>
                <ThemedText variant="secondary" size="sm">
                  تبديل سريع بين الأوضاع
                </ThemedText>
              </View>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{
                false: colors.border,
                true: colors.primary + "50",
              }}
              thumbColor={isDarkMode ? colors.primary : colors.textSecondary}
              ios_backgroundColor={colors.border}
            />
          </View>
        </ThemedCard> */}

        {/* خيارات المظهر */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="palette" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              اختيار المظهر
            </ThemedText>
          </View>

          {themeOptions.map((option) => (
            <Pressable
              key={option.id}
              style={[
                styles.themeOption,
                {
                  backgroundColor: colors.surface,
                  borderColor: option.active ? colors.primary : colors.border,
                  borderWidth: option.active ? 2 : 1,
                },
              ]}
              onPress={() => {
                if (option.id === "dark" && !isDarkMode) {
                  toggleTheme();
                } else if (option.id === "light" && isDarkMode) {
                  toggleTheme();
                }
              }}
              android_ripple={{ color: colors.primary + "20" }}
            >
              <View style={styles.themePreview}>
                <LinearGradient
                  colors={option.colors}
                  style={styles.previewGradient}
                >
                  <MaterialIcons
                    name={option.icon}
                    size={24}
                    color={option.id === "dark" ? "#FFFFFF" : "#000000"}
                  />
                </LinearGradient>
              </View>

              <View style={styles.themeInfo}>
                <ThemedText variant="primary" size="md" weight="bold">
                  {option.title}
                </ThemedText>
                <ThemedText variant="secondary" size="sm">
                  {option.description}
                </ThemedText>
              </View>

              {option.active && (
                <MaterialIcons
                  name="check-circle"
                  size={24}
                  color={colors.primary}
                />
              )}
            </Pressable>
          ))}
        </ThemedCard>

        {/* معلومات إضافية */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="info" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              معلومات المظهر
            </ThemedText>
          </View>

          <View style={styles.infoList}>
            <View style={styles.infoItem}>
              <MaterialIcons
                name="visibility"
                size={20}
                color={colors.primary}
              />
              <ThemedText variant="secondary" size="sm" style={styles.infoText}>
                الوضع المظلم يقلل من إجهاد العينين في الإضاءة المنخفضة
              </ThemedText>
            </View>

            <View style={styles.infoItem}>
              <MaterialIcons
                name="battery-saver"
                size={20}
                color={colors.primary}
              />
              <ThemedText variant="secondary" size="sm" style={styles.infoText}>
                الوضع المظلم يوفر طاقة البطارية في الشاشات OLED
              </ThemedText>
            </View>

            <View style={styles.infoItem}>
              <MaterialIcons
                name="auto-awesome"
                size={20}
                color={colors.primary}
              />
              <ThemedText variant="secondary" size="sm" style={styles.infoText}>
                يمكنك التبديل بين الأوضاع في أي وقت
              </ThemedText>
            </View>

            <View style={styles.infoItem}>
              <MaterialIcons name="save" size={20} color={colors.primary} />
              <ThemedText variant="secondary" size="sm" style={styles.infoText}>
                إعداداتك محفوظة تلقائياً ومتزامنة عبر التطبيق
              </ThemedText>
            </View>
          </View>
        </ThemedCard>

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpace} />
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    textAlign: "center",
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    textAlign: "center",
    opacity: 0.9,
  },
  section: {
    margin: SPACING.md,
    padding: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    marginLeft: SPACING.sm,
  },
  quickToggle: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  toggleInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  toggleText: {
    marginRight: SPACING.md,
  },
  themeOption: {
    flexDirection: "row",
    alignItems: "center",
    padding: SPACING.lg,
    borderRadius: 16,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  themePreview: {
    marginLeft: SPACING.md,
  },
  previewGradient: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  themeInfo: {
    flex: 1,
  },
  infoList: {
    gap: SPACING.md,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  infoText: {
    flex: 1,
    marginLeft: SPACING.sm,
    lineHeight: 20,
    textAlign: "left",
  },
  bottomSpace: {
    height: SPACING.xl,
  },
});

export default ThemeScreen;
