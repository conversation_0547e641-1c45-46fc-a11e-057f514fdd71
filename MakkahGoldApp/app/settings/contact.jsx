// صفحة تواصل معنا - Contact Us Screen
import React from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  I18nManager,
  Pressable,
  Linking,
  Alert,
  RefreshControl,
} from "react-native";
import { Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  Loading,
} from "../../src/components";
import { useAppSettings } from "../../src/utils/hooks";

// RTL مُفعل بالفعل في _layout.jsx

const ContactScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();

  // جلب إعدادات التطبيق من API
  const { loading, refetch, contactInfo, socialLinks, workingHours } =
    useAppSettings();

  // دوال التواصل
  const handlePhoneCall = (phone) => {
    if (phone) {
      Linking.openURL(`tel:${phone}`);
    }
  };

  const handleEmail = (email) => {
    if (email) {
      Linking.openURL(`mailto:${email}`);
    }
  };

  const handleWhatsApp = (phone) => {
    if (phone) {
      const whatsappUrl = `whatsapp://send?phone=${phone.replace(
        /[^0-9]/g,
        ""
      )}`;
      Linking.canOpenURL(whatsappUrl).then((supported) => {
        if (supported) {
          Linking.openURL(whatsappUrl);
        } else {
          Alert.alert("خطأ", "تطبيق واتساب غير مثبت على جهازك");
        }
      });
    }
  };

  // دالة لتنسيق رقم التليفون مع علامة + في البداية
  const formatPhoneNumber = (phone) => {
    if (!phone) return "";

    // إزالة أي رموز غير رقمية موجودة
    const cleanPhone = phone.replace(/[^0-9]/g, "");

    // إضافة علامة + في البداية
    return `+${cleanPhone}`;
  };

  const handleSocialLink = (url, platform) => {
    if (url) {
      Linking.openURL(url).catch(() => {
        Alert.alert("خطأ", `لا يمكن فتح رابط ${platform}`);
      });
    }
  };

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <ThemedStatusBar />
        <Loading text="جاري تحميل معلومات التواصل..." />
      </>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: "تواصل معنا",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: colors.white,
          headerTitleStyle: { fontFamily: "Cairo-Bold" },
        }}
      />
      <ThemedStatusBar />

      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={refetch}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header */}
        {/* <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons
            name="contact-support"
            size={48}
            color={colors.surface}
          />
          <ThemedText
            variant="surface"
            size="xl"
            weight="bold"
            style={styles.headerTitle}
          >
            تواصل معنا
          </ThemedText>
          <ThemedText variant="surface" size="md" style={styles.headerSubtitle}>
            نحن هنا لخدمتك في أي وقت
          </ThemedText>
        </LinearGradient> */}

        {/* طرق التواصل السريع */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="phone" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              طرق التواصل السريع
            </ThemedText>
          </View>

          {/* الهاتف */}
          {contactInfo?.phone && (
            <Pressable
              style={[styles.contactItem, { backgroundColor: colors.surface }]}
              onPress={() => handlePhoneCall(contactInfo.phone)}
              android_ripple={{ color: colors.primary + "20" }}
            >
              <LinearGradient
                colors={[colors.primary, colors.primaryDark || colors.primary]}
                style={styles.contactIcon}
              >
                <MaterialIcons name="phone" size={24} color={colors.surface} />
              </LinearGradient>
              <View style={styles.contactContent}>
                <ThemedText
                  style={styles.contactTitle}
                  variant="primary"
                  size="md"
                  weight="bold"
                >
                  اتصل بنا
                </ThemedText>
                <ThemedText
                  style={[
                    styles.contactDesc,
                    { textAlign: "center", direction: "ltr" },
                  ]}
                  variant="secondary"
                  size="sm"
                >
                  {formatPhoneNumber(contactInfo.phone)}
                </ThemedText>
              </View>
              <MaterialIcons
                name="arrow-back"
                size={20}
                color={colors.textSecondary}
              />
            </Pressable>
          )}

          {/* واتساب */}
          {contactInfo?.whatsapp && (
            <Pressable
              style={[styles.contactItem, { backgroundColor: colors.surface }]}
              onPress={() => handleWhatsApp(contactInfo.whatsapp)}
              android_ripple={{ color: "#25D366" + "20" }}
            >
              <LinearGradient
                colors={["#25D366", "#128C7E"]}
                style={styles.contactIcon}
              >
                <MaterialIcons name="chat" size={24} color={colors.surface} />
              </LinearGradient>
              <View style={styles.contactContent}>
                <ThemedText
                  style={styles.contactTitle}
                  variant="primary"
                  size="md"
                  weight="bold"
                >
                  واتساب
                </ThemedText>
                <ThemedText
                  style={[
                    styles.contactDesc,
                    { textAlign: "center", direction: "ltr" },
                  ]}
                  variant="secondary"
                  size="sm"
                >
                  {formatPhoneNumber(contactInfo.whatsapp)}
                </ThemedText>
              </View>
              <MaterialIcons
                name="arrow-back"
                size={20}
                color={colors.textSecondary}
              />
            </Pressable>
          )}

          {/* البريد الإلكتروني */}
          {contactInfo?.email && (
            <Pressable
              style={[styles.contactItem, { backgroundColor: colors.surface }]}
              onPress={() => handleEmail(contactInfo.email)}
              android_ripple={{ color: colors.primary + "20" }}
            >
              <LinearGradient
                colors={[
                  colors.warning || colors.primary,
                  colors.warningDark || colors.primary,
                ]}
                style={styles.contactIcon}
              >
                <MaterialIcons name="email" size={24} color={colors.surface} />
              </LinearGradient>
              <View style={styles.contactContent}>
                <ThemedText
                  style={styles.contactTitle}
                  variant="primary"
                  size="md"
                  weight="bold"
                >
                  البريد الإلكتروني
                </ThemedText>
                <ThemedText
                  style={styles.contactDesc}
                  variant="secondary"
                  size="sm"
                >
                  {contactInfo.email}
                </ThemedText>
              </View>
              <MaterialIcons
                name="arrow-back" // RTL support
                size={20}
                color={colors.textSecondary}
              />
            </Pressable>
          )}
        </ThemedCard>

        {/* العنوان */}
        {contactInfo?.address && (
          <ThemedCard style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialIcons
                name="location-on"
                size={24}
                color={colors.primary}
              />
              <ThemedText
                variant="primary"
                size="lg"
                weight="bold"
                style={styles.sectionTitle}
              >
                العنوان
              </ThemedText>
            </View>
            <ThemedText variant="secondary" size="md" style={styles.address}>
              {contactInfo.address}
            </ThemedText>
          </ThemedCard>
        )}

        {/* وسائل التواصل الاجتماعي */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="share" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              تابعنا على
            </ThemedText>
          </View>

          <View style={styles.socialGrid}>
            {socialLinks?.facebook && (
              <Pressable
                style={[styles.socialButton, { backgroundColor: "#1877F2" }]}
                onPress={() => handleSocialLink(socialLinks.facebook, "فيسبوك")}
                android_ripple={{ color: "#ffffff30" }}
              >
                <MaterialIcons name="facebook" size={32} color="#ffffff" />
                <ThemedText variant="surface" size="sm" weight="bold">
                  فيسبوك
                </ThemedText>
              </Pressable>
            )}

            {socialLinks?.instagram && (
              <Pressable
                style={[styles.socialButton, { backgroundColor: "#E4405F" }]}
                onPress={() =>
                  handleSocialLink(socialLinks.instagram, "إنستغرام")
                }
                android_ripple={{ color: "#ffffff30" }}
              >
                <MaterialIcons name="camera-alt" size={32} color="#ffffff" />
                <ThemedText variant="surface" size="sm" weight="bold">
                  إنستغرام
                </ThemedText>
              </Pressable>
            )}

            {socialLinks?.twitter && (
              <Pressable
                style={[styles.socialButton, { backgroundColor: "#1DA1F2" }]}
                onPress={() => handleSocialLink(socialLinks.twitter, "تويتر")}
                android_ripple={{ color: "#ffffff30" }}
              >
                <MaterialIcons
                  name="alternate-email"
                  size={32}
                  color="#ffffff"
                />
                <ThemedText variant="surface" size="sm" weight="bold">
                  تويتر
                </ThemedText>
              </Pressable>
            )}

            {socialLinks?.youtube && (
              <Pressable
                style={[styles.socialButton, { backgroundColor: "#FF0000" }]}
                onPress={() => handleSocialLink(socialLinks.youtube, "يوتيوب")}
                android_ripple={{ color: "#ffffff30" }}
              >
                <MaterialIcons
                  name="play-circle-filled"
                  size={32}
                  color="#ffffff"
                />
                <ThemedText variant="surface" size="sm" weight="bold">
                  يوتيوب
                </ThemedText>
              </Pressable>
            )}

            {socialLinks?.tiktok && (
              <Pressable
                style={[styles.socialButton, { backgroundColor: "#000000" }]}
                onPress={() => handleSocialLink(socialLinks.tiktok, "تيك توك")}
                android_ripple={{ color: "#ffffff30" }}
              >
                <MaterialIcons name="music-note" size={32} color="#ffffff" />
                <ThemedText variant="surface" size="sm" weight="bold">
                  تيك توك
                </ThemedText>
              </Pressable>
            )}

            {socialLinks?.linkedin && (
              <Pressable
                style={[styles.socialButton, { backgroundColor: "#0077B5" }]}
                onPress={() =>
                  handleSocialLink(socialLinks.linkedin, "لينكد إن")
                }
                android_ripple={{ color: "#ffffff30" }}
              >
                <MaterialIcons name="business" size={32} color="#ffffff" />
                <ThemedText variant="surface" size="sm" weight="bold">
                  لينكد إن
                </ThemedText>
              </Pressable>
            )}
          </View>
        </ThemedCard>

        {/* ساعات العمل */}
        {/* <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons
              name="access-time"
              size={24}
              color={colors.primary}
            />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              ساعات العمل
            </ThemedText>
          </View>

          <View style={styles.workingHours}>
            {workingHours?.weekdays && (
              <View style={styles.workingHourItem}>
                <ThemedText variant="primary" size="md" weight="bold">
                  {workingHours.weekdays.days || "السبت - الخميس"}
                </ThemedText>
                <ThemedText variant="secondary" size="sm">
                  {workingHours.weekdays.hours || "9:00 ص - 10:00 م"}
                </ThemedText>
              </View>
            )}
            {workingHours?.friday && (
              <View style={styles.workingHourItem}>
                <ThemedText variant="primary" size="md" weight="bold">
                  {workingHours.friday.day || "الجمعة"}
                </ThemedText>
                <ThemedText variant="secondary" size="sm">
                  {workingHours.friday.hours || "2:00 م - 10:00 م"}
                </ThemedText>
              </View>
            )}

            {(!workingHours ||
              (!workingHours.weekdays && !workingHours.friday)) && (
              <>
                <View style={styles.workingHourItem}>
                  <ThemedText variant="primary" size="md" weight="bold">
                    السبت - الخميس
                  </ThemedText>
                  <ThemedText variant="secondary" size="sm">
                    9:00 ص - 10:00 م
                  </ThemedText>
                </View>
                <View style={styles.workingHourItem}>
                  <ThemedText variant="primary" size="md" weight="bold">
                    الجمعة
                  </ThemedText>
                  <ThemedText variant="secondary" size="sm">
                    2:00 م - 10:00 م
                  </ThemedText>
                </View>
              </>
            )}
          </View>
        </ThemedCard> */}

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpace} />
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    textAlign: "center",
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    textAlign: "center",
    opacity: 0.9,
  },
  section: {
    margin: SPACING.md,
    padding: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    marginRight: SPACING.sm,
    textAlign: "center",
  },
  contactItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: SPACING.md,
    borderRadius: 12,
    marginBottom: SPACING.md,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  contactIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: SPACING.md,
  },
  contactContent: {
    flex: 1,
  },
  contactTitle: {
    textAlign: "left",
  },
  contactDesc: {
    textAlign: "left",
  },
  address: {
    lineHeight: 24,
    textAlign: "center",
  },
  socialGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: SPACING.md,
  },
  socialButton: {
    flex: 1,
    minWidth: "30%",
    alignItems: "center",
    padding: SPACING.lg,
    borderRadius: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  workingHours: {
    gap: SPACING.md,
  },
  workingHourItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border + "30",
  },
  bottomSpace: {
    height: SPACING.xl,
  },
});

export default ContactScreen;
