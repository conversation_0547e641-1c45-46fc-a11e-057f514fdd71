// صفحة سياسة الخصوصية - Privacy Policy Screen
import React from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  I18nManager,
  RefreshControl,
} from "react-native";
import { Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  Loading,
} from "../../src/components";
import { useAppSettings } from "../../src/utils/hooks";

// RTL مُفعل بالفعل في _layout.jsx

const PrivacyScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();

  // جلب إعدادات التطبيق من API
  const { loading, refetch, privacySettings } = useAppSettings();

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <ThemedStatusBar />
        <Loading text="جاري تحميل سياسة الخصوصية..." />
      </>
    );
  }

  // النصوص الافتراضية
  const defaultPrivacyPolicy = `
نحن في مكة جولد جروب نحترم خصوصيتك ونلتزم بحماية معلوماتك الشخصية. توضح سياسة الخصوصية هذه كيفية جمع واستخدام وحماية معلوماتك عند استخدام تطبيقنا.

المعلومات التي نجمعها:
• معلومات الجهاز والاستخدام لتحسين الأداء
• تفضيلات المستخدم والإعدادات
• بيانات التفاعل مع التطبيق لتحسين التجربة

كيف نستخدم المعلومات:
• تقديم وتحسين خدماتنا
• إرسال التحديثات والإشعارات المهمة
• تخصيص تجربة المستخدم
• ضمان أمان التطبيق

حماية المعلومات:
• نستخدم تقنيات التشفير المتقدمة
• لا نشارك معلوماتك مع أطراف ثالثة
• نحتفظ بالمعلومات محلياً على جهازك
• يمكنك حذف بياناتك في أي وقت
  `;

  const defaultTermsConditions = `
شروط الاستخدام:

1. الاستخدام المقبول:
• التطبيق مخصص للاستخدام الشخصي فقط
• يُمنع استخدام التطبيق لأغراض تجارية غير مصرح بها
• يجب احترام حقوق الملكية الفكرية

2. المحتوى والمعلومات:
• جميع الأسعار استرشادية وقابلة للتغيير
• المعلومات المقدمة لأغراض إعلامية فقط
• لا نتحمل مسؤولية القرارات المالية المتخذة بناءً على المعلومات

3. المسؤولية:
• نسعى لتقديم معلومات دقيقة ولكن لا نضمن ذلك
• المستخدم مسؤول عن التحقق من المعلومات
• لا نتحمل مسؤولية أي خسائر مالية

4. التحديثات:
• نحتفظ بالحق في تحديث الشروط
• سيتم إشعار المستخدمين بالتغييرات المهمة
• الاستمرار في الاستخدام يعني الموافقة على الشروط الجديدة
  `;

  return (
    <>
      <Stack.Screen
        options={{
          title: "سياسة الخصوصية",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: colors.white,
          headerTitleStyle: { fontFamily: "Cairo-Bold" },
        }}
      />
      <ThemedStatusBar />

      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={refetch}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header */}
        {/* <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="privacy-tip" size={48} color={colors.surface} />
          <ThemedText
            variant="surface"
            size="xl"
            weight="bold"
            style={styles.headerTitle}
          >
            سياسة الخصوصية
          </ThemedText>
          <ThemedText variant="surface" size="md" style={styles.headerSubtitle}>
            نحن نحترم خصوصيتك ونحمي بياناتك
          </ThemedText>
        </LinearGradient> */}

        {/* سياسة الخصوصية */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="security" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              سياسة الخصوصية
            </ThemedText>
          </View>
          <ThemedText variant="secondary" size="md" style={styles.content}>
            {privacySettings?.privacy_policy || defaultPrivacyPolicy}
          </ThemedText>
        </ThemedCard>

        {/* شروط الاستخدام */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="gavel" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              شروط الاستخدام
            </ThemedText>
          </View>
          <ThemedText variant="secondary" size="md" style={styles.content}>
            {privacySettings?.terms_conditions || defaultTermsConditions}
          </ThemedText>
        </ThemedCard>

        {/* سياسة الإرجاع */}
        {privacySettings?.return_policy && (
          <ThemedCard style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialIcons
                name="assignment-return"
                size={24}
                color={colors.primary}
              />
              <ThemedText
                variant="primary"
                size="lg"
                weight="bold"
                style={styles.sectionTitle}
              >
                سياسة الإرجاع والاستبدال
              </ThemedText>
            </View>
            <ThemedText variant="secondary" size="md" style={styles.content}>
              {privacySettings.return_policy}
            </ThemedText>
          </ThemedCard>
        )}

        {/* معلومات الاتصال للاستفسارات */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="help" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              الاستفسارات والدعم
            </ThemedText>
          </View>
          <ThemedText variant="secondary" size="md" style={styles.content}>
            إذا كان لديك أي استفسارات حول سياسة الخصوصية أو شروط الاستخدام، يرجى
            التواصل معنا من خلال: • صفحة "تواصل معنا" في التطبيق • البريد
            الإلكتروني أو الهاتف المذكور في معلومات التواصل • وسائل التواصل
            الاجتماعي الرسمية نحن ملتزمون بالرد على استفساراتك في أقرب وقت ممكن.
          </ThemedText>
        </ThemedCard>

        {/* تاريخ آخر تحديث */}
        <ThemedCard style={styles.section}>
          <View style={styles.updateInfo}>
            <MaterialIcons
              name="update"
              size={20}
              color={colors.textSecondary}
            />
            <ThemedText variant="secondary" size="sm" style={styles.updateText}>
              آخر تحديث: {new Date().toLocaleDateString("ar-SA")}
            </ThemedText>
          </View>
        </ThemedCard>

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpace} />
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    textAlign: "center",
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    textAlign: "center",
    opacity: 0.9,
  },
  section: {
    margin: SPACING.md,
    padding: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: SPACING.lg,
  },
  sectionTitle: {
    marginLeft: SPACING.sm,
  },
  content: {
    lineHeight: 26,
    textAlign: "left",
  },
  updateInfo: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.sm,
  },
  updateText: {
    marginRight: SPACING.sm,
    textAlign: "center",
  },
  bottomSpace: {
    height: SPACING.xl,
  },
});

export default PrivacyScreen;
