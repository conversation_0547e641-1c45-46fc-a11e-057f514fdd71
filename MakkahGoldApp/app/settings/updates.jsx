// صفحة إعدادات التحديثات - Updates Settings
import React, { useState, useEffect, useMemo } from "react";
import {
  View,
  ScrollView,
  Alert,
  Switch,
  TouchableOpacity,
  ActivityIndicator,
  I18nManager,
  StyleSheet,
} from "react-native";
import { Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../../src/contexts/ThemeContext";
import { ThemedText, ThemedCard, ThemedButton } from "../../src/components";
import UpdateManager from "../../src/utils/UpdateManager";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const UpdatesSettingsScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();
  const [loading, setLoading] = useState(false);
  const [checkingUpdates, setCheckingUpdates] = useState(false);
  const [updateInfo, setUpdateInfo] = useState(null);
  const [settings, setSettings] = useState({
    autoCheck: true,
    autoDownload: true,
    wifiOnly: false,
    checkInterval: 30 * 60 * 1000, // 30 دقيقة
  });

  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flex: 1,
          backgroundColor: colors.background,
        },
        scrollContainer: {
          paddingHorizontal: 16,
          paddingTop: 16,
          paddingBottom: insets.bottom + 100,
          marginBottom: 26,
        },
        section: {
          marginBottom: 16,
        },
        settingItem: {
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          paddingVertical: 16,
          paddingHorizontal: 16,
          borderBottomWidth: 1,
          borderBottomColor: colors.border,
        },
        settingItemLast: {
          borderBottomWidth: 0,
        },
        settingInfo: {
          flex: 1,
          marginRight: 16,
        },
        settingTitle: {
          marginBottom: 4,
        },
        settingDescription: {
          lineHeight: 20,
        },
        updateInfoContainer: {
          padding: 16,
          backgroundColor: colors.surface,
          borderRadius: 12,
          marginBottom: 16,
        },
        updateInfoRow: {
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: 8,
        },
        updateInfoRowLast: {
          marginBottom: 0,
        },
        statusIndicator: {
          flexDirection: "row",
          alignItems: "center",
        },
        statusDot: {
          width: 8,
          height: 8,
          borderRadius: 4,
          marginLeft: 8,
        },
        buttonContainer: {
          marginTop: 16,
        },
        intervalContainer: {
          flexDirection: "row",
          alignItems: "center",
          flexWrap: "wrap",
          marginTop: 8,
        },
        intervalButton: {
          paddingHorizontal: 12,
          paddingVertical: 6,
          borderRadius: 16,
          borderWidth: 1,
          borderColor: colors.border,
          marginRight: 8,
          marginBottom: 8,
        },
        intervalButtonActive: {
          backgroundColor: colors.primary,
          borderColor: colors.primary,
        },
      }),
    [colors, insets.bottom]
  );

  // تحميل معلومات التحديث والإعدادات
  useEffect(() => {
    loadUpdateInfo();
  }, []);

  const loadUpdateInfo = async () => {
    try {
      setLoading(true);
      const info = UpdateManager.getUpdateInfo();
      setUpdateInfo(info);
      if (info.settings) {
        setSettings(info.settings);
      }
    } catch (error) {
      console.error("خطأ في تحميل معلومات التحديث:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = async (key, value) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await UpdateManager.updateSettings({ [key]: value });
    } catch (error) {
      console.error("خطأ في تحديث الإعدادات:", error);
      Alert.alert("خطأ", "حدث خطأ أثناء حفظ الإعدادات");
    }
  };

  const handleCheckForUpdates = async () => {
    try {
      setCheckingUpdates(true);
      await UpdateManager.forceCheckForUpdates();
      // إعادة تحميل معلومات التحديث
      await loadUpdateInfo();
    } catch (error) {
      console.error("خطأ في التحقق من التحديثات:", error);
    } finally {
      setCheckingUpdates(false);
    }
  };

  const formatLastUpdateCheck = (date) => {
    if (!date) return "لم يتم التحقق بعد";

    const now = new Date();
    const diffMs = now - new Date(date);
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return "الآن";
    if (diffMins < 60) return `منذ ${diffMins} دقيقة`;
    if (diffHours < 24) return `منذ ${diffHours} ساعة`;
    return `منذ ${diffDays} يوم`;
  };

  const getIntervalText = (interval) => {
    const minutes = interval / (1000 * 60);
    if (minutes < 60) return `${minutes} دقيقة`;
    const hours = minutes / 60;
    if (hours < 24) return `${hours} ساعة`;
    const days = hours / 24;
    return `${days} يوم`;
  };

  const intervalOptions = [
    { value: 15 * 60 * 1000, label: "15 دقيقة" },
    { value: 30 * 60 * 1000, label: "30 دقيقة" },
    { value: 60 * 60 * 1000, label: "ساعة" },
    { value: 2 * 60 * 60 * 1000, label: "ساعتان" },
    { value: 6 * 60 * 60 * 1000, label: "6 ساعات" },
    { value: 24 * 60 * 60 * 1000, label: "يوم" },
  ];

  if (loading) {
    return (
      <View
        style={[
          styles.container,
          { justifyContent: "center", alignItems: "center" },
        ]}
      >
        <ActivityIndicator size="large" color={colors.primary} />
        <ThemedText style={{ marginTop: 16 }}>
          جاري تحميل إعدادات التحديثات...
        </ThemedText>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: "إعدادات التحديثات",
          headerStyle: { backgroundColor: colors.surface },
          headerTintColor: colors.textPrimary,
        }}
      />

      <View style={styles.container}>
        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {/* تحذير Expo Go */}
          {updateInfo?.environment?.isExpoGo && (
            <ThemedCard
              style={[
                styles.section,
                {
                  backgroundColor: colors.warning + "20",
                  borderColor: colors.warning,
                  borderWidth: 1,
                },
              ]}
            >
              <View style={styles.updateInfoContainer}>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginBottom: 12,
                  }}
                >
                  <MaterialIcons
                    name="warning"
                    size={24}
                    color={colors.warning}
                  />
                  <ThemedText
                    variant="title"
                    size="lg"
                    style={{ marginRight: 8, color: colors.warning }}
                  >
                    تنبيه مهم
                  </ThemedText>
                </View>
                <ThemedText variant="secondary" style={{ lineHeight: 22 }}>
                  التحديثات غير متاحة في Expo Go. لاختبار ميزة التحديثات، يرجى:
                </ThemedText>
                <ThemedText
                  variant="secondary"
                  style={{ marginTop: 8, marginRight: 16 }}
                >
                  • بناء APK باستخدام: eas build --platform android --profile
                  production
                </ThemedText>
                <ThemedText variant="secondary" style={{ marginRight: 16 }}>
                  • تثبيت APK على الجهاز
                </ThemedText>
                <ThemedText variant="secondary" style={{ marginRight: 16 }}>
                  • اختبار التحديثات في البناء المستقل
                </ThemedText>
              </View>
            </ThemedCard>
          )}

          {/* معلومات التحديث الحالية */}
          <ThemedCard style={styles.section}>
            <View style={styles.updateInfoContainer}>
              <ThemedText
                variant="title"
                size="lg"
                style={{ marginBottom: 16 }}
              >
                معلومات التحديث
              </ThemedText>

              <View style={styles.updateInfoRow}>
                <ThemedText variant="secondary">حالة التحديثات:</ThemedText>
                <View style={styles.statusIndicator}>
                  <ThemedText variant="primary">
                    {updateInfo?.environment?.supportsUpdates
                      ? "مفعل"
                      : "غير مفعل"}
                  </ThemedText>
                  <View
                    style={[
                      styles.statusDot,
                      {
                        backgroundColor: updateInfo?.environment
                          ?.supportsUpdates
                          ? colors.success
                          : colors.error,
                      },
                    ]}
                  />
                </View>
              </View>

              <View style={styles.updateInfoRow}>
                <ThemedText variant="secondary">البيئة:</ThemedText>
                <ThemedText>
                  {updateInfo?.environment?.isExpoGo ? "Expo Go" : "بناء مستقل"}
                </ThemedText>
              </View>

              <View style={styles.updateInfoRow}>
                <ThemedText variant="secondary">القناة:</ThemedText>
                <ThemedText>{updateInfo?.channel || "غير محدد"}</ThemedText>
              </View>

              <View style={styles.updateInfoRow}>
                <ThemedText variant="secondary">إصدار التشغيل:</ThemedText>
                <ThemedText>
                  {updateInfo?.runtimeVersion || "غير محدد"}
                </ThemedText>
              </View>

              <View style={[styles.updateInfoRow, styles.updateInfoRowLast]}>
                <ThemedText variant="secondary">آخر فحص:</ThemedText>
                <ThemedText>
                  {formatLastUpdateCheck(updateInfo?.lastUpdateCheck)}
                </ThemedText>
              </View>
            </View>
          </ThemedCard>

          {/* إعدادات التحديث */}
          <ThemedCard style={styles.section}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <ThemedText variant="title" style={styles.settingTitle}>
                  التحقق التلقائي
                </ThemedText>
                <ThemedText
                  variant="secondary"
                  size="sm"
                  style={styles.settingDescription}
                >
                  التحقق من التحديثات تلقائياً في الخلفية
                </ThemedText>
              </View>
              <Switch
                value={settings.autoCheck}
                onValueChange={(value) =>
                  handleSettingChange("autoCheck", value)
                }
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.surface}
              />
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <ThemedText variant="title" style={styles.settingTitle}>
                  التحميل التلقائي
                </ThemedText>
                <ThemedText
                  variant="secondary"
                  size="sm"
                  style={styles.settingDescription}
                >
                  تحميل التحديثات تلقائياً عند توفرها
                </ThemedText>
              </View>
              <Switch
                value={settings.autoDownload}
                onValueChange={(value) =>
                  handleSettingChange("autoDownload", value)
                }
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.surface}
                disabled={!settings.autoCheck}
              />
            </View>

            <View style={[styles.settingItem, styles.settingItemLast]}>
              <View style={styles.settingInfo}>
                <ThemedText variant="title" style={styles.settingTitle}>
                  Wi-Fi فقط
                </ThemedText>
                <ThemedText
                  variant="secondary"
                  size="sm"
                  style={styles.settingDescription}
                >
                  تحميل التحديثات عبر Wi-Fi فقط لتوفير البيانات
                </ThemedText>
              </View>
              <Switch
                value={settings.wifiOnly}
                onValueChange={(value) =>
                  handleSettingChange("wifiOnly", value)
                }
                trackColor={{ false: colors.border, true: colors.primary }}
                thumbColor={colors.surface}
              />
            </View>
          </ThemedCard>

          {/* فترة التحقق */}
          {settings.autoCheck && (
            <ThemedCard style={styles.section}>
              <View style={{ padding: 16 }}>
                <ThemedText variant="title" style={{ marginBottom: 8 }}>
                  فترة التحقق من التحديثات
                </ThemedText>
                <ThemedText
                  variant="secondary"
                  size="sm"
                  style={{ marginBottom: 16 }}
                >
                  كم مرة يتم التحقق من التحديثات
                </ThemedText>

                <View style={styles.intervalContainer}>
                  {intervalOptions.map((option) => (
                    <TouchableOpacity
                      key={option.value}
                      style={[
                        styles.intervalButton,
                        settings.checkInterval === option.value &&
                          styles.intervalButtonActive,
                      ]}
                      onPress={() =>
                        handleSettingChange("checkInterval", option.value)
                      }
                    >
                      <ThemedText
                        size="sm"
                        style={{
                          color:
                            settings.checkInterval === option.value
                              ? colors.surface
                              : colors.textPrimary,
                        }}
                      >
                        {option.label}
                      </ThemedText>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </ThemedCard>
          )}

          {/* أزرار الإجراءات */}
          <View style={styles.buttonContainer}>
            <ThemedButton
              title={
                checkingUpdates ? "جاري التحقق..." : "التحقق من التحديثات الآن"
              }
              onPress={handleCheckForUpdates}
              disabled={
                checkingUpdates || !updateInfo?.environment?.supportsUpdates
              }
              icon={
                checkingUpdates ? (
                  <ActivityIndicator size="small" color={colors.surface} />
                ) : (
                  <MaterialIcons
                    name="refresh"
                    size={20}
                    color={colors.surface}
                  />
                )
              }
              style={{ marginBottom: 12 }}
            />
          </View>
        </ScrollView>
      </View>
    </>
  );
};

export default UpdatesSettingsScreen;
