// صفحة حول التطبيق - About App Screen
import React from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  I18nManager,
  Image,
  RefreshControl,
} from "react-native";
import { Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  Loading,
} from "../../src/components";
import { useAppSettings } from "../../src/utils/hooks";

// RTL مُفعل بالفعل في _layout.jsx

const AboutScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();

  // جلب إعدادات التطبيق من API
  const { loading, refetch, appInfo, companyInfo, storeLinks } =
    useAppSettings();

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <ThemedStatusBar />
        <Loading text="جاري تحميل معلومات التطبيق..." />
      </>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: "حول التطبيق",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: colors.white,
          headerTitleStyle: { fontFamily: "Cairo-Bold" },
        }}
      />
      <ThemedStatusBar />

      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={refetch}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header مع لوجو التطبيق */}
        {/* <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <Image
            source={require("../../assets/logo.jpg")}
            style={styles.logo}
            resizeMode="contain"
          />
          <ThemedText
            variant="surface"
            size="xl"
            weight="bold"
            style={styles.appTitle}
          >
            {appInfo?.name || "مجوهرات مكة جولد جروب"}
          </ThemedText>
          <ThemedText variant="surface" size="md" style={styles.appVersion}>
            الإصدار {appInfo?.version || "2.0.0"}
          </ThemedText>
        </LinearGradient> */}

        {/* وصف التطبيق */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="info" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              حول التطبيق
            </ThemedText>
          </View>
          <ThemedText variant="secondary" size="md" style={styles.description}>
            {appInfo?.description ||
              "تطبيق مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية"}
          </ThemedText>
        </ThemedCard>

        {/* معلومات الشركة */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="business" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              عن الشركة
            </ThemedText>
          </View>
          <ThemedText
            variant="primary"
            size="md"
            weight="bold"
            style={styles.companyName}
          >
            {companyInfo?.name || "مكة جولد جروب"}
          </ThemedText>
          <ThemedText variant="secondary" size="md" style={styles.description}>
            {companyInfo?.description ||
              "شركة رائدة في مجال المجوهرات والمعادن الثمينة، نقدم أفضل المنتجات بأعلى معايير الجودة والحرفية"}
          </ThemedText>
        </ThemedCard>

        {/* الميزات */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="star" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              ميزات التطبيق
            </ThemedText>
          </View>

          {[
            {
              icon: "trending-up",
              title: "أسعار المعادن المباشرة",
              desc: "متابعة أسعار الذهب والفضة لحظة بلحظة",
            },
            {
              icon: "calculate",
              title: "حاسبة الزكاة",
              desc: "حساب زكاة الذهب والفضة بدقة",
            },
            {
              icon: "assessment",
              title: "حاسبة قيمة المجوهرات",
              desc: "تقدير قيمة المجوهرات حسب الوزن والعيار",
            },
            {
              icon: "category",
              title: "عرض المنتجات",
              desc: "استعراض مجموعة واسعة من المجوهرات",
            },
            {
              icon: "dark-mode",
              title: "الوضع المظلم",
              desc: "تجربة مريحة للعينين في جميع الأوقات",
            },
          ].map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <MaterialIcons
                name={feature.icon}
                size={20}
                color={colors.primary}
              />
              <View style={styles.featureContent}>
                <ThemedText
                  style={styles.featureTitle}
                  variant="primary"
                  size="md"
                  weight="bold"
                >
                  {feature.title}
                </ThemedText>
                <ThemedText
                  style={styles.featureDesc}
                  variant="secondary"
                  size="sm"
                >
                  {feature.desc}
                </ThemedText>
              </View>
            </View>
          ))}
        </ThemedCard>

        {/* روابط التحميل */}
        {(storeLinks?.app_store_url || storeLinks?.play_store_url) && (
          <ThemedCard style={styles.section}>
            <View style={styles.sectionHeader}>
              <MaterialIcons name="download" size={24} color={colors.primary} />
              <ThemedText
                variant="primary"
                size="lg"
                weight="bold"
                style={styles.sectionTitle}
              >
                تحميل التطبيق
              </ThemedText>
            </View>

            <View style={styles.storeLinks}>
              {storeLinks?.app_store_url && (
                <View
                  style={[
                    styles.storeButton,
                    { backgroundColor: colors.surface },
                  ]}
                >
                  <MaterialIcons
                    name="apple"
                    size={24}
                    color={colors.primary}
                  />
                  <ThemedText variant="primary" size="sm" weight="bold">
                    App Store
                  </ThemedText>
                </View>
              )}

              {storeLinks?.play_store_url && (
                <View
                  style={[
                    styles.storeButton,
                    { backgroundColor: colors.surface },
                  ]}
                >
                  <MaterialIcons
                    name="android"
                    size={24}
                    color={colors.primary}
                  />
                  <ThemedText variant="primary" size="sm" weight="bold">
                    Google Play
                  </ThemedText>
                </View>
              )}
            </View>
          </ThemedCard>
        )}

        {/* معلومات قانونية */}
        <ThemedCard style={styles.section}>
          <View style={styles.sectionHeader}>
            <MaterialIcons name="gavel" size={24} color={colors.primary} />
            <ThemedText
              variant="primary"
              size="lg"
              weight="bold"
              style={styles.sectionTitle}
            >
              معلومات قانونية
            </ThemedText>
          </View>
          <ThemedText variant="secondary" size="sm" style={styles.copyright}>
            {companyInfo?.copyright ||
              "جميع الحقوق محفوظة © 2024 مكة جولد جروب"}
          </ThemedText>
          <ThemedText variant="secondary" size="xs" style={styles.disclaimer}>
            هذا التطبيق مخصص لعرض المنتجات والمعلومات فقط. جميع الأسعار
            استرشادية وقابلة للتغيير.
          </ThemedText>
        </ThemedCard>

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpace} />
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: SPACING.md,
  },
  appTitle: {
    textAlign: "center",
    marginBottom: SPACING.sm,
  },
  appVersion: {
    textAlign: "center",
    opacity: 0.9,
  },
  section: {
    margin: SPACING.md,
    padding: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    marginLeft: SPACING.sm,
  },
  description: {
    lineHeight: 24,
    textAlign: "left",
  },
  companyName: {
    marginBottom: SPACING.sm,
    textAlign: "left",
  },
  featureItem: {
    flexDirection: "row-reverse",
    alignItems: "flex-end",
    marginBottom: SPACING.md,
  },
  featureContent: {
    flex: 1,
    marginRight: SPACING.sm,
  },
  featureTitle: {
    textAlign: "left",
  },
  featureDesc: {
    textAlign: "left",
  },
  storeLinks: {
    flexDirection: "row",
    justifyContent: "space-around",
    gap: SPACING.md,
  },
  storeButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: SPACING.md,
    borderRadius: 12,
    gap: SPACING.sm,
  },
  copyright: {
    textAlign: "center",
    marginBottom: SPACING.sm,
  },
  disclaimer: {
    textAlign: "center",
    fontStyle: "italic",
    opacity: 0.7,
  },
  bottomSpace: {
    height: SPACING.xl,
  },
});

export default AboutScreen;
