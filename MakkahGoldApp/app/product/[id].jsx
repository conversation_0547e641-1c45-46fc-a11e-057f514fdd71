// صفحة عرض المنتج الواحد - Product Detail Screen
import React, { useState, useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Linking,
  Share,
  I18nManager,
  FlatList,
  Modal,
  Animated,
} from "react-native";
import { Image } from "expo-image";

import { useLocalSearchParams, router, Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  useProduct,
  useRelatedProducts,
  useAppSettings,
} from "../../src/utils/hooks";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import ThemedCard from "../../src/components/ThemedCard";
import ThemedText from "../../src/components/ThemedText";
import Loading from "../../src/components/Loading";
import ErrorMessage from "../../src/components/ErrorMessage";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

// إجبار RTL يدوياً للعمل في Expo Go
const isRTL = true;

const { width, height } = Dimensions.get("window");

export default function ProductDetailScreen() {
  const { id } = useLocalSearchParams();
  const { colors } = useTheme();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isImageModalVisible, setIsImageModalVisible] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const scrollViewRef = useRef(null);
  const imageScrollRef = useRef(null);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // جلب بيانات المنتج
  const { data: product, loading, error, refetch } = useProduct(id);

  // جلب المنتجات المشابهة
  const { data: relatedProducts } = useRelatedProducts(id);

  // جلب إعدادات التطبيق للحصول على رقم الواتساب
  const { contactInfo } = useAppSettings();

  // Animation للظهور
  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  if (loading) {
    return <Loading text="جاري تحميل المنتج..." />;
  }

  if (error) {
    return <ErrorMessage message={error} onRetry={refetch} showRetry={true} />;
  }

  if (!product) {
    return (
      <ErrorMessage
        message="المنتج غير موجود"
        onRetry={() => router.back()}
        retryText="العودة"
        showRetry={true}
      />
    );
  }

  // الدوال المساعدة
  const formatPrice = (price) => {
    if (!price || isNaN(price) || price <= 0) {
      return "السعر غير متاح";
    }
    try {
      return `${price.toLocaleString("en-US")} جنيه`;
    } catch (error) {
      return "السعر غير متاح";
    }
  };

  const calculateDiscount = (price, oldPrice) => {
    if (!oldPrice || oldPrice <= price) return null;
    return Math.round(((oldPrice - price) / oldPrice) * 100);
  };

  const getArabicMaterialInfo = (product) => {
    // استخدام البيانات الجديدة من Laravel API
    const metalName = product.metal?.name_ar?.trim();
    const purityName = product.metal_purity_info?.name_ar?.trim();

    if (metalName && purityName) {
      return `${metalName} - ${purityName}`;
    } else if (metalName) {
      return metalName;
    } else if (purityName) {
      return purityName;
    }
    return "";
  };

  const openWhatsApp = () => {
    // التحقق من وجود رقم الواتساب من API
    if (!contactInfo?.whatsapp) {
      console.warn("رقم الواتساب غير متاح في إعدادات التطبيق");
      return;
    }

    const productName = product.name || "منتج غير محدد";
    const message = `مرحباً، أريد الاستفسار عن منتج: ${productName}`;
    const phoneNumber = contactInfo.whatsapp; // رقم الواتساب من API
    const cleanPhoneNumber = phoneNumber.replace(/[^0-9]/g, ""); // إزالة الرموز
    const url = `whatsapp://send?phone=${cleanPhoneNumber}&text=${encodeURIComponent(
      message
    )}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // فتح واتساب ويب كبديل
          const webUrl = `https://wa.me/${cleanPhoneNumber}?text=${encodeURIComponent(
            message
          )}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => console.error("خطأ في فتح واتساب:", err));
  };

  const handleShare = async () => {
    try {
      await Share.share({
        message: `تحقق من هذا المنتج الرائع: ${product.name || "منتج مميز"}`,
        title: product.name || "منتج مميز",
      });
    } catch (error) {
      console.error("خطأ في المشاركة:", error);
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    // هنا يمكن إضافة منطق حفظ المفضلة
  };

  // مكونات أزرار الهيدر
  const HeaderButtons = () => (
    <View style={styles.headerButtons}>
      <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
        <MaterialIcons name="share" size={24} color={colors.white} />
      </TouchableOpacity>
      {/* <TouchableOpacity style={styles.headerButton} onPress={toggleFavorite}>
        <MaterialIcons
          name={isFavorite ? "favorite" : "favorite-border"}
          size={24}
          color={colors.white}
        />
      </TouchableOpacity> */}
    </View>
  );

  const handleImagePress = (index) => {
    setSelectedImageIndex(index);
    setIsImageModalVisible(true);
  };

  // إعداد الصور - استخدام البيانات الحقيقية من Laravel API مع دعم مصادر متعددة
  const productImages = product.images || [];
  const hasImages =
    productImages.length > 0 || product.image || product.primary_image;
  const primaryImage = productImages.find((img) => img.is_primary) ||
    productImages[0] || { url: product.image || product.primary_image };
  const discount = calculateDiscount(product.price, product.old_price);

  // مكون معرض الصور
  const ImageGallery = () => (
    <View style={styles.imageGalleryContainer}>
      {hasImages ? (
        <>
          {/* الصورة الرئيسية */}
          <TouchableOpacity
            style={styles.mainImageContainer}
            onPress={() => handleImagePress(selectedImageIndex)}
            activeOpacity={0.9}
          >
            <Image
              source={{
                uri:
                  productImages[selectedImageIndex]?.url ||
                  productImages[selectedImageIndex]?.image_url ||
                  primaryImage?.url ||
                  primaryImage?.image_url ||
                  product.image ||
                  product.primary_image,
              }}
              style={styles.mainImage}
              resizeMode="cover"
              onError={(error) => {
                console.log(
                  "Main product image load error:",
                  error.nativeEvent.error
                );
              }}
            />

            {/* أيقونة التكبير */}
            <View style={styles.zoomIconContainer}>
              <MaterialIcons name="zoom-in" size={24} color={colors.white} />
            </View>

            {/* شارة الخصم */}
            {discount && (
              <View
                style={[
                  styles.discountBadge,
                  { backgroundColor: colors.error },
                ]}
              >
                <Text style={styles.discountText}>-{discount}%</Text>
              </View>
            )}
          </TouchableOpacity>

          {/* الصور المصغرة */}
          {productImages.length > 1 && (
            <FlatList
              ref={imageScrollRef}
              data={productImages}
              horizontal
              showsHorizontalScrollIndicator={false}
              keyExtractor={(item, index) => `image-${index}`}
              style={styles.thumbnailList}
              contentContainerStyle={styles.thumbnailContainer}
              renderItem={({ item, index }) => (
                <TouchableOpacity
                  style={[
                    styles.thumbnailItem,
                    {
                      borderColor:
                        selectedImageIndex === index
                          ? colors.primary
                          : colors.border,
                      borderWidth: selectedImageIndex === index ? 2 : 1,
                    },
                  ]}
                  onPress={() => setSelectedImageIndex(index)}
                >
                  <Image
                    source={{ uri: item.url || item.image_url }}
                    style={styles.thumbnailImage}
                    resizeMode="cover"
                    onError={(error) => {
                      console.log(
                        "Thumbnail image load error:",
                        error.nativeEvent.error
                      );
                    }}
                  />
                </TouchableOpacity>
              )}
            />
          )}
        </>
      ) : (
        <View
          style={[
            styles.noImageContainer,
            { backgroundColor: colors.lightGray },
          ]}
        >
          <MaterialIcons name="image" size={80} color={colors.textSecondary} />
          <Text style={[styles.noImageText, { color: colors.textSecondary }]}>
            لا توجد صور متاحة
          </Text>
        </View>
      )}
    </View>
  );

  // مكون معلومات المنتج
  const ProductInfo = () => (
    <ThemedCard style={styles.productInfoCard}>
      {/* اسم المنتج */}
      <ThemedText style={styles.productName}>
        {product.name || "اسم المنتج غير متاح"}
      </ThemedText>

      {/* الفئة */}
      {product.category && (
        <TouchableOpacity style={styles.categoryContainer}>
          <MaterialIcons name="category" size={16} color={colors.primary} />
          <ThemedText style={[styles.categoryText, { color: colors.primary }]}>
            {product.category.name}
          </ThemedText>
        </TouchableOpacity>
      )}

      {/* السعر */}
      <View style={styles.priceContainer}>
        {product.show_price ? (
          <View style={styles.priceRow}>
            <ThemedText
              style={[styles.currentPrice, { color: colors.primary }]}
            >
              {formatPrice(product.price)}
            </ThemedText>
            {product.old_price && product.old_price > product.price && (
              <ThemedText
                style={[styles.oldPrice, { color: colors.textSecondary }]}
              >
                {formatPrice(product.old_price)}
              </ThemedText>
            )}
          </View>
        ) : (
          <TouchableOpacity
            style={[styles.contactButton, { backgroundColor: colors.primary }]}
            onPress={openWhatsApp}
          >
            <MaterialIcons name="chat" size={20} color={colors.white} />
            <Text style={[styles.contactButtonText, { color: colors.white }]}>
              تواصل للسعر
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* أزرار الإجراءات */}
      <View style={styles.actionButtonsContainer}>
        {/* <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.surface }]}
          onPress={toggleFavorite}
        >
          <MaterialIcons
            name={isFavorite ? "favorite" : "favorite-border"}
            size={24}
            color={isFavorite ? colors.error : colors.textSecondary}
          />
        </TouchableOpacity> */}

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.surface }]}
          onPress={handleShare}
        >
          <MaterialIcons name="share" size={24} color={colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, { backgroundColor: colors.surface }]}
          onPress={openWhatsApp}
        >
          <MaterialIcons name="chat" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>
    </ThemedCard>
  );

  // مكون المواصفات
  const ProductSpecs = () => {
    const materialInfo = getArabicMaterialInfo(product);

    const specs = [
      { label: "المادة والعيار", value: materialInfo || "غير محدد" },
      {
        label: "الوزن",
        value: product.weight ? `${product.weight} جرام` : "غير محدد",
      },
      { label: "الأبعاد", value: product.dimensions || "غير محدد" },
      { label: "رقم المنتج", value: product.sku || "غير محدد" },
    ].filter((spec) => spec.value !== "غير محدد");

    if (specs.length === 0) return null;

    return (
      <ThemedCard style={styles.specsCard}>
        <ThemedText style={styles.sectionTitle}>المواصفات</ThemedText>
        {specs.map((spec, index) => (
          <View key={index} style={styles.specRow}>
            <ThemedText
              style={[styles.specLabel, { color: colors.textSecondary }]}
            >
              {spec.label}
            </ThemedText>
            <ThemedText style={styles.specValue}>{spec.value}</ThemedText>
          </View>
        ))}
      </ThemedCard>
    );
  };

  // مكون الوصف
  const ProductDescription = () => {
    if (!product.description) return null;

    return (
      <ThemedCard style={styles.descriptionCard}>
        <ThemedText style={styles.sectionTitle}>الوصف</ThemedText>
        <ThemedText
          style={[styles.descriptionText, { color: colors.textSecondary }]}
        >
          {product.description}
        </ThemedText>
      </ThemedCard>
    );
  };

  // مكون المنتجات المشابهة
  const RelatedProducts = () => {
    if (!relatedProducts || relatedProducts.length === 0) return null;

    const renderRelatedProduct = ({ item }) => (
      <TouchableOpacity
        style={[styles.relatedProductCard, { backgroundColor: colors.surface }]}
        onPress={() => router.push(`/product/${item.id}`)}
      >
        <Image
          source={{
            uri:
              item.images?.[0]?.url ||
              item.images?.[0]?.image_url ||
              item.images?.find((img) => img.is_primary)?.url ||
              item.images?.find((img) => img.is_primary)?.image_url ||
              item.image ||
              item.primary_image,
          }}
          style={styles.relatedProductImage}
          resizeMode="cover"
          onError={(error) => {
            console.log(
              "Related product image load error:",
              error.nativeEvent.error
            );
          }}
        />
        <View style={styles.relatedProductInfo}>
          <ThemedText style={styles.relatedProductName} numberOfLines={2}>
            {item.name}
          </ThemedText>
          {item.show_price ? (
            <ThemedText
              style={[styles.relatedProductPrice, { color: colors.primary }]}
            >
              {formatPrice(item.price)}
            </ThemedText>
          ) : (
            <ThemedText
              style={[styles.contactForPrice, { color: colors.primary }]}
            >
              تواصل للسعر
            </ThemedText>
          )}
        </View>
      </TouchableOpacity>
    );

    return (
      <View style={styles.relatedProductsContainer}>
        <ThemedText style={styles.sectionTitle}>منتجات مشابهة</ThemedText>
        <FlatList
          data={relatedProducts.slice(0, 6)}
          horizontal
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => `related-${item.id}`}
          contentContainerStyle={styles.relatedProductsList}
          renderItem={renderRelatedProduct}
        />
      </View>
    );
  };

  // مكون Modal للصور
  const ImageModal = () => (
    <Modal
      visible={isImageModalVisible}
      transparent={true}
      animationType="fade"
      onRequestClose={() => setIsImageModalVisible(false)}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity
          style={styles.modalCloseButton}
          onPress={() => setIsImageModalVisible(false)}
        >
          <MaterialIcons name="close" size={30} color={colors.white} />
        </TouchableOpacity>

        <FlatList
          data={productImages}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          initialScrollIndex={selectedImageIndex}
          keyExtractor={(item, index) => `modal-image-${index}`}
          renderItem={({ item }) => (
            <View style={styles.modalImageContainer}>
              <Image
                source={{ uri: item.url || item.image_url }}
                style={styles.modalImage}
                resizeMode="contain"
                onError={(error) => {
                  console.log(
                    "Modal image load error:",
                    error.nativeEvent.error
                  );
                }}
              />
            </View>
          )}
        />
      </View>
    </Modal>
  );

  // العرض الرئيسي
  return (
    <>
      <Stack.Screen
        options={{
          title: "تفاصيل المنتج",
          headerRight: () => <HeaderButtons />,
        }}
      />
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <ScrollView
          ref={scrollViewRef}
          style={[styles.scrollView, { backgroundColor: colors.background }]}
          showsVerticalScrollIndicator={false}
          bounces={true}
        >
          {/* محتوى الصفحة */}
          <View style={styles.content}>
            <ImageGallery />
            <ProductInfo />
            <ProductSpecs />
            <ProductDescription />
            <RelatedProducts />
          </View>
        </ScrollView>

        {/* Modal للصور */}
        <ImageModal />
      </Animated.View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },

  // Content Styles
  content: {
    flex: 1,
    paddingBottom: SPACING.xl,
  },

  // Header Button Styles
  headerButtons: {
    flexDirection: isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    marginLeft: isRTL ? 0 : SPACING.xs, // RTL support
    marginRight: isRTL ? SPACING.xs : 0, // RTL support
  },

  // Image Gallery Styles
  imageGalleryContainer: {
    marginBottom: SPACING.md,
  },
  mainImageContainer: {
    width: width,
    height: width * 0.8,
    position: "relative",
  },
  mainImage: {
    width: "100%",
    height: "100%",
  },
  zoomIconContainer: {
    position: "absolute",
    top: SPACING.md,
    left: isRTL ? undefined : SPACING.md, // RTL support
    right: isRTL ? SPACING.md : undefined, // RTL support
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  discountBadge: {
    position: "absolute",
    top: SPACING.md,
    right: isRTL ? undefined : SPACING.md, // RTL support
    left: isRTL ? SPACING.md : undefined, // RTL support
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
  discountText: {
    color: COLORS.white,
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.bold,
  },
  thumbnailList: {
    marginTop: SPACING.sm,
  },
  thumbnailContainer: {
    paddingHorizontal: SPACING.md,
  },
  thumbnailItem: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginLeft: isRTL ? 0 : SPACING.sm, // RTL support
    marginRight: isRTL ? SPACING.sm : 0, // RTL support
    overflow: "hidden",
  },
  thumbnailImage: {
    width: "100%",
    height: "100%",
  },
  noImageContainer: {
    width: width,
    height: width * 0.6,
    justifyContent: "center",
    alignItems: "center",
  },
  noImageText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    marginTop: SPACING.sm,
    textAlign: "center",
  },

  // Product Info Styles
  productInfoCard: {
    margin: SPACING.md,
    padding: SPACING.lg,
    justifyContent: "center",
  },
  productName: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.families.bold,
    marginBottom: SPACING.sm,
    textAlign: "center",
    lineHeight: 32,
  },
  categoryContainer: {
    flexDirection: isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    marginBottom: SPACING.md,
    justifyContent: "center", // RTL support
  },
  categoryText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    marginRight: isRTL ? 0 : SPACING.xs, // RTL support
    marginLeft: isRTL ? SPACING.xs : 0, // RTL support
  },
  priceContainer: {
    marginBottom: SPACING.lg,
  },
  priceRow: {
    flexDirection: isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    justifyContent: "center", // RTL support
  },
  currentPrice: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.families.bold,
    marginLeft: isRTL ? 0 : SPACING.sm, // RTL support
    marginRight: isRTL ? SPACING.sm : 0, // RTL support
  },
  oldPrice: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    textDecorationLine: "line-through",
  },
  contactButton: {
    flexDirection: isRTL ? "row-reverse" : "row", // RTL support
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    borderRadius: 12,
  },
  contactButtonText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.bold,
    marginRight: isRTL ? 0 : SPACING.sm, // RTL support
    marginLeft: isRTL ? SPACING.sm : 0, // RTL support
  },
  actionButtonsContainer: {
    flexDirection: isRTL ? "row-reverse" : "row", // RTL support
    justifyContent: "space-around",
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: COLORS.lightGray,
  },
  actionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
  },

  // Specs Styles
  specsCard: {
    margin: SPACING.md,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONTS.sizes.lg,
    fontFamily: FONTS.families.bold,
    marginBottom: SPACING.md,
    textAlign: isRTL ? "left" : "right", // RTL support
  },
  specRow: {
    flexDirection: isRTL ? "row" : "row-reverse", // RTL support
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
  },
  specLabel: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    flex: 1,
    textAlign: isRTL ? "left" : "right", // RTL support
  },
  specValue: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.bold,
    flex: 1,
    textAlign: isRTL ? "left" : "right", // RTL support
  },

  // Description Styles
  descriptionCard: {
    margin: SPACING.md,
    padding: SPACING.lg,
  },
  descriptionText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    lineHeight: 24,
    textAlign: isRTL ? "right" : "left", // RTL support
  },

  // Related Products Styles
  relatedProductsContainer: {
    margin: SPACING.md,
  },
  relatedProductsList: {
    paddingVertical: SPACING.md,
  },
  relatedProductCard: {
    width: 150,
    marginLeft: isRTL ? 0 : SPACING.md, // RTL support
    marginRight: isRTL ? SPACING.md : 0, // RTL support
    borderRadius: 12,
    overflow: "hidden",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  relatedProductImage: {
    width: "100%",
    height: 120,
  },
  relatedProductInfo: {
    padding: SPACING.sm,
  },
  relatedProductName: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.bold,
    marginBottom: SPACING.xs,
    textAlign: isRTL ? "left" : "right", // RTL support
  },
  relatedProductPrice: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.bold,
    textAlign: isRTL ? "right" : "left", // RTL support
  },
  contactForPrice: {
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.families.regular,
    textAlign: isRTL ? "right" : "left", // RTL support
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalCloseButton: {
    position: "absolute",
    top: 50,
    right: isRTL ? undefined : SPACING.md, // RTL support
    left: isRTL ? SPACING.md : undefined, // RTL support
    zIndex: 1000,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalImageContainer: {
    width: width,
    height: height,
    justifyContent: "center",
    alignItems: "center",
  },
  modalImage: {
    width: width - SPACING.lg * 2,
    height: height - 200,
  },
});
