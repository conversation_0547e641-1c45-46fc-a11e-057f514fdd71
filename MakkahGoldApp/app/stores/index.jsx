// صفحة فروع المتاجر - Stores Screen
import React, { useState } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  I18nManager,
  Pressable,
  Linking,
  Alert,
  RefreshControl,
  Dimensions,
  Platform,
} from "react-native";
import { Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING, RTL_CONFIG } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  Loading,
  ErrorMessage,
} from "../../src/components";
import { useStores } from "../../src/utils/hooks";

// RTL مُفعل بالفعل في _layout.jsx

// الحصول على أبعاد الشاشة للتصميم المتجاوب
const { width: screenWidth } = Dimensions.get("window");
const isTablet = screenWidth >= 768;

const StoresScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = useState(false);

  // جلب بيانات الفروع من API
  const { data: stores, loading, error, refetch } = useStores();

  // دالة التحديث بالسحب للأسفل
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Error refreshing stores:", error);
    } finally {
      setRefreshing(false);
    }
  };

  // دالة الاتصال بالفرع
  const handleCall = (phone) => {
    if (!phone) {
      Alert.alert("خطأ", "رقم الهاتف غير متوفر");
      return;
    }

    Alert.alert("الاتصال بالفرع", `هل تريد الاتصال بالرقم ${phone}؟`, [
      { text: "إلغاء", style: "cancel" },
      {
        text: "اتصال",
        onPress: () => {
          Linking.openURL(`tel:${phone}`).catch(() => {
            Alert.alert("خطأ", "لا يمكن فتح تطبيق الهاتف");
          });
        },
      },
    ]);
  };

  // دالة التوجه للفرع
  const handleDirections = (latitude, longitude, name) => {
    if (!latitude || !longitude) {
      Alert.alert("خطأ", "موقع الفرع غير متوفر");
      return;
    }

    const url = Platform.select({
      ios: `maps:0,0?q=${latitude},${longitude}`,
      android: `geo:0,0?q=${latitude},${longitude}(${name})`,
    });

    Linking.openURL(url).catch(() => {
      // Fallback to Google Maps
      const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
      Linking.openURL(googleMapsUrl).catch(() => {
        Alert.alert("خطأ", "لا يمكن فتح تطبيق الخرائط");
      });
    });
  };

  // دالة مشاركة معلومات الفرع
  const handleShare = (store) => {
    const message = `
${store.name}
${store.address}
${store.city}
هاتف: ${store.phone}
ساعات العمل: ${store.working_hours}

تطبيق مجوهرات مكة جولد جروب
    `.trim();

    const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;

    Linking.openURL(whatsappUrl).catch(() => {
      Alert.alert("خطأ", "لا يمكن فتح تطبيق واتساب");
    });
  };

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <ThemedStatusBar />
        <Loading text="جاري تحميل الفروع..." />
      </>
    );
  }

  // عرض حالة الخطأ
  if (error) {
    return (
      <>
        <ThemedStatusBar />
        <ErrorMessage message="حدث خطأ أثناء تحميل الفروع" onRetry={refetch} />
      </>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: "فروعنا",
          headerStyle: { backgroundColor: colors.primary },
          headerTintColor: colors.white,
          headerTitleStyle: { fontFamily: "Cairo-Bold" },
        }}
      />
      <ThemedStatusBar />

      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Header */}
        {/* <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="store" size={48} color={colors.surface} />
          <ThemedText
            variant="surface"
            size="xl"
            weight="bold"
            style={styles.headerTitle}
          >
            فروعنا
          </ThemedText>
          <ThemedText variant="surface" size="md" style={styles.headerSubtitle}>
            اكتشف أقرب فرع إليك
          </ThemedText>
        </LinearGradient> */}

        {/* قائمة الفروع */}
        <View style={styles.storesContainer}>
          {stores && stores.length > 0 ? (
            stores.reverse().map((store) => (
              <ThemedCard key={store.id} style={styles.storeCard}>
                {/* اسم الفرع */}
                <View style={styles.storeHeader}>
                  <MaterialIcons
                    name="location-on"
                    size={24}
                    color={colors.primary}
                  />
                  <ThemedText
                    align="left"
                    variant="primary"
                    size="lg"
                    weight="bold"
                    style={styles.storeName}
                  >
                    {store.name}
                  </ThemedText>
                </View>

                {/* العنوان */}
                <View style={styles.storeInfo}>
                  <MaterialIcons
                    name="place"
                    size={20}
                    color={colors.secondary}
                  />
                  <ThemedText
                    align="left"
                    variant="secondary"
                    size="md"
                    style={styles.storeAddress}
                  >
                    {store.address}, {store.city}
                  </ThemedText>
                </View>

                {/* رقم الهاتف */}
                <View style={styles.storeInfo}>
                  <MaterialIcons
                    name="phone"
                    size={20}
                    color={colors.secondary}
                  />
                  <ThemedText
                    align="left"
                    variant="secondary"
                    size="md"
                    style={styles.storePhone}
                  >
                    {store.phone}
                  </ThemedText>
                </View>

                {/* ساعات العمل */}
                <View style={styles.storeInfo}>
                  <MaterialIcons
                    name="schedule"
                    size={20}
                    color={colors.secondary}
                  />
                  <ThemedText
                    align="left"
                    variant="secondary"
                    size="sm"
                    style={styles.workingHours}
                  >
                    {store.working_hours}
                  </ThemedText>
                </View>

                {/* أزرار التفاعل */}
                <View style={styles.actionButtons}>
                  {/* زر الاتصال */}
                  <Pressable
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.primary + "15" },
                    ]}
                    android_ripple={{ color: colors.primary + "20" }}
                    onPress={() => handleCall(store.phone)}
                  >
                    <MaterialIcons
                      name="phone"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText
                      variant="primary"
                      size="sm"
                      weight="bold"
                      style={styles.actionButtonText}
                    >
                      اتصال
                    </ThemedText>
                  </Pressable>

                  {/* زر التوجه */}
                  <Pressable
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.primary + "15" },
                    ]}
                    android_ripple={{ color: colors.primary + "20" }}
                    onPress={() =>
                      handleDirections(
                        store.latitude,
                        store.longitude,
                        store.name
                      )
                    }
                  >
                    <MaterialIcons
                      name="directions"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText
                      variant="primary"
                      size="sm"
                      weight="bold"
                      style={styles.actionButtonText}
                    >
                      توجه
                    </ThemedText>
                  </Pressable>

                  {/* زر المشاركة */}
                  <Pressable
                    style={[
                      styles.actionButton,
                      { backgroundColor: colors.primary + "15" },
                    ]}
                    android_ripple={{ color: colors.primary + "20" }}
                    onPress={() => handleShare(store)}
                  >
                    <MaterialIcons
                      name="share"
                      size={20}
                      color={colors.primary}
                    />
                    <ThemedText
                      variant="primary"
                      size="sm"
                      weight="bold"
                      style={styles.actionButtonText}
                    >
                      مشاركة
                    </ThemedText>
                  </Pressable>
                </View>
              </ThemedCard>
            ))
          ) : (
            <ThemedCard style={styles.emptyCard}>
              <MaterialIcons
                name="store"
                size={64}
                color={colors.secondary}
                style={styles.emptyIcon}
              />
              <ThemedText
                variant="secondary"
                size="lg"
                weight="bold"
                style={styles.emptyTitle}
              >
                لا توجد فروع متاحة
              </ThemedText>
              <ThemedText
                variant="secondary"
                size="md"
                style={styles.emptyMessage}
              >
                سيتم إضافة الفروع قريباً
              </ThemedText>
            </ThemedCard>
          )}
        </View>

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpace} />
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    textAlign: "center",
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  headerSubtitle: {
    textAlign: "center",
    opacity: 0.9,
  },
  storesContainer: {
    padding: SPACING.md,
    gap: SPACING.md,
    flexDirection: "column",
  },
  storeCard: {
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  storeHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: SPACING.md,
  },
  storeName: {
    flex: 1,
    marginHorizontal: SPACING.sm,
  },
  storeInfo: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: SPACING.sm,
    paddingHorizontal: SPACING.xs,
  },
  storeAddress: {
    flex: 1,
    marginHorizontal: SPACING.sm,
    lineHeight: 22,
  },
  storePhone: {
    flex: 1,
    marginHorizontal: SPACING.sm,
    fontFamily: FONTS.families.bold,
  },
  workingHours: {
    flex: 1,
    marginHorizontal: SPACING.sm,
    lineHeight: 20,
    opacity: 0.8,
  },
  actionButtons: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: SPACING.lg,
    paddingTop: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: "rgba(0,0,0,0.1)",
    gap: SPACING.sm,
  },
  actionButton: {
    flex: 1,
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: 12,
    minHeight: 60,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionButtonText: {
    marginTop: SPACING.xs,
    textAlign: "center",
  },
  emptyCard: {
    padding: SPACING.xl,
    alignItems: "center",
    marginTop: SPACING.xl,
  },
  emptyIcon: {
    marginBottom: SPACING.lg,
    opacity: 0.5,
  },
  emptyTitle: {
    textAlign: "center",
    marginBottom: SPACING.sm,
  },
  emptyMessage: {
    textAlign: "center",
    opacity: 0.7,
  },
  bottomSpace: {
    height: SPACING.xl,
  },
});

export default StoresScreen;
