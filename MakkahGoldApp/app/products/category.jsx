// صفحة منتجات القسم - Category Products Screen
import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  RefreshControl,
  I18nManager,
  Dimensions,
  Pressable,
  ActivityIndicator,
  TouchableOpacity,
  Linking,
} from "react-native";
import { Image } from "expo-image";

import { router, useLocalSearchParams, Stack } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { useAppSettings } from "../../src/utils/hooks";
import { ThemedText } from "../../src/components";
import apiService from "../../src/services/api";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

// الحصول على أبعاد الشاشة للتصميم المتجاوب
const { width: screenWidth } = Dimensions.get("window");

// حساب عدد الأعمدة حسب حجم الشاشة
const getNumColumns = () => {
  if (screenWidth >= 768) return 3; // Tablet
  return 2; // Phone
};

const CategoryProductsScreen = () => {
  const { colors } = useTheme();
  const params = useLocalSearchParams();
  const { categoryId, categoryName } = params;

  // جلب إعدادات التطبيق للحصول على رقم الواتساب
  const { contactInfo } = useAppSettings();

  // Debug: طباعة المعاملات المُستلمة
  console.log("Category params:", { categoryId, categoryName });

  // تنظيف وفك ترميز اسم القسم
  const cleanCategoryName = categoryName
    ? decodeURIComponent(categoryName).trim()
    : "منتجات القسم";

  // State للمنتجات والتحميل
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(true);

  const numColumns = getNumColumns();
  const itemWidth =
    (screenWidth - SPACING.md * 2 - SPACING.sm * (numColumns - 1)) / numColumns;

  // تنسيق السعر - نفس المنطق المستخدم في CategoryProductGrid
  const formatPrice = (price) => {
    if (!price || isNaN(price) || price <= 0) {
      return "السعر غير متاح";
    }
    try {
      return `${price.toLocaleString("en-US")} جنيه`;
    } catch (error) {
      return "السعر غير متاح";
    }
  };

  // حساب نسبة الخصم
  const calculateDiscount = (price, oldPrice) => {
    if (!oldPrice || oldPrice <= price) return null;
    return Math.round(((oldPrice - price) / oldPrice) * 100);
  };

  // فتح واتساب للتواصل - استخدام رقم الواتساب من API
  const openWhatsApp = (productName) => {
    // التحقق من وجود رقم الواتساب من API
    if (!contactInfo?.whatsapp) {
      console.warn("رقم الواتساب غير متاح في إعدادات التطبيق");
      return;
    }

    const safeName = String(productName || "منتج غير محدد");
    const message = `مرحباً، أريد الاستفسار عن سعر منتج: ${safeName}`;
    const phoneNumber = contactInfo.whatsapp; // رقم الواتساب من API
    const cleanPhoneNumber = phoneNumber.replace(/[^0-9]/g, ""); // إزالة الرموز
    const url = `whatsapp://send?phone=${cleanPhoneNumber}&text=${encodeURIComponent(
      message
    )}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          // فتح واتساب ويب كبديل
          const webUrl = `https://wa.me/${cleanPhoneNumber}?text=${encodeURIComponent(
            message
          )}`;
          return Linking.openURL(webUrl);
        }
      })
      .catch((err) => console.error("خطأ في فتح واتساب:", err));
  };

  // دالة جلب المنتجات
  const fetchProducts = useCallback(
    async (pageNum = 1, isRefresh = false) => {
      try {
        if (pageNum === 1) {
          isRefresh ? setRefreshing(true) : setLoading(true);
        } else {
          setLoadingMore(true);
        }

        const response = await apiService.getProducts({
          category_id: categoryId,
          page: pageNum,
          per_page: 20,
        });

        const newProducts = response.data || [];

        if (pageNum === 1) {
          setProducts(newProducts);
        } else {
          setProducts((prev) => [...prev, ...newProducts]);
        }

        setHasNextPage(response.has_next_page || false);
        setPage(pageNum);
        setError(null);
      } catch (err) {
        setError(err.message || "خطأ في تحميل المنتجات");
        if (pageNum === 1) {
          setProducts([]);
        }
      } finally {
        setLoading(false);
        setRefreshing(false);
        setLoadingMore(false);
      }
    },
    [categoryId]
  );

  // تحميل المنتجات عند فتح الصفحة
  useEffect(() => {
    if (categoryId) {
      fetchProducts(1);
    }
  }, [categoryId, fetchProducts]);

  // دالة إعادة التحميل
  const onRefresh = useCallback(() => {
    fetchProducts(1, true);
  }, [fetchProducts]);

  // دالة تحميل المزيد
  const loadMore = useCallback(() => {
    if (!loadingMore && hasNextPage) {
      fetchProducts(page + 1);
    }
  }, [loadingMore, hasNextPage, page, fetchProducts]);

  // دالة التنقل إلى تفاصيل المنتج
  const navigateToProduct = (product) => {
    router.push(`/product/${product.id}`);
  };

  // مكون زر إعادة التحميل للهيدر
  const HeaderRefreshButton = () => (
    <Pressable
      style={({ pressed }) => [
        styles.headerRefreshButton,
        pressed && { opacity: 0.7 },
      ]}
      onPress={onRefresh}
      android_ripple={{ color: "rgba(255,255,255,0.2)", radius: 20 }}
    >
      <MaterialIcons
        name="refresh"
        size={24}
        color={colors.white}
        style={refreshing && styles.refreshIconActive}
      />
    </Pressable>
  );

  // عرض حالة التحميل الأولي
  if (loading && products.length === 0) {
    return (
      <View
        style={[
          styles.container,
          styles.loadingContainer,
          { backgroundColor: colors.background },
        ]}
      >
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
          جاري تحميل منتجات {cleanCategoryName}...
        </Text>
      </View>
    );
  }

  // عرض حالة الخطأ
  if (error && products.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            خطأ في تحميل المنتجات
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <Pressable
            style={({ pressed }) => [
              styles.retryButton,
              { backgroundColor: colors.primary },
              pressed && { transform: [{ scale: 0.95 }] },
            ]}
            onPress={() => fetchProducts(1)}
            android_ripple={{ color: "rgba(255,255,255,0.2)" }}
          >
            <MaterialIcons name="refresh" size={20} color={colors.surface} />
            <Text style={[styles.retryText, { color: colors.surface }]}>
              إعادة المحاولة
            </Text>
          </Pressable>
        </View>
      </View>
    );
  }

  return (
    <>
      <Stack.Screen
        options={{
          title: cleanCategoryName,
          headerRight: () => <HeaderRefreshButton />,
          headerBackTitle: "", // Remove back button text completely
          headerBackTitleVisible: false, // Hide back button title
        }}
      />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* معلومات عدد المنتجات */}
        <View style={styles.productsCountInfo}>
          <Text
            style={[styles.productsCountText, { color: colors.textSecondary }]}
          >
            {products.length} منتج متاح
          </Text>
        </View>

        {/* Products Grid with Enhanced FlatList */}
        <FlatList
          data={products}
          numColumns={numColumns}
          key={numColumns}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.productsContainer}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
          onEndReached={loadMore}
          onEndReachedThreshold={0.5}
          renderItem={({ item }) => (
            <Pressable
              style={({ pressed }) => [
                styles.productCard,
                {
                  width: itemWidth,
                  backgroundColor: colors.surface,
                },
                pressed && styles.productCardPressed,
              ]}
              onPress={() => navigateToProduct(item)}
              android_ripple={{ color: colors.primary + "20" }}
            >
              {/* Enhanced Product Card with LinearGradient */}
              <LinearGradient
                colors={[
                  colors.surface,
                  colors.surfaceVariant || colors.surface,
                ]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.productCardGradient}
              >
                {/* Product Image */}
                <View style={styles.imageContainer}>
                  {item.image ||
                  item.primary_image ||
                  (item.images && item.images.length > 0) ? (
                    <Image
                      source={{
                        uri:
                          item.image ||
                          item.primary_image ||
                          item.images?.[0]?.image_url ||
                          item.images?.[0]?.url,
                      }}
                      style={styles.productImage}
                      resizeMode="cover"
                      onError={(error) => {
                        console.log(
                          "Image load error:",
                          error.nativeEvent.error
                        );
                      }}
                    />
                  ) : (
                    <LinearGradient
                      colors={[
                        colors.primary,
                        colors.primaryDark || colors.primary,
                      ]}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 1 }}
                      style={styles.placeholderImage}
                    >
                      <MaterialIcons
                        name="diamond"
                        size={isTablet ? 40 : 32}
                        color={colors.surface}
                      />
                    </LinearGradient>
                  )}

                  {/* علامة الخصم */}
                  {(item.discount_percentage ||
                    calculateDiscount(item.price, item.old_price)) &&
                    item.show_price !== false && (
                      <View
                        style={[
                          styles.discountBadge,
                          { backgroundColor: colors.accent },
                        ]}
                      >
                        <ThemedText variant="inverse" size="xs" weight="bold">
                          {`-${
                            item.discount_percentage ||
                            calculateDiscount(item.price, item.old_price)
                          }%`}
                        </ThemedText>
                      </View>
                    )}
                </View>

                {/* Product Info */}
                <View style={styles.productInfo}>
                  <ThemedText
                    variant="primary"
                    size="sm"
                    weight="regular"
                    numberOfLines={2}
                    style={styles.productName}
                  >
                    {item.name}
                  </ThemedText>

                  {/* السعر أو زر تواصل للسعر - حسب إعدادات المنتج */}
                  <View style={styles.priceContainer}>
                    {item.show_price !== false ? (
                      <>
                        <ThemedText variant="primary" size="md" weight="bold">
                          {formatPrice(item.price)}
                        </ThemedText>
                        {item.old_price && item.old_price > item.price && (
                          <ThemedText
                            variant="secondary"
                            size="sm"
                            style={styles.oldPrice}
                          >
                            {formatPrice(item.old_price)}
                          </ThemedText>
                        )}
                      </>
                    ) : (
                      <TouchableOpacity
                        style={[
                          styles.contactButton,
                          { backgroundColor: colors.accent },
                        ]}
                        onPress={() => openWhatsApp(item.name)}
                        activeOpacity={0.8}
                      >
                        <MaterialIcons
                          name="chat"
                          size={16}
                          color={colors.textInverse}
                        />
                        <ThemedText
                          variant="inverse"
                          size="sm"
                          weight="bold"
                          style={styles.contactButtonText}
                        >
                          تواصل للسعر
                        </ThemedText>
                      </TouchableOpacity>
                    )}
                  </View>

                  {/* معلومات المعدن والعيار - البيانات تأتي جاهزة من API */}
                  {(item.metal?.name_ar || item.metal_purity_info?.name_ar) && (
                    <ThemedText
                      variant="secondary"
                      size="xs"
                      numberOfLines={1}
                      style={styles.materialInfo}
                    >
                      {(() => {
                        const metalName = item.metal?.name_ar?.trim();
                        const purityName =
                          item.metal_purity_info?.name_ar?.trim();

                        if (metalName && purityName) {
                          return `${metalName} - ${purityName}`;
                        } else if (metalName) {
                          return metalName;
                        } else if (purityName) {
                          return purityName;
                        }
                        return "";
                      })()}
                    </ThemedText>
                  )}
                </View>
              </LinearGradient>
            </Pressable>
          )}
          ItemSeparatorComponent={() => <View style={{ height: SPACING.sm }} />}
          columnWrapperStyle={numColumns > 1 ? styles.row : null}
          ListFooterComponent={() =>
            loadingMore ? (
              <View style={styles.loadingMoreContainer}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text
                  style={[
                    styles.loadingMoreText,
                    { color: colors.textSecondary },
                  ]}
                >
                  جاري تحميل المزيد...
                </Text>
              </View>
            ) : null
          }
          ListEmptyComponent={() =>
            !loading && (
              <View style={styles.emptyContainer}>
                <MaterialIcons
                  name="inventory"
                  size={64}
                  color={colors.textSecondary}
                />
                <Text
                  style={[styles.emptyText, { color: colors.textSecondary }]}
                >
                  لا توجد منتجات في هذا القسم
                </Text>
              </View>
            )
          }
        />
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: "center",
    fontFamily: "Cairo-Medium",
  },
  // Header Button Styles
  headerRefreshButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
  },
  // Products Count Info
  productsCountInfo: {
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
  },
  productsCountText: {
    fontSize: 14,
    fontFamily: "Cairo-Regular",
    textAlign: "left",
  },
  refreshIconActive: {
    transform: [{ rotate: "360deg" }],
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorTitle: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    fontFamily: "Cairo-Bold",
  },
  errorMessage: {
    marginTop: 8,
    fontSize: 14,
    textAlign: "center",
    fontFamily: "Cairo-Regular",
  },
  retryButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 20,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  retryText: {
    fontWeight: "bold",
    fontFamily: "Cairo-Bold",
  },
  productsContainer: {
    padding: SPACING.md,
    paddingBottom: SPACING.xl,
  },
  row: {
    justifyContent: "space-between",
  },
  productCard: {
    borderRadius: 16,
    marginBottom: SPACING.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  productCardPressed: {
    transform: [{ scale: 0.98 }],
    elevation: 3,
  },
  productCardGradient: {
    borderRadius: 16,
    padding: SPACING.md,
  },
  imageContainer: {
    width: "100%",
    height: 120,
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: SPACING.sm,
  },
  productImage: {
    width: "100%",
    height: "100%",
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  productInfo: {
    alignItems: "center",
    marginBottom: SPACING.sm,
    minHeight: 70,
  },
  productName: {
    fontSize: 14,
    fontFamily: "Cairo-Bold",
    textAlign: "center",
    marginBottom: SPACING.xs,
    lineHeight: 20,
  },
  productPrice: {
    fontSize: 14,
    fontFamily: "Cairo-Bold",
    textAlign: "center",
    marginBottom: SPACING.xs,
  },
  materialInfo: {
    textAlign: "center",
    marginTop: SPACING.xs,
    opacity: 0.8,
  },
  loadingMoreContainer: {
    alignItems: "center",
    paddingVertical: SPACING.lg,
    gap: SPACING.sm,
  },
  loadingMoreText: {
    fontSize: 12,
    fontFamily: "Cairo-Regular",
    textAlign: "center",
  },
  emptyContainer: {
    alignItems: "center",
    padding: 40,
    marginTop: SPACING.xl,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: "center",
    fontFamily: "Cairo-Regular",
  },
  // أنماط التصميم الموحد مع CategoryProductGrid
  productItem: {
    marginBottom: SPACING.md,
  },
  priceContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: SPACING.xs,
    gap: SPACING.xs,
  },
  oldPrice: {
    textDecorationLine: "line-through",
  },
  contactButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 16,
    gap: SPACING.xs,
    marginTop: SPACING.xs,
  },
  contactButtonText: {
    fontSize: 12,
  },
  discountBadge: {
    position: "absolute",
    top: SPACING.sm,
    right: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: 12,
  },
});

export default CategoryProductsScreen;
