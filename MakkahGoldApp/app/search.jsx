// صفحة نتائج البحث - Search Results Screen
import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Image,
  I18nManager,
  Dimensions,
} from "react-native";
import { useLocalSearchParams, router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "../src/contexts/ThemeContext";
import { apiService } from "../src/services/api";
import { COLORS, FONTS, SPACING } from "../src/constants";
import ThemedCard from "../src/components/ThemedCard";
import ThemedText from "../src/components/ThemedText";
import Loading from "../src/components/Loading";
import ErrorMessage from "../src/components/ErrorMessage";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const { width } = Dimensions.get("window");

export default function SearchResultsScreen() {
  const { q } = useLocalSearchParams();
  const { colors, isDarkMode } = useTheme();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState(null);
  const [loadingMore, setLoadingMore] = useState(false);

  // جلب نتائج البحث
  const searchProducts = async (query, page = 1, append = false) => {
    try {
      if (!append) {
        setLoading(true);
        setError(null);
      } else {
        setLoadingMore(true);
      }

      const response = await apiService.searchProducts(query, page);

      if (append) {
        setProducts((prev) => [...prev, ...(response.data || [])]);
      } else {
        setProducts(response.data || []);
      }

      setPagination(response.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : "خطأ في البحث");
      console.error("Search Error:", err);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  useEffect(() => {
    if (q && q.trim().length >= 2) {
      searchProducts(q.trim());
    }
  }, [q]);

  // تحميل المزيد من النتائج
  const loadMore = () => {
    if (
      pagination &&
      pagination.current_page < pagination.last_page &&
      !loadingMore
    ) {
      searchProducts(q.trim(), pagination.current_page + 1, true);
    }
  };

  // تنسيق السعر
  const formatPrice = (price) => {
    if (!price || isNaN(price) || price <= 0) {
      return "السعر غير متاح";
    }
    try {
      return `${price.toLocaleString("en-US")} جنيه`;
    } catch (error) {
      return "السعر غير متاح";
    }
  };

  // مكون الهيدر للبحث
  const renderSearchHeader = () => (
    <View style={styles.searchHeader}>
      <ThemedText
        align="right"
        style={[styles.searchQuery, { color: colors.textSecondary }]}
      >
        نتائج البحث عن: "{q}"
      </ThemedText>
    </View>
  );

  // مكون عدد النتائج
  const renderResultsHeader = () => (
    <View style={styles.resultsHeader}>
      <ThemedText
        style={[styles.resultsCount, { color: colors.textSecondary }]}
      >
        {pagination?.total || products.length} نتيجة
      </ThemedText>
    </View>
  );

  // مكون عرض المنتج
  const renderProduct = ({ item }) => (
    <TouchableOpacity
      style={[styles.productCard, { backgroundColor: colors.surface }]}
      onPress={() => router.push(`/product/${item.id}`)}
    >
      <Image
        source={{
          uri:
            item.image ||
            item.images?.[0]?.url ||
            item.images?.find((img) => img.is_primary)?.url,
        }}
        style={styles.productImage}
        resizeMode="cover"
      />

      <View style={styles.productInfo}>
        <ThemedText align="left" style={styles.productName} numberOfLines={2}>
          {item.name || item.name_ar || "منتج غير محدد"}
        </ThemedText>

        {item.category && (
          <View style={styles.categoryContainer}>
            <MaterialIcons
              name="category"
              size={14}
              color={colors.textSecondary}
            />
            <ThemedText
              style={[styles.categoryText, { color: colors.textSecondary }]}
            >
              {item.category.name || item.category.name_ar}
            </ThemedText>
          </View>
        )}

        <View style={styles.priceContainer}>
          {item.show_price ? (
            <ThemedText style={[styles.price, { color: colors.primary }]}>
              {formatPrice(item.price)}
            </ThemedText>
          ) : (
            <ThemedText
              style={[styles.contactPrice, { color: colors.primary }]}
            >
              تواصل للسعر
            </ThemedText>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return <Loading text={`جاري البحث عن "${q}"...`} />;
  }
  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {error ? (
        <View style={styles.content}>
          <ErrorMessage
            message={error}
            onRetry={() => searchProducts(q.trim())}
            showRetry={true}
          />
        </View>
      ) : products.length > 0 ? (
        <FlatList
          data={products}
          renderItem={renderProduct}
          keyExtractor={(item) => `search-${item.id}`}
          numColumns={2}
          columnWrapperStyle={styles.row}
          contentContainerStyle={styles.productsList}
          showsVerticalScrollIndicator={false}
          onEndReached={loadMore}
          onEndReachedThreshold={0.1}
          ListHeaderComponent={
            <View style={styles.content}>
              {renderSearchHeader()}
              {renderResultsHeader()}
            </View>
          }
          ListFooterComponent={
            loadingMore ? (
              <View style={styles.loadingMore}>
                <ThemedText style={{ color: colors.textSecondary }}>
                  جاري تحميل المزيد...
                </ThemedText>
              </View>
            ) : null
          }
        />
      ) : (
        <View style={styles.content}>
          {renderSearchHeader()}
          <View style={styles.noResultsContainer}>
            <MaterialIcons
              name="search-off"
              size={80}
              color={colors.textSecondary}
            />
            <ThemedText
              style={[styles.noResultsTitle, { color: colors.textPrimary }]}
            >
              لا توجد نتائج
            </ThemedText>
            <ThemedText
              style={[styles.noResultsText, { color: colors.textSecondary }]}
            >
              لم نجد أي منتجات تطابق بحثك عن "{q}"
            </ThemedText>
            <TouchableOpacity
              style={[styles.retryButton, { backgroundColor: colors.primary }]}
              onPress={() => router.back()}
            >
              <ThemedText
                style={[styles.retryButtonText, { color: colors.white }]}
              >
                العودة للبحث
              </ThemedText>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Content Styles
  content: {
    paddingHorizontal: SPACING.md,
  },
  searchHeader: {
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    marginBottom: SPACING.md,
  },
  searchQuery: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    textAlign: "left",
  },
  resultsHeader: {
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.lightGray,
    marginBottom: SPACING.md,
  },
  resultsCount: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    textAlign: "left",
  },

  // Products List Styles
  productsList: {
    paddingHorizontal: SPACING.md,
    paddingBottom: SPACING.xl,
  },
  row: {
    justifyContent: "space-between",
    marginBottom: SPACING.md,
  },
  productCard: {
    width: (width - SPACING.md * 3) / 2,
    borderRadius: 12,
    overflow: "hidden",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
  },
  productImage: {
    width: "100%",
    height: 120,
  },
  productInfo: {
    padding: SPACING.sm,
  },
  productName: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.bold,
    marginBottom: SPACING.xs,
    textAlign: "left",
    lineHeight: 20,
  },
  categoryContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    marginBottom: SPACING.xs,
  },
  categoryText: {
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.families.regular,
    marginRight: SPACING.xs,
  },
  priceContainer: {
    marginTop: SPACING.xs,
  },
  price: {
    fontSize: FONTS.sizes.sm,
    fontFamily: FONTS.families.bold,
    textAlign: "right",
  },
  contactPrice: {
    fontSize: FONTS.sizes.xs,
    fontFamily: FONTS.families.regular,
    textAlign: "right",
  },

  // Loading More Styles
  loadingMore: {
    paddingVertical: SPACING.lg,
    alignItems: "center",
  },

  // No Results Styles
  noResultsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: SPACING.lg,
    minHeight: 400, // ارتفاع أدنى لضمان العرض المناسب
  },
  noResultsTitle: {
    fontSize: FONTS.sizes.xl,
    fontFamily: FONTS.families.bold,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
    textAlign: "center",
  },
  noResultsText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: SPACING.xl,
  },
  retryButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 12,
  },
  retryButtonText: {
    fontSize: FONTS.sizes.md,
    fontFamily: FONTS.families.bold,
  },
});
