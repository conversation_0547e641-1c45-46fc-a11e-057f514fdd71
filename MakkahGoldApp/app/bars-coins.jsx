// صفحة السبائك والعملات - Bars & Coins Screen
import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  I18nManager,
  Image,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../src/contexts/ThemeContext";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  AppHeader,
  Loading,
} from "../src/components";
import { apiService } from "../src/services/api";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const BarsCoinsScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedType, setSelectedType] = useState("سبيكة"); // سبيكة كافتراضي
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);
  const [selectedProductTypeId, setSelectedProductTypeId] = useState(null);
  const [showCompanyDropdown, setShowCompanyDropdown] = useState(false);
  const [showProductDropdown, setShowProductDropdown] = useState(false);

  // البيانات
  const [companies, setCompanies] = useState([]);
  const [productTypes, setProductTypes] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [calculatedPrices, setCalculatedPrices] = useState(null);
  const [currentPrices, setCurrentPrices] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // تحميل البيانات الأولية
  useEffect(() => {
    loadInitialData();
  }, []);

  // تحميل البيانات عند تغيير النوع
  useEffect(() => {
    if (selectedType) {
      loadCompanies();
      setSelectedCompanyId(null);
      setSelectedProductTypeId(null);
      setSelectedProduct(null);
      setCalculatedPrices(null);
    }
  }, [selectedType]);

  // تحميل أنواع المنتجات عند تغيير الشركة
  useEffect(() => {
    if (selectedCompanyId) {
      loadProductTypesForCompany();
      setSelectedProductTypeId(null);
      setSelectedProduct(null);
      setCalculatedPrices(null);
    }
  }, [selectedCompanyId]);

  // تحميل المنتج المحدد (يحتوي على الحسابات المنسقة)
  useEffect(() => {
    if (selectedCompanyId && selectedProductTypeId) {
      loadSelectedProduct();
    }
  }, [selectedCompanyId, selectedProductTypeId]);

  // تحميل البيانات الأولية
  const loadInitialData = async () => {
    try {
      setLoading(true);
      await Promise.all([loadCurrentPrices(), loadCompanies()]);
    } catch (err) {
      setError("حدث خطأ في تحميل البيانات");
      console.error("Error loading initial data:", err);
    } finally {
      setLoading(false);
    }
  };

  // تحميل أسعار المعادن الحالية
  const loadCurrentPrices = async () => {
    try {
      const prices = await apiService.getBarsCoinsCurrentPrices();
      setCurrentPrices(
        prices || {
          gold_24k: { buy_price: "5490", sell_price: "5500" },
          gold_21k: { buy_price: "4800", sell_price: "4810" },
        }
      );
    } catch (err) {
      console.error("Error loading current prices:", err);
      // استخدام أسعار افتراضية في حالة الخطأ
      setCurrentPrices({
        gold_24k: { buy_price: "5490", sell_price: "5500" },
        gold_21k: { buy_price: "4800", sell_price: "4810" },
      });
    }
  };

  // تحميل الشركات
  const loadCompanies = async () => {
    try {
      const companies = await apiService.getBarsCoinsCompanies(selectedType);
      setCompanies(companies || []);
    } catch (err) {
      console.error("Error loading companies:", err);
      setCompanies([]);
    }
  };

  // تحميل أنواع المنتجات للشركة المحددة
  const loadProductTypesForCompany = async () => {
    try {
      const productTypes = await apiService.getBarsCoinsProductTypes(
        selectedType,
        selectedCompanyId
      );
      setProductTypes(productTypes || []);
    } catch (err) {
      console.error("Error loading product types:", err);
      setProductTypes([]);
    }
  };

  // تحميل المنتج المحدد
  const loadSelectedProduct = async () => {
    try {
      const product = await apiService.getBarsCoinsCompanyProduct(
        selectedCompanyId,
        selectedProductTypeId
      );
      setSelectedProduct(product);
    } catch (err) {
      console.error("Error loading selected product:", err);
      setSelectedProduct(null);
    }
  };

  // معالجة التحديث
  const onRefresh = async () => {
    setRefreshing(true);
    await loadInitialData();
    if (selectedCompanyId && selectedProductTypeId) {
      await loadSelectedProduct(); // يحتوي على الحسابات المنسقة
    }
    setRefreshing(false);
  };

  // معالجة تغيير نوع المنتج
  const handleProductTypeChange = (productTypeId) => {
    setSelectedProductTypeId(productTypeId);
  };

  // إغلاق الـ dropdowns عند تغيير النوع
  const handleTypeChange = (type) => {
    setSelectedType(type);
    setShowCompanyDropdown(false);
    setShowProductDropdown(false);
    setCalculatedPrices(null);
  };

  // إغلاق dropdown المنتج عند تغيير الشركة
  const handleCompanyChange = (companyId) => {
    setSelectedCompanyId(companyId);
    setShowProductDropdown(false);
    setCalculatedPrices(null);
  };

  // إغلاق جميع الـ dropdowns
  const closeAllDropdowns = () => {
    setShowCompanyDropdown(false);
    setShowProductDropdown(false);
  };

  // استخدام الحسابات من API عند تغيير المنتج
  useEffect(() => {
    if (selectedProduct && selectedProduct.calculated_prices) {
      setCalculatedPrices(selectedProduct.calculated_prices);
    } else {
      setCalculatedPrices(null);
    }
  }, [selectedProduct]);

  // إنشاء الـ styles
  const styles = createStyles(colors);

  // عرض حالة التحميل
  if (loading) {
    return <Loading text="جاري تحميل بيانات السبائك والعملات..." />;
  }

  // عرض حالة الخطأ
  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedStatusBar />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <ThemedText variant="primary" size="lg" style={styles.errorTitle}>
            خطأ في تحميل البيانات
          </ThemedText>
          <ThemedText variant="secondary" size="md" style={styles.errorMessage}>
            {error}
          </ThemedText>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={loadInitialData}
          >
            <ThemedText variant="inverse" size="md" weight="bold">
              إعادة المحاولة
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedStatusBar />

      {/* Header مشترك مثل صفحة الأقسام */}
      <AppHeader
        title="السبائك والعملات"
        subtitle="تصفح أسعار السبائك والعملات الذهبية"
        icon="account-balance"
        showBackButton={false}
        useGradient={true}
      />

      {/* التبويبات مثل الويب */}
      <View style={styles.tabsContainer}>
        <View style={styles.tabsWrapper}>
          <TouchableOpacity
            style={[
              styles.tabButton,
              selectedType === "سبيكة" && styles.activeTabButton,
            ]}
            onPress={() => handleTypeChange("سبيكة")}
          >
            <ThemedText
              variant={selectedType === "سبيكة" ? "inverse" : "secondary"}
              size="md"
              weight="bold"
              style={styles.tabText}
            >
              سبائك عيار 24
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.tabButton,
              selectedType === "عملة" && styles.activeTabButton,
            ]}
            onPress={() => handleTypeChange("عملة")}
          >
            <ThemedText
              variant={selectedType === "عملة" ? "inverse" : "secondary"}
              size="md"
              weight="bold"
              style={styles.tabText}
            >
              عملات عيار 21
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.scrollContent,
          {
            paddingBottom: Math.max(insets.bottom + 80, 100),
          },
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
        onScrollBeginDrag={closeAllDropdowns}
      >
        <View style={styles.scrollContentWrapper}>
          {/* كارت الاختيار الجديد */}
          <ThemedCard style={styles.selectionCard}>
            <View style={styles.selectionContent}>
              {/* Header الكارت */}
              <View style={styles.cardHeader}>
                <MaterialIcons
                  name="settings"
                  size={24}
                  color={colors.primary}
                />
                <ThemedText variant="primary" size="lg" weight="bold">
                  اختيار المنتج
                </ThemedText>
              </View>

              {/* قسم اختيار الشركة مع اللوجو */}
              <View style={styles.selectionSection}>
                <View style={styles.sectionHeaderWithLogo}>
                  <ThemedText
                    align="left"
                    variant="primary"
                    size="md"
                    weight="medium"
                    style={styles.sectionLabel}
                  >
                    الشركة المصنعة
                  </ThemedText>

                  {/* اللوجو - افتراضي أو للشركة المختارة */}
                  <View style={styles.logoContainer}>
                    {selectedCompanyId &&
                    companies.find((c) => c.id === selectedCompanyId)?.logo ? (
                      <Image
                        source={{
                          uri: companies.find((c) => c.id === selectedCompanyId)
                            ?.logo,
                        }}
                        style={styles.companyLogoMain}
                        resizeMode="contain"
                      />
                    ) : (
                      <Image
                        source={require("../assets/icon.png")} // لوجو التطبيق الافتراضي
                        style={styles.companyLogoMain}
                        resizeMode="contain"
                      />
                    )}
                  </View>
                </View>

                <TouchableOpacity
                  style={[
                    styles.modernDropdown,
                    { borderColor: colors.border },
                    showCompanyDropdown && { borderColor: colors.primary },
                  ]}
                  onPress={() => setShowCompanyDropdown(!showCompanyDropdown)}
                >
                  <View style={styles.dropdownHeader}>
                    <View style={styles.dropdownTextContainer}>
                      <ThemedText
                        align="center"
                        variant={selectedCompanyId ? "primary" : "secondary"}
                        size="md"
                      >
                        {selectedCompanyId
                          ? companies.find((c) => c.id === selectedCompanyId)
                              ?.name
                          : "اختر الشركة المصنعة"}
                      </ThemedText>
                    </View>
                    <MaterialIcons
                      name={showCompanyDropdown ? "expand-less" : "expand-more"}
                      size={24}
                      color={colors.primary}
                    />
                  </View>
                </TouchableOpacity>

                {/* قائمة الشركات */}
                {showCompanyDropdown && (
                  <View
                    style={[
                      styles.modernDropdownMenu,
                      {
                        backgroundColor: colors.card,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <ScrollView
                      style={styles.dropdownScrollView}
                      nestedScrollEnabled={true}
                      showsVerticalScrollIndicator={false}
                    >
                      {companies.length > 0 ? (
                        companies.map((company, index) => (
                          <TouchableOpacity
                            key={company.id}
                            style={[
                              styles.modernDropdownItem,
                              selectedCompanyId === company.id && {
                                backgroundColor: colors.primary + "20",
                              },
                              index === companies.length - 1 &&
                                styles.lastDropdownItem,
                            ]}
                            onPress={() => {
                              handleCompanyChange(company.id);
                              setShowCompanyDropdown(false);
                            }}
                          >
                            <ThemedText
                              variant={
                                selectedCompanyId === company.id
                                  ? "primary"
                                  : "secondary"
                              }
                              size="md"
                              weight={
                                selectedCompanyId === company.id
                                  ? "medium"
                                  : "normal"
                              }
                            >
                              {company.name}
                            </ThemedText>
                            {selectedCompanyId === company.id && (
                              <MaterialIcons
                                name="check"
                                size={20}
                                color={colors.primary}
                              />
                            )}
                          </TouchableOpacity>
                        ))
                      ) : (
                        <View style={styles.modernDropdownItem}>
                          <ThemedText variant="secondary" size="md">
                            لا توجد شركات متاحة
                          </ThemedText>
                        </View>
                      )}
                    </ScrollView>
                  </View>
                )}
              </View>

              {/* قسم اختيار المنتج */}
              <View style={styles.selectionSection}>
                <ThemedText
                  align="left"
                  variant="primary"
                  size="md"
                  weight="medium"
                  style={styles.sectionLabel}
                >
                  نوع المنتج
                </ThemedText>

                <TouchableOpacity
                  style={[
                    styles.modernDropdown,
                    { borderColor: colors.border },
                    !selectedCompanyId && styles.disabledDropdown,
                    showProductDropdown && { borderColor: colors.primary },
                  ]}
                  onPress={() =>
                    selectedCompanyId &&
                    setShowProductDropdown(!showProductDropdown)
                  }
                  disabled={!selectedCompanyId}
                >
                  <View style={styles.dropdownHeader}>
                    <View style={styles.dropdownTextContainer}>
                      <ThemedText
                        align="center"
                        variant={
                          selectedProductTypeId ? "primary" : "secondary"
                        }
                        size="md"
                        style={!selectedCompanyId && styles.disabledText}
                      >
                        {selectedProductTypeId
                          ? productTypes.find(
                              (p) => p.id === selectedProductTypeId
                            )?.name
                          : selectedCompanyId
                          ? "اختر نوع المنتج"
                          : "اختر الشركة أولاً"}
                      </ThemedText>
                    </View>
                    <MaterialIcons
                      name={showProductDropdown ? "expand-less" : "expand-more"}
                      size={24}
                      color={
                        selectedCompanyId
                          ? colors.primary
                          : colors.textSecondary
                      }
                    />
                  </View>
                </TouchableOpacity>

                {/* قائمة المنتجات */}
                {showProductDropdown && selectedCompanyId && (
                  <View
                    style={[
                      styles.modernDropdownMenu,
                      {
                        backgroundColor: colors.card,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <ScrollView
                      style={styles.dropdownScrollView}
                      nestedScrollEnabled={true}
                      showsVerticalScrollIndicator={false}
                    >
                      {productTypes.length > 0 ? (
                        productTypes.map((productType, index) => (
                          <TouchableOpacity
                            key={productType.id}
                            style={[
                              styles.modernDropdownItem,
                              selectedProductTypeId === productType.id && {
                                backgroundColor: colors.primary + "20",
                              },
                              index === productTypes.length - 1 &&
                                styles.lastDropdownItem,
                            ]}
                            onPress={() => {
                              handleProductTypeChange(productType.id);
                              setShowProductDropdown(false);
                            }}
                          >
                            <ThemedText
                              variant={
                                selectedProductTypeId === productType.id
                                  ? "primary"
                                  : "secondary"
                              }
                              size="md"
                              weight={
                                selectedProductTypeId === productType.id
                                  ? "medium"
                                  : "normal"
                              }
                            >
                              {productType.name}
                            </ThemedText>
                            {selectedProductTypeId === productType.id && (
                              <MaterialIcons
                                name="check"
                                size={20}
                                color={colors.primary}
                              />
                            )}
                          </TouchableOpacity>
                        ))
                      ) : (
                        <View style={styles.modernDropdownItem}>
                          <ThemedText variant="secondary" size="md">
                            لا توجد منتجات متاحة
                          </ThemedText>
                        </View>
                      )}
                    </ScrollView>
                  </View>
                )}
              </View>
            </View>
          </ThemedCard>

          {/* جدول الأسعار مثل الموقع */}
          {calculatedPrices && (
            <ThemedCard style={styles.pricesCard}>
              {/* Header جدول الأسعار */}
              <View
                style={[
                  styles.modernPricesHeader,
                  { backgroundColor: colors.primary },
                ]}
              >
                <MaterialIcons name="receipt" size={24} color="white" />
                <ThemedText variant="inverse" size="lg" weight="bold">
                  تفصيل الأسعار بالجنيه المصري
                </ThemedText>
              </View>

              <View style={styles.modernPricesContent}>
                {/* ملحوظة هامة مثل الويب */}
                {/* <View
                    style={[
                      styles.noteCard,
                      {
                        backgroundColor: colors.surface,
                        borderColor: colors.border,
                      },
                    ]}
                  >
                    <ThemedText
                      variant="secondary"
                      size="sm"
                      style={styles.noteText}
                    >
                      <ThemedText
                        align="center"
                        variant="primary"
                        size="sm"
                        weight="bold"
                      >
                        ملحوظة هامة:{" "}
                      </ThemedText>
                      أسعار المصنعيات بالأدنى حسب آخر تحديث معلن من الشركة
                      المنتجة. سعر جرام الذهب محدث حسب آخر سعر للسوق الصاغة،
                      وليس مستمد من الشركة المنتجة نفسها وغير ملزم لها.
                    </ThemedText>
                  </View> */}
                {/* ملحوظة هامة */}
                <View
                  style={[
                    styles.importantNote,
                    {
                      backgroundColor: colors.warning + "10",
                      borderColor: colors.warning,
                    },
                  ]}
                >
                  <MaterialIcons name="info" size={20} color={colors.warning} />
                  <ThemedText
                    variant="secondary"
                    size="sm"
                    style={styles.noteText}
                  >
                    ملحوظة هامة: أسعار المصنعيات بالأدنى حسب آخر تحديث معلن من
                    الشركة المنتجة. سعر جرام الذهب محدث حسب آخر سعر بسوق الصاغة.
                  </ThemedText>
                </View>
                {/* جدول الأسعار مثل الموقع */}
                <View style={styles.priceTable}>
                  {/* سعر الجرام عيار 24 */}
                  <View
                    style={[
                      styles.priceTableRow,
                      { backgroundColor: colors.surface },
                    ]}
                  >
                    <ThemedText
                      align="left"
                      variant="primary"
                      size="md"
                      weight="medium"
                      style={styles.priceLabel}
                    >
                      سعر الجرام عيار{" "}
                      {calculatedPrices?.purity ||
                        (selectedType === "سبيكة" ? "24" : "21")}
                    </ThemedText>
                    <ThemedText
                      align="right"
                      variant="primary"
                      size="lg"
                      weight="bold"
                      style={styles.priceValue}
                    >
                      {calculatedPrices?.raw_gold_price_per_gram || "0"} جنيه
                    </ThemedText>
                  </View>

                  {/* مصنعية الجرام */}
                  <View
                    style={[
                      styles.priceTableRow,
                      { backgroundColor: colors.background },
                    ]}
                  >
                    <ThemedText
                      variant="primary"
                      size="md"
                      weight="medium"
                      style={styles.priceLabel}
                    >
                      مصنعية الجرام
                    </ThemedText>
                    <ThemedText
                      variant="primary"
                      size="lg"
                      weight="bold"
                      style={styles.priceValue}
                    >
                      {calculatedPrices?.manufacturing_per_gram || "0.0"} جنيه
                    </ThemedText>
                  </View>

                  {/* السعر شامل المصنعية والضريبة */}
                  <View
                    style={[
                      styles.priceTableRow,
                      { backgroundColor: colors.surface },
                    ]}
                  >
                    <ThemedText
                      variant="primary"
                      size="md"
                      weight="medium"
                      style={styles.priceLabel}
                    >
                      السعر شامل المصنعية والضريبة
                    </ThemedText>
                    <ThemedText
                      variant="primary"
                      size="lg"
                      weight="bold"
                      style={styles.priceValue}
                    >
                      {calculatedPrices?.total_price || "0"} جنيه
                    </ThemedText>
                  </View>

                  {/* قيمة الاسترداد للجرام */}
                  <View
                    style={[
                      styles.priceTableRow,
                      { backgroundColor: colors.background },
                    ]}
                  >
                    <ThemedText
                      variant="primary"
                      size="md"
                      weight="medium"
                      style={styles.priceLabel}
                    >
                      قيمة الاسترداد للجرام
                    </ThemedText>
                    <ThemedText
                      variant="primary"
                      size="lg"
                      weight="bold"
                      style={styles.priceValue}
                    >
                      {calculatedPrices?.refund_per_gram || "0.0"} جنيه
                    </ThemedText>
                  </View>

                  {/* سعر إعادة البيع */}
                  <View
                    style={[
                      styles.priceTableRow,
                      styles.refundRow,
                      { backgroundColor: colors.success + "10" },
                    ]}
                  >
                    <ThemedText
                      variant="primary"
                      size="md"
                      weight="bold"
                      style={styles.priceLabel}
                    >
                      سعر إعادة البيع
                    </ThemedText>
                    <ThemedText
                      variant="primary"
                      size="xl"
                      weight="bold"
                      style={[styles.priceValue, { color: colors.success }]}
                    >
                      {calculatedPrices?.resale_price || "0"} جنيه
                    </ThemedText>
                  </View>
                </View>
              </View>
            </ThemedCard>
          )}

          {/* رسالة عدم وجود بيانات */}
          {!selectedProduct && (
            <ThemedCard style={styles.emptyCard}>
              <View style={styles.modernEmptyContent}>
                <View
                  style={[
                    styles.emptyIconContainer,
                    { backgroundColor: colors.primary + "10" },
                  ]}
                >
                  <MaterialIcons
                    name="inventory"
                    size={48}
                    color={colors.primary}
                  />
                </View>
                <ThemedText
                  variant="primary"
                  size="lg"
                  weight="bold"
                  style={styles.emptyTitle}
                >
                  اختر المنتج لعرض الأسعار
                </ThemedText>
                <ThemedText
                  variant="secondary"
                  size="md"
                  style={styles.emptyMessage}
                >
                  قم بتحديد الشركة المصنعة ونوع المنتج لعرض تفاصيل الأسعار
                  والحسابات
                </ThemedText>

                {/* خطوات الاستخدام */}
                <View style={styles.stepsContainer}>
                  <View style={styles.stepItem}>
                    <View
                      style={[
                        styles.stepNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <ThemedText variant="inverse" size="sm" weight="bold">
                        1
                      </ThemedText>
                    </View>
                    <ThemedText variant="secondary" size="sm">
                      اختر نوع المنتج من التبويبات أعلاه
                    </ThemedText>
                  </View>

                  <View style={styles.stepItem}>
                    <View
                      style={[
                        styles.stepNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <ThemedText variant="inverse" size="sm" weight="bold">
                        2
                      </ThemedText>
                    </View>
                    <ThemedText variant="secondary" size="sm">
                      حدد الشركة المصنعة من القائمة
                    </ThemedText>
                  </View>

                  <View style={styles.stepItem}>
                    <View
                      style={[
                        styles.stepNumber,
                        { backgroundColor: colors.primary },
                      ]}
                    >
                      <ThemedText variant="inverse" size="sm" weight="bold">
                        3
                      </ThemedText>
                    </View>
                    <ThemedText variant="secondary" size="sm">
                      اختر المنتج المطلوب لعرض الأسعار
                    </ThemedText>
                  </View>
                </View>
              </View>
            </ThemedCard>
          )}
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (colors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    // Tabs Styles
    tabsContainer: {
      paddingHorizontal: 16,
      paddingVertical: 16,
    },
    tabsWrapper: {
      flexDirection: "row",
      backgroundColor: "#374151",
      borderRadius: 25,
      padding: 4,
    },
    tabButton: {
      flex: 1,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 20,
      alignItems: "center",
    },
    activeTabButton: {
      backgroundColor: "#F59E0B",
      shadowColor: "#F59E0B",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 4,
    },
    tabText: {
      textAlign: "center",
    },
    // Scroll Styles
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: 16,
    },
    scrollContentWrapper: {
      flex: 1,
      overflow: "visible", // مهم لظهور الدروب داون
    },
    // Selection Card Styles
    selectionCard: {
      marginBottom: 16,
      borderRadius: 16,
      overflow: "visible", // تغيير لإظهار الدروب داون
    },
    selectionContent: {
      padding: 12, // تقليل من 20 إلى 12
    },
    cardHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 12, // تقليل من 20 إلى 12
      gap: 8, // تقليل من 10 إلى 8
    },
    selectionSection: {
      marginBottom: 12, // تقليل من 20 إلى 12
    },
    sectionLabel: {
      marginBottom: 8,
    },
    // Header مع اللوجو
    sectionHeaderWithLogo: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 6, // تقليل من 8 إلى 6
    },
    logoContainer: {
      width: 50, // تقليل من 50 إلى 40
      height: 50, // تقليل من 50 إلى 40
      borderRadius: 25, // تقليل من 25 إلى 20
      backgroundColor: colors.card,
      borderWidth: 2, // تقليل من 2 إلى 1.5
      borderColor: colors.border,
      justifyContent: "center",
      alignItems: "center",
      overflow: "hidden",
    },
    companyLogoMain: {
      width: 50, // تقليل من 40 إلى 32
      height: 50, // تقليل من 40 إلى 32
      borderRadius: 20, // تقليل من 20 إلى 16
    },
    // Modern Dropdown Styles
    modernDropdown: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      paddingHorizontal: 12, // تقليل من 16 إلى 12
      paddingVertical: 8, // تقليل من 12 إلى 8
      borderWidth: 2,
      borderColor: colors.border,
      minHeight: 40, // تقليل من 48 إلى 40
    },
    dropdownHeader: {
      flexDirection: "row-reverse",
      justifyContent: "space-between",
      alignItems: "center",
    },
    dropdownTextContainer: {
      flex: 1,
    },
    disabledDropdown: {
      backgroundColor: colors.surface + "60",
      opacity: 0.6,
    },
    disabledText: {
      opacity: 0.5,
    },
    modernDropdownMenu: {
      marginTop: 8,
      borderRadius: 12,
      borderWidth: 1,
      maxHeight: 200,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 5,
    },
    dropdownScrollView: {
      maxHeight: 200,
    },
    modernDropdownItem: {
      paddingHorizontal: 12, // تقليل من 16 إلى 12
      paddingVertical: 8, // تقليل من 12 إلى 8
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },
    lastDropdownItem: {
      borderBottomWidth: 0,
    },
    // Company Info Styles
    companyInfoSection: {
      padding: 16,
      borderRadius: 12,
      marginBottom: 16,
    },
    logoAndInfo: {
      flexDirection: "row",
      alignItems: "center",
      gap: 15,
    },
    modernLogoContainer: {
      width: 60,
      height: 60,
      borderRadius: 12,
      padding: 8,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
    },
    logoImage: {
      width: "100%",
      height: "100%",
      borderRadius: 8,
    },
    companyInfo: {
      flex: 1,
    },
    // Important Note Styles
    modernImportantNote: {
      flexDirection: "row",
      alignItems: "flex-start",
      gap: 10,
      padding: 12,
      borderRadius: 8,
      borderLeftWidth: 4,
    },
    noteText: {
      flex: 1,
      lineHeight: 18,
    },
    // Prices Card Styles
    pricesCard: {
      marginBottom: 16,
      borderRadius: 12,
      overflow: "hidden",
    },
    modernPricesHeader: {
      flexDirection: "row",
      alignItems: "center",
      gap: 10,
      paddingVertical: 16,
      paddingHorizontal: 20,
    },
    modernPricesContent: {
      backgroundColor: colors.surface,
    },
    // ملحوظة هامة مثل الويب
    noteCard: {
      margin: 16,
      padding: 16,
      borderRadius: 12,
      borderWidth: 1,
    },
    noteText: {
      lineHeight: 20,
      textAlign: "left",
    },
    // جدول الأسعار الجديد مثل الموقع
    priceTable: {
      marginTop: 0,
    },
    priceTableRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: 16,
      paddingHorizontal: 20,
      borderBottomWidth: 1,
      borderBottomColor: colors.border + "30",
    },
    priceLabel: {
      flex: 1,
      textAlign: "left",
    },
    priceValue: {
      textAlign: "right",
      minWidth: 120,
    },
    refundRow: {
      borderBottomWidth: 0,
      borderTopWidth: 2,
      borderTopColor: colors.success,
    },
    // ملحوظة هامة
    importantNote: {
      flexDirection: "row",
      alignItems: "flex-start",
      gap: 10,
      padding: 16,
      margin: 16,
      borderRadius: 8,
      borderLeftWidth: 4,
    },
    noteText: {
      flex: 1,
      lineHeight: 18,
    },
    // Empty State Styles
    emptyCard: {
      marginTop: 20,
      padding: 24,
      alignItems: "center",
      backgroundColor: colors.surface,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: colors.border,
    },
    modernEmptyContent: {
      alignItems: "center",
      maxWidth: 300,
    },
    emptyIconContainer: {
      padding: 20,
      borderRadius: 50,
      marginBottom: 16,
    },
    emptyTitle: {
      marginBottom: 8,
      textAlign: "center",
    },
    emptyMessage: {
      textAlign: "center",
      marginBottom: 20,
      lineHeight: 22,
    },
    stepsContainer: {
      alignSelf: "stretch",
      gap: 12,
    },
    stepItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: 8,
    },
    stepNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      justifyContent: "center",
      alignItems: "center",
      marginRight: 12,
    },
    // Error Styles
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: 32,
    },
    errorTitle: {
      marginTop: 16,
      marginBottom: 8,
      textAlign: "center",
    },
    errorMessage: {
      textAlign: "center",
      marginBottom: 24,
    },
    retryButton: {
      paddingHorizontal: 24,
      paddingVertical: 12,
      borderRadius: 8,
    },
  });

export default BarsCoinsScreen;
