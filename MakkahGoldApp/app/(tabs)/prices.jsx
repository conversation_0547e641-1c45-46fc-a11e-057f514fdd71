// صفحة أسعار المعادن - Prices Screen
import React, { useState } from "react";
import {
  View,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  I18nManager,
  Platform,
  Dimensions,
} from "react-native";
import { router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";
import { SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { useCurrentPrices } from "../../src/utils/hooks";
import {
  ThemedStatusBar,
  ThemedText,
  ThemedCard,
  Loading,
} from "../../src/components";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

// الحصول على أبعاد الشاشة للتصميم المتجاوب
const { width: screenWidth } = Dimensions.get("window");

// تحديد نوع الجهاز بناءً على عرض الشاشة
const isTablet = screenWidth >= 768;

const PricesScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMetal, setSelectedMetal] = useState("gold"); // الذهب كافتراضي

  // استخدام البيانات الحقيقية من API
  const { data: prices, loading, error, refetch } = useCurrentPrices();

  // المسافات المتجاوبة
  const responsiveSpacing = isTablet ? SPACING.xl : SPACING.sm;

  // إنشاء الـ styles داخل المكون للوصول للمتغيرات المتجاوبة
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },

    // Enhanced Header Styles - محسن للمنصتين مع ضبط دقيق للأبعاد
    headerGradient: {
      paddingHorizontal: responsiveSpacing,
      paddingBottom: SPACING.sm, // تقليل أكثر للمساحة
      paddingTop: Platform.select({
        ios: Math.max(insets.top, 44) + SPACING.sm, // تقليل أكثر
        android: Math.max(insets.top, 24) + SPACING.xs, // تقليل أكثر
      }),
      // ضبط الظلال حسب المنصة
      elevation: Platform.OS === "android" ? 8 : 0,
      shadowColor: Platform.select({
        ios: "rgba(0, 0, 0, 0.3)",
        android: "#000",
      }),
      shadowOffset: Platform.select({
        ios: { width: 0, height: 4 },
        android: { width: 0, height: 2 },
      }),
      shadowOpacity: Platform.select({
        ios: 0.3,
        android: 0.2,
      }),
      shadowRadius: Platform.select({
        ios: 8,
        android: 4,
      }),
      // ضمان عدم تداخل المحتوى
      zIndex: Platform.OS === "android" ? 1000 : 1,
    },
    headerContent: {
      justifyContent: "center",
      minHeight: Platform.select({
        ios: isTablet ? 50 : 30, // تقليل كبير في الارتفاع
        android: isTablet ? 48 : 28, // تقليل كبير في الارتفاع
      }),
      paddingVertical: SPACING.xs,
    },
    // صفوف الهيدر المدمج
    headerTopRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
      marginBottom: SPACING.xs,
    },
    headerTitleSection: {
      flexDirection: "row",
      alignItems: "center",
      // flex: 1,
    },
    headerBottomRow: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      width: "100%",
    },
    headerTitle: {
      textAlign: "center", // تغيير للتخطيط الأفقي
      marginBottom: 0, // إزالة المسافة السفلية
      //   marginLeft: SPACING.sm,
      fontSize: Platform.select({
        ios: isTablet ? 22 : 18, // تقليل حجم الخط
        android: isTablet ? 20 : 16, // تقليل حجم الخط
      }),
      fontWeight: Platform.select({
        ios: "700",
        android: "bold",
      }),
      letterSpacing: Platform.select({
        ios: 0.5,
        android: 0.3,
      }),
    },
    headerSubtitle: {
      textAlign: "center",
      fontSize: Platform.select({
        ios: isTablet ? 17 : 15,
        android: isTablet ? 16 : 14,
      }),
      opacity: 0.9,
      fontWeight: Platform.select({
        ios: "500",
        android: "normal",
      }),
    },
    headerIcon: {
      marginBottom: SPACING.xs, // تقليل من sm إلى xs
      // إضافة تأثير بصري للأيقونة
      shadowColor: "rgba(0, 0, 0, 0.3)",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.3,
      shadowRadius: 4,
    },
    iconContainer: {
      position: "relative",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: 0, // إزالة المسافة السفلية
    },
    liveIndicatorHeader: {
      position: "absolute",
      top: -2,
      right: -2,
      width: 10, // تقليل الحجم
      height: 10, // تقليل الحجم
      borderRadius: 5,
      borderWidth: 2,
      borderColor: "rgba(255, 255, 255, 0.8)",
    },
    // أزرار ومؤشرات الهيدر المدمج
    compactRefreshButton: {
      width: isTablet ? 36 : 32,
      height: isTablet ? 36 : 32,
      borderRadius: isTablet ? 18 : 16,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 1,
      borderColor: "rgba(255, 255, 255, 0.3)",
    },
    compactUpdateText: {
      opacity: 0.9,
      marginRight: SPACING.xs,
    },
    compactLiveIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      borderWidth: 1,
      borderColor: "rgba(255, 255, 255, 0.8)",
    },

    // Error Styles
    errorContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: responsiveSpacing,
    },
    errorTitle: {
      marginTop: SPACING.md,
      marginBottom: SPACING.sm,
      textAlign: "center",
    },
    errorMessage: {
      textAlign: "center",
      marginBottom: SPACING.lg,
      lineHeight: 24,
    },
    retryButton: {
      paddingHorizontal: SPACING.xl,
      paddingVertical: SPACING.md,
      borderRadius: isTablet ? 16 : 12,
      elevation: Platform.OS === "android" ? 3 : 0,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },

    // Update Info - Modern Arabic Design
    updateInfo: {
      marginHorizontal: responsiveSpacing,
      marginTop: SPACING.sm,
      marginBottom: SPACING.md,
      borderRadius: isTablet ? 24 : 20,
      elevation: Platform.OS === "android" ? 8 : 0,
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 12,
      overflow: "hidden",
    },
    updateContent: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: isTablet ? SPACING.xl : SPACING.lg,
      paddingVertical: isTablet ? SPACING.lg : SPACING.md,
      gap: SPACING.md,
    },
    updateLeftSection: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
      gap: SPACING.sm,
    },
    updateIconContainer: {
      width: isTablet ? 48 : 40,
      height: isTablet ? 48 : 40,
      borderRadius: isTablet ? 24 : 20,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "rgba(212, 175, 55, 0.15)",
    },
    updateTextContainer: {
      flex: 1,
      paddingRight: SPACING.sm,
    },
    updateMainText: {
      textAlign: "right",
      marginBottom: 2,
      fontSize: isTablet ? 16 : 14,
      fontWeight: "600",
    },
    updateSubtext: {
      textAlign: "right",
      opacity: 0.75,
      fontSize: isTablet ? 14 : 12,
    },
    refreshButton: {
      paddingHorizontal: isTablet ? SPACING.lg : SPACING.md,
      paddingVertical: isTablet ? SPACING.md : SPACING.sm,
      borderRadius: isTablet ? 20 : 16,
      elevation: Platform.OS === "android" ? 4 : 0,
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.25,
      shadowRadius: 6,
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.xs,
      minWidth: isTablet ? 120 : 100,
      justifyContent: "center",
    },
    refreshButtonText: {
      textAlign: "center",
      fontSize: isTablet ? 14 : 12,
      fontWeight: "600",
    },
    liveIndicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: "#4CAF50",
      marginLeft: SPACING.xs,
    },
    refreshIcon: {
      transform: [{ rotate: "0deg" }],
    },
    refreshIconActive: {
      transform: [{ rotate: "360deg" }],
    },

    // Filter Styles
    filterContainer: {
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: "rgba(212, 175, 55, 0.2)",
    },
    filterContent: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: responsiveSpacing,
      gap: SPACING.md,
    },
    filterButton: {
      flex: 1,
      maxWidth: isTablet ? 200 : 150,
      paddingHorizontal: isTablet ? SPACING.lg : SPACING.md,
      paddingVertical: isTablet ? SPACING.md : SPACING.sm,
      borderRadius: isTablet ? 25 : 20,
      borderWidth: 2,
      elevation: Platform.OS === "android" ? 2 : 0,
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.2,
      shadowRadius: 3,
    },
    filterButtonText: {
      textAlign: "center",
    },

    scrollView: {
      flex: 1,
    },

    // Statistics Styles
    statisticsContainer: {
      paddingHorizontal: responsiveSpacing,
      paddingVertical: SPACING.md,
    },
    statisticsCard: {
      marginBottom: SPACING.md,
      borderRadius: isTablet ? 16 : 12,
      elevation: Platform.OS === "android" ? 3 : 0,
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
    },
    statisticsHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: SPACING.md,
      gap: SPACING.sm,
    },
    statisticsContent: {
      gap: SPACING.sm,
    },
    statisticsRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
    },

    // Metal Card Styles
    metalCard: {
      marginHorizontal: responsiveSpacing,
      marginBottom: SPACING.lg,
      borderRadius: isTablet ? 20 : 16,
      elevation: Platform.OS === "android" ? 6 : 0,
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.2,
      shadowRadius: 6,
    },
    metalHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "flex-start",
      marginBottom: SPACING.lg,
      paddingBottom: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: "rgba(212, 175, 55, 0.2)",
    },
    metalTitleContainer: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
      gap: SPACING.sm,
    },
    metalName: {
      flex: 1,
      textAlign: "left",
    },
    historyButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: isTablet ? 12 : 8,
      gap: SPACING.xs,
      elevation: Platform.OS === "android" ? 2 : 0,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
    },
    historyButtonText: {
      textAlign: "right",
    },

    // Compact Color Legend Styles - مفتاح ألوان مضغوط
    colorLegend: {
      flexDirection: "row",
      justifyContent: "center",
      alignItems: "center",
      gap: SPACING.sm,
      marginBottom: SPACING.md,
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.md,
      borderRadius: isTablet ? 12 : 10,
      borderWidth: colors.isDark ? 0 : 1,
      borderColor: colors.border + "30",
      elevation: Platform.OS === "android" ? (colors.isDark ? 1 : 0) : 0,
      shadowColor: colors.isDark ? colors.surface : colors.primary,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: colors.isDark ? 0.1 : 0.05,
      shadowRadius: 2,
    },
    legendItem: {
      flexDirection: "row",
      alignItems: "center",
      gap: SPACING.xs / 2,
    },
    legendIcon: {
      width: isTablet ? 20 : 18,
      height: isTablet ? 20 : 18,
      borderRadius: isTablet ? 10 : 9,
      alignItems: "center",
      justifyContent: "center",
    },
    legendSeparator: {
      width: 1,
      height: isTablet ? 16 : 14,
      backgroundColor: colors.border,
      opacity: colors.isDark ? 0.2 : 0.4,
    },

    // Compact Table Styles - تصميم مضغوط ومناسب للعين
    pricesTable: {
      borderRadius: isTablet ? 12 : 10,
      overflow: "hidden",
      borderWidth: colors.isDark ? 0 : 1,
      borderColor: colors.border,
      backgroundColor: colors.surface,
      elevation: Platform.OS === "android" ? 2 : 0,
      shadowColor: colors.isDark ? colors.surface : colors.primary,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: colors.isDark ? 0.2 : 0.08,
      shadowRadius: 4,
    },
    tableHeader: {
      flexDirection: "row",
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.primary + "20",
      minHeight: isTablet ? 45 : 40,
    },
    tableHeaderCell: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      gap: SPACING.xs / 2,
      paddingHorizontal: SPACING.xs,
    },
    tableHeaderText: {
      textAlign: "center",
      fontSize: isTablet ? 12 : 10,
      fontWeight: "bold",
    },

    // Compact Row Styles
    compactTableRow: {
      flexDirection: "row",
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      borderBottomWidth: 1,
      alignItems: "center",
      minHeight: isTablet ? 55 : 50,
    },
    lastTableRow: {
      borderBottomWidth: 0,
    },

    // Compact Cell Styles
    compactTableCell: {
      justifyContent: "center",
      paddingHorizontal: SPACING.xs / 2,
    },
    compactPurityText: {
      textAlign: "center",
      fontSize: isTablet ? 14 : 12,
      fontWeight: "700",
      lineHeight: isTablet ? 18 : 16,
    },

    // Compact Price Styles
    compactPriceContainer: {
      alignItems: "center",
      justifyContent: "center",
    },
    compactPriceText: {
      textAlign: "center",
      fontSize: isTablet ? 13 : 11,
      fontWeight: "bold",
      lineHeight: isTablet ? 16 : 14,
    },
    compactPriceUnit: {
      textAlign: "center",
      fontSize: isTablet ? 9 : 8,
      opacity: 0.7,
      marginTop: 1,
    },

    // Compact Change Styles
    compactChangeChip: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      gap: 2,
      paddingHorizontal: SPACING.xs,
      paddingVertical: 3,
      borderRadius: 8,
      borderWidth: 0.5,
      minWidth: isTablet ? 50 : 45,
    },
    compactChangeText: {
      textAlign: "center",
      fontSize: isTablet ? 9 : 8,
      fontWeight: "600",
      lineHeight: isTablet ? 11 : 10,
    },

    // Calculator Section Styles
    calculatorsSection: {
      marginHorizontal: responsiveSpacing,
      marginBottom: SPACING.lg,
      borderRadius: isTablet ? 20 : 16,
      elevation: Platform.OS === "android" ? 6 : 0,
      shadowColor: "#D4AF37",
      shadowOffset: { width: 0, height: 3 },
      shadowOpacity: 0.2,
      shadowRadius: 6,
    },
    sectionTitle: {
      marginBottom: SPACING.lg,
      textAlign: "right",
    },
    calculatorCard: {
      flexDirection: "row",
      alignItems: "center",
      padding: SPACING.lg,
      borderRadius: isTablet ? 16 : 12,
      marginBottom: SPACING.md,
      elevation: Platform.OS === "android" ? 2 : 0,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      gap: SPACING.md,
    },
    calculatorInfo: {
      flex: 1,
    },
    calculatorTitle: {
      marginBottom: SPACING.xs,
      textAlign: "right",
    },
    calculatorSubtitle: {
      textAlign: "right",
      lineHeight: 22,
    },

    // Disclaimer Section Styles
    disclaimerSection: {
      marginHorizontal: responsiveSpacing,
      marginBottom: SPACING.xl,
      borderRadius: isTablet ? 20 : 16,
      elevation: Platform.OS === "android" ? 4 : 0,
      shadowColor: "#FF9800",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
    },
    disclaimerHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: SPACING.md,
      gap: SPACING.sm,
    },
    disclaimerTitle: {
      textAlign: "right",
    },
    disclaimerText: {
      textAlign: "left",
      lineHeight: 24,
      marginBottom: SPACING.lg,
    },
  });

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  // دالة التحديث اليدوي
  const handleManualRefresh = React.useCallback(async () => {
    if (refreshing) return; // منع التحديث المتعدد
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch, refreshing]);

  // دالة للحصول على آخر تحديث من مصادر متعددة
  const getLastUpdated = () => {
    if (!filteredPrices || filteredPrices.length === 0) return null;

    const metalGroup = filteredPrices[0];
    if (!metalGroup || !metalGroup.prices || metalGroup.prices.length === 0)
      return null;

    // محاولة الحصول على آخر تحديث من أول سعر
    const firstPrice = metalGroup.prices[0];
    return (
      firstPrice.last_updated || firstPrice.updated_at || firstPrice.created_at
    );
  };

  const formatCurrency = (amount) => {
    return `${amount.toLocaleString("en-US")} ج.م`;
  };

  // دالة لتنسيق اسم العيار من "21 عيار" إلى "عيار 21"
  const formatPurityName = (purityName) => {
    if (!purityName) return "";

    // تحويل نمط "21 عيار" إلى "عيار 21"
    if (purityName.includes("عيار")) {
      const match = purityName.match(/(\d+)\s*عيار/);
      if (match) {
        return `عيار ${match[1]}`;
      }
    }

    // للفضة والمعادن الأخرى، الإبقاء على النمط الأصلي
    if (purityName.includes("فضة")) {
      return purityName;
    }

    // إذا كان النمط غير متوقع، إضافة "عيار" قبل الرقم
    const numberMatch = purityName.match(/(\d+)K?/);
    if (numberMatch) {
      return `عيار ${numberMatch[1]}`;
    }

    return purityName;
  };

  // تنسيق الوقت بالعربية مع الوقت النسبي
  const formatLastUpdated = (dateString) => {
    if (!dateString) return "تم التحديث مؤخراً";

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMinutes = Math.floor((now - date) / (1000 * 60));
      const diffInHours = Math.floor(diffInMinutes / 60);
      const diffInDays = Math.floor(diffInHours / 24);

      // تنسيق الوقت بالعربية
      const formattedTime = date.toLocaleTimeString("ar-EG", {
        hour12: true,
        hour: "2-digit",
        minute: "2-digit",
      });

      // تنسيق التاريخ بالعربية
      const formattedDate = date.toLocaleDateString("ar-EG", {
        year: "numeric",
        month: "long",
        day: "2-digit",
      });

      // حساب الوقت النسبي بالعربية
      let relativeTime = "";
      if (diffInMinutes < 1) {
        relativeTime = "الآن";
      } else if (diffInMinutes < 60) {
        relativeTime = `منذ ${diffInMinutes} دقيقة`;
      } else if (diffInHours < 24) {
        relativeTime = `منذ ${diffInHours} ساعة`;
      } else if (diffInDays < 7) {
        relativeTime = `منذ ${diffInDays} يوم`;
      } else {
        relativeTime = "منذ أكثر من أسبوع";
      }

      return {
        fullDate: `${formattedDate} الساعة ${formattedTime}`,
        relativeTime: relativeTime,
        shortTime: relativeTime,
      };
    } catch (error) {
      return {
        fullDate: "تم التحديث مؤخراً",
        relativeTime: "الآن",
        shortTime: "الآن",
      };
    }
  };

  // فلترة الأسعار حسب المعدن المحدد مع ترتيب العيارات تنازلياً
  const filteredPrices =
    selectedMetal && prices
      ? prices
          .filter((group) => group.metal_type === selectedMetal)
          .map((group) => ({
            ...group,
            prices: group.prices.sort(
              (a, b) => parseFloat(b.purity) - parseFloat(a.purity)
            ),
          }))
      : [];

  // عرض حالة التحميل
  if (loading) {
    return <Loading text="جاري تحميل أسعار المعادن..." />;
  }

  // عرض حالة الخطأ
  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedStatusBar />
        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <ThemedText variant="primary" size="lg" style={styles.errorTitle}>
            خطأ في تحميل البيانات
          </ThemedText>
          <ThemedText variant="secondary" size="md" style={styles.errorMessage}>
            {error}
          </ThemedText>
          <TouchableOpacity
            style={[styles.retryButton, { backgroundColor: colors.primary }]}
            onPress={refetch}
          >
            <ThemedText variant="inverse" size="md" weight="bold">
              إعادة المحاولة
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedStatusBar />

      {/* Enhanced Compact Header - هيدر مدمج ومضغوط مع معلومات التحديث */}
      <LinearGradient
        colors={[
          colors.primary,
          colors.primary,
          `rgba(${parseInt(colors.primary.slice(1, 3), 16)}, ${parseInt(
            colors.primary.slice(3, 5),
            16
          )}, ${parseInt(colors.primary.slice(5, 7), 16)}, 0.9)`,
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        style={[
          styles.headerGradient,
          {
            shadowColor: colors.isDark
              ? "rgba(255, 255, 255, 0.1)"
              : "rgba(0, 0, 0, 0.3)",
          },
        ]}
      >
        <View style={styles.headerContent}>
          {/* الصف الأول: العنوان مع زر التحديث */}
          <View style={styles.headerTopRow}>
            <View style={styles.headerTitleSection}>
              <View style={styles.iconContainer}>
                <MaterialIcons
                  name="trending-up"
                  size={isTablet ? 24 : 20} // تقليل أكثر للمساحة
                  color={colors.surface}
                  style={styles.headerIcon}
                />
                <View
                  style={[
                    styles.liveIndicatorHeader,
                    {
                      backgroundColor: refreshing ? "#FF9800" : "#4CAF50",
                    },
                  ]}
                />
              </View>
              <ThemedText
                variant="inverse"
                size={isTablet ? "lg" : "md"} // تقليل حجم الخط
                weight="bold"
                style={styles.headerTitle}
              >
                أسعار المعادن
              </ThemedText>
            </View>

            {/* زر التحديث مدمج في الهيدر */}
            <TouchableOpacity
              style={[
                styles.compactRefreshButton,
                {
                  backgroundColor: refreshing
                    ? "rgba(255, 255, 255, 0.1)"
                    : "rgba(255, 255, 255, 0.2)",
                  opacity: refreshing ? 0.8 : 1,
                },
              ]}
              onPress={handleManualRefresh}
              disabled={refreshing}
              activeOpacity={0.8}
            >
              <MaterialIcons
                name="refresh"
                size={isTablet ? 18 : 16}
                color={colors.surface}
                style={[
                  {
                    transform: [
                      {
                        rotate: refreshing ? "360deg" : "0deg",
                      },
                    ],
                  },
                ]}
              />
            </TouchableOpacity>
          </View>

          {/* الصف الثاني: معلومات التحديث مدمجة */}
          <View style={styles.headerBottomRow}>
            <ThemedText
              variant="inverse"
              size={isTablet ? "sm" : "xs"}
              style={styles.compactUpdateText}
            >
              آخر تحديث: {formatLastUpdated(getLastUpdated()).relativeTime}
            </ThemedText>
            <View
              style={[
                styles.compactLiveIndicator,
                {
                  backgroundColor: refreshing ? "#FF9800" : "#4CAF50",
                },
              ]}
            />
          </View>
        </View>
      </LinearGradient>

      {/* فلتر أنواع المعادن */}
      <View
        style={[styles.filterContainer, { backgroundColor: colors.surface }]}
      >
        <View style={styles.filterContent}>
          <TouchableOpacity
            style={[
              styles.filterButton,
              {
                backgroundColor:
                  selectedMetal === "gold" ? colors.primary : colors.background,
              },
              { borderColor: colors.primary },
            ]}
            onPress={() => setSelectedMetal("gold")}
          >
            <ThemedText
              variant={selectedMetal === "gold" ? "inverse" : "primary"}
              size="sm"
              weight="bold"
              style={styles.filterButtonText}
            >
              الذهب
            </ThemedText>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.filterButton,
              {
                backgroundColor:
                  selectedMetal === "silver"
                    ? colors.primary
                    : colors.background,
              },
              { borderColor: colors.primary },
            ]}
            onPress={() => setSelectedMetal("silver")}
          >
            <ThemedText
              variant={selectedMetal === "silver" ? "inverse" : "primary"}
              size="sm"
              weight="bold"
              style={styles.filterButtonText}
            >
              الفضة
            </ThemedText>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={{
          // مساحة إضافية في الأسفل لتجنب التداخل مع شريط التبويبات
          paddingBottom:
            Platform.OS === "android"
              ? Math.max(insets.bottom + 80, 100)
              : Math.max(insets.bottom + 60, 80),
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* بطاقات أسعار المعادن */}
        {filteredPrices.length === 0 ? (
          <ThemedCard
            style={[styles.metalCard, { backgroundColor: colors.surface }]}
          >
            <View style={styles.errorContainer}>
              <MaterialIcons
                name="info"
                size={48}
                color={colors.textSecondary}
              />
              <ThemedText
                variant="primary"
                size="md"
                weight="bold"
                style={styles.errorTitle}
              >
                لا توجد أسعار متاحة
              </ThemedText>
              <ThemedText
                variant="secondary"
                size="sm"
                style={styles.errorMessage}
              >
                لا توجد أسعار متاحة لهذا المعدن حالياً. يرجى المحاولة مرة أخرى
                لاحقاً.
              </ThemedText>
            </View>
          </ThemedCard>
        ) : (
          filteredPrices.map((metalGroup) => (
            <ThemedCard
              key={metalGroup.metal_type}
              style={[styles.metalCard, { backgroundColor: colors.surface }]}
            >
              <View style={styles.metalHeader}>
                <View style={styles.metalTitleContainer}>
                  <MaterialIcons
                    name={metalGroup.metal_type === "gold" ? "star" : "circle"}
                    size={24}
                    color={
                      metalGroup.metal_type === "gold"
                        ? colors.primary
                        : colors.textSecondary
                    }
                  />
                  <ThemedText
                    variant="primary"
                    size="lg"
                    weight="bold"
                    style={styles.metalName}
                  >
                    أسعار {metalGroup.metal_name} بالجنيه المصري
                  </ThemedText>
                </View>
              </View>

              {/* ملاحظة هامة حول خصم المحل */}
              <View
                style={{
                  paddingHorizontal: SPACING.md,
                  paddingVertical: SPACING.sm,
                  marginBottom: SPACING.xs,
                  marginHorizontal: SPACING.xs,
                  borderRadius: isTablet ? 12 : 10,
                  borderWidth: 1,
                  borderColor: colors.warning + "40",
                  backgroundColor: colors.warning + "10",
                  // إضافة تأثيرات الظل والارتفاع
                  //   elevation: Platform.OS === "android" ? 1 : 0,
                  shadowColor: colors.warning,
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.25,
                  shadowRadius: 4,
                  // تأثير التدرج الخفيف
                  borderStyle: "solid",
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: SPACING.xs,
                  }}
                >
                  {/* أيقونة تحذير متحركة */}
                  <MaterialIcons
                    name="info"
                    size={isTablet ? 16 : 14}
                    color={colors.warning}
                    style={{
                      opacity: 0.9,
                    }}
                  />
                  <ThemedText
                    variant="primary"
                    size="xs"
                    style={{
                      textAlign: "center",
                      lineHeight: isTablet ? 18 : 16,
                      fontWeight: "600",
                      color: colors.warning,
                      flex: 1,
                    }}
                  >
                    يخصم المحل 1% إلى 2% من سعر الشراء عند شراء المصوغات
                  </ThemedText>
                  {/* أيقونة ثانية للتوازن */}
                  <MaterialIcons
                    name="info"
                    size={isTablet ? 16 : 14}
                    color={colors.warning}
                    style={{
                      opacity: 0.9,
                    }}
                  />
                </View>
              </View>

              {/* مفتاح الألوان المضغوط */}
              <View
                style={[
                  styles.colorLegend,
                  { backgroundColor: colors.surface },
                ]}
              >
                <View style={styles.legendItem}>
                  <View
                    style={[
                      styles.legendIcon,
                      { backgroundColor: colors.success + "20" },
                    ]}
                  >
                    <MaterialIcons
                      name="sell"
                      size={10}
                      color={colors.success}
                    />
                  </View>
                  <ThemedText variant="secondary" size="xs" weight="medium">
                    سعر البيع
                  </ThemedText>
                </View>
                <View style={styles.legendSeparator} />
                <View style={styles.legendItem}>
                  <View
                    style={[
                      styles.legendIcon,
                      { backgroundColor: colors.error + "20" },
                    ]}
                  >
                    <MaterialIcons
                      name="shopping-cart"
                      size={10}
                      color={colors.error}
                    />
                  </View>
                  <ThemedText variant="secondary" size="xs" weight="medium">
                    سعر الشراء
                  </ThemedText>
                </View>
                <View style={styles.legendSeparator} />
                <View style={styles.legendItem}>
                  <View
                    style={[
                      styles.legendIcon,
                      { backgroundColor: colors.primary + "20" },
                    ]}
                  >
                    <MaterialIcons
                      name="trending-up"
                      size={10}
                      color={colors.primary}
                    />
                  </View>
                  <ThemedText variant="secondary" size="xs" weight="medium">
                    التغيير
                  </ThemedText>
                </View>
              </View>

              {/* جدول الأسعار المحسن - Compact Modern Design */}
              <View style={styles.pricesTable}>
                {/* رأس الجدول */}
                <View
                  style={[
                    styles.tableHeader,
                    { backgroundColor: colors.primary },
                  ]}
                >
                  <View style={[styles.tableHeaderCell, { flex: 1.5 }]}>
                    <ThemedText
                      variant="inverse"
                      size="sm"
                      weight="bold"
                      style={styles.tableHeaderText}
                    >
                      العيار
                    </ThemedText>
                  </View>
                  <View style={[styles.tableHeaderCell, { flex: 2 }]}>
                    <MaterialIcons
                      name="sell"
                      size={14}
                      color={colors.surface}
                    />
                    <ThemedText
                      variant="inverse"
                      size="xs"
                      weight="bold"
                      style={styles.tableHeaderText}
                    >
                      سعر البيع
                    </ThemedText>
                  </View>
                  <View style={[styles.tableHeaderCell, { flex: 2 }]}>
                    <MaterialIcons
                      name="shopping-cart"
                      size={14}
                      color={colors.surface}
                    />
                    <ThemedText
                      variant="inverse"
                      size="xs"
                      weight="bold"
                      style={styles.tableHeaderText}
                    >
                      سعر الشراء
                    </ThemedText>
                  </View>
                  <View style={[styles.tableHeaderCell, { flex: 1 }]}>
                    <MaterialIcons
                      name="trending-up"
                      size={14}
                      color={colors.surface}
                    />
                    <ThemedText
                      variant="inverse"
                      size="xs"
                      weight="bold"
                      style={styles.tableHeaderText}
                    >
                      التغيير
                    </ThemedText>
                  </View>
                </View>

                {/* صفوف الأسعار */}
                {metalGroup.prices.map((price, index) => (
                  <View
                    key={price.purity || index}
                    style={[
                      styles.compactTableRow,
                      {
                        backgroundColor:
                          index % 2 === 0 ? colors.background : colors.surface,
                        borderBottomColor: colors.border + "30",
                      },
                      index === metalGroup.prices.length - 1 &&
                        styles.lastTableRow,
                    ]}
                  >
                    {/* العيار */}
                    <View style={[styles.compactTableCell, { flex: 1.5 }]}>
                      <ThemedText
                        variant="primary"
                        size="sm"
                        weight="bold"
                        style={styles.compactPurityText}
                      >
                        {formatPurityName(price.purity_name)}
                      </ThemedText>
                    </View>

                    {/* سعر البيع */}
                    <View style={[styles.compactTableCell, { flex: 2 }]}>
                      <View style={styles.compactPriceContainer}>
                        <ThemedText
                          variant="primary"
                          size="sm"
                          weight="bold"
                          style={[
                            styles.compactPriceText,
                            { color: colors.success },
                          ]}
                        >
                          {formatCurrency(price.price_per_gram)}
                        </ThemedText>
                        <ThemedText
                          variant="secondary"
                          size="xs"
                          style={styles.compactPriceUnit}
                        >
                          للجرام
                        </ThemedText>
                      </View>
                    </View>

                    {/* سعر الشراء */}
                    <View style={[styles.compactTableCell, { flex: 2 }]}>
                      <View style={styles.compactPriceContainer}>
                        <ThemedText
                          variant="primary"
                          size="sm"
                          weight="bold"
                          style={[
                            styles.compactPriceText,
                            {
                              color: price.purchase_price_per_gram
                                ? colors.error
                                : colors.textSecondary,
                            },
                          ]}
                        >
                          {price.purchase_price_per_gram
                            ? formatCurrency(price.purchase_price_per_gram)
                            : "غير متاح"}
                        </ThemedText>
                        <ThemedText
                          variant="secondary"
                          size="xs"
                          style={styles.compactPriceUnit}
                        >
                          للجرام
                        </ThemedText>
                      </View>
                    </View>

                    {/* التغيير */}
                    <View style={[styles.compactTableCell, { flex: 1 }]}>
                      {price.price_change_percentage !== null ? (
                        <View
                          style={[
                            styles.compactChangeChip,
                            {
                              backgroundColor:
                                price.price_change_direction === "up"
                                  ? colors.success + "15"
                                  : price.price_change_direction === "down"
                                  ? colors.error + "15"
                                  : colors.textSecondary + "15",
                              borderColor:
                                price.price_change_direction === "up"
                                  ? colors.success
                                  : price.price_change_direction === "down"
                                  ? colors.error
                                  : colors.textSecondary,
                            },
                          ]}
                        >
                          <MaterialIcons
                            name={
                              price.price_change_direction === "up"
                                ? "arrow-upward"
                                : price.price_change_direction === "down"
                                ? "arrow-downward"
                                : "remove"
                            }
                            size={10}
                            color={
                              price.price_change_direction === "up"
                                ? colors.success
                                : price.price_change_direction === "down"
                                ? colors.error
                                : colors.textSecondary
                            }
                          />
                          <ThemedText
                            variant="secondary"
                            size="xs"
                            style={[
                              styles.compactChangeText,
                              {
                                color:
                                  price.price_change_direction === "up"
                                    ? colors.success
                                    : price.price_change_direction === "down"
                                    ? colors.error
                                    : colors.textSecondary,
                              },
                            ]}
                          >
                            {price.price_change_percentage > 0 ? "+" : ""}
                            {price.price_change_percentage?.toFixed(1) || "0.0"}
                            %
                          </ThemedText>
                        </View>
                      ) : (
                        <View
                          style={[
                            styles.compactChangeChip,
                            {
                              backgroundColor: colors.textSecondary + "10",
                              borderColor: colors.textSecondary + "30",
                            },
                          ]}
                        >
                          <ThemedText
                            variant="secondary"
                            size="xs"
                            style={[
                              styles.compactChangeText,
                              { color: colors.textSecondary },
                            ]}
                          >
                            --
                          </ThemedText>
                        </View>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            </ThemedCard>
          ))
        )}

        {/* إخلاء المسؤولية */}
        <ThemedCard
          style={[
            styles.disclaimerSection,
            { backgroundColor: colors.surface },
          ]}
        >
          <View style={styles.disclaimerHeader}>
            <MaterialIcons name="warning" size={24} color={colors.warning} />
            <ThemedText
              variant="primary"
              size="md"
              weight="bold"
              style={styles.disclaimerTitle}
            >
              تنويه مهم - إخلاء المسؤولية
            </ThemedText>
          </View>
          <ThemedText
            variant="secondary"
            size="sm"
            style={styles.disclaimerText}
          >
            • الأسعار المعروضة إرشادية فقط وقد تختلف عن أسعار السوق الفعلية
            {"\n"}• يتم تحديث الأسعار دورياً ولكنها قد لا تعكس التغيرات اللحظية
            {"\n"}• سعر البيع (أخضر): السعر الذي يبيع به التاجر للعميل
            {"\n"}• سعر الشراء (أحمر): السعر الذي يشتري به التاجر من العميل
            {"\n"}• الفرق بين سعري البيع والشراء يمثل هامش ربح التاجر
            {"\n"}• أسعار البيع والشراء النهائية تخضع لعوامل إضافية مثل:{"\n"}-
            رسوم الصياغة والتشكيل{"\n"}- تكلفة الشهادات والفحص{"\n"}- الضرائب
            والرسوم الحكومية{"\n"}- حالة وجودة القطعة{"\n"}• ننصح بالتأكد من
            الأسعار مباشرة مع التجار قبل البيع أو الشراء{"\n"}• الشركة غير
            مسؤولة عن أي خسائر ناتجة عن الاعتماد على هذه الأسعار
          </ThemedText>
        </ThemedCard>
      </ScrollView>
    </View>
  );
};

export default PricesScreen;
