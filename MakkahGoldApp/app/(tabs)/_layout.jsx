// تخطيط التبويبات - Tabs Layout
import React from "react";
import { Tabs } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { Platform, Dimensions, I18nManager } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme } from "../../src/contexts/ThemeContext";

const { width } = Dimensions.get("window");

// تفعيل RTL للتبويبات
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

export default function TabsLayout() {
  const { colors, isDarkMode } = useTheme();
  const insets = useSafeAreaInsets();

  // حساب ارتفاع التابز بناءً على حجم الشاشة والجهاز
  const getTabBarHeight = () => {
    const baseHeight = 60;
    let bottomPadding;

    if (Platform.OS === "ios") {
      bottomPadding = Math.max(insets.bottom, 10);
    } else {
      // Android مع edge-to-edge: نحتاج مساحة إضافية للـ system navigation
      bottomPadding = Math.max(insets.bottom, 16); // مساحة أكبر لـ Android
    }

    return baseHeight + bottomPadding;
  };

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: colors.textSecondary,
        tabBarStyle: {
          backgroundColor: colors.surface,
          borderTopWidth: 1,
          borderTopColor: colors.border,
          height: getTabBarHeight(),
          paddingBottom:
            Platform.OS === "ios"
              ? Math.max(insets.bottom, 8)
              : Math.max(insets.bottom, 12), // مساحة أكبر لـ Android
          paddingTop: 8,
          paddingHorizontal: width > 400 ? 16 : 8,
          elevation: Platform.OS === "android" ? 8 : 0,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: -2 },
          shadowOpacity: isDarkMode ? 0.3 : 0.1,
          shadowRadius: 8,
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          // إضافة safe area للـ Android مع edge-to-edge
          ...(Platform.OS === "android" && {
            marginBottom: 0, // تأكد من عدم وجود margin إضافي
            paddingBottom: Math.max(insets.bottom + 8, 16), // تأكد من المساحة الكافية
          }),
        },
        tabBarLabelStyle: {
          fontFamily: "Cairo-Regular",
          fontSize: width > 400 ? 12 : 10, // تكيف حجم الخط مع الشاشة
          marginTop: 4,
          marginBottom: Platform.OS === "ios" ? 2 : 0,
        },
        tabBarIconStyle: {
          marginTop: Platform.OS === "ios" ? 4 : 0,
        },
        headerStyle: {
          backgroundColor: colors.surface,
          elevation: Platform.OS === "android" ? 4 : 0,
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: isDarkMode ? 0.3 : 0.1,
          shadowRadius: 4,
        },
        headerTintColor: colors.textPrimary,
        headerTitleStyle: {
          fontFamily: "Cairo-Bold",
          fontSize: 18,
          color: colors.textPrimary,
        },
        headerTitleAlign: "center",
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "الرئيسية",
          headerTitle: "مجوهرات مكة جولد جروب",
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons
              name="home"
              size={width > 400 ? size : size - 2}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="categories"
        options={{
          title: "الأقسام",
          headerTitle: "أقسام المنتجات",
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons
              name="category"
              size={width > 400 ? size : size - 2}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="prices"
        options={{
          title: "الأسعار",
          headerTitle: "أسعار المعادن",
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons
              name="trending-up"
              size={width > 400 ? size : size - 2}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="bars-coins"
        options={{
          title: "السبائك",
          headerTitle: "السبائك والعملات",
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons
              name="account-balance"
              size={width > 400 ? size : size - 2}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="calculators"
        options={{
          title: "الحاسبات",
          headerTitle: "الحاسبات",
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons
              name="calculate"
              size={width > 400 ? size : size - 2}
              color={color}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="settings"
        options={{
          title: "الإعدادات",
          headerTitle: "الإعدادات",
          headerShown: false,
          tabBarIcon: ({ color, size }) => (
            <MaterialIcons
              name="settings"
              size={width > 400 ? size : size - 2}
              color={color}
            />
          ),
        }}
      />
    </Tabs>
  );
}
