// صفحة الأقسام - Categories Screen
import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  I18nManager,
  Dimensions,
  Pressable,
  ActivityIndicator,
  FlatList,
  Platform,
} from "react-native";
import { Image } from "expo-image";

import { router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { useCategories } from "../../src/utils/hooks";
import { ThemedStatusBar } from "../../src/components";

// الحصول على أبعاد الشاشة للتصميم المتجاوب
const { width: screenWidth } = Dimensions.get("window");
const isTablet = screenWidth >= 768;
const isSmallScreen = screenWidth < 375;

// حساب عدد الأعمدة حسب حجم الشاشة
const getNumColumns = () => {
  if (isTablet) return 3;
  if (isSmallScreen) return 2;
  return 2;
};

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const CategoriesScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();
  const { data: categories, loading, error, refetch } = useCategories();

  const numColumns = getNumColumns();
  const itemWidth =
    (screenWidth - SPACING.md * 2 - SPACING.sm * (numColumns - 1)) / numColumns;

  // دالة إعادة التحميل
  const handleRefresh = () => {
    refetch();
  };

  // دالة التنقل إلى صفحة المنتجات
  const navigateToProducts = (category) => {
    router.push({
      pathname: "/products/category",
      params: {
        categoryId: category.id,
        categoryName: category.name,
      },
    });
  };

  // عرض حالة التحميل
  if (loading) {
    return (
      <View
        style={[
          styles.container,
          styles.loadingContainer,
          { backgroundColor: colors.background },
        ]}
      >
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={[styles.loadingText, { color: colors.textPrimary }]}>
          جاري تحميل الأقسام...
        </Text>
      </View>
    );
  }

  // عرض حالة الخطأ
  if (error && !categories.length) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="category" size={32} color={colors.surface} />
          <Text style={[styles.headerTitle, { color: colors.surface }]}>
            أقسام المنتجات
          </Text>
          <Text
            style={[
              styles.headerSubtitle,
              { color: colors.surface, opacity: 0.9 },
            ]}
          >
            تصفح مجموعة متنوعة من المجوهرات
          </Text>
        </LinearGradient>

        <View style={styles.errorContainer}>
          <MaterialIcons name="error" size={64} color={colors.error} />
          <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
            خطأ في تحميل البيانات
          </Text>
          <Text style={[styles.errorMessage, { color: colors.textSecondary }]}>
            {error}
          </Text>
          <Pressable
            style={({ pressed }) => [
              styles.retryButton,
              { backgroundColor: colors.primary },
              pressed && { transform: [{ scale: 0.95 }] },
            ]}
            onPress={handleRefresh}
            android_ripple={{ color: "rgba(255,255,255,0.2)" }}
          >
            <MaterialIcons name="refresh" size={20} color={colors.surface} />
            <Text style={[styles.retryText, { color: colors.surface }]}>
              إعادة المحاولة
            </Text>
          </Pressable>
        </View>
      </View>
    );
  }

  return (
    <>
      <ThemedStatusBar />
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="category" size={32} color={colors.surface} />
          <Text style={[styles.headerTitle, { color: colors.surface }]}>
            أقسام المنتجات
          </Text>
          <Text
            style={[
              styles.headerSubtitle,
              { color: colors.surface, opacity: 0.9 },
            ]}
          >
            تصفح مجموعة متنوعة من المجوهرات
          </Text>
        </LinearGradient>

        {/* Categories Grid */}
        <FlatList
          data={categories}
          numColumns={numColumns}
          key={numColumns} // إعادة رسم عند تغيير عدد الأعمدة
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={[
            styles.categoriesContainer,
            {
              // مساحة إضافية في الأسفل لتجنب التداخل مع شريط التبويبات
              paddingBottom:
                Platform.OS === "android"
                  ? Math.max(insets.bottom + 80, 100)
                  : Math.max(insets.bottom + 60, 80),
            },
          ]}
          showsVerticalScrollIndicator={false}
          renderItem={({ item }) => (
            <Pressable
              style={({ pressed }) => [
                styles.categoryCard,
                {
                  width: itemWidth,
                  backgroundColor: colors.surface,
                },
                pressed && styles.categoryCardPressed,
              ]}
              onPress={() => navigateToProducts(item)}
              android_ripple={{ color: colors.primary + "20" }}
            >
              {/* Category Image */}
              <View style={styles.imageContainer}>
                {item.image ||
                item.primary_image ||
                (item.images && item.images.length > 0) ? (
                  <Image
                    source={{
                      uri:
                        item.image ||
                        item.primary_image ||
                        item.images?.[0]?.image_url ||
                        item.images?.[0]?.url,
                    }}
                    style={styles.categoryImage}
                    resizeMode="cover"
                    onError={(error) => {
                      console.log(
                        "Category image load error:",
                        error.nativeEvent.error
                      );
                    }}
                  />
                ) : (
                  <LinearGradient
                    colors={[
                      colors.primary,
                      colors.primaryDark || colors.primary,
                    ]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.placeholderImage}
                  >
                    <MaterialIcons
                      name="category"
                      size={isTablet ? 40 : 32}
                      color={colors.surface}
                    />
                  </LinearGradient>
                )}
              </View>

              {/* Category Info */}
              <View style={styles.categoryInfo}>
                <Text
                  style={[styles.categoryName, { color: colors.textPrimary }]}
                >
                  {item.name}
                </Text>
                {item.products_count !== undefined && (
                  <Text
                    style={[
                      styles.productsCount,
                      { color: colors.textSecondary },
                    ]}
                  >
                    {item.products_count} منتج
                  </Text>
                )}
              </View>

              {/* Arrow Icon */}
              <View
                style={[
                  styles.arrowContainer,
                  { backgroundColor: colors.primary + "10" },
                ]}
              ></View>
            </Pressable>
          )}
          ItemSeparatorComponent={() => <View style={{ height: SPACING.sm }} />}
          columnWrapperStyle={numColumns > 1 ? styles.row : null}
        />

        {/* Empty State */}
        {!loading && categories.length === 0 && (
          <View style={styles.emptyContainer}>
            <MaterialIcons
              name="category"
              size={64}
              color={colors.textSecondary}
            />
            <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
              لا توجد أقسام متاحة حالياً
            </Text>
          </View>
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 16,
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    color: COLORS.textPrimary,
    textAlign: "center",
    fontFamily: FONTS.families.medium,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    fontSize: isTablet ? FONTS.sizes.xxl : FONTS.sizes.xl,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm,
    textAlign: "center",
  },
  headerSubtitle: {
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
    lineHeight: 22,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  errorTitle: {
    marginTop: 16,
    fontSize: isTablet ? FONTS.sizes.xl : FONTS.sizes.lg,
    fontWeight: "bold",
    color: COLORS.textPrimary,
    textAlign: "center",
    fontFamily: FONTS.families.bold,
  },
  errorMessage: {
    marginTop: 8,
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    color: COLORS.textSecondary,
    textAlign: "center",
    fontFamily: FONTS.families.regular,
  },
  retryButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 20,
    backgroundColor: COLORS.primary,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  retryText: {
    color: COLORS.surface,
    fontWeight: "bold",
    fontFamily: FONTS.families.bold,
  },
  categoriesContainer: {
    padding: SPACING.md,
    paddingBottom: SPACING.xl,
  },
  row: {
    justifyContent: "space-between",
  },
  categoryCard: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    padding: SPACING.md,
    marginBottom: SPACING.sm,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  categoryCardPressed: {
    transform: [{ scale: 0.98 }],
    elevation: 3,
  },
  imageContainer: {
    width: "100%",
    height: isTablet ? 120 : 100,
    borderRadius: 12,
    overflow: "hidden",
    marginBottom: SPACING.sm,
  },
  categoryImage: {
    width: "100%",
    height: "100%",
  },
  placeholderImage: {
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  categoryInfo: {
    alignItems: "center",
    marginBottom: SPACING.sm,
  },
  categoryName: {
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    textAlign: "center",
    marginBottom: SPACING.xs,
  },
  productsCount: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
  },
  arrowContainer: {
    alignSelf: "center",
    padding: SPACING.xs,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.05)",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyText: {
    marginTop: 16,
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    color: COLORS.textSecondary,
    textAlign: "center",
    fontFamily: FONTS.families.regular,
  },
});

export default CategoriesScreen;
