// الصفحة الرئيسية الحديثة - Modern Home Screen
import React, { useState } from "react";
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  I18nManager,
  Image,
  TouchableOpacity,
  Platform,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import {
  ThemedStatusBar,
  ThemedText,
  SearchBar,
  CategoryTabs,
  CategoryProductGrid,
  PromoBanner,
  Loading,
} from "../../src/components";
import { useCategories, useHomeSliders } from "../../src/utils/hooks";

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const HomeScreen = () => {
  const { colors, isDarkMode, toggleTheme } = useTheme();
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("all");

  // استدعاء البيانات - categories تتضمن آخر 4 منتجات لكل قسم
  const {
    data: categories,
    loading: categoriesLoading,
    refetch: refetchCategories,
  } = useCategories();

  const {
    data: sliders,
    loading: slidersLoading,
    refetch: refetchSliders,
  } = useHomeSliders();

  // الحصول على الفئات المفلترة حسب الفئة المختارة
  const getFilteredCategories = () => {
    if (!categories || categories.length === 0) return [];

    if (selectedCategory === "all") {
      // جمع جميع المنتجات من جميع الفئات
      const allProducts = categories.flatMap((category) =>
        (category.products || []).map((product) => ({
          ...product,
          categoryInfo: {
            id: category.id,
            name: category.name,
            show_price: category.show_price,
          },
        }))
      );

      // ترتيب المنتجات حسب created_at وأخذ أحدث 4 منتجات
      const sortedProducts = allProducts
        .sort((a, b) => {
          const dateA = new Date(a.created_at || 0).getTime();
          const dateB = new Date(b.created_at || 0).getTime();
          return dateB - dateA; // ترتيب تنازلي (الأحدث أولاً)
        })
        .slice(0, 4);

      // إنشاء فئة وهمية تحتوي على أحدث 4 منتجات
      return [
        {
          id: 0,
          name: "أحدث المنتجات",
          name_ar: "أحدث المنتجات",
          name_en: "Latest Products",
          description: "أحدث المنتجات المضافة",
          description_ar: "أحدث المنتجات المضافة",
          slug: "latest-products",
          image: null,
          show_price: true, // افتراضي لعرض الأسعار
          products: sortedProducts.map((product) => ({
            ...product,
            // استخدام show_price من الفئة الأصلية للمنتج
            categoryShowPrice: product.categoryInfo?.show_price !== false,
          })),
        },
      ];
    } else {
      // عرض الفئة المختارة فقط مع منتجاتها
      const selectedCat = categories.find((cat) => cat.id === selectedCategory);
      return selectedCat ? [selectedCat] : [];
    }
  };

  const filteredCategories = getFilteredCategories();

  // تحديث البيانات
  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    await Promise.all([refetchCategories(), refetchSliders()]);
    setRefreshing(false);
  }, [refetchCategories, refetchSliders]);

  // معالجة الأحداث
  const handleProductPress = (productId) => {
    router.push(`/product/${productId}`);
  };

  const handleCategoryPress = (categoryId) => {
    setSelectedCategory(categoryId);
  };

  const handleBannerPress = (slider) => {
    if (slider.button_link) {
      // يمكن إضافة منطق التنقل هنا
      console.log("Banner pressed:", slider.button_link);
    }
  };

  // عرض حالة التحميل
  if (categoriesLoading && slidersLoading) {
    return <Loading text="جاري تحميل البيانات..." />;
  }

  // إنشاء الـ styles داخل المكون للوصول للمتغيرات
  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    header: {
      flexDirection: I18nManager.isRTL ? "row-reverse" : "row", // RTL support
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      paddingTop: Platform.OS === "ios" ? insets.top + SPACING.sm : SPACING.md,
      paddingBottom: SPACING.sm,
      elevation: Platform.OS === "android" ? 4 : 0,
      shadowColor: colors.shadow || "#000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.3 : 0.1,
      shadowRadius: 4,
      minHeight: Platform.OS === "ios" ? 44 + insets.top : 60,
      marginTop: Platform.OS === "ios" ? SPACING.sm : SPACING.lg,
    },
    themeToggleButton: {
      padding: SPACING.sm,
      borderRadius: 8,
      backgroundColor: "rgba(212, 175, 55, 0.1)", // خلفية ذهبية شفافة
      width: 56, // عرض ثابت للتوازن مع اللوجو
      height: 40,
      alignItems: "center",
      justifyContent: "center",
    },
    headerCenter: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: SPACING.sm, // تقليل padding لإفساح مجال أكبر للوجو
    },
    logoContainer: {
      position: "relative",
      alignItems: "center",
      justifyContent: "center",
      width: 56, // عرض ثابت للتوازن
      height: 56, // ارتفاع ثابت للتوازن
    },
    logo: {
      width: 52,
      height: 52,
      borderRadius: 26,
      backgroundColor: "transparent", // خلفية شفافة
      shadowColor: colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: isDarkMode ? 0.4 : 0.15,
      shadowRadius: 3,
      elevation: 2, // ظل خفيف لـ Android
      // تحسين جودة الصورة
      ...(Platform.OS === "ios" && {
        // تحسينات iOS
        shadowPath: undefined,
        shouldRasterizeIOS: true,
        rasterizationScale: 2,
      }),
    },
    appTitle: {
      textAlign: "center",
    },
    notificationButton: {
      padding: SPACING.sm,
      borderRadius: 8,
    },
    productsSection: {
      paddingVertical: SPACING.md,
    },
    noProductsContainer: {
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: SPACING.xl * 2,
    },
    noProductsText: {
      marginTop: SPACING.md,
      textAlign: "center",
    },
  });

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ThemedStatusBar />

      {/* Header مع اللوجو واسم التطبيق */}
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <TouchableOpacity
          style={styles.themeToggleButton}
          onPress={toggleTheme}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name={isDarkMode ? "light-mode" : "dark-mode"}
            size={24}
            color={colors.primary}
          />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <ThemedText
            variant="primary"
            size="md"
            weight="bold"
            style={styles.appTitle}
          >
            مكة جولد جروب
          </ThemedText>
        </View>

        <View style={styles.logoContainer}>
          <Image
            source={require("../../assets/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          {
            // مساحة إضافية في الأسفل لتجنب التداخل مع شريط التبويبات
            paddingBottom:
              Platform.OS === "android"
                ? Math.max(insets.bottom + 80, 100) // مساحة كافية لـ Android مع edge-to-edge
                : Math.max(insets.bottom + 60, 80), // مساحة كافية لـ iOS
          },
        ]}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* البانر الإعلاني */}
        {sliders && sliders.length > 0 && (
          <PromoBanner sliders={sliders} onBannerPress={handleBannerPress} />
        )}

        {/* شريط البحث */}
        <SearchBar placeholder="البحث عن المجوهرات..." showFilter={false} />

        {/* فئات المنتجات */}
        {categories && categories.length > 0 && (
          <CategoryTabs
            categories={categories.map((cat) => ({
              id: cat.id,
              name: cat.name || cat.name_en || "فئة غير محددة", // استخدام الاسم العربي أولاً
              count: cat.products?.length || 0, // عدد المنتجات من products array
            }))}
            activeCategory={selectedCategory}
            onCategoryPress={handleCategoryPress}
            showCounts={false} // إخفاء عدد المنتجات حسب المطلوب
          />
        )}

        {/* شبكة المنتجات */}
        {filteredCategories && filteredCategories.length > 0 && (
          <View style={styles.productsSection}>
            <CategoryProductGrid
              categories={filteredCategories}
              onProductPress={handleProductPress}
              numColumns={2}
              showFavorites={false}
            />
          </View>
        )}

        {/* رسالة عدم وجود منتجات */}
        {filteredCategories && filteredCategories.length === 0 && (
          <View style={styles.noProductsContainer}>
            <MaterialIcons
              name="inventory"
              size={64}
              color={colors.textSecondary}
            />
            <ThemedText
              variant="secondary"
              size="lg"
              style={styles.noProductsText}
            >
              لا توجد منتجات في هذه الفئة
            </ThemedText>
          </View>
        )}
      </ScrollView>
    </View>
  );
};

export default HomeScreen;
