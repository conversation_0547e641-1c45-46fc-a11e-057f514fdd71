// صفحة الحاسبات - Calculators Screen
import React from "react";
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  I18nManager,
  Dimensions,
  Pressable,
  Platform,
} from "react-native";
import { router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { ThemedStatusBar } from "../../src/components";

// الحصول على أبعاد الشاشة للتصميم المتجاوب
const { width: screenWidth } = Dimensions.get("window");
const isTablet = screenWidth >= 768;

// تعطيل RTL - دعم اتجاه من اليسار إلى اليمين
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const CalculatorsScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();

  const calculators = [
    {
      id: "zakat",
      title: "حاسبة الزكاة",
      subtitle: "احسب زكاة الذهب والفضة",
      icon: "calculate",
      route: "/calculator/zakat",
      color: colors.primary,
      gradient: [colors.primary, colors.primaryDark || colors.primary],
    },
    {
      id: "jewelry",
      title: "حاسبة قيمة المجوهرات",
      subtitle: "احسب قيمة مجوهراتك بناءً على الوزن والعيار",
      icon: "diamond",
      route: "/calculator/jewelry",
      color: colors.secondary || colors.primary,
      gradient: [
        colors.secondary || colors.primary,
        colors.secondaryDark || colors.primary,
      ],
    },
    {
      id: "history",
      title: "تاريخ أسعار المعادن",
      subtitle: "تتبع تغيرات الأسعار عبر الزمن",
      icon: "trending-up",
      route: "/calculator/history",
      color: colors.success || colors.primary,
      gradient: [
        colors.success || colors.primary,
        colors.successDark || colors.primary,
      ],
    },
  ];

  return (
    <>
      <ThemedStatusBar />
      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        contentContainerStyle={[
          styles.scrollContent,
          {
            // مساحة إضافية في الأسفل لتجنب التداخل مع شريط التبويبات
            paddingBottom:
              Platform.OS === "android"
                ? Math.max(insets.bottom + 80, 100)
                : Math.max(insets.bottom + 60, 80),
          },
        ]}
        showsVerticalScrollIndicator={false}
      >
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="calculate" size={32} color={colors.surface} />
          <Text style={[styles.headerTitle, { color: colors.surface }]}>
            الحاسبات المتاحة
          </Text>
          <Text
            style={[
              styles.headerSubtitle,
              { color: colors.surface, opacity: 0.9 },
            ]}
          >
            اختر الحاسبة المناسبة لاحتياجاتك
          </Text>
        </LinearGradient>

        <View style={styles.calculatorsGrid}>
          {calculators.map((calculator) => (
            <Pressable
              key={calculator.id}
              style={({ pressed }) => [
                styles.calculatorCard,
                { backgroundColor: colors.surface },
                pressed && styles.calculatorCardPressed,
              ]}
              onPress={() => router.push(calculator.route)}
              android_ripple={{ color: colors.primary + "20" }}
            >
              {/* Enhanced Icon Container with Gradient */}
              <LinearGradient
                colors={calculator.gradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.iconContainer}
              >
                <MaterialIcons
                  name={calculator.icon}
                  size={32}
                  color={colors.surface}
                />
              </LinearGradient>

              <View style={styles.calculatorInfo}>
                <Text
                  style={[
                    styles.calculatorTitle,
                    { color: colors.textPrimary },
                  ]}
                >
                  {calculator.title}
                </Text>
                <Text
                  style={[
                    styles.calculatorSubtitle,
                    { color: colors.textSecondary },
                  ]}
                >
                  {calculator.subtitle}
                </Text>
              </View>

              <View style={styles.arrowContainer}>
                <MaterialIcons
                  name="arrow-back" // RTL support // LTR support
                  size={20}
                  color={colors.textSecondary}
                />
              </View>
            </Pressable>
          ))}
        </View>

        {/* Enhanced Info Section */}
        <LinearGradient
          colors={[colors.surface, colors.surfaceVariant || colors.surface]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.infoSection}
        >
          <View style={styles.sectionTitleContainer}>
            <MaterialIcons
              name="info-outline"
              size={24}
              color={colors.primary}
            />
            <Text style={[styles.sectionTitle, { color: colors.textPrimary }]}>
              معلومات مهمة
            </Text>
          </View>

          <View
            style={[styles.infoCard, { backgroundColor: colors.background }]}
          >
            <MaterialIcons name="info" size={20} color={colors.primary} />
            <Text style={[styles.infoText, { color: colors.textPrimary }]}>
              جميع الحسابات تعتمد على أحدث أسعار المعادن المتاحة
            </Text>
          </View>

          <View
            style={[styles.infoCard, { backgroundColor: colors.background }]}
          >
            <MaterialIcons
              name="security"
              size={20}
              color={colors.success || colors.primary}
            />
            <Text style={[styles.infoText, { color: colors.textPrimary }]}>
              النتائج المعروضة هي تقديرية ويُنصح بمراجعة أهل الاختصاص
            </Text>
          </View>

          <View
            style={[styles.infoCard, { backgroundColor: colors.background }]}
          >
            <MaterialIcons name="update" size={20} color={colors.primary} />
            <Text style={[styles.infoText, { color: colors.textPrimary }]}>
              يتم تحديث الأسعار بشكل دوري لضمان دقة الحسابات
            </Text>
          </View>
        </LinearGradient>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    paddingBottom: 80, // Extra space to prevent content from being hidden behind tab bar
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    fontSize: isTablet ? FONTS.sizes.xxl : FONTS.sizes.xl,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm,
    textAlign: "center",
  },
  headerSubtitle: {
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
    lineHeight: 22,
  },
  calculatorsGrid: {
    padding: SPACING.md,
  },
  calculatorCard: {
    flexDirection: "row", // LTR support
    alignItems: "center",
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    borderRadius: 16,
    marginBottom: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  calculatorCardPressed: {
    transform: [{ scale: 0.98 }],
    elevation: 3,
  },
  iconContainer: {
    width: isTablet ? 70 : 60,
    height: isTablet ? 70 : 60,
    borderRadius: isTablet ? 35 : 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: SPACING.md, // LTR support
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  calculatorInfo: {
    flex: 1,
  },
  calculatorTitle: {
    fontSize: isTablet ? FONTS.sizes.xl : FONTS.sizes.lg,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    textAlign: "left", // LTR support
  },
  calculatorSubtitle: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    lineHeight: isTablet ? 24 : 20,
    textAlign: "left", // LTR support
  },
  arrowContainer: {
    padding: SPACING.xs,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.05)",
  },
  infoSection: {
    padding: SPACING.md,
    backgroundColor: COLORS.white,
    marginTop: SPACING.md,
    marginBottom: SPACING.lg,
    borderRadius: 16,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  sectionTitleContainer: {
    flexDirection: "row", // LTR support
    alignItems: "center",
    marginBottom: SPACING.md,
    gap: SPACING.sm,
  },
  sectionTitle: {
    fontSize: isTablet ? FONTS.sizes.xl : FONTS.sizes.lg,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    textAlign: "right", // LTR support
  },
  infoCard: {
    flexDirection: "row", // LTR support
    alignItems: "flex-start",
    padding: SPACING.md,
    backgroundColor: COLORS.background,
    borderRadius: 12,
    marginBottom: SPACING.sm,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  infoText: {
    flex: 1,
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    color: COLORS.textPrimary,
    marginLeft: SPACING.md, // LTR support
    lineHeight: isTablet ? 24 : 20,
    textAlign: "left", // LTR support
  },
});

export default CalculatorsScreen;
