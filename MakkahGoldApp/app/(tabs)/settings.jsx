// صفحة الإعدادات - Settings Screen
import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  I18nManager,
  Dimensions,
  Pressable,
  RefreshControl,
  Linking,
  Platform,
} from "react-native";
import { router } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { COLORS, FONTS, SPACING } from "../../src/constants";
import { useTheme } from "../../src/contexts/ThemeContext";
import { ThemedStatusBar, Loading } from "../../src/components";
import { useAppSettings } from "../../src/utils/hooks";

// الحصول على أبعاد الشاشة للتصميم المتجاوب
const { width: screenWidth } = Dimensions.get("window");
const isTablet = screenWidth >= 768;

// تفعيل RTL
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

const SettingsScreen = () => {
  const { colors } = useTheme();
  const insets = useSafeAreaInsets();

  // جلب إعدادات التطبيق من API
  const {
    data: settings,
    loading,
    error,
    refetch,
    appInfo,
    companyInfo,
    contactInfo,
    socialLinks,
  } = useAppSettings();

  const menuItems = [
    {
      id: "about",
      title: "حول التطبيق",
      subtitle: "معلومات عن التطبيق والشركة",
      icon: "info",
      route: "/settings/about",
      color: colors.success || colors.primary,
      gradient: [
        colors.success || colors.primary,
        colors.successDark || colors.primary,
      ],
    },
    {
      id: "contact",
      title: "تواصل معنا",
      subtitle: "طرق التواصل والدعم الفني",
      icon: "contact-support",
      route: "/settings/contact",
      color: colors.warning || colors.primary,
      gradient: [
        colors.warning || colors.primary,
        colors.warningDark || colors.primary,
      ],
    },
    {
      id: "stores",
      title: "فروعنا",
      subtitle: "اكتشف أقرب فرع إليك",
      icon: "store",
      route: "/stores",
      color: "#FF9800",
      gradient: ["#FF9800", "#F57C00"],
    },
    {
      id: "privacy",
      title: "سياسة الخصوصية",
      subtitle: "شروط الاستخدام وسياسة الخصوصية",
      icon: "privacy-tip",
      route: "/settings/privacy",
      color: colors.error || colors.primary,
      gradient: [
        colors.error || colors.primary,
        colors.errorDark || colors.primary,
      ],
    },
    {
      id: "theme",
      title: "المظهر",
      subtitle: "تبديل بين الوضع الفاتح والمظلم",
      icon: "brightness-6",
      route: "/settings/theme",
      color: colors.primary,
      gradient: [colors.primary, colors.primaryDark || colors.primary],
    },
    // {
    //   id: "updates",
    //   title: "التحديثات",
    //   subtitle: "إعدادات التحديثات التلقائية",
    //   icon: "system-update",
    //   route: "/settings/updates",
    //   color: "#4CAF50",
    //   gradient: ["#4CAF50", "#388E3C"],
    // },
  ];

  // عرض حالة التحميل
  if (loading) {
    return (
      <>
        <ThemedStatusBar />
        <Loading text="جاري تحميل الإعدادات..." />
      </>
    );
  }

  // عرض رسالة خطأ مع إمكانية إعادة المحاولة
  if (error) {
    return (
      <>
        <ThemedStatusBar />
        <View
          style={[styles.container, { backgroundColor: colors.background }]}
        >
          <View style={styles.errorContainer}>
            <MaterialIcons
              name="error-outline"
              size={64}
              color={colors.error}
            />
            <Text style={[styles.errorTitle, { color: colors.textPrimary }]}>
              خطأ في تحميل الإعدادات
            </Text>
            <Text
              style={[styles.errorMessage, { color: colors.textSecondary }]}
            >
              {error}
            </Text>
            <Pressable
              style={[styles.retryButton, { backgroundColor: colors.primary }]}
              onPress={refetch}
              android_ripple={{ color: colors.surface + "20" }}
            >
              <MaterialIcons name="refresh" size={20} color={colors.surface} />
              <Text style={[styles.retryText, { color: colors.surface }]}>
                إعادة المحاولة
              </Text>
            </Pressable>
          </View>
        </View>
      </>
    );
  }

  return (
    <>
      <ThemedStatusBar />
      <ScrollView
        style={[styles.container, { backgroundColor: colors.background }]}
        contentContainerStyle={{
          // مساحة إضافية في الأسفل لتجنب التداخل مع شريط التبويبات
          paddingBottom:
            Platform.OS === "android"
              ? Math.max(insets.bottom + 80, 100)
              : Math.max(insets.bottom + 60, 80),
        }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={false}
            onRefresh={refetch}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      >
        {/* Enhanced Header with Gradient */}
        <LinearGradient
          colors={[colors.primary, colors.primaryDark || colors.primary]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={[styles.header, { paddingTop: insets.top + 20 }]}
        >
          <MaterialIcons name="settings" size={32} color={colors.surface} />
          <Text style={[styles.headerTitle, { color: colors.surface }]}>
            الإعدادات
          </Text>
          <Text
            style={[
              styles.headerSubtitle,
              { color: colors.surface, opacity: 0.9 },
            ]}
          >
            إدارة إعدادات التطبيق والحساب
          </Text>
        </LinearGradient>

        {/* معلومات التطبيق */}
        <LinearGradient
          colors={[colors.surface, colors.surfaceVariant || colors.surface]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.appInfo}
        >
          <Text style={[styles.appName, { color: colors.textPrimary }]}>
            {appInfo?.name || "مجوهرات مكة جولد جروب"}
          </Text>
          <Text style={[styles.appVersion, { color: colors.textSecondary }]}>
            الإصدار {appInfo?.version || "2.0.0"}
          </Text>
          {appInfo?.description && (
            <Text
              style={[styles.appDescription, { color: colors.textSecondary }]}
            >
              {appInfo.description}
            </Text>
          )}
        </LinearGradient>

        {/* قائمة الإعدادات */}
        <View style={styles.menuSection}>
          {menuItems.map((item) => (
            <Pressable
              key={item.id}
              style={({ pressed }) => [
                styles.menuItem,
                { backgroundColor: colors.surface },
                pressed && styles.menuItemPressed,
              ]}
              onPress={() => router.push(item.route)}
              android_ripple={{ color: colors.primary + "20" }}
            >
              {/* Enhanced Icon Container with Gradient */}
              <LinearGradient
                colors={item.gradient}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={styles.iconContainer}
              >
                <MaterialIcons
                  name={item.icon}
                  size={24}
                  color={colors.surface}
                />
              </LinearGradient>

              <View style={styles.menuItemInfo}>
                <Text
                  style={[styles.menuItemTitle, { color: colors.textPrimary }]}
                >
                  {item.title}
                </Text>
                <Text
                  style={[
                    styles.menuItemSubtitle,
                    { color: colors.textSecondary },
                  ]}
                >
                  {item.subtitle}
                </Text>
              </View>

              <View style={styles.arrowContainer}>
                <MaterialIcons
                  name="arrow-back"
                  size={20}
                  color={colors.textSecondary}
                />
              </View>
            </Pressable>
          ))}
        </View>

        {/* معلومات الشركة */}
        <LinearGradient
          colors={[colors.surface, colors.surfaceVariant || colors.surface]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.companyInfo}
        >
          <Text style={[styles.companyTitle, { color: colors.primary }]}>
            {companyInfo?.name || "مكة جولد جروب"}
          </Text>
          <Text
            style={[styles.companyDescription, { color: colors.textSecondary }]}
          >
            {companyInfo?.description ||
              "شركة رائدة في مجال المجوهرات والمعادن الثمينة، نقدم أفضل المنتجات بأعلى معايير الجودة والحرفية"}
          </Text>

          <View style={styles.socialLinks}>
            {socialLinks?.facebook && (
              <Pressable
                style={[
                  styles.socialButton,
                  { backgroundColor: colors.background },
                ]}
                android_ripple={{ color: colors.primary + "20" }}
                onPress={() => {
                  Linking.openURL(socialLinks.facebook).catch(() => {
                    console.log("Error opening Facebook link");
                  });
                }}
              >
                <MaterialIcons name="facebook" size={24} color="#1877F2" />
              </Pressable>
            )}

            {socialLinks?.instagram && (
              <Pressable
                style={[
                  styles.socialButton,
                  { backgroundColor: colors.background },
                ]}
                android_ripple={{ color: colors.primary + "20" }}
                onPress={() => {
                  Linking.openURL(socialLinks.instagram).catch(() => {
                    console.log("Error opening Instagram link");
                  });
                }}
              >
                <MaterialIcons name="camera-alt" size={24} color="#E4405F" />
              </Pressable>
            )}

            {socialLinks?.twitter && (
              <Pressable
                style={[
                  styles.socialButton,
                  { backgroundColor: colors.background },
                ]}
                android_ripple={{ color: colors.primary + "20" }}
                onPress={() => {
                  Linking.openURL(socialLinks.twitter).catch(() => {
                    console.log("Error opening Twitter link");
                  });
                }}
              >
                <MaterialIcons
                  name="alternate-email"
                  size={24}
                  color="#1DA1F2"
                />
              </Pressable>
            )}

            {socialLinks?.youtube && (
              <Pressable
                style={[
                  styles.socialButton,
                  { backgroundColor: colors.background },
                ]}
                android_ripple={{ color: colors.primary + "20" }}
                onPress={() => {
                  Linking.openURL(socialLinks.youtube).catch(() => {
                    console.log("Error opening YouTube link");
                  });
                }}
              >
                <MaterialIcons
                  name="play-circle-filled"
                  size={24}
                  color="#FF0000"
                />
              </Pressable>
            )}

            {socialLinks?.tiktok && (
              <Pressable
                style={[
                  styles.socialButton,
                  { backgroundColor: colors.background },
                ]}
                android_ripple={{ color: colors.primary + "20" }}
                onPress={() => {
                  Linking.openURL(socialLinks.tiktok).catch(() => {
                    console.log("Error opening TikTok link");
                  });
                }}
              >
                <MaterialIcons name="music-note" size={24} color="#000000" />
              </Pressable>
            )}

            {socialLinks?.linkedin && (
              <Pressable
                style={[
                  styles.socialButton,
                  { backgroundColor: colors.background },
                ]}
                android_ripple={{ color: colors.primary + "20" }}
                onPress={() => {
                  Linking.openURL(socialLinks.linkedin).catch(() => {
                    console.log("Error opening LinkedIn link");
                  });
                }}
              >
                <MaterialIcons name="business" size={24} color="#0077B5" />
              </Pressable>
            )}
          </View>
        </LinearGradient>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.textSecondary }]}>
            جميع الحقوق محفوظة © 2024 مكة جولد جروب
          </Text>
        </View>
      </ScrollView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  headerTitle: {
    fontSize: isTablet ? FONTS.sizes.xxl : FONTS.sizes.xl,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.sm,
    marginTop: SPACING.sm,
    textAlign: "center",
  },
  headerSubtitle: {
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
    lineHeight: 22,
  },
  appInfo: {
    padding: SPACING.lg,
    alignItems: "center",
    marginTop: SPACING.md,
    marginHorizontal: SPACING.md,
    borderRadius: 16,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  appName: {
    fontSize: isTablet ? FONTS.sizes.xl : FONTS.sizes.lg,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    textAlign: "center",
  },
  appVersion: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
  },
  appDescription: {
    fontSize: isTablet ? FONTS.sizes.sm : FONTS.sizes.xs,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
    marginTop: SPACING.sm,
    lineHeight: isTablet ? 20 : 18,
  },
  menuSection: {
    marginTop: SPACING.md,
    marginHorizontal: SPACING.md,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    padding: SPACING.lg,
    borderRadius: 16,
    marginBottom: SPACING.md,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  menuItemPressed: {
    transform: [{ scale: 0.98 }],
    elevation: 3,
  },
  iconContainer: {
    width: isTablet ? 50 : 40,
    height: isTablet ? 50 : 40,
    borderRadius: isTablet ? 25 : 20,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: SPACING.md, // RTL
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  menuItemInfo: {
    flex: 1,
  },
  menuItemTitle: {
    fontSize: isTablet ? FONTS.sizes.lg : FONTS.sizes.md,
    fontFamily: FONTS.families.bold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
    textAlign: "left",
    marginLeft: SPACING.md, // RTL
  },
  menuItemSubtitle: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "left",
    lineHeight: isTablet ? 24 : 20,
    marginLeft: SPACING.md, // RTL
  },
  arrowContainer: {
    padding: SPACING.xs,
    borderRadius: 20,
    backgroundColor: "rgba(0,0,0,0.05)",
  },
  companyInfo: {
    padding: SPACING.lg,
    marginTop: SPACING.md,
    marginHorizontal: SPACING.md,
    alignItems: "center",
    borderRadius: 16,
    elevation: 6,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
  },
  companyTitle: {
    fontSize: isTablet ? FONTS.sizes.xl : FONTS.sizes.lg,
    fontFamily: FONTS.families.bold,
    color: COLORS.primary,
    marginBottom: SPACING.md,
    textAlign: "center",
  },
  companyDescription: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
    lineHeight: isTablet ? 24 : 22,
    marginBottom: SPACING.lg,
  },
  socialLinks: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    gap: SPACING.sm,
    marginTop: SPACING.md,
  },
  socialButton: {
    width: isTablet ? 60 : 50,
    height: isTablet ? 60 : 50,
    borderRadius: isTablet ? 30 : 25,
    backgroundColor: COLORS.background,
    justifyContent: "center",
    alignItems: "center",
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  footer: {
    padding: SPACING.lg,
    alignItems: "center",
    marginBottom: SPACING.xl,
  },
  footerText: {
    fontSize: isTablet ? FONTS.sizes.sm : FONTS.sizes.xs,
    fontFamily: FONTS.families.regular,
    color: COLORS.textSecondary,
    textAlign: "center",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: SPACING.xl,
  },
  errorTitle: {
    fontSize: isTablet ? FONTS.sizes.xl : FONTS.sizes.lg,
    fontFamily: FONTS.families.bold,
    textAlign: "center",
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  errorMessage: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.regular,
    textAlign: "center",
    lineHeight: 22,
    marginBottom: SPACING.xl,
  },
  retryButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 12,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  retryText: {
    fontSize: isTablet ? FONTS.sizes.md : FONTS.sizes.sm,
    fontFamily: FONTS.families.bold,
    marginRight: SPACING.sm,
  },
});

export default SettingsScreen;
