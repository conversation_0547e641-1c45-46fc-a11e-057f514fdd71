<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\CurrencyController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\ProductController;
use App\Http\Controllers\Frontend\CategoryController;
use App\Http\Controllers\Frontend\CartController;
use App\Http\Controllers\Frontend\CheckoutController;
use App\Http\Controllers\Frontend\AccountController;
use App\Http\Controllers\Payment\StripeController;
use App\Http\Controllers\Payment\PayPalController;

// Frontend Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [App\Http\Controllers\Frontend\ContactController::class, 'index'])->name('contact');
Route::post('/contact', [App\Http\Controllers\Frontend\ContactController::class, 'store'])->name('contact.store');
Route::get('/faq', [App\Http\Controllers\Frontend\FaqController::class, 'index'])->name('faq');
Route::post('/faq/mark-helpful', [App\Http\Controllers\Frontend\FaqController::class, 'markHelpful'])->name('faq.mark-helpful');
Route::get('/privacy', [App\Http\Controllers\Frontend\PrivacyController::class, 'index'])->name('privacy');
Route::get('/terms', [App\Http\Controllers\Frontend\TermsController::class, 'index'])->name('terms');
Route::get('/metal-prices', [HomeController::class, 'metalPrices'])->name('metal-prices');
Route::get('/search', [HomeController::class, 'search'])->name('search');

// Zakat Calculator
Route::get('/zakat-calculator', [App\Http\Controllers\Frontend\ZakatCalculatorController::class, 'index'])->name('zakat-calculator');

// Jewelry Value Calculator
Route::get('/jewelry-value-calculator', [App\Http\Controllers\Frontend\JewelryValueCalculatorController::class, 'index'])->name('jewelry-value-calculator');

// Products
Route::get('/products', [ProductController::class, 'index'])->name('products');
Route::get('/shop', [ProductController::class, 'index'])->name('shop'); // Add 'shop' route as an alternative to 'products' route
Route::get('/product/{slug}', [ProductController::class, 'show'])->name('product.show');

// Categories
Route::get('/category/{slug}', [CategoryController::class, 'show'])->name('category');

// Companies
Route::get('/companies', [App\Http\Controllers\CompanyController::class, 'index'])->name('companies');
Route::get('/companies/{company}', [App\Http\Controllers\CompanyController::class, 'show'])->name('companies.show');
Route::get('/companies/{company}/products', [App\Http\Controllers\CompanyController::class, 'products'])->name('companies.products');

// صفحة السبائك والعملات
Route::get('/bars-coins', function () {
    return view('companies.bars-coins');
})->name('bars-coins');

// Cart
Route::get('/cart', [CartController::class, 'index'])->name('cart');
Route::post('/cart/add', [CartController::class, 'add'])->name('cart.add');
Route::post('/cart/add/{productId}', [CartController::class, 'addProduct'])->name('cart.add.product');
Route::post('/cart/update', [CartController::class, 'update'])->name('cart.update');
Route::get('/cart/remove/{rowId}', [CartController::class, 'remove'])->name('cart.remove');
Route::get('/cart/clear', [CartController::class, 'clear'])->name('cart.clear');

// Checkout
Route::get('/checkout', [CheckoutController::class, 'index'])->name('checkout');
Route::post('/checkout', [CheckoutController::class, 'store'])->name('checkout.store');
Route::get('/checkout/success/{id}', [CheckoutController::class, 'success'])->name('checkout.success');
Route::get('/checkout/failed/{id}', [CheckoutController::class, 'failed'])->name('checkout.failed');

// Authentication Routes
Route::get('/login', function () {
    return view('auth.login');
})->middleware('guest')->name('login');

Route::post('/login', function (Illuminate\Http\Request $request) {
    $credentials = $request->validate([
        'email' => ['required', 'email'],
        'password' => ['required'],
    ]);

    if (Auth::attempt($credentials)) {
        $request->session()->regenerate();

        return redirect()->intended(route('account.dashboard'));
    }

    return back()->withErrors([
        'email' => __('Invalid credentials.'),
    ])->onlyInput('email');
})->middleware('guest');

Route::get('/register', function () {
    return view('auth.register');
})->middleware(['guest', 'check.registration'])->name('register');

Route::post('/register', function (Illuminate\Http\Request $request) {
    $request->validate([
        'name' => ['required', 'string', 'max:255'],
        'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
        'password' => ['required', 'string', 'min:8', 'confirmed'],
    ]);

    $user = \App\Models\User::create([
        'name' => $request->name,
        'email' => $request->email,
        'password' => Hash::make($request->password),
    ]);

    Auth::login($user);

    return redirect()->route('account.dashboard');
})->middleware(['guest', 'check.registration']);

Route::post('/logout', function () {
    Auth::logout();
    return redirect()->route('home');
})->middleware('auth')->name('logout');

// Account
Route::middleware(['auth'])->group(function () {
    Route::get('/account', [AccountController::class, 'dashboard'])->name('account');
    Route::get('/account/dashboard', [AccountController::class, 'dashboard'])->name('account.dashboard');
    Route::get('/account/orders', [AccountController::class, 'orders'])->name('account.orders');
    Route::get('/account/orders/{id}', [AccountController::class, 'orderShow'])->name('account.orders.show');
    Route::get('/account/wishlist', [AccountController::class, 'wishlist'])->name('account.wishlist');
    Route::get('/account/settings', [AccountController::class, 'settings'])->name('account.settings');
    Route::post('/account/profile', [AccountController::class, 'updateProfile'])->name('account.profile.update');
    Route::post('/account/password', [AccountController::class, 'updatePassword'])->name('account.password.update');
    Route::get('/account/wishlist/add/{productId}', [AccountController::class, 'addToWishlist'])->name('account.wishlist.add');
    Route::get('/account/wishlist/remove/{wishlistId}', [AccountController::class, 'removeFromWishlist'])->name('account.wishlist.remove');
    Route::post('/account/wishlist/toggle/{productId}', [AccountController::class, 'toggleWishlist'])->name('account.wishlist.toggle');
});

// Wishlist Routes (for AJAX calls)
Route::post('/wishlist/toggle/{productId}', [AccountController::class, 'toggleWishlist'])->name('wishlist.toggle');

// Static Pages Routes - Now handled by dedicated controllers
// FAQ: handled by FaqController
// Privacy: handled by PrivacyController
// Terms: handled by TermsController
// Shipping & Returns: can be added later if needed
Route::get('/stores', [App\Http\Controllers\Frontend\StoreController::class, 'index'])->name('stores');

// Test Language Pages
Route::view('/test-language', 'test-language')->name('test.language');
Route::view('/language-test', 'language-test')->name('language.test');

// Appointment
Route::get('/appointment', [App\Http\Controllers\Frontend\AppointmentController::class, 'index'])->name('appointment');
Route::post('/appointment', [App\Http\Controllers\Frontend\AppointmentController::class, 'store'])->name('appointment.store');
Route::get('/appointment/confirmation/{id}', [App\Http\Controllers\Frontend\AppointmentController::class, 'confirmation'])->name('appointment.confirmation');
Route::post('/appointment/cancel/{id}', [App\Http\Controllers\Frontend\AppointmentController::class, 'cancel'])->name('appointment.cancel');
Route::get('/blog', [App\Http\Controllers\Frontend\BlogController::class, 'index'])->name('blog');
Route::get('/blog/{slug}', [App\Http\Controllers\Frontend\BlogController::class, 'show'])->name('blog.show');


Route::get('/careers', [App\Http\Controllers\Frontend\CareerController::class, 'index'])->name('careers');
Route::get('/careers/{slug}', [App\Http\Controllers\Frontend\CareerController::class, 'show'])->name('careers.show');
Route::post('/careers/apply', [App\Http\Controllers\Frontend\CareerController::class, 'apply'])->name('careers.apply');

// Newsletter
Route::post('/newsletter/subscribe', [App\Http\Controllers\Frontend\NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
Route::get('/newsletter/unsubscribe/{token}', [App\Http\Controllers\Frontend\NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');

// Wishlist
Route::get('/wishlist', function () {
    return redirect()->route('account.wishlist');
})->name('wishlist');

// Language switcher route
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Currency switcher route
Route::get('/currency/{currency}', [CurrencyController::class, 'switch'])->name('currency.switch');

// Payment Routes
Route::prefix('payment')->name('payment.')->group(function () {
    // Stripe Routes
    Route::post('/stripe/create-session', [StripeController::class, 'createSession'])->name('stripe.create-session');
    Route::get('/stripe/success', [StripeController::class, 'success'])->name('stripe.success');
    Route::get('/stripe/cancel', [StripeController::class, 'cancel'])->name('stripe.cancel');

    // PayPal Routes
    Route::post('/paypal/process', [PayPalController::class, 'process'])->name('paypal.process');
    Route::get('/paypal/success', [PayPalController::class, 'success'])->name('paypal.success');
    Route::get('/paypal/cancel', [PayPalController::class, 'cancel'])->name('paypal.cancel');
});

// Filament Admin Panel
Route::get('/admin', function () {
    return redirect('/admin/login');
})->name('admin');
