// تحسينات JavaScript لصفحات الأدوار والصلاحيات

document.addEventListener("DOMContentLoaded", function () {
    // تحسين تجربة البحث في الصلاحيات
    function enhancePermissionSearch() {
        const searchInputs = document.querySelectorAll(
            '[data-testid="checkbox-list.search"]'
        );

        searchInputs.forEach((input) => {
            if (input) {
                input.placeholder = "ابحث في الصلاحيات...";
                input.setAttribute("dir", "rtl");

                // إضافة أيقونة البحث
                const wrapper = input.parentElement;
                if (wrapper && !wrapper.querySelector(".search-icon")) {
                    const icon = document.createElement("div");
                    icon.className =
                        "search-icon absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400";
                    icon.innerHTML = `
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    `;
                    wrapper.style.position = "relative";
                    wrapper.appendChild(icon);
                    input.style.paddingLeft = "2.5rem";
                }
            }
        });
    }

    // تحسين عرض الصلاحيات مع الترجمة
    function enhancePermissionDisplay() {
        const checkboxLabels = document.querySelectorAll(
            ".fi-fo-checkbox-list-option label"
        );

        checkboxLabels.forEach((label) => {
            const text = label.textContent.trim();

            // إضافة tooltip للصلاحيات
            if (text.includes("_")) {
                label.setAttribute("title", `الصلاحية الأصلية: ${text}`);
                label.style.cursor = "help";
            }
        });
    }

    // تحسين أقسام الصلاحيات الجديدة
    function enhancePermissionSections() {
        // إضافة عدادات للأقسام
        const sections = document.querySelectorAll(".fi-section");

        sections.forEach((section) => {
            const checkboxes = section.querySelectorAll(
                'input[type="checkbox"]'
            );
            const checkedCount = section.querySelectorAll(
                'input[type="checkbox"]:checked'
            ).length;

            // إضافة عداد في رأس القسم
            const heading = section.querySelector(".fi-section-header-heading");
            if (heading && checkboxes.length > 0) {
                // إزالة العداد السابق إن وجد
                const existingBadge = heading.querySelector(
                    ".permission-count-badge"
                );
                if (existingBadge) {
                    existingBadge.remove();
                }

                // إضافة عداد جديد
                const badge = document.createElement("span");
                badge.className =
                    "permission-count-badge ml-auto text-xs px-2 py-1 rounded-full font-medium";
                badge.textContent = `${checkedCount}/${checkboxes.length}`;

                // تغيير لون الشارة حسب النسبة
                if (checkedCount === 0) {
                    badge.classList.add("bg-gray-200", "text-gray-700");
                } else if (checkedCount === checkboxes.length) {
                    badge.classList.add("bg-green-100", "text-green-700");
                } else {
                    badge.classList.add("bg-yellow-100", "text-yellow-700");
                }

                heading.appendChild(badge);
            }
        });
    }

    // تحسين أزرار التحكم السريع
    function enhanceQuickControlButtons() {
        // إضافة تأثيرات للأزرار
        const actionButtons = document.querySelectorAll(".fi-ac-group-item");

        actionButtons.forEach((button) => {
            button.addEventListener("click", function () {
                // إضافة تأثير نبضة
                this.classList.add("animate-pulse");
                setTimeout(() => {
                    this.classList.remove("animate-pulse");
                }, 500);

                // تحديث العدادات بعد التنفيذ
                setTimeout(() => {
                    enhancePermissionSections();
                }, 100);
            });
        });
    }

    // تحسين عرض الأقسام
    function enhanceSectionHeaders() {
        const sectionHeaders = document.querySelectorAll(".fi-section-header");

        sectionHeaders.forEach((header) => {
            const actions = header.querySelector(".fi-section-header-actions");
            if (actions) {
                // إضافة تأثيرات للأزرار في رؤوس الأقسام
                const buttons = actions.querySelectorAll(".fi-btn");
                buttons.forEach((button) => {
                    button.addEventListener("click", function () {
                        // تحديث العدادات بعد التنفيذ
                        setTimeout(() => {
                            enhancePermissionSections();
                        }, 100);
                    });
                });
            }
        });
    }

    // تحسين عرض الإحصائيات
    function enhanceStatsDisplay() {
        const badges = document.querySelectorAll(".fi-badge");

        badges.forEach((badge) => {
            const count = parseInt(badge.textContent);
            if (!isNaN(count)) {
                // إضافة ألوان مختلفة حسب العدد
                if (count === 0) {
                    badge.classList.add("bg-gray-100", "text-gray-600");
                } else if (count <= 5) {
                    badge.classList.add("bg-green-100", "text-green-700");
                } else if (count <= 10) {
                    badge.classList.add("bg-blue-100", "text-blue-700");
                } else {
                    badge.classList.add("bg-purple-100", "text-purple-700");
                }
            }
        });
    }

    // تحسين النماذج
    function enhanceFormExperience() {
        // إضافة تأكيد عند مغادرة الصفحة مع تغييرات غير محفوظة
        const forms = document.querySelectorAll("form");
        let formChanged = false;

        forms.forEach((form) => {
            const inputs = form.querySelectorAll("input, select, textarea");

            inputs.forEach((input) => {
                input.addEventListener("change", () => {
                    formChanged = true;
                });
            });
        });

        window.addEventListener("beforeunload", (e) => {
            if (formChanged) {
                e.preventDefault();
                e.returnValue = "لديك تغييرات غير محفوظة. هل تريد المغادرة؟";
            }
        });

        // إزالة التحذير عند الحفظ
        const saveButtons = document.querySelectorAll('[type="submit"]');
        saveButtons.forEach((button) => {
            button.addEventListener("click", () => {
                formChanged = false;
            });
        });
    }

    // تحسين عرض الجداول
    function enhanceTableDisplay() {
        const tables = document.querySelectorAll(".fi-ta-table");

        tables.forEach((table) => {
            // إضافة تأثير hover للصفوف
            const rows = table.querySelectorAll(".fi-ta-row");

            rows.forEach((row) => {
                row.addEventListener("mouseenter", () => {
                    row.style.backgroundColor = "#f9fafb";
                });

                row.addEventListener("mouseleave", () => {
                    row.style.backgroundColor = "";
                });
            });
        });
    }

    // تحسين الرسائل التوضيحية
    function enhanceHelpTexts() {
        const helpTexts = document.querySelectorAll(
            ".fi-fo-field-wrp-helper-text"
        );

        helpTexts.forEach((text) => {
            // إضافة أيقونة معلومات
            if (!text.querySelector(".info-icon")) {
                const icon = document.createElement("span");
                icon.className = "info-icon inline-block ml-1";
                icon.innerHTML = `
                    <svg class="w-3 h-3 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                `;
                text.insertBefore(icon, text.firstChild);
            }
        });
    }

    // تحسين الأزرار
    function enhanceButtons() {
        const buttons = document.querySelectorAll(".fi-btn");

        buttons.forEach((button) => {
            // إضافة تأثير loading عند النقر
            button.addEventListener("click", function (e) {
                if (
                    this.type === "submit" ||
                    this.classList.contains("submit-btn")
                ) {
                    const originalText = this.textContent;
                    const spinner = `
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    `;

                    this.innerHTML = spinner + "جاري المعالجة...";
                    this.disabled = true;

                    // إعادة تعيين النص بعد 3 ثوان (في حالة عدم إعادة تحميل الصفحة)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                }
            });
        });
    }

    // تحسين الاستجابة للجوال
    function enhanceMobileExperience() {
        if (window.innerWidth <= 768) {
            // تحسين عرض الجداول على الجوال
            const tables = document.querySelectorAll(".fi-ta-table");
            tables.forEach((table) => {
                table.style.fontSize = "0.875rem";
            });

            // تحسين عرض النماذج على الجوال
            const sections = document.querySelectorAll(".fi-section");
            sections.forEach((section) => {
                section.style.margin = "0.5rem";
                section.style.borderRadius = "0.5rem";
            });
        }
    }

    // تشغيل جميع التحسينات
    function initializeEnhancements() {
        enhancePermissionSearch();
        enhancePermissionDisplay();
        enhancePermissionSections();
        enhanceQuickControlButtons();
        enhanceSectionHeaders();
        enhanceStatsDisplay();
        enhanceFormExperience();
        enhanceTableDisplay();
        enhanceHelpTexts();
        enhanceButtons();
        enhanceMobileExperience();
    }

    // تشغيل التحسينات عند تحميل الصفحة
    initializeEnhancements();

    // إعادة تشغيل التحسينات عند تحديث المحتوى (Livewire)
    document.addEventListener("livewire:navigated", initializeEnhancements);

    // للتوافق مع Livewire v2
    if (window.Livewire) {
        window.Livewire.hook("message.processed", initializeEnhancements);
    }

    // إعادة تشغيل التحسينات عند تغيير حجم النافذة
    window.addEventListener("resize", enhanceMobileExperience);
});
