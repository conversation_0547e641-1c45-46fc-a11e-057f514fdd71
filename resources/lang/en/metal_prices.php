<?php

return [
    // Page titles
    'page_title' => 'Precious Metal Prices',
    'page_subtitle' => 'Follow the latest gold, silver and platinum prices in Egyptian Pounds',
    'currency_badge' => 'Prices in Egyptian Pounds only',
    'last_update' => 'Last Update',
    'breadcrumb_home' => 'Home',
    'breadcrumb_metal_prices' => 'Metal Prices',

    // Purity labels
    'purities' => [
        '24K' => '24 Karat',
        '22K' => '22 Karat',
        '21K' => '21 Karat',
        '18K' => '18 Karat',
        '14K' => '14 Karat',
        '12K' => '12 Karat',
        '9K' => '9 Karat',
        '999' => 'Silver 999',
        '925' => 'Silver 925',
        '900' => 'Silver 900',
        '800' => 'Silver 800',
        '600' => 'Silver 600',
        'جنيه_ذهب' => 'Gold Pound',
        'نصف_جنيه_ذهب' => 'Half Gold Pound',
        'ربع_جنيه_ذهب' => 'Quarter Gold Pound',
        '950' => 'Platinum 950',
    ],

    // Metal types
    'metals' => [
        'gold' => 'Gold',
        'silver' => 'Silver',
        'platinum' => 'Platinum',
    ],

    // Table headers
    'table_headers' => [
        'purity' => 'Purity',
        'price_per_gram' => 'Price per Gram',
        'price_per_ounce' => 'Price per Ounce',
        'price_per_piece' => 'Price per Piece',
        'change' => 'Change',
        'type' => 'Type',
        'date_time' => 'Date & Time',
    ],

    // Metal prices
    'gold_prices_title' => 'Gold Prices in Egyptian Pounds',
    'silver_prices_title' => 'Silver Prices in Egyptian Pounds',
    'gold_coins_title' => 'Gold Coins Prices',
    'platinum_prices_title' => 'Platinum Prices in Egyptian Pounds',

    // Price change indicators
    'price_changes' => [
        'up' => 'Up',
        'down' => 'Down',
        'stable' => 'Stable',
        'not_available' => 'Not Available',
    ],

    // Statistics
    'statistics' => [
        'title' => 'Price Statistics',
        'gold_title' => 'Gold Statistics',
        'silver_title' => 'Silver Statistics',
        'gold_coins_title' => 'Gold Coins Statistics',
        'highest_price' => 'Highest Price',
        'lowest_price' => 'Lowest Price',
        'average_price' => 'Average Price',
        'total_purities' => 'Total Purities',
        'last_update' => 'Last Update',
        'purity_details' => 'Purity Details',
        'price_per_gram' => 'Price per Gram',
        'price_per_piece' => 'Price per Piece',
        'updated_at' => 'Updated',
        'no_data' => 'No data available',
        'view_details' => 'View Details',
        'hide_details' => 'Hide Details',
    ],

    // Chart
    'chart' => [
        'title' => 'Metal Prices Chart',
        'select_metal' => 'Select Metal and Purity',
        'metals' => [
            'gold_24k' => '24K Gold',
            'gold_22k' => '22K Gold',
            'gold_21k' => '21K Gold',
            'gold_18k' => '18K Gold',
            'gold_14k' => '14K Gold',
            'gold_12k' => '12K Gold',
            'gold_9k' => '9K Gold',
            'silver_999' => 'Silver 999',
            'silver_925' => 'Silver 925',
            'silver_900' => 'Silver 900',
            'silver_800' => 'Silver 800',
            'silver_600' => 'Silver 600',
            'gold_coins' => 'Gold Coins',
        ],
        'periods' => [
            '7' => 'Week',
            '30' => 'Month',
            '90' => '3 Months',
            '180' => '6 Months',
            '365' => 'Year',
        ],
        'no_data' => 'Not enough data to display chart',
        'no_data_period' => 'for the selected period and metal',
        'no_data_initial' => 'Please add metal price data first',
    ],

    // System messages
    'messages' => [
        'no_gold_prices' => 'No gold prices available currently',
        'no_silver_prices' => 'No silver prices available currently',
        'no_gold_coins' => 'No gold coins prices available currently',
        'no_platinum_prices' => 'No platinum prices available currently',
        'loading' => 'Loading...',
        'error' => 'Error loading data',
    ],

    // Units
    'units' => [
        'egp' => 'EGP',
        'gram' => 'gram',
        'ounce' => 'ounce',
        'per_gram' => 'EGP/gram',
        'per_ounce' => 'EGP/ounce',
    ],

    // Disclaimer
    'disclaimer' => [
        'title' => 'Disclaimer',
        'content' => 'The prices displayed are for reference only and may differ slightly from actual market prices. Prices are updated regularly but may not reflect immediate market changes. Actual buying and selling prices may include additional fees such as crafting fees, certification fees and taxes.',
    ],

    // Days of the week
    'days' => [
        'sunday' => 'Sunday',
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
    ],

    // Months
    'months' => [
        'january' => 'January',
        'february' => 'February',
        'march' => 'March',
        'april' => 'April',
        'may' => 'May',
        'june' => 'June',
        'july' => 'July',
        'august' => 'August',
        'september' => 'September',
        'october' => 'October',
        'november' => 'November',
        'december' => 'December',
    ],
];
