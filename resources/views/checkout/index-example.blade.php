@extends('layouts.app-with-settings')

@section('title', __('إتمام الطلب') . ' - ' . config('app.name'))

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- فحص وضع العرض فقط -->
    <x-feature-check feature="display_only">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">{{ __('إتمام الطلب') }}</h1>

            <form action="{{ route('checkout.process') }}" method="POST" id="checkout-form">
                @csrf
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Checkout Form -->
                    <div class="lg:col-span-2 space-y-8">
                        <!-- Customer Information -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 class="text-xl font-semibold text-gray-900 mb-6">{{ __('معلومات العميل') }}</h2>
                            
                            @guest
                                <!-- Guest Checkout (إذا كان مفعلاً) -->
                                <x-feature-check feature="guest_checkout" :show-message="false">
                                    <div class="mb-6">
                                        <div class="flex items-center justify-between">
                                            <label class="flex items-center">
                                                <input type="radio" name="checkout_type" value="guest" class="mr-2" checked>
                                                <span class="text-sm font-medium text-gray-700">{{ __('الشراء كزائر') }}</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="radio" name="checkout_type" value="register" class="mr-2">
                                                <span class="text-sm font-medium text-gray-700">{{ __('إنشاء حساب جديد') }}</span>
                                            </label>
                                        </div>
                                    </div>
                                </x-feature-check>
                                
                                <!-- Social Login (إذا كان مفعلاً) -->
                                <x-social-login />
                            @endguest

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">{{ __('الاسم الأول') }}</label>
                                    <input type="text" id="first_name" name="first_name" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                           value="{{ auth()->user()->first_name ?? old('first_name') }}">
                                </div>
                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">{{ __('الاسم الأخير') }}</label>
                                    <input type="text" id="last_name" name="last_name" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                           value="{{ auth()->user()->last_name ?? old('last_name') }}">
                                </div>
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">{{ __('البريد الإلكتروني') }}</label>
                                    <input type="email" id="email" name="email" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                           value="{{ auth()->user()->email ?? old('email') }}">
                                </div>
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">{{ __('رقم الهاتف') }}</label>
                                    <input type="tel" id="phone" name="phone" required 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                           value="{{ auth()->user()->phone ?? old('phone') }}">
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Information -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 class="text-xl font-semibold text-gray-900 mb-6">{{ __('معلومات الشحن') }}</h2>
                            
                            <!-- Shipping Method -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-4">{{ __('طريقة الاستلام') }}</label>
                                <div class="space-y-3">
                                    <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                        <input type="radio" name="shipping_method" value="delivery" class="mr-3" checked>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">{{ __('التوصيل للمنزل') }}</div>
                                            <div class="text-sm text-gray-500">{{ __('سيتم توصيل الطلب إلى عنوانك') }}</div>
                                        </div>
                                        <div class="text-sm font-medium text-gray-900">{{ __('50 ج.م') }}</div>
                                    </label>
                                    
                                    <!-- Local Pickup (إذا كان مفعلاً) -->
                                    <x-feature-check feature="local_pickup" :show-message="false">
                                        <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                                            <input type="radio" name="shipping_method" value="pickup" class="mr-3">
                                            <div class="flex-1">
                                                <div class="font-medium text-gray-900">{{ __('الاستلام من المتجر') }}</div>
                                                <div class="text-sm text-gray-500">{{ __('استلم طلبك من فرعنا الرئيسي') }}</div>
                                            </div>
                                            <div class="text-sm font-medium text-green-600">{{ __('مجاناً') }}</div>
                                        </label>
                                    </x-feature-check>
                                </div>
                            </div>

                            <!-- Shipping Address -->
                            <div id="shipping-address" class="space-y-4">
                                <div>
                                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">{{ __('العنوان') }}</label>
                                    <textarea id="address" name="address" rows="3" required 
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                              placeholder="{{ __('أدخل العنوان الكامل') }}">{{ old('address') }}</textarea>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="city" class="block text-sm font-medium text-gray-700 mb-2">{{ __('المدينة') }}</label>
                                        <input type="text" id="city" name="city" required 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                               value="{{ old('city') }}">
                                    </div>
                                    <div>
                                        <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-2">{{ __('الرمز البريدي') }}</label>
                                        <input type="text" id="postal_code" name="postal_code" 
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                                               value="{{ old('postal_code') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <h2 class="text-xl font-semibold text-gray-900 mb-6">{{ __('طريقة الدفع') }}</h2>
                            <x-payment-methods />
                        </div>

                        <!-- Marketing Consent (إذا كان مفعلاً) -->
                        @if(isset($superAdminSettings) && $superAdminSettings->isMarketingConsentRequired())
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                            <label class="flex items-start">
                                <input type="checkbox" name="marketing_consent" class="mt-1 mr-3">
                                <span class="text-sm text-gray-700">
                                    {{ $superAdminSettings->getMarketingConsentText() }}
                                </span>
                            </label>
                        </div>
                        @endif
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-4">
                            <h2 class="text-xl font-semibold text-gray-900 mb-6">{{ __('ملخص الطلب') }}</h2>
                            
                            <!-- Cart Items -->
                            <div class="space-y-4 mb-6">
                                @foreach($cartItems as $item)
                                <div class="flex items-center space-x-4">
                                    <img src="{{ $item->product->image_url }}" alt="{{ $item->product->name }}" 
                                         class="w-16 h-16 object-cover rounded-lg">
                                    <div class="flex-1">
                                        <h4 class="font-medium text-gray-900">{{ $item->product->name }}</h4>
                                        <p class="text-sm text-gray-500">{{ __('الكمية: :quantity', ['quantity' => $item->quantity]) }}</p>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ number_format($item->total_price) }} {{ __('ج.م') }}
                                    </div>
                                </div>
                                @endforeach
                            </div>

                            <!-- Order Totals -->
                            <div class="border-t pt-4 space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">{{ __('المجموع الفرعي') }}</span>
                                    <span class="font-medium">{{ number_format($subtotal) }} {{ __('ج.م') }}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">{{ __('الشحن') }}</span>
                                    <span class="font-medium" id="shipping-cost">{{ number_format($shippingCost) }} {{ __('ج.م') }}</span>
                                </div>
                                @if($taxAmount > 0)
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">{{ __('الضريبة') }}</span>
                                    <span class="font-medium">{{ number_format($taxAmount) }} {{ __('ج.م') }}</span>
                                </div>
                                @endif
                                <div class="border-t pt-2 flex justify-between text-lg font-semibold">
                                    <span>{{ __('المجموع الكلي') }}</span>
                                    <span id="total-amount">{{ number_format($total) }} {{ __('ج.م') }}</span>
                                </div>
                            </div>

                            <!-- Place Order Button -->
                            <button type="submit" 
                                    class="w-full mt-6 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200">
                                {{ __('تأكيد الطلب') }}
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </x-feature-check>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const shippingMethods = document.querySelectorAll('input[name="shipping_method"]');
    const shippingAddress = document.getElementById('shipping-address');
    const shippingCostElement = document.getElementById('shipping-cost');
    const totalAmountElement = document.getElementById('total-amount');
    
    shippingMethods.forEach(method => {
        method.addEventListener('change', function() {
            if (this.value === 'pickup') {
                shippingAddress.style.display = 'none';
                shippingCostElement.textContent = '0 {{ __("ج.م") }}';
                // تحديث المجموع الكلي
                updateTotal(0);
            } else {
                shippingAddress.style.display = 'block';
                shippingCostElement.textContent = '50 {{ __("ج.م") }}';
                // تحديث المجموع الكلي
                updateTotal(50);
            }
        });
    });
    
    function updateTotal(shippingCost) {
        const subtotal = {{ $subtotal }};
        const tax = {{ $taxAmount }};
        const total = subtotal + shippingCost + tax;
        totalAmountElement.textContent = total.toLocaleString() + ' {{ __("ج.م") }}';
    }
});
</script>
@endpush
@endsection
