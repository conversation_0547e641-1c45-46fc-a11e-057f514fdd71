<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=tajawal:400,500,700&display=swap" rel="stylesheet" />
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
        
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        
        <!-- Bootstrap Icons -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

        <!-- Styles -->
        <style>
            body {
                font-family: {{ app()->getLocale() == 'ar' ? 'Tajawal' : 'Instrument Sans' }}, sans-serif;
                background-color: #f8f9fa;
                color: #333;
            }
            .hero-section {
                background-color: #f5f5f5;
                padding: 80px 0;
                margin-bottom: 40px;
            }
            .logo-container {
                max-width: 300px;
                margin-bottom: 30px;
            }
            .language-switcher {
                position: absolute;
                top: 20px;
                {{ app()->getLocale() == 'ar' ? 'left' : 'right' }}: 20px;
            }
            .gold-color {
                color: #D4AF37;
            }
            .btn-gold {
                background-color: #D4AF37;
                border-color: #D4AF37;
                color: white;
            }
            .btn-gold:hover {
                background-color: #C5A028;
                border-color: #C5A028;
                color: white;
            }
            .feature-box {
                padding: 20px;
                border-radius: 5px;
                margin-bottom: 20px;
                background-color: white;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                height: 100%;
            }
            .feature-icon {
                font-size: 2rem;
                margin-bottom: 15px;
                color: #D4AF37;
            }
            .product-card {
                transition: all 0.3s ease;
                border: 1px solid #eee;
            }
            .product-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            }
            .price-tag {
                font-size: 1.2rem;
            }
            footer {
                background-color: #333;
                color: white;
                padding: 40px 0;
            }
        </style>
    </head>
    <body>
        <div class="language-switcher">
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    {{ __('messages.language') }}
                </button>
                <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                    <li><a class="dropdown-item" href="{{ route('language.switch', 'ar') }}">{{ __('messages.arabic') }}</a></li>
                    <li><a class="dropdown-item" href="{{ route('language.switch', 'en') }}">{{ __('messages.english') }}</a></li>
                </ul>
            </div>
        </div>

        <div class="hero-section text-center">
            <div class="container">
                <div class="logo-container mx-auto">
                    <img src="https://via.placeholder.com/300x300?text=Makkah+Gold" alt="{{ config('app.name') }}" class="img-fluid rounded-circle">
                </div>
                <h1 class="display-4 gold-color">{{ __('messages.welcome') }}</h1>
                <p class="lead">مجوهرات فاخرة بتصاميم فريدة</p>
                <div class="mt-4">
                    @if (Route::has('login'))
                        <div class="d-flex justify-content-center gap-3">
                            @auth
                                <a href="{{ url('/dashboard') }}" class="btn btn-gold">{{ __('messages.account') }}</a>
                            @else
                                <a href="{{ route('login') }}" class="btn btn-gold">{{ __('messages.login') }}</a>

                                @if (Route::has('register'))
                                    <a href="{{ route('register') }}" class="btn btn-outline-secondary">{{ __('messages.register') }}</a>
                                @endif
                            @endauth
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="container mb-5">
            <!-- الفئات الرئيسية -->
            <h2 class="text-center mb-4 gold-color">فئات المجوهرات</h2>
            <div class="row mb-5">
                @foreach($categories as $category)
                <div class="col-md-3 mb-4">
                    <div class="feature-box text-center">
                        <div class="feature-icon">
                            <i class="bi bi-gem"></i>
                        </div>
                        <h3>{{ app()->getLocale() == 'ar' ? $category->name_ar : $category->name_en }}</h3>
                        <p>{{ app()->getLocale() == 'ar' ? $category->description_ar : $category->description_en }}</p>
                        <a href="#" class="btn btn-sm btn-gold mt-2">عرض المنتجات</a>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- المنتجات المميزة -->
            <h2 class="text-center mb-4 gold-color">منتجات مميزة</h2>
            <div class="row">
                @foreach($featuredProducts as $product)
                <div class="col-md-4 mb-4">
                    <div class="card h-100 product-card">
                        <div class="position-relative">
                            @if($product->primaryImage)
                                <img src="{{ asset($product->primaryImage->image_path) }}" class="card-img-top" alt="{{ $product->name }}">
                            @else
                                <img src="https://via.placeholder.com/300x300?text=No+Image" class="card-img-top" alt="{{ $product->name }}">
                            @endif
                            <span class="position-absolute top-0 end-0 badge bg-warning m-2">مميز</span>
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title">{{ app()->getLocale() == 'ar' ? $product->name_ar : $product->name_en }}</h5>
                            <p class="card-text small text-muted">{{ $product->material_type }} - {{ $product->metal_purity }}</p>
                            <p class="card-text small">{{ $product->weight }} جرام</p>
                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="price-tag gold-color fw-bold">{{ number_format($product->price, 2) }} ر.س</span>
                                    <a href="#" class="btn btn-sm btn-outline-secondary">عرض التفاصيل</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- المميزات -->
            <h2 class="text-center my-5 gold-color">لماذا تختار مجوهرات مكة جولد؟</h2>
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-box">
                        <div class="feature-icon">
                            <i class="bi bi-gem"></i>
                        </div>
                        <h3>جودة استثنائية</h3>
                        <p>نقدم مجوهرات فاخرة مصنوعة من أجود أنواع المعادن الثمينة والأحجار الكريمة</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box">
                        <div class="feature-icon">
                            <i class="bi bi-shop"></i>
                        </div>
                        <h3>فروع متعددة</h3>
                        <p>تفضل بزيارة أحد فروعنا المنتشرة في أنحاء المملكة واستمتع بتجربة تسوق استثنائية</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-box">
                        <div class="feature-icon">
                            <i class="bi bi-calendar-check"></i>
                        </div>
                        <h3>خدمة مميزة</h3>
                        <p>احجز موعدًا للحصول على استشارة خاصة من خبراء المجوهرات لدينا</p>
                    </div>
                </div>
            </div>
        </div>

        <footer class="bg-dark text-white py-4">
            <div class="container text-center">
                <p>&copy; {{ date('Y') }} {{ config('app.name') }}. جميع الحقوق محفوظة.</p>
            </div>
        </footer>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    </body>
</html>
