<x-filament-widgets::widget>
    <x-filament::section>
        <div class="space-y-6">
            <!-- Header -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 rtl:space-x-reverse">
                    <div class="p-2 bg-gradient-to-br from-amber-400 to-yellow-500 rounded-lg">
                        <x-heroicon-o-currency-dollar class="h-6 w-6 text-white" />
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 dark:text-white">
                            أسعار المعادن الثمينة
                        </h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            أحدث الأسعار المحدثة
                        </p>
                    </div>
                </div>

                @if ($resource_url)
                    <x-filament::button tag="a" href="{{ $resource_url }}" size="sm" color="amber" outlined
                        icon="heroicon-o-arrow-top-right-on-square">
                        إدارة الأسعار
                    </x-filament::button>
                @endif
            </div>

            <!-- Stats Summary -->
            <div
                class="grid grid-cols-3 gap-4 p-4 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl border border-amber-200 dark:border-amber-800">
                <div class="text-center">
                    <div class="text-2xl font-bold text-amber-600 dark:text-amber-400">
                        {{ number_format($total_prices) }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        إجمالي الأسعار
                    </div>
                </div>

                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ number_format($active_prices) }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        أسعار نشطة
                    </div>
                </div>

                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ number_format($today_updates) }}
                    </div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        محدثة اليوم
                    </div>
                </div>
            </div>

            <!-- Metal Prices by Type -->
            @if ($grouped_prices && count($grouped_prices) > 0)
                <div class="space-y-6">
                    @foreach ($grouped_prices as $metalType => $metalData)
                        <div class="space-y-3">
                            <!-- Metal Header -->
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                @if ($metalType === 'gold')
                                    <div class="w-4 h-4 bg-gradient-to-r from-yellow-400 to-amber-500 rounded-full">
                                    </div>
                                @elseif($metalType === 'silver')
                                    <div class="w-4 h-4 bg-gradient-to-r from-gray-300 to-gray-400 rounded-full"></div>
                                @elseif($metalType === 'platinum')
                                    <div class="w-4 h-4 bg-gradient-to-r from-gray-400 to-gray-500 rounded-full"></div>
                                @else
                                    <div class="w-4 h-4 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full"></div>
                                @endif

                                <h4 class="text-lg font-bold text-gray-900 dark:text-white">
                                    {{ $metalData['name'] }}
                                </h4>

                                <span
                                    class="px-2 py-1 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full">
                                    {{ count($metalData['prices']) }} عيار
                                </span>
                            </div>

                            <!-- Prices Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                @foreach ($metalData['prices'] as $price)
                                    <div
                                        class="relative p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow duration-200">
                                        <!-- Price Badge -->
                                        <div class="absolute top-2 left-2 rtl:right-2 rtl:left-auto">
                                            @if ($metalType === 'gold')
                                                <span
                                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                                                    {{ $price['purity_name'] }}
                                                </span>
                                            @elseif($metalType === 'silver')
                                                <span
                                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                                    {{ $price['purity_name'] }}
                                                </span>
                                            @else
                                                <span
                                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    {{ $price['purity_name'] }}
                                                </span>
                                            @endif
                                        </div>

                                        <!-- Price Display -->
                                        <div class="mt-8 text-center">
                                            <div class="text-2xl font-bold text-gray-900 dark:text-white">
                                                {{ number_format($price['price_per_gram'], 2) }}
                                            </div>
                                            <div class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                                جنيه مصري / جرام
                                            </div>
                                        </div>

                                        <!-- Update Time -->
                                        <div class="mt-3 text-center">
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                آخر تحديث
                                            </div>
                                            <div class="text-xs font-medium text-gray-600 dark:text-gray-300">
                                                {{ \Carbon\Carbon::parse($price['updated_at'])->format('Y-m-d') }}
                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                {{ \Carbon\Carbon::parse($price['updated_at'])->format('H:i') }}
                                            </div>
                                        </div>

                                        <!-- Time Ago -->
                                        <div class="mt-2 text-center">
                                            <span
                                                class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                <x-heroicon-o-clock class="w-3 h-3 mr-1 rtl:ml-1 rtl:mr-0" />
                                                {{ \Carbon\Carbon::parse($price['updated_at'])->diffForHumans() }}
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Empty State -->
                <div class="text-center py-12">
                    <div
                        class="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                        <x-heroicon-o-currency-dollar class="w-12 h-12 text-gray-400" />
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        لا توجد أسعار متاحة
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">
                        لم يتم إضافة أي أسعار للمعادن بعد.
                    </p>
                    @if ($resource_url)
                        <x-filament::button tag="a" href="{{ $resource_url }}" color="primary"
                            icon="heroicon-o-plus">
                            إضافة أسعار جديدة
                        </x-filament::button>
                    @endif
                </div>
            @endif

            <!-- Footer -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
                <div class="flex items-center space-x-2 rtl:space-x-reverse text-xs text-gray-500 dark:text-gray-400">
                    <x-heroicon-o-arrow-path class="w-4 h-4" />
                    <span>يتم التحديث كل 30 ثانية</span>
                </div>

                @if ($last_update)
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                        آخر تحديث عام: {{ \Carbon\Carbon::parse($last_update)->format('Y-m-d H:i:s') }}
                    </div>
                @endif
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>
