<div class="overflow-x-auto">
    <table class="w-full text-sm text-right text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="px-6 py-3">نوع المعدن</th>
                <th scope="col" class="px-6 py-3">العيار</th>
                <th scope="col" class="px-6 py-3">السعر الجديد</th>
                <th scope="col" class="px-6 py-3">السعر الحالي</th>
                <th scope="col" class="px-6 py-3">الفرق</th>
                <th scope="col" class="px-6 py-3">نسبة التغيير</th>
                <th scope="col" class="px-6 py-3">الاتجاه</th>
            </tr>
        </thead>
        <tbody>
            @forelse($prices as $price)
                <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                    <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">
                        <div class="flex items-center">
                            @if($price['metal_type'] === 'gold')
                                <div class="w-3 h-3 bg-yellow-400 rounded-full ml-2"></div>
                            @elseif($price['metal_type'] === 'silver')
                                <div class="w-3 h-3 bg-gray-400 rounded-full ml-2"></div>
                            @elseif($price['metal_type'] === 'gold_coin')
                                <div class="w-3 h-3 bg-orange-400 rounded-full ml-2"></div>
                            @endif
                            {{ $price['metal_type_name'] }}
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">
                            {{ $price['purity_name'] }}
                        </span>
                    </td>
                    <td class="px-6 py-4 font-bold text-green-600">
                        {{ $price['formatted_new_price'] }} ج.م
                    </td>
                    <td class="px-6 py-4">
                        @if($price['current_price'] > 0)
                            {{ $price['formatted_current_price'] }} ج.م
                        @else
                            <span class="text-gray-400">غير متاح</span>
                        @endif
                    </td>
                    <td class="px-6 py-4">
                        @if($price['difference'] > 0)
                            <span class="text-green-600 font-medium">
                                +{{ $price['formatted_difference'] }} ج.م
                            </span>
                        @elseif($price['difference'] < 0)
                            <span class="text-red-600 font-medium">
                                {{ $price['formatted_difference'] }} ج.م
                            </span>
                        @else
                            <span class="text-gray-500">
                                {{ $price['formatted_difference'] }} ج.م
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4">
                        @if($price['difference_percent'] > 0)
                            <span class="text-green-600 font-medium">
                                +{{ $price['formatted_difference_percent'] }}%
                            </span>
                        @elseif($price['difference_percent'] < 0)
                            <span class="text-red-600 font-medium">
                                {{ $price['formatted_difference_percent'] }}%
                            </span>
                        @else
                            <span class="text-gray-500">
                                {{ $price['formatted_difference_percent'] }}%
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4">
                        @if($price['direction'] === 'up')
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                ارتفاع
                            </span>
                        @elseif($price['direction'] === 'down')
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                انخفاض
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                ثابت
                            </span>
                        @endif
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                        لا توجد أسعار للعرض
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@if(count($prices) > 0)
    <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center">
            <svg class="w-5 h-5 text-blue-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            <div class="text-sm text-blue-700">
                <p class="font-medium">ملاحظة مهمة:</p>
                <p>عند الموافقة على حفظ هذه الأسعار، ستصبح الأسعار الجديدة هي المفعلة وسيتم إلغاء تفعيل الأسعار السابقة لنفس العيارات.</p>
                <p class="mt-1">إجمالي الأسعار المراد حفظها: <strong>{{ count($prices) }}</strong> سعر</p>
            </div>
        </div>
    </div>
@endif
