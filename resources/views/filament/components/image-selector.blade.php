@php
    $productImages = $getRecord()?->product_images ?? [];
    $primaryIndex = $getRecord()?->primary_image_index ?? 0;
@endphp

<div x-data="{
    primaryIndex: {{ $primaryIndex }},
    selectPrimary(index) {
        this.primaryIndex = index;
        // Update the hidden field
        const hiddenField = document.querySelector('input[name=\'primary_image_index\']');
        if (hiddenField) {
            hiddenField.value = index;
            hiddenField.dispatchEvent(new Event('input'));
        }
    }
}" class="space-y-3">

    @if (is_array($productImages) && count($productImages) > 1)
        <div class="text-sm font-medium text-gray-700 dark:text-gray-300">
            اختر الصورة الرئيسية:
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            @foreach ($productImages as $index => $image)
                <div class="relative cursor-pointer group" @click="selectPrimary({{ $index }})">
                    <div class="relative overflow-hidden rounded-lg border-2 transition-all duration-200"
                        :class="primaryIndex === {{ $index }} ? 'border-primary-500 ring-2 ring-primary-500/20' :
                            'border-gray-200 hover:border-gray-300'">
                        <img src="{{ Storage::url($image) }}" alt="صورة المنتج {{ $index + 1 }}"
                            class="w-full h-24 object-cover">

                        <!-- Primary Badge -->
                        <div x-show="primaryIndex === {{ $index }}"
                            class="absolute top-2 right-2 bg-primary-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                            رئيسية
                        </div>

                        <!-- Hover Overlay -->
                        <div class="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
                            x-show="primaryIndex !== {{ $index }}">
                            <span class="text-white text-xs font-medium bg-black/50 px-2 py-1 rounded">
                                اختر كرئيسية
                            </span>
                        </div>
                    </div>

                    <div class="text-center mt-1 text-xs text-gray-500">
                        صورة {{ $index + 1 }}
                    </div>
                </div>
            @endforeach
        </div>

        <div class="text-xs text-gray-500">
            💡 انقر على أي صورة لجعلها الصورة الرئيسية
        </div>
    @endif
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('imageSelector', () => ({
            primaryIndex: 0,
            images: [],

            selectPrimary(index) {
                this.primaryIndex = index;
                // Update the hidden field
                const hiddenField = document.querySelector('input[name="primary_image_index"]');
                if (hiddenField) {
                    hiddenField.value = index;
                    hiddenField.dispatchEvent(new Event('input'));
                }
            }
        }));
    });
</script>
