<x-filament-panels::page>
    @php
        $stats = $this->getStats();
    @endphp

    <div class="space-y-6">
        <!-- إحصائيات سريعة -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-8 h-8 text-blue-400 ml-3" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-blue-600">إجمالي الأسعار</p>
                        <p class="text-2xl font-bold text-blue-900">{{ $stats['total'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-yellow-400 rounded-full ml-3"></div>
                    <div>
                        <p class="text-sm font-medium text-yellow-600">أسعار الذهب</p>
                        <p class="text-2xl font-bold text-yellow-900">{{ $stats['gold'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-400 rounded-full ml-3"></div>
                    <div>
                        <p class="text-sm font-medium text-gray-600">أسعار الفضة</p>
                        <p class="text-2xl font-bold text-gray-900">{{ $stats['silver'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-orange-400 rounded-full ml-3"></div>
                    <div>
                        <p class="text-sm font-medium text-orange-600">الجنيهات الذهبية</p>
                        <p class="text-2xl font-bold text-orange-900">{{ $stats['gold_coin'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات عامة -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-blue-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"></path>
                </svg>
                <div class="text-sm text-blue-700">
                    <p class="font-medium">جميع الأسعار المستخرجة من موقع iSagha</p>
                    <p>هذه جميع الأسعار التي تم استخراجها من موقع iSagha باستخدام تقنيات مختلفة للـ web scraping.</p>
                    <p class="mt-1">آخر تحديث: <strong>{{ now()->format('Y-m-d H:i:s') }}</strong></p>
                </div>
            </div>
        </div>

        <!-- جدول الأسعار المستخرجة -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">جميع الأسعار المستخرجة</h3>
                <p class="mt-1 text-sm text-gray-600">الأسعار كما تم استخراجها من موقع iSagha مع السياق الأصلي</p>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">#</th>
                            <th scope="col" class="px-6 py-3">النص الأصلي</th>
                            <th scope="col" class="px-6 py-3">السعر</th>
                            <th scope="col" class="px-6 py-3">نوع المعدن المتوقع</th>
                            <th scope="col" class="px-6 py-3">العيار المتوقع</th>
                            <th scope="col" class="px-6 py-3">السياق</th>
                            <th scope="col" class="px-6 py-3">الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($extractedPrices as $index => $price)
                            @php
                                $text = strtolower($price['purity_text']);
                                $detectedMetal = 'غير محدد';
                                $detectedPurity = 'غير محدد';
                                $status = 'غير مطابق';
                                $statusColor = 'gray';

                                // تحديد نوع المعدن
                                if (strpos($text, 'ذهب') !== false || strpos($text, 'gold') !== false) {
                                    if (strpos($text, 'جنيه') !== false) {
                                        $detectedMetal = 'جنيهات ذهبية';
                                    } else {
                                        $detectedMetal = 'ذهب';
                                    }
                                } elseif (strpos($text, 'فضة') !== false || strpos($text, 'silver') !== false) {
                                    $detectedMetal = 'فضة';
                                }

                                // استخراج العيار
                                if (preg_match('/(\d+)\s*(?:عيار|قيراط|K|k)/i', $price['purity_text'], $matches)) {
                                    $detectedPurity = $matches[1] . 'K';
                                } elseif (preg_match('/(\d+)/', $price['purity_text'], $matches)) {
                                    $number = $matches[1];
                                    if ($number >= 600) {
                                        $detectedPurity = $number;
                                    } elseif ($number <= 24) {
                                        $detectedPurity = $number . 'K';
                                    }
                                }

                                // تحديد الحالة
                                if ($detectedMetal !== 'غير محدد' && $detectedPurity !== 'غير محدد') {
                                    $status = 'قابل للمطابقة';
                                    $statusColor = 'green';
                                } elseif ($detectedMetal !== 'غير محدد') {
                                    $status = 'معدن محدد';
                                    $statusColor = 'yellow';
                                }
                            @endphp

                            <tr class="bg-white border-b hover:bg-gray-50">
                                <td class="px-6 py-4 font-medium text-gray-900">
                                    {{ $index + 1 }}
                                </td>
                                <td class="px-6 py-4 font-medium text-gray-900 max-w-xs">
                                    <div class="truncate" title="{{ $price['purity_text'] }}">
                                        {{ $price['purity_text'] }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 font-bold text-green-600">
                                    {{ number_format($price['price'], 2) }} ج.م
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        @if ($detectedMetal === 'ذهب')
                                            <div class="w-3 h-3 bg-yellow-400 rounded-full ml-2"></div>
                                        @elseif($detectedMetal === 'فضة')
                                            <div class="w-3 h-3 bg-gray-400 rounded-full ml-2"></div>
                                        @elseif($detectedMetal === 'جنيهات ذهبية')
                                            <div class="w-3 h-3 bg-orange-400 rounded-full ml-2"></div>
                                        @else
                                            <div class="w-3 h-3 bg-gray-300 rounded-full ml-2"></div>
                                        @endif
                                        {{ $detectedMetal }}
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    @if ($detectedPurity !== 'غير محدد')
                                        <span
                                            class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                            {{ $detectedPurity }}
                                        </span>
                                    @else
                                        <span class="text-gray-400">{{ $detectedPurity }}</span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 text-xs text-gray-500 max-w-xs">
                                    <div class="truncate" title="{{ $price['context'] }}">
                                        {{ $price['context'] }}
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        @if ($statusColor === 'green') bg-green-100 text-green-800
                                        @elseif($statusColor === 'yellow') bg-yellow-100 text-yellow-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ $status }}
                                    </span>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    لا توجد أسعار مستخرجة
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- تحليل إضافي -->
        @if (count($extractedPrices) > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- نطاقات الأسعار -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">توزيع الأسعار</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">أسعار منخفضة (أقل من 100 ج.م)</span>
                            <span class="font-medium">{{ count($stats['price_ranges']['low']) }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">أسعار متوسطة (100-1000 ج.م)</span>
                            <span class="font-medium">{{ count($stats['price_ranges']['medium']) }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">أسعار عالية (أكثر من 1000 ج.م)</span>
                            <span class="font-medium">{{ count($stats['price_ranges']['high']) }}</span>
                        </div>
                    </div>
                </div>

                <!-- ملاحظات -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-yellow-800 mb-4">ملاحظات مهمة</h4>
                    <ul class="text-sm text-yellow-700 space-y-2">
                        <li>• الأسعار المعروضة هي كما تم استخراجها من الموقع</li>
                        <li>• قد تحتوي على أسعار مكررة أو غير دقيقة</li>
                        <li>• يتم فلترة الأسعار عند الربط مع العيارات الموجودة</li>
                        <li>• الأسعار "القابلة للمطابقة" هي التي يمكن ربطها بعيارات النظام</li>
                    </ul>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
