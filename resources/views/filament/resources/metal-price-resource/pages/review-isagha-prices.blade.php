<x-filament-panels::page>
    <div class="space-y-6">
        <!-- إحصائيات سريعة في صف واحد -->
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            @php
                $totalPrices = count($prices);
                $increasedPrices = collect($prices)->where('direction', 'up')->count();
                $decreasedPrices = collect($prices)->where('direction', 'down')->count();
                $stablePrices = collect($prices)->where('direction', 'stable')->count();
            @endphp

            <!-- إجمالي الأسعار -->
            <div
                class="relative overflow-hidden bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 rounded-xl shadow-md border border-slate-200/50 dark:border-slate-700/50 hover:shadow-lg transition-all duration-300 group">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5"></div>
                <div class="relative p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-xs font-medium text-slate-600 dark:text-slate-400 mb-1">إجمالي الأسعار</p>
                            <p class="text-2xl font-bold text-slate-900 dark:text-white">{{ $totalPrices }}</p>
                        </div>
                        <div
                            class="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أسعار مرتفعة -->
            <div
                class="relative overflow-hidden bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-900/20 dark:to-green-900/30 rounded-xl shadow-md border border-emerald-200/50 dark:border-emerald-700/50 hover:shadow-lg transition-all duration-300 group">
                <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/10 to-green-500/10"></div>
                <div class="relative p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-xs font-medium text-emerald-700 dark:text-emerald-300 mb-1">أسعار مرتفعة</p>
                            <p class="text-2xl font-bold text-emerald-900 dark:text-emerald-100">{{ $increasedPrices }}
                            </p>
                            @if ($totalPrices > 0)
                                <p class="text-xs text-emerald-600 dark:text-emerald-400 mt-0.5">
                                    {{ round(($increasedPrices / $totalPrices) * 100, 1) }}% من الإجمالي</p>
                            @endif
                        </div>
                        <div
                            class="p-2 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أسعار منخفضة -->
            <div
                class="relative overflow-hidden bg-gradient-to-br from-rose-50 to-red-100 dark:from-rose-900/20 dark:to-red-900/30 rounded-xl shadow-md border border-rose-200/50 dark:border-rose-700/50 hover:shadow-lg transition-all duration-300 group">
                <div class="absolute inset-0 bg-gradient-to-br from-rose-500/10 to-red-500/10"></div>
                <div class="relative p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-xs font-medium text-rose-700 dark:text-rose-300 mb-1">أسعار منخفضة</p>
                            <p class="text-2xl font-bold text-rose-900 dark:text-rose-100">{{ $decreasedPrices }}</p>
                            @if ($totalPrices > 0)
                                <p class="text-xs text-rose-600 dark:text-rose-400 mt-0.5">
                                    {{ round(($decreasedPrices / $totalPrices) * 100, 1) }}% من الإجمالي</p>
                            @endif
                        </div>
                        <div
                            class="p-2 bg-gradient-to-br from-rose-500 to-red-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أسعار ثابتة -->
            <div
                class="relative overflow-hidden bg-gradient-to-br from-amber-50 to-yellow-100 dark:from-amber-900/20 dark:to-yellow-900/30 rounded-xl shadow-md border border-amber-200/50 dark:border-amber-700/50 hover:shadow-lg transition-all duration-300 group">
                <div class="absolute inset-0 bg-gradient-to-br from-amber-500/10 to-yellow-500/10"></div>
                <div class="relative p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <p class="text-xs font-medium text-amber-700 dark:text-amber-300 mb-1">أسعار ثابتة</p>
                            <p class="text-2xl font-bold text-amber-900 dark:text-amber-100">{{ $stablePrices }}</p>
                            @if ($totalPrices > 0)
                                <p class="text-xs text-amber-600 dark:text-amber-400 mt-0.5">
                                    {{ round(($stablePrices / $totalPrices) * 100, 1) }}% من الإجمالي</p>
                            @endif
                        </div>
                        <div
                            class="p-2 bg-gradient-to-br from-amber-500 to-yellow-600 rounded-lg shadow-md group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات عامة -->
        <div
            class="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 ml-2" fill="currentColor"
                    viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clip-rule="evenodd"></path>
                </svg>
                <div class="text-sm text-primary-700 dark:text-primary-300">
                    <p class="font-medium">مراجعة الأسعار المجلبة من iSagha</p>
                    <p>راجع الأسعار التالية قبل الموافقة على حفظها. سيتم تطبيق منطق ذكي مع الاحتفاظ بالتاريخ الكامل:</p>
                    <ul class="mt-2 space-y-1 text-xs">
                        <li><span class="font-medium text-emerald-600">• الأسعار الجديدة:</span> ستتم إضافتها
                            (معادن/عيارات جديدة)</li>
                        <li><span class="font-medium text-blue-600">• الأسعار المتغيرة:</span> ستتم إضافتها (سجل جديد +
                            الاحتفاظ بالقديم)</li>
                        <li><span class="font-medium text-gray-600">• الأسعار المطابقة:</span> ستتم تجاهلها (لا تغيير)
                        </li>
                    </ul>
                    <p class="mt-2 text-xs bg-blue-50 p-2 rounded">
                        <strong>💾 ميزة:</strong> جميع الأسعار تُحفظ كسجلات جديدة دون فقدان التاريخ السابق
                    </p>
                    <p class="mt-2">تاريخ الجلب: <strong>{{ now()->format('Y-m-d H:i') }}</strong></p>
                </div>
            </div>
        </div>

        <!-- جدول الأسعار بتصميم عصري -->
        <div
            class="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-2xl rounded-3xl overflow-hidden border border-slate-200/50 dark:border-slate-700/50">
            <!-- رأس الجدول -->
            <div
                class="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 px-8 py-6 border-b border-slate-200/50 dark:border-slate-700/50">
                <div class="flex items-center justify-between">
                    <div>
                        <h3
                            class="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                            الأسعار المجلبة من iSagha
                        </h3>
                        <p class="mt-2 text-sm text-slate-600 dark:text-slate-400">
                            جميع الأسعار بالجنيه المصري (ج.م) لكل جرام • الأسعار المتغيرة مميزة بألوان واضحة
                        </p>
                    </div>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse">
                        </div>
                        <div class="w-3 h-3 bg-gradient-to-r from-rose-400 to-red-500 rounded-full animate-pulse"></div>
                        <div class="w-3 h-3 bg-gradient-to-r from-amber-400 to-yellow-500 rounded-full animate-pulse">
                        </div>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full text-sm text-right">
                    <!-- رأس الجدول المحسن -->
                    <thead class="bg-gradient-to-r from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800">
                        <tr>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                                        </path>
                                    </svg>
                                    نوع المعدن
                                </div>
                            </th>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    العيار
                                </div>
                            </th>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                        </path>
                                    </svg>
                                    السعر الجديد
                                </div>
                            </th>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    السعر الحالي
                                </div>
                            </th>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z">
                                        </path>
                                    </svg>
                                    الفرق
                                </div>
                            </th>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                    نسبة التغيير
                                </div>
                            </th>
                            <th scope="col"
                                class="px-6 py-4 text-xs font-bold text-slate-700 dark:text-slate-300 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                    الاتجاه
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-slate-200/50 dark:divide-slate-700/50">
                        @forelse($prices as $index => $price)
                            @php
                                $isChanged = $price['direction'] !== 'stable';
                                $isEven = $index % 2 === 0;

                                // ألوان عصرية ومميزة للتغييرات
                                if ($price['direction'] === 'up') {
                                    $rowClass =
                                        'bg-gradient-to-r from-emerald-50/80 via-green-50/60 to-emerald-50/80 dark:from-emerald-900/20 dark:via-green-900/15 dark:to-emerald-900/20 border-l-4 border-emerald-400 hover:from-emerald-100/90 hover:via-green-100/70 hover:to-emerald-100/90 dark:hover:from-emerald-900/30 dark:hover:via-green-900/25 dark:hover:to-emerald-900/30';
                                    $glowClass = 'shadow-emerald-200/50 dark:shadow-emerald-900/30';
                                } elseif ($price['direction'] === 'down') {
                                    $rowClass =
                                        'bg-gradient-to-r from-rose-50/80 via-red-50/60 to-rose-50/80 dark:from-rose-900/20 dark:via-red-900/15 dark:to-rose-900/20 border-l-4 border-rose-400 hover:from-rose-100/90 hover:via-red-100/70 hover:to-rose-100/90 dark:hover:from-rose-900/30 dark:hover:via-red-900/25 dark:hover:to-rose-900/30';
                                    $glowClass = 'shadow-rose-200/50 dark:shadow-rose-900/30';
                                } else {
                                    $rowClass = $isEven
                                        ? 'bg-gradient-to-r from-slate-50/50 to-white dark:from-slate-800/50 dark:to-slate-800 hover:from-slate-100/70 hover:to-slate-50 dark:hover:from-slate-700/70 dark:hover:to-slate-700'
                                        : 'bg-gradient dark:bg-slate-800 hover:bg-slate-50/70 dark:hover:bg-slate-700/70';
                                    $glowClass = 'shadow-slate-200/30 dark:shadow-slate-700/30';
                                }
                            @endphp
                            <tr
                                class="{{ $rowClass }} {{ $glowClass }} transition-all duration-300 hover:shadow-lg hover:scale-[1.01] group">
                                <!-- نوع المعدن -->
                                <td class="px-6 py-5">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        @if ($price['metal_type'] === 'gold')
                                            <div class="relative">
                                                <div
                                                    class="w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full shadow-lg">
                                                </div>
                                                <div
                                                    class="absolute inset-0 w-4 h-4 bg-gradient-to-br from-yellow-400 to-amber-500 rounded-full animate-ping opacity-20">
                                                </div>
                                            </div>
                                        @elseif($price['metal_type'] === 'silver')
                                            <div class="relative">
                                                <div
                                                    class="w-4 h-4 bg-gradient-to-br from-slate-400 to-gray-500 rounded-full shadow-lg">
                                                </div>
                                                <div
                                                    class="absolute inset-0 w-4 h-4 bg-gradient-to-br from-slate-400 to-gray-500 rounded-full animate-ping opacity-20">
                                                </div>
                                            </div>
                                        @elseif($price['metal_type'] === 'gold_coin')
                                            <div class="relative">
                                                <div
                                                    class="w-4 h-4 bg-gradient-to-br from-orange-400 to-amber-600 rounded-full shadow-lg">
                                                </div>
                                                <div
                                                    class="absolute inset-0 w-4 h-4 bg-gradient-to-br from-orange-400 to-amber-600 rounded-full animate-ping opacity-20">
                                                </div>
                                            </div>
                                        @endif
                                        <div class="flex flex-col">
                                            <span
                                                class="font-bold text-slate-900 dark:text-white text-base group-hover:text-slate-700 dark:group-hover:text-slate-200 transition-colors">
                                                {{ $price['metal_type_name'] }}
                                            </span>
                                            @if ($isChanged)
                                                <span
                                                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-bold mt-1 {{ $price['direction'] === 'up' ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200' : 'bg-rose-100 text-rose-800 dark:bg-rose-900/50 dark:text-rose-200' }}">
                                                    <div
                                                        class="w-1.5 h-1.5 rounded-full mr-1 {{ $price['direction'] === 'up' ? 'bg-emerald-500' : 'bg-rose-500' }} animate-pulse">
                                                    </div>
                                                    متغير
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                </td>

                                <!-- العيار -->
                                <td class="px-6 py-5">
                                    <span
                                        class="inline-flex items-center px-3 py-1.5 rounded-xl text-sm font-bold bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 dark:from-indigo-900/50 dark:to-purple-900/50 dark:text-indigo-200 shadow-sm group-hover:shadow-md transition-shadow">
                                        <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        {{ $price['purity_name'] }}
                                    </span>
                                </td>
                                <!-- السعر الجديد -->
                                <td class="px-6 py-5">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <div class="flex flex-col">
                                            <div class="flex items-center">
                                                <span
                                                    class="text-2xl font-black {{ $isChanged ? ($price['direction'] === 'up' ? 'text-emerald-600 dark:text-emerald-400' : 'text-rose-600 dark:text-rose-400') : 'text-slate-900 dark:text-white' }} group-hover:scale-105 transition-transform">
                                                    {{ $price['formatted_new_price'] }}
                                                </span>
                                                <span
                                                    class="text-sm font-medium text-slate-500 dark:text-slate-400 mr-1">ج.م</span>
                                            </div>
                                            @if ($isChanged)
                                                <div class="flex items-center mt-1">
                                                    <div
                                                        class="flex items-center px-2 py-0.5 rounded-full text-xs font-bold {{ $price['direction'] === 'up' ? 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/50 dark:text-emerald-300' : 'bg-rose-100 text-rose-700 dark:bg-rose-900/50 dark:text-rose-300' }}">
                                                        @if ($price['direction'] === 'up')
                                                            <svg class="w-3 h-3 ml-1" fill="currentColor"
                                                                viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd"
                                                                    d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                                                                    clip-rule="evenodd"></path>
                                                            </svg>
                                                            جديد مرتفع
                                                        @else
                                                            <svg class="w-3 h-3 ml-1" fill="currentColor"
                                                                viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd"
                                                                    d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z"
                                                                    clip-rule="evenodd"></path>
                                                            </svg>
                                                            جديد منخفض
                                                        @endif
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <!-- السعر الحالي -->
                                <td class="px-6 py-5">
                                    @if ($price['current_price'] > 0)
                                        <div class="flex items-center">
                                            <span
                                                class="text-lg font-bold text-slate-600 dark:text-slate-300">{{ $price['formatted_current_price'] }}</span>
                                            <span
                                                class="text-sm font-medium text-slate-500 dark:text-slate-400 mr-1">ج.م</span>
                                        </div>
                                        <span class="text-xs text-slate-500 dark:text-slate-400">السعر السابق</span>
                                    @else
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 text-slate-400 ml-1" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                                                </path>
                                            </svg>
                                            <span class="text-slate-400 dark:text-slate-500 italic font-medium">غير
                                                متاح</span>
                                        </div>
                                    @endif
                                </td>
                                <!-- الفرق -->
                                <td class="px-6 py-5">
                                    @if ($price['difference'] > 0)
                                        <div class="flex flex-col items-start">
                                            <div
                                                class="flex items-center px-3 py-1.5 rounded-xl bg-gradient-to-r from-emerald-100 to-green-100 dark:from-emerald-900/50 dark:to-green-900/50 shadow-sm">
                                                <svg class="w-4 h-4 text-emerald-600 dark:text-emerald-400 ml-1"
                                                    fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                                                        clip-rule="evenodd"></path>
                                                </svg>
                                                <span
                                                    class="font-black text-emerald-700 dark:text-emerald-300">+{{ $price['formatted_difference'] }}</span>
                                                <span
                                                    class="text-xs font-bold text-emerald-600 dark:text-emerald-400 mr-1">ج.م</span>
                                            </div>
                                        </div>
                                    @elseif($price['difference'] < 0)
                                        <div class="flex flex-col items-start">
                                            <div
                                                class="flex items-center px-3 py-1.5 rounded-xl bg-gradient-to-r from-rose-100 to-red-100 dark:from-rose-900/50 dark:to-red-900/50 shadow-sm">
                                                <svg class="w-4 h-4 text-rose-600 dark:text-rose-400 ml-1"
                                                    fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z"
                                                        clip-rule="evenodd"></path>
                                                </svg>
                                                <span
                                                    class="font-black text-rose-700 dark:text-rose-300">{{ $price['formatted_difference'] }}</span>
                                                <span
                                                    class="text-xs font-bold text-rose-600 dark:text-rose-400 mr-1">ج.م</span>
                                            </div>
                                        </div>
                                    @else
                                        <div
                                            class="flex items-center px-3 py-1.5 rounded-xl bg-gradient-to-r from-slate-100 to-gray-100 dark:from-slate-700 dark:to-gray-700">
                                            <svg class="w-4 h-4 text-slate-500 ml-1" fill="currentColor"
                                                viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="font-bold text-slate-600 dark:text-slate-400">0.00 ج.م</span>
                                        </div>
                                    @endif
                                </td>

                                <!-- نسبة التغيير -->
                                <td class="px-6 py-5">
                                    @if ($price['difference_percent'] > 0)
                                        <div
                                            class="flex items-center px-3 py-2 rounded-xl bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/30 dark:to-green-900/30 border border-emerald-200 dark:border-emerald-800">
                                            <div class="flex flex-col items-center">
                                                <span
                                                    class="text-xl font-black text-emerald-600 dark:text-emerald-400">+{{ $price['formatted_difference_percent'] }}%</span>
                                                <span
                                                    class="text-xs font-bold text-emerald-500 dark:text-emerald-500">زيادة</span>
                                            </div>
                                        </div>
                                    @elseif($price['difference_percent'] < 0)
                                        <div
                                            class="flex items-center px-3 py-2 rounded-xl bg-gradient-to-r from-rose-50 to-red-50 dark:from-rose-900/30 dark:to-red-900/30 border border-rose-200 dark:border-rose-800">
                                            <div class="flex flex-col items-center">
                                                <span
                                                    class="text-xl font-black text-rose-600 dark:text-rose-400">{{ $price['formatted_difference_percent'] }}%</span>
                                                <span
                                                    class="text-xs font-bold text-rose-500 dark:text-rose-500">نقص</span>
                                            </div>
                                        </div>
                                    @else
                                        <div
                                            class="flex items-center px-3 py-2 rounded-xl bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-700 dark:to-gray-700">
                                            <div class="flex flex-col items-center">
                                                <span
                                                    class="text-lg font-bold text-slate-600 dark:text-slate-400">0.0%</span>
                                                <span
                                                    class="text-xs font-medium text-slate-500 dark:text-slate-500">ثابت</span>
                                            </div>
                                        </div>
                                    @endif
                                </td>

                                <!-- الاتجاه -->
                                <td class="px-6 py-5">
                                    @if ($price['direction'] === 'up')
                                        <div
                                            class="inline-flex items-center px-4 py-2 rounded-2xl bg-gradient-to-r from-emerald-500 to-green-600 text-white shadow-lg shadow-emerald-200 dark:shadow-emerald-900/50 group-hover:shadow-xl transition-shadow">
                                            <svg class="w-5 h-5 ml-2 animate-bounce" fill="currentColor"
                                                viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 5.414V17a1 1 0 11-2 0V5.414L6.707 7.707a1 1 0 01-1.414 0z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="font-black text-sm">ارتفاع</span>
                                        </div>
                                    @elseif($price['direction'] === 'down')
                                        <div
                                            class="inline-flex items-center px-4 py-2 rounded-2xl bg-gradient-to-r from-rose-500 to-red-600 text-white shadow-lg shadow-rose-200 dark:shadow-rose-900/50 group-hover:shadow-xl transition-shadow">
                                            <svg class="w-5 h-5 ml-2 animate-bounce" fill="currentColor"
                                                viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 14.586V3a1 1 0 012 0v11.586l2.293-2.293a1 1 0 011.414 0z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="font-black text-sm">انخفاض</span>
                                        </div>
                                    @else
                                        <div
                                            class="inline-flex items-center px-4 py-2 rounded-2xl bg-gradient-to-r from-slate-400 to-gray-500 text-white shadow-lg shadow-slate-200 dark:shadow-slate-700/50">
                                            <svg class="w-5 h-5 ml-2" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                            <span class="font-black text-sm">ثابت</span>
                                        </div>
                                    @endif
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    لا توجد أسعار للعرض
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- مفتاح الألوان العصري -->
        <div
            class="bg-gradient-to-r from-slate-50 via-white to-slate-50 dark:from-slate-800 dark:via-slate-900 dark:to-slate-800 rounded-2xl p-6 border border-slate-200/50 dark:border-slate-700/50 shadow-lg">
            <div class="flex items-center mb-4">
                <svg class="w-5 h-5 text-slate-600 dark:text-slate-400 ml-2" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                    </path>
                </svg>
                <h4
                    class="text-lg font-bold bg-gradient-to-r from-slate-900 to-slate-700 dark:from-white dark:to-slate-300 bg-clip-text text-transparent">
                    دليل الألوان والرموز
                </h4>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- أسعار مرتفعة -->
                <div
                    class="flex items-center p-4 rounded-xl bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 border border-emerald-200/50 dark:border-emerald-700/50 hover:shadow-md transition-shadow">
                    <div class="relative ml-3">
                        <div class="w-6 h-6 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full shadow-lg">
                        </div>
                        <div
                            class="absolute inset-0 w-6 h-6 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-ping opacity-20">
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <span class="font-bold text-emerald-700 dark:text-emerald-300">أسعار مرتفعة</span>
                        <span class="text-xs text-emerald-600 dark:text-emerald-400">خلفية خضراء + حدود ملونة</span>
                    </div>
                </div>

                <!-- أسعار منخفضة -->
                <div
                    class="flex items-center p-4 rounded-xl bg-gradient-to-r from-rose-50 to-red-50 dark:from-rose-900/20 dark:to-red-900/20 border border-rose-200/50 dark:border-rose-700/50 hover:shadow-md transition-shadow">
                    <div class="relative ml-3">
                        <div class="w-6 h-6 bg-gradient-to-r from-rose-400 to-red-500 rounded-full shadow-lg"></div>
                        <div
                            class="absolute inset-0 w-6 h-6 bg-gradient-to-r from-rose-400 to-red-500 rounded-full animate-ping opacity-20">
                        </div>
                    </div>
                    <div class="flex flex-col">
                        <span class="font-bold text-rose-700 dark:text-rose-300">أسعار منخفضة</span>
                        <span class="text-xs text-rose-600 dark:text-rose-400">خلفية حمراء + حدود ملونة</span>
                    </div>
                </div>

                <!-- أسعار ثابتة -->
                <div
                    class="flex items-center p-4 rounded-xl bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-700 dark:to-gray-700 border border-slate-200/50 dark:border-slate-600/50 hover:shadow-md transition-shadow">
                    <div class="relative ml-3">
                        <div class="w-6 h-6 bg-gradient-to-r from-slate-400 to-gray-500 rounded-full shadow-lg"></div>
                    </div>
                    <div class="flex flex-col">
                        <span class="font-bold text-slate-700 dark:text-slate-300">أسعار ثابتة</span>
                        <span class="text-xs text-slate-600 dark:text-slate-400">خلفية محايدة + لا تغيير</span>
                    </div>
                </div>
            </div>

            <!-- معلومات إضافية -->
            <div
                class="mt-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200/50 dark:border-blue-700/50">
                <div class="flex items-center">
                    <svg class="w-4 h-4 text-blue-600 dark:text-blue-400 ml-2" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm font-medium text-blue-700 dark:text-blue-300">
                        الصفوف المتغيرة تحتوي على تأثيرات بصرية إضافية مثل الحدود الملونة والظلال والرسوم المتحركة
                    </span>
                </div>
            </div>
        </div>

        <!-- ملاحظات إضافية -->
        @if (count($prices) > 0)
            <div
                class="bg-warning-50 dark:bg-warning-900/20 border border-warning-200 dark:border-warning-800 rounded-lg p-4">
                <div class="flex items-start">
                    <svg class="w-5 h-5 text-warning-600 dark:text-warning-400 ml-2 mt-0.5 flex-shrink-0"
                        fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-sm text-warning-700 dark:text-warning-300">
                        <p class="font-semibold text-base mb-2">تنبيه مهم قبل الحفظ:</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div class="space-y-2">
                                <p class="font-medium">المنطق الذكي للحفظ:</p>
                                <ul class="list-disc list-inside space-y-1 text-sm">
                                    <li><span class="text-emerald-600 font-medium">إضافة جديدة:</span> المعادن/العيارات
                                        غير الموجودة
                                    </li>
                                    <li><span class="text-blue-600 font-medium">إضافة متغيرة:</span> سجل جديد +
                                        الاحتفاظ بالقديم</li>
                                    <li><span class="text-gray-600 font-medium">تجاهل:</span> الأسعار المطابقة</li>
                                </ul>
                            </div>
                            <div class="space-y-2">
                                <p class="font-medium">مميزات النظام:</p>
                                <ul class="list-disc list-inside space-y-1 text-sm">
                                    <li>الاحتفاظ بالتاريخ الكامل للأسعار</li>
                                    <li>عدم فقدان أي بيانات تاريخية</li>
                                    <li>تحسين الأداء بتجنب العمليات غير الضرورية</li>
                                    <li>ستظهر الأسعار الجديدة فوراً في الموقع</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3 p-3 bg-info-100 dark:bg-info-900/30 rounded-md">
                            <p class="text-xs font-medium">💾 ميزة: جميع الأسعار تُحفظ كسجلات جديدة مع الاحتفاظ
                                بالتاريخ الكامل دون فقدان أي بيانات.</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>
