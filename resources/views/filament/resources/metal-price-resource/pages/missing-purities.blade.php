<x-filament-panels::page>
    <div class="space-y-6">
        <!-- معلومات عامة -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-yellow-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div class="text-sm text-yellow-700">
                    <p class="font-medium">عيارات مفقودة من موقع iSagha</p>
                    <p>تم العثور على عيارات في موقع iSagha غير موجودة في النظام. يمكنك إضافتها لتتمكن من جلب أسعارها.</p>
                    <p class="mt-1">إجمالي العيارات المفقودة: <strong>{{ count($missingPurities) }}</strong> عيار</p>
                </div>
            </div>
        </div>

        <!-- جدول العيارات المفقودة -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">العيارات المفقودة</h3>
                <p class="mt-1 text-sm text-gray-600">العيارات التي تم العثور عليها في موقع iSagha ولكنها غير موجودة في النظام</p>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-right text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">نوع المعدن</th>
                            <th scope="col" class="px-6 py-3">العيار المكتشف</th>
                            <th scope="col" class="px-6 py-3">الاسم المقترح</th>
                            <th scope="col" class="px-6 py-3">الاسم العربي المقترح</th>
                            <th scope="col" class="px-6 py-3">السعر الموجود</th>
                            <th scope="col" class="px-6 py-3">السياق</th>
                            <th scope="col" class="px-6 py-3">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($missingPurities as $index => $missing)
                            <tr class="bg-white border-b hover:bg-gray-50">
                                <td class="px-6 py-4 font-medium text-gray-900">
                                    <div class="flex items-center">
                                        @if($missing['metal_type'] === 'gold')
                                            <div class="w-3 h-3 bg-yellow-400 rounded-full ml-2"></div>
                                            ذهب
                                        @elseif($missing['metal_type'] === 'silver')
                                            <div class="w-3 h-3 bg-gray-400 rounded-full ml-2"></div>
                                            فضة
                                        @elseif($missing['metal_type'] === 'gold_coin')
                                            <div class="w-3 h-3 bg-orange-400 rounded-full ml-2"></div>
                                            جنيهات ذهبية
                                        @else
                                            <div class="w-3 h-3 bg-blue-400 rounded-full ml-2"></div>
                                            {{ $missing['metal_type'] }}
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                        {{ $missing['detected_purity'] }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 font-medium">
                                    {{ $missing['suggested_name'] }}
                                </td>
                                <td class="px-6 py-4 font-medium">
                                    {{ $missing['suggested_name_ar'] }}
                                </td>
                                <td class="px-6 py-4 font-bold text-green-600">
                                    {{ number_format($missing['price'], 2) }} ج.م
                                </td>
                                <td class="px-6 py-4 text-xs text-gray-500 max-w-xs truncate">
                                    {{ $missing['context'] }}
                                </td>
                                <td class="px-6 py-4">
                                    <button 
                                        wire:click="addSinglePurity({{ $index }})"
                                        class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                    >
                                        <svg class="w-3 h-3 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        إضافة
                                    </button>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                    لا توجد عيارات مفقودة
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- ملاحظات إضافية -->
        @if(count($missingPurities) > 0)
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-blue-400 ml-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="text-sm text-blue-700">
                        <p class="font-medium">معلومات مهمة:</p>
                        <ul class="list-disc list-inside mt-1 space-y-1">
                            <li>يمكنك إضافة العيارات واحداً تلو الآخر أو إضافة الجميع مرة واحدة</li>
                            <li>بعد إضافة العيارات، ستتمكن من جلب أسعارها من موقع iSagha</li>
                            <li>يمكنك تعديل أسماء العيارات لاحقاً من إدارة أنواع المعادن</li>
                            <li>العيارات المضافة ستكون نشطة بشكل افتراضي</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                @php
                    $goldCount = collect($missingPurities)->where('metal_type', 'gold')->count();
                    $silverCount = collect($missingPurities)->where('metal_type', 'silver')->count();
                    $coinCount = collect($missingPurities)->where('metal_type', 'gold_coin')->count();
                @endphp
                
                @if($goldCount > 0)
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-yellow-400 rounded-full ml-2"></div>
                            <div>
                                <p class="text-sm font-medium text-yellow-800">عيارات الذهب</p>
                                <p class="text-lg font-bold text-yellow-900">{{ $goldCount }}</p>
                            </div>
                        </div>
                    </div>
                @endif
                
                @if($silverCount > 0)
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-gray-400 rounded-full ml-2"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-800">عيارات الفضة</p>
                                <p class="text-lg font-bold text-gray-900">{{ $silverCount }}</p>
                            </div>
                        </div>
                    </div>
                @endif
                
                @if($coinCount > 0)
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-orange-400 rounded-full ml-2"></div>
                            <div>
                                <p class="text-sm font-medium text-orange-800">الجنيهات الذهبية</p>
                                <p class="text-lg font-bold text-orange-900">{{ $coinCount }}</p>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif
    </div>
</x-filament-panels::page>
