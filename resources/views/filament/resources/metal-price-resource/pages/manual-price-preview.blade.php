<x-filament-panels::page>
    <div class="space-y-6">
        {{-- ملخص البيانات المُدخلة --}}
        <div
            class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-6 border border-green-200 dark:border-green-700">
            <h3 class="text-lg font-semibold text-green-900 dark:text-green-100 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                    </path>
                </svg>
                📊 البيانات المُدخلة
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">سعر عيار 21</div>
                    <div class="text-xl font-bold text-green-600 dark:text-green-400">
                        {{ number_format($inputData['gold_21_price'] ?? 0, 2) }} ج.م
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">نوع الخصم</div>
                    <div class="text-lg font-semibold text-gray-700 dark:text-gray-300">
                        {{ ($inputData['discount_type'] ?? '') === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت' }}
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">قيمة الخصم</div>
                    <div class="text-lg font-semibold text-blue-600 dark:text-blue-400">
                        {{ $inputData['discount_value'] ?? 0 }}{{ ($inputData['discount_type'] ?? '') === 'percentage' ? '%' : ' ج.م' }}
                    </div>
                </div>
            </div>
        </div>

        {{-- جدول الأسعار المحسوبة --}}
        <div
            class="overflow-hidden bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 rounded-xl">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                    💰 الأسعار المحسوبة
                </h3>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                العيار
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                سعر البيع (ج.م)
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                سعر الشراء (ج.م)
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الفرق (ج.م)
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach ($prices as $price)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200">
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span
                                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                        {{ is_array($price) ? $price['purity'] : $price->purity }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span class="text-lg font-bold text-green-600 dark:text-green-400">
                                        {{ is_array($price) ? $price['formatted_sell'] : $price->formatted_sell }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span class="text-lg font-bold text-red-600 dark:text-red-400">
                                        {{ is_array($price) ? $price['formatted_buy'] : $price->formatted_buy }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                    <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                        {{ is_array($price) ? $price['formatted_discount'] : $price->formatted_discount }}
                                    </span>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        {{-- ملخص إجمالي --}}
        <div
            class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
            <h4 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 00-2 2h-2a2 2 0 01-2-2z">
                    </path>
                </svg>
                📈 ملخص إجمالي
            </h4>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي قيم البيع</div>
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        @php
                            $totalSell = 0;
                            foreach ($prices as $price) {
                                $totalSell += is_array($price) ? $price['sell_price'] : $price->sell_price;
                            }
                        @endphp
                        {{ number_format($totalSell, 2) }} ج.م
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي قيم الشراء</div>
                    <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                        @php
                            $totalBuy = 0;
                            foreach ($prices as $price) {
                                $totalBuy += is_array($price) ? $price['buy_price'] : $price->buy_price;
                            }
                        @endphp
                        {{ number_format($totalBuy, 2) }} ج.م
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الخصم</div>
                    <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">
                        {{ number_format($totalSell - $totalBuy, 2) }} ج.م
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">عدد العيارات</div>
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ count($prices) }} عيار
                    </div>
                </div>
            </div>
        </div>

        {{-- ملاحظة --}}
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-800 dark:text-yellow-200">
                        <strong>ملاحظة:</strong> هذه معاينة للأسعار المحسوبة. استخدم الأزرار في الأعلى لحفظ الأسعار أو
                        تعديل البيانات أو إلغاء العملية.
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
