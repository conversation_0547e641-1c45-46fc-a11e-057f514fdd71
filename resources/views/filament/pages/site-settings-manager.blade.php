<x-filament-panels::page>
    <div class="space-y-6">
        <div class="p-6 bg-white rounded-xl shadow">
            <h2 class="text-xl font-bold mb-4">{{ __('إدارة إعدادات الموقع') }}</h2>
            <p class="text-gray-500 mb-6">{{ __('يمكنك استيراد وتصدير إعدادات الموقع من هنا، أو إعادة تعيينها إلى القيم الافتراضية.') }}</p>
            
            {{ $this->form }}
            
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium mb-2">{{ __('ملاحظات مهمة') }}</h3>
                <ul class="list-disc list-inside space-y-2 text-gray-600">
                    <li>{{ __('عند تصدير الإعدادات، سيتم إخفاء كلمات المرور الحساسة.') }}</li>
                    <li>{{ __('عند استيراد الإعدادات، سيتم استبدال جميع الإعدادات الحالية.') }}</li>
                    <li>{{ __('يمكنك تعديل ملف الإعدادات المصدر قبل استيراده مرة أخرى.') }}</li>
                    <li>{{ __('إعادة تعيين الإعدادات ستعيدها إلى القيم الافتراضية ولا يمكن التراجع عن هذا الإجراء.') }}</li>
                </ul>
            </div>
        </div>
        
        <div class="p-6 bg-white rounded-xl shadow">
            <h2 class="text-xl font-bold mb-4">{{ __('روابط سريعة') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a href="{{ route('filament.admin.resources.site-settings.index') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition flex items-center">
                    <div class="mr-4 bg-primary-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ __('تعديل الإعدادات') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('تعديل إعدادات الموقع الأساسية') }}</p>
                    </div>
                </a>
                
                <a href="{{ route('home') }}" target="_blank" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition flex items-center">
                    <div class="mr-4 bg-green-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ __('معاينة الموقع') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('معاينة التغييرات على الموقع') }}</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</x-filament-panels::page>
