<div class="space-y-6">
    {{-- جدول الأسعار المحسوبة --}}
    <div class="overflow-hidden bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10 rounded-xl">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                💰 الأسعار المحسوبة
            </h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            العيار
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            سعر البيع (ج.م)
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            سعر الشراء (ج.م)
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الفرق (ج.م)
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($prices as $price)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    {{ $price->purity }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="text-sm font-semibold text-green-600 dark:text-green-400">
                                    {{ $price->formatted_sell }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="text-sm font-semibold text-red-600 dark:text-red-400">
                                    {{ $price->formatted_buy }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <span class="text-sm font-medium text-gray-600 dark:text-gray-400">
                                    {{ $price->formatted_discount }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    {{-- ملخص إجمالي --}}
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-700">
        <h4 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-4">
            📈 ملخص إجمالي
        </h4>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي قيم البيع</div>
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                    {{ number_format($prices->sum('sell_price'), 2) }} ج.م
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي قيم الشراء</div>
                <div class="text-2xl font-bold text-red-600 dark:text-red-400">
                    {{ number_format($prices->sum('buy_price'), 2) }} ج.م
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">إجمالي الخصم</div>
                <div class="text-2xl font-bold text-gray-600 dark:text-gray-400">
                    {{ number_format($prices->sum('discount_amount'), 2) }} ج.م
                </div>
            </div>
        </div>
        
        <div class="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
            <div class="flex justify-between items-center">
                <span class="text-sm font-medium text-blue-900 dark:text-blue-100">عدد العيارات المحدثة:</span>
                <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $prices->count() }} عيار</span>
            </div>
        </div>
    </div>

    {{-- ملاحظة --}}
    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>ملاحظة:</strong> هذه معاينة فقط للأسعار المحسوبة. اضغط "حفظ الأسعار" لتطبيق التحديثات على قاعدة البيانات.
                </p>
            </div>
        </div>
    </div>
</div>
