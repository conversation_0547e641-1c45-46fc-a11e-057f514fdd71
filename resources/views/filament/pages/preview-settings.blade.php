<x-filament-panels::page>
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-1 space-y-6">
            {{ $this->form }}
        </div>
        
        <div class="lg:col-span-2">
            <div class="p-6 bg-white rounded-xl shadow">
                <h2 class="text-xl font-bold mb-4">{{ __('معاينة') }}</h2>
                <p class="text-gray-500 mb-6">{{ __('معاينة تأثير الإعدادات على الموقع. قم بتعديل الإعدادات في اليسار لرؤية التغييرات مباشرة.') }}</p>
                
                <div class="border rounded-lg overflow-hidden">
                    <div class="bg-gray-100 px-4 py-2 border-b flex justify-between items-center">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <div class="w-3 h-3 rounded-full bg-red-500"></div>
                            <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                            <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        </div>
                        <div class="text-sm text-gray-500">{{ __('معاينة') }} - {{ $previewData['site_name'] }}</div>
                    </div>
                    
                    <div class="p-4 bg-white">
                        @if($previewData['selectedPreview'] === 'header')
                            <div class="bg-gray-900 text-white p-4 rounded-lg">
                                <div class="container mx-auto">
                                    <!-- Top Bar -->
                                    <div class="flex justify-between items-center py-2 border-b border-gray-800 mb-4">
                                        <div class="flex items-center space-x-4 space-x-reverse">
                                            <a href="#" class="text-sm hover:text-white/80">
                                                <i class="fas fa-phone-alt ml-1"></i> {{ $previewData['contact_phone'] }}
                                            </a>
                                            <a href="#" class="text-sm hover:text-white/80">
                                                <i class="fas fa-envelope ml-1"></i> {{ $previewData['contact_email'] }}
                                            </a>
                                        </div>
                                        <div class="flex items-center space-x-4 space-x-reverse">
                                            <div class="relative dropdown-container">
                                                <button class="text-sm dropdown-toggle flex items-center">
                                                    <span class="ml-1">العربية</span>
                                                    <i class="fas fa-chevron-down text-xs"></i>
                                                </button>
                                            </div>
                                            <div class="relative dropdown-container">
                                                <button class="text-sm dropdown-toggle flex items-center">
                                                    <span class="ml-1">ج.م</span>
                                                    <i class="fas fa-chevron-down text-xs"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Main Header -->
                                    <div class="flex flex-wrap justify-between items-center py-4">
                                        <!-- Logo -->
                                        <a href="#" class="flex items-center">
                                            <img src="{{ asset('images/logo.png') }}" alt="{{ $previewData['site_name'] }}" class="h-12">
                                            <div class="mr-3">
                                                <h1 class="text-2xl font-bold text-yellow-500">{{ $previewData['site_name'] }}</h1>
                                                <p class="text-xs text-gray-500">{{ $previewData['site_description'] }}</p>
                                            </div>
                                        </a>
                                        
                                        <!-- Search -->
                                        <div class="w-full md:w-auto mt-4 md:mt-0 order-3 md:order-2">
                                            <div class="relative">
                                                <input type="text" placeholder="ابحث عن منتج..." class="w-full md:w-64 bg-gray-800 text-white rounded-full py-2 px-4 pr-10 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                                                <button class="absolute left-3 top-2.5 text-gray-400 hover:text-white">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- Icons -->
                                        <div class="flex items-center space-x-6 space-x-reverse order-2 md:order-3">
                                            <a href="#" class="text-white hover:text-yellow-500 transition duration-300">
                                                <i class="far fa-heart text-xl"></i>
                                            </a>
                                            <a href="#" class="text-white hover:text-yellow-500 transition duration-300 relative">
                                                <i class="fas fa-shopping-cart text-xl"></i>
                                                <span class="absolute -top-2 -right-2 bg-yellow-500 text-black text-xs w-5 h-5 rounded-full flex items-center justify-center">0</span>
                                            </a>
                                            <a href="#" class="text-white hover:text-yellow-500 transition duration-300">
                                                <i class="far fa-user text-xl"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @elseif($previewData['selectedPreview'] === 'footer')
                            <div class="bg-gray-900 text-white p-4 rounded-lg">
                                <div class="container mx-auto">
                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-8 py-8">
                                        <!-- About -->
                                        <div>
                                            <h3 class="text-xl font-bold mb-4">{{ __('About Us') }}</h3>
                                            <p class="text-gray-400 mb-4">{{ $previewData['site_description'] }}</p>
                                            <div class="flex space-x-4 space-x-reverse">
                                                @if($previewData['facebook_url'])
                                                <a href="{{ $previewData['facebook_url'] }}" target="_blank" class="text-gray-400 hover:text-white transition duration-300">
                                                    <i class="fab fa-facebook-f"></i>
                                                </a>
                                                @endif
                                                @if($previewData['twitter_url'])
                                                <a href="{{ $previewData['twitter_url'] }}" target="_blank" class="text-gray-400 hover:text-white transition duration-300">
                                                    <i class="fab fa-twitter"></i>
                                                </a>
                                                @endif
                                                @if($previewData['instagram_url'])
                                                <a href="{{ $previewData['instagram_url'] }}" target="_blank" class="text-gray-400 hover:text-white transition duration-300">
                                                    <i class="fab fa-instagram"></i>
                                                </a>
                                                @endif
                                            </div>
                                        </div>
                                        
                                        <!-- Contact -->
                                        <div>
                                            <h3 class="text-xl font-bold mb-4">{{ __('Contact Us') }}</h3>
                                            <ul class="space-y-2">
                                                <li class="flex items-start">
                                                    <i class="fas fa-map-marker-alt mt-1 ml-2 text-yellow-500"></i>
                                                    <span class="text-gray-400">المقر الرئيسي: القاهرة، مصر</span>
                                                </li>
                                                <li class="flex items-center">
                                                    <i class="fas fa-phone-alt ml-2 text-yellow-500"></i>
                                                    <span class="text-gray-400">{{ $previewData['contact_phone'] }}</span>
                                                </li>
                                                <li class="flex items-center">
                                                    <i class="fas fa-envelope ml-2 text-yellow-500"></i>
                                                    <span class="text-gray-400">{{ $previewData['contact_email'] }}</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    
                                    <hr class="border-gray-800 my-8">
                                    
                                    <!-- Bottom Footer -->
                                    <div class="flex flex-col md:flex-row justify-between items-center">
                                        <p class="text-gray-400 text-sm mb-4 md:mb-0">
                                            {!! $previewData['footer_text'] !!}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        @elseif($previewData['selectedPreview'] === 'cookie_banner')
                            @if($previewData['show_cookie_banner'])
                            <div class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 shadow-lg">
                                <div class="container mx-auto flex flex-col md:flex-row justify-between items-center">
                                    <div class="mb-4 md:mb-0 md:ml-4">
                                        <p>{{ $previewData['cookie_banner_text'] }}</p>
                                    </div>
                                    <div class="flex space-x-4 space-x-reverse">
                                        <button class="bg-yellow-500 hover:bg-yellow-600 text-black px-4 py-2 rounded-lg transition duration-300">
                                            {{ $previewData['cookie_banner_button_text'] }}
                                        </button>
                                        <button class="text-gray-300 hover:text-white transition duration-300">
                                            {{ __('سياسة الخصوصية') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            @else
                            <div class="p-4 bg-yellow-50 rounded-lg">
                                <p class="text-yellow-700">{{ __('شريط ملفات تعريف الارتباط غير مفعل حالياً.') }}</p>
                            </div>
                            @endif
                        @elseif($previewData['selectedPreview'] === 'social_sharing')
                            <div class="p-4 border rounded-lg">
                                <h3 class="text-lg font-bold mb-4">{{ __('مشاركة المنتج') }}</h3>
                                
                                @if($previewData['enable_social_sharing'])
                                <div class="flex space-x-4 space-x-reverse">
                                    @if($previewData['share_on_facebook'])
                                    <a href="#" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition duration-300">
                                        <i class="fab fa-facebook-f ml-1"></i>
                                        {{ __('فيسبوك') }}
                                    </a>
                                    @endif
                                    
                                    @if($previewData['share_on_twitter'])
                                    <a href="#" class="bg-blue-400 text-white px-3 py-2 rounded-lg hover:bg-blue-500 transition duration-300">
                                        <i class="fab fa-twitter ml-1"></i>
                                        {{ __('تويتر') }}
                                    </a>
                                    @endif
                                    
                                    @if($previewData['share_on_whatsapp'])
                                    <a href="#" class="bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition duration-300">
                                        <i class="fab fa-whatsapp ml-1"></i>
                                        {{ __('واتساب') }}
                                    </a>
                                    @endif
                                </div>
                                @else
                                <div class="p-4 bg-yellow-50 rounded-lg">
                                    <p class="text-yellow-700">{{ __('مشاركة المنتجات غير مفعلة حالياً.') }}</p>
                                </div>
                                @endif
                            </div>
                        @elseif($previewData['selectedPreview'] === 'payment_methods')
                            <div class="p-4 border rounded-lg">
                                <h3 class="text-lg font-bold mb-4">{{ __('طرق الدفع المتاحة') }}</h3>
                                
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    @if($previewData['enable_credit_card'])
                                    <div class="p-3 border rounded-lg flex flex-col items-center">
                                        <i class="far fa-credit-card text-2xl text-blue-600 mb-2"></i>
                                        <span class="text-sm">{{ __('بطاقة ائتمان') }}</span>
                                    </div>
                                    @endif
                                    
                                    @if($previewData['enable_paypal'])
                                    <div class="p-3 border rounded-lg flex flex-col items-center">
                                        <i class="fab fa-paypal text-2xl text-blue-800 mb-2"></i>
                                        <span class="text-sm">{{ __('PayPal') }}</span>
                                    </div>
                                    @endif
                                    
                                    @if($previewData['enable_bank_transfer'])
                                    <div class="p-3 border rounded-lg flex flex-col items-center">
                                        <i class="fas fa-university text-2xl text-gray-600 mb-2"></i>
                                        <span class="text-sm">{{ __('تحويل بنكي') }}</span>
                                    </div>
                                    @endif
                                    
                                    @if($previewData['enable_cash_on_delivery'])
                                    <div class="p-3 border rounded-lg flex flex-col items-center">
                                        <i class="fas fa-money-bill-wave text-2xl text-green-600 mb-2"></i>
                                        <span class="text-sm">{{ __('الدفع عند الاستلام') }}</span>
                                    </div>
                                    @endif
                                </div>
                                
                                @if(!$previewData['enable_credit_card'] && !$previewData['enable_paypal'] && !$previewData['enable_bank_transfer'] && !$previewData['enable_cash_on_delivery'])
                                <div class="p-4 bg-yellow-50 rounded-lg mt-4">
                                    <p class="text-yellow-700">{{ __('لا توجد طرق دفع مفعلة حالياً.') }}</p>
                                </div>
                                @endif
                            </div>
                        @elseif($previewData['selectedPreview'] === 'product_card')
                            <div class="product-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300 max-w-xs mx-auto">
                                <a href="#" class="block relative">
                                    <img src="{{ asset('images/products/default.jpg') }}" alt="منتج" class="w-full h-64 object-cover">
                                    <span class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">خصم 10%</span>
                                </a>
                                <div class="p-4">
                                    <a href="#" class="text-xs text-yellow-600 hover:text-yellow-700 mb-2 inline-block">خواتم</a>
                                    <h3 class="text-lg font-bold mb-2">
                                        <a href="#" class="text-gray-800 hover:text-yellow-600">خاتم ذهب عيار 21</a>
                                    </h3>
                                    <div class="flex items-center mb-3">
                                        <div class="flex text-yellow-400">
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star"></i>
                                            <i class="fas fa-star-half-alt"></i>
                                        </div>
                                        <span class="text-gray-500 text-xs mr-1">(12)</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <span class="text-gray-400 line-through text-sm">5,500.00 ج.م</span>
                                            <span class="text-lg font-bold text-gray-800">4,950.00 ج.م</span>
                                        </div>
                                        <div class="flex space-x-2 space-x-reverse">
                                            <a href="#" class="text-gray-400 hover:text-red-500 transition duration-300">
                                                <i class="far fa-heart"></i>
                                            </a>
                                            <a href="#" class="text-gray-400 hover:text-yellow-600 transition duration-300">
                                                <i class="fas fa-shopping-cart"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @elseif($previewData['selectedPreview'] === 'home_slider')
                            <div class="relative h-96 rounded-lg overflow-hidden">
                                <img src="{{ asset('images/slider/default.jpg') }}" alt="شريحة عرض" class="w-full h-full object-cover">
                                <div class="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent flex items-center">
                                    <div class="text-white p-8 max-w-lg">
                                        <h2 class="text-4xl font-bold mb-4">مجوهرات فاخرة</h2>
                                        <p class="text-lg mb-6">اكتشف تشكيلتنا الجديدة من المجوهرات الذهبية الفاخرة بتصاميم عصرية وأنيقة</p>
                                        <a href="#" class="inline-block bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-3 rounded-full font-medium transition duration-300">تسوق الآن</a>
                                    </div>
                                </div>
                                <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                                    <button class="w-3 h-3 rounded-full bg-white opacity-50"></button>
                                    <button class="w-3 h-3 rounded-full bg-white"></button>
                                    <button class="w-3 h-3 rounded-full bg-white opacity-50"></button>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                
                <div class="mt-4 text-sm text-gray-500">
                    <p>{{ __('ملاحظة: هذه معاينة تقريبية فقط وقد تختلف عن المظهر الفعلي للموقع.') }}</p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
