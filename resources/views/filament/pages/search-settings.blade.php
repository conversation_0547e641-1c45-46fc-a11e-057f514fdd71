<x-filament-panels::page>
    <div class="space-y-6">
        <div class="p-6 bg-white rounded-xl shadow">
            <h2 class="text-xl font-bold mb-4">{{ __('بحث في إعدادات الموقع') }}</h2>
            <p class="text-gray-500 mb-6">{{ __('يمكنك البحث في إعدادات الموقع بالاسم أو القيمة.') }}</p>
            
            {{ $this->form }}
            
            @if(count($searchResults) > 0)
                <div class="mt-8">
                    <h3 class="text-lg font-medium mb-4">{{ __('نتائج البحث') }} ({{ count($searchResults) }})</h3>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-right text-gray-500 border-collapse">
                            <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3">{{ __('الاسم') }}</th>
                                    <th scope="col" class="px-6 py-3">{{ __('القيمة') }}</th>
                                    <th scope="col" class="px-6 py-3">{{ __('التبويب') }}</th>
                                    <th scope="col" class="px-6 py-3">{{ __('الإجراءات') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($searchResults as $result)
                                    <tr class="bg-white border-b hover:bg-gray-50">
                                        <td class="px-6 py-4 font-medium text-gray-900">
                                            {{ $result['name'] }}
                                        </td>
                                        <td class="px-6 py-4">
                                            @if(strlen($result['value']) > 50)
                                                <div x-data="{ expanded: false }">
                                                    <div x-show="!expanded" class="cursor-pointer" @click="expanded = true">
                                                        {{ substr($result['value'], 0, 50) }}...
                                                        <span class="text-primary-600">(عرض المزيد)</span>
                                                    </div>
                                                    <div x-show="expanded" class="cursor-pointer" @click="expanded = false">
                                                        {{ $result['value'] }}
                                                        <span class="text-primary-600">(عرض أقل)</span>
                                                    </div>
                                                </div>
                                            @else
                                                {{ $result['value'] }}
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-primary-100 text-primary-800">
                                                {{ $result['tab'] }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="{{ route('filament.admin.resources.site-settings.index') }}" class="text-primary-600 hover:text-primary-900">
                                                {{ __('تعديل') }}
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @elseif($searchQuery && strlen($searchQuery) >= 2)
                <div class="mt-8 p-4 bg-yellow-50 rounded-lg">
                    <p class="text-yellow-700">{{ __('لم يتم العثور على نتائج مطابقة لـ') }} "{{ $searchQuery }}"</p>
                </div>
            @endif
        </div>
        
        <div class="p-6 bg-white rounded-xl shadow">
            <h2 class="text-xl font-bold mb-4">{{ __('روابط سريعة') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <a href="{{ route('filament.admin.resources.site-settings.index') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition flex items-center">
                    <div class="mr-4 bg-primary-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ __('تعديل الإعدادات') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('تعديل إعدادات الموقع الأساسية') }}</p>
                    </div>
                </a>
                
                <a href="{{ route('filament.admin.pages.site-settings-manager') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition flex items-center">
                    <div class="mr-4 bg-indigo-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ __('إدارة الإعدادات') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('استيراد وتصدير إعدادات الموقع') }}</p>
                    </div>
                </a>
                
                <a href="{{ route('filament.admin.resources.setting-changes.index') }}" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition flex items-center">
                    <div class="mr-4 bg-green-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ __('سجل التغييرات') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('عرض سجل تغييرات الإعدادات') }}</p>
                    </div>
                </a>
                
                <a href="{{ route('home') }}" target="_blank" class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition flex items-center">
                    <div class="mr-4 bg-yellow-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900">{{ __('معاينة الموقع') }}</h3>
                        <p class="text-sm text-gray-500">{{ __('معاينة التغييرات على الموقع') }}</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</x-filament-panels::page>
