<div class="wishlist-manager">
    @if ($showWishlist)
        <button wire:click="toggleWishlist" wire:loading.attr="disabled"
            class="w-full py-3 px-6 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-2 shadow-md hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed
            {{ $isInWishlist
                ? 'bg-red-50 border-2 border-red-500 text-red-600 hover:bg-red-500 hover:text-white'
                : 'bg-white border-2 border-gray-300 text-gray-600 hover:border-red-400 hover:text-red-500 hover:bg-red-50' }}">
            <i class="fas fa-heart {{ $isInWishlist ? 'text-red-500' : 'text-gray-400' }} transition-all duration-300"
                wire:loading.remove></i>
            <i class="fas fa-spinner fa-spin" wire:loading></i>
            <span wire:loading.remove>
                {{ $isInWishlist ? 'في المفضلة' : 'إضافة للمفضلة' }}
            </span>
            <span wire:loading>جاري التحديث...</span>
        </button>
    @endif


</div>
