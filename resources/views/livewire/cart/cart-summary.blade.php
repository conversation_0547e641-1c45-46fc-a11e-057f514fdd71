<div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="p-4 border-b border-gray-200">
        <h2 class="text-xl font-bold">ملخص الطلب</h2>
    </div>

    <div class="p-4">
        <div class="space-y-3 text-sm">
            <div class="flex justify-between">
                <span class="text-gray-600">المجموع الفرعي:</span>
                <span class="font-medium">{{ number_format($cart->subtotal, 2) }} ج.م</span>
            </div>

            <div class="flex justify-between">
                <span class="text-gray-600">الضريبة (14%):</span>
                <span class="font-medium">{{ number_format($cart->tax, 2) }} ج.م</span>
            </div>

            <div class="flex justify-between">
                <span class="text-gray-600">الشحن:</span>
                @if ($cart->shipping > 0)
                    <span class="font-medium">{{ number_format($cart->shipping, 2) }} ج.م</span>
                @else
                    <span class="text-green-500 font-medium">مجاني</span>
                @endif
            </div>

            @if ($cart->discount > 0)
                <div class="flex justify-between text-green-500">
                    <span>الخصم:</span>
                    <span class="font-medium">- {{ number_format($cart->discount, 2) }} ج.م</span>
                </div>
            @endif

            <div class="border-t border-gray-200 pt-3 mt-3">
                <div class="flex justify-between font-bold text-lg">
                    <span>الإجمالي:</span>
                    <span>{{ number_format($cart->total, 2) }} ج.م</span>
                </div>
            </div>
        </div>

        <!-- Coupon Code -->
        <div class="mt-6">
            <div class="mb-4">
                <label for="coupon-code" class="block text-gray-700 font-medium mb-2">كود الخصم</label>
                <div class="flex">
                    <input type="text" id="coupon-code" wire:model="couponCode"
                        class="flex-grow border border-gray-300 rounded-r-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="أدخل كود الخصم">
                    @if ($cart->coupon_code)
                        <button wire:click="removeCoupon"
                            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-l-md transition duration-300">
                            إلغاء
                        </button>
                    @else
                        <button wire:click="applyCoupon"
                            class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-l-md transition duration-300">
                            تطبيق
                        </button>
                    @endif
                </div>
                @if ($couponError)
                    <p class="text-red-500 text-sm mt-1">{{ $couponError }}</p>
                @endif
                @if ($couponSuccess)
                    <p class="text-green-500 text-sm mt-1">{{ $couponSuccess }}</p>
                @endif
            </div>
        </div>

        <!-- Checkout Button -->
        <div class="mt-6">
            <button wire:click="proceedToCheckout"
                class="w-full bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300 flex items-center justify-center">
                <i class="fas fa-credit-card ml-2"></i>
                إتمام الطلب
            </button>
        </div>

        <!-- Payment Methods -->
        <div class="mt-6">
            <p class="text-gray-500 text-sm mb-2">طرق الدفع المتاحة:</p>
            <div class="flex items-center space-x-4 space-x-reverse">
                <img src="{{ asset('images/payment/visa.png') }}" alt="Visa" class="h-8">
                <img src="{{ asset('images/payment/mastercard.png') }}" alt="Mastercard" class="h-8">
                <img src="{{ asset('images/payment/paypal.png') }}" alt="PayPal" class="h-8">
                <img src="{{ asset('images/payment/fawry.png') }}" alt="Fawry" class="h-8">
            </div>
        </div>
    </div>
</div>
