<div class="flex flex-col md:flex-row p-4 gap-4">
    <!-- Product Image -->
    <div class="w-24 h-24 flex-shrink-0">
        <a href="{{ route('product.show', $item->product->slug) }}">
            <img src="{{ $item->product->image ? asset('storage/' . $item->product->image) : asset('images/products/default.jpg') }}"
                alt="{{ $item->product->name_ar }}" class="w-full h-full object-cover rounded-md">
        </a>
    </div>

    <!-- Product Info -->
    <div class="flex-grow">
        <div class="flex flex-col md:flex-row justify-between">
            <div>
                <h3 class="font-bold text-gray-800">
                    <a href="{{ route('product.show', $item->product->slug) }}" class="hover:text-primary-500">
                        {{ $item->product->name_ar }}
                    </a>
                </h3>
                <p class="text-sm text-gray-500 mb-2">
                    {{ $item->product->metal_type == 'gold' ? 'ذهب' : ($item->product->metal_type == 'silver' ? 'فضة' : 'بلاتين') }}
                    عيار {{ $item->product->purity }}</p>

                <div class="text-primary-500 font-bold">
                    {{ number_format($item->price, 2) }} ج.م
                </div>
            </div>

            <div class="mt-4 md:mt-0">
                <div class="flex items-center">
                    <label for="quantity-{{ $item->id }}" class="text-gray-700 ml-2">الكمية:</label>
                    <div class="flex items-center border border-gray-300 rounded-md">
                        <button type="button" wire:click="decrementQuantity"
                            class="px-3 py-1 text-gray-500 hover:text-primary-500 focus:outline-none">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" id="quantity-{{ $item->id }}" wire:model.live="quantity"
                            min="1" max="{{ $item->product->stock_quantity }}"
                            class="w-12 text-center border-0 focus:outline-none focus:ring-0">
                        <button type="button" wire:click="incrementQuantity"
                            class="px-3 py-1 text-gray-500 hover:text-primary-500 focus:outline-none">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>

                <div class="mt-2 text-right">
                    <button wire:click="removeItem" class="text-red-500 hover:text-red-700 text-sm">
                        <i class="fas fa-trash-alt ml-1"></i>
                        حذف
                    </button>
                </div>
            </div>
        </div>

        <div class="mt-2 text-sm text-gray-500">
            <div class="flex justify-between">
                <span>السعر:</span>
                <span>{{ number_format($item->price, 2) }} ج.م</span>
            </div>
            <div class="flex justify-between font-bold text-gray-700">
                <span>الإجمالي:</span>
                <span>{{ number_format($item->total, 2) }} ج.م</span>
            </div>
        </div>
    </div>
</div>
