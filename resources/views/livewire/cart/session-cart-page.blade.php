<div>
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">سلة التسوق</span>
            </div>
        </div>
    </div>

    <section class="py-12">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold mb-8 text-center">سلة التسوق</h1>

            @if (count($cartItems) > 0)
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Cart Items -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-4">
                            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                                <h2 class="text-xl font-bold">المنتجات ({{ count($cartItems) }})</h2>
                                <button wire:click="clearCart"
                                    class="text-red-500 hover:text-red-700 text-sm font-medium">
                                    <i class="fas fa-trash-alt ml-1"></i>
                                    تفريغ السلة
                                </button>
                            </div>

                            <div class="divide-y divide-gray-200">
                                @foreach ($cartItems as $rowId => $item)
                                    <div class="p-4 flex flex-col md:flex-row">
                                        <div class="md:w-1/4 mb-4 md:mb-0">
                                            <img src="{{ isset($item['options']['image']) ? asset('storage/' . $item['options']['image']) : asset('images/products/default.jpg') }}"
                                                alt="{{ $item['name'] }}"
                                                class="w-full h-32 object-cover rounded">
                                        </div>
                                        <div class="md:w-3/4 md:pl-6 flex flex-col">
                                            <div class="flex justify-between mb-2">
                                                <h3 class="text-lg font-bold">
                                                    <a href="{{ route('product.show', $item['options']['slug']) }}"
                                                        class="text-gray-800 hover:text-primary-500">
                                                        {{ $item['name'] }}
                                                    </a>
                                                </h3>
                                                <button wire:click="removeCartItem('{{ $rowId }}')"
                                                    class="text-gray-400 hover:text-red-500">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>

                                            @if (isset($item['options']['metal_type']) && $item['options']['metal_type'])
                                                <p class="text-gray-500 text-sm mb-1">
                                                    نوع المعدن: {{ $item['options']['metal_type'] }}
                                                </p>
                                            @endif

                                            @if (isset($item['options']['purity']) && $item['options']['purity'])
                                                <p class="text-gray-500 text-sm mb-1">
                                                    النقاء: {{ $item['options']['purity'] }}
                                                </p>
                                            @endif

                                            @if (isset($item['weight']) && $item['weight'])
                                                <p class="text-gray-500 text-sm mb-1">
                                                    الوزن: {{ $item['weight'] }} جرام
                                                </p>
                                            @endif

                                            <div class="mt-auto flex flex-col sm:flex-row justify-between items-start sm:items-center pt-2">
                                                <div class="flex items-center mb-2 sm:mb-0">
                                                    <button wire:click="updateCartItem('{{ $rowId }}', {{ max(1, $item['quantity'] - 1) }})"
                                                        class="bg-gray-200 text-gray-700 w-8 h-8 rounded-l-md flex items-center justify-center hover:bg-gray-300">
                                                        <i class="fas fa-minus text-xs"></i>
                                                    </button>
                                                    <input type="number" min="1" value="{{ $item['quantity'] }}"
                                                        wire:change="updateCartItem('{{ $rowId }}', $event.target.value)"
                                                        class="w-12 h-8 text-center border-t border-b border-gray-200 focus:outline-none focus:ring-0">
                                                    <button wire:click="updateCartItem('{{ $rowId }}', {{ $item['quantity'] + 1 }})"
                                                        class="bg-gray-200 text-gray-700 w-8 h-8 rounded-r-md flex items-center justify-center hover:bg-gray-300">
                                                        <i class="fas fa-plus text-xs"></i>
                                                    </button>
                                                </div>
                                                <div class="text-primary-500 font-bold">
                                                    {{ number_format($item['price'] * $item['quantity'], 2) }} ج.م
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>

                        <div class="flex justify-between mt-6">
                            <a href="{{ route('products') }}"
                                class="inline-flex items-center text-primary-500 hover:text-primary-600 font-medium">
                                <i class="fas fa-arrow-right ml-2"></i>
                                متابعة التسوق
                            </a>
                        </div>
                    </div>

                    <!-- Cart Summary -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden sticky top-4">
                            <div class="p-4 border-b border-gray-200">
                                <h2 class="text-xl font-bold">ملخص الطلب</h2>
                            </div>
                            <div class="p-4">
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">المجموع الفرعي:</span>
                                        <span class="font-medium">{{ number_format($subtotal, 2) }} ج.م</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">الضريبة (14%):</span>
                                        <span class="font-medium">{{ number_format($tax, 2) }} ج.م</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">الشحن:</span>
                                        @if ($shipping > 0)
                                            <span class="font-medium">{{ number_format($shipping, 2) }} ج.م</span>
                                        @else
                                            <span class="text-green-500 font-medium">مجاني</span>
                                        @endif
                                    </div>

                                    @if ($discount > 0)
                                        <div class="flex justify-between text-green-500">
                                            <span>الخصم:</span>
                                            <span class="font-medium">-{{ number_format($discount, 2) }} ج.م</span>
                                        </div>
                                    @endif

                                    <div class="border-t border-gray-200 pt-3 mt-3">
                                        <div class="flex justify-between font-bold text-lg">
                                            <span>الإجمالي:</span>
                                            <span class="text-primary-500">{{ number_format($total, 2) }} ج.م</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Coupon Code -->
                                <div class="mt-6">
                                    <div class="mb-4">
                                        <label for="coupon" class="block text-gray-700 font-medium mb-2">كود الخصم</label>
                                        <div class="flex">
                                            <input type="text" id="coupon" wire:model="couponCode"
                                                class="flex-1 border border-gray-300 rounded-r-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                                                placeholder="أدخل كود الخصم">
                                            <button wire:click="applyCoupon"
                                                class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-l-md transition duration-300">
                                                تطبيق
                                            </button>
                                        </div>
                                        @if ($couponError)
                                            <p class="text-red-500 text-sm mt-1">{{ $couponError }}</p>
                                        @endif
                                        @if ($couponSuccess)
                                            <div class="flex justify-between items-center text-green-500 text-sm mt-1">
                                                <p>{{ $couponSuccess }}</p>
                                                <button wire:click="removeCoupon" class="text-gray-400 hover:text-red-500">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <a href="{{ route('checkout') }}"
                                    class="block w-full bg-primary-500 hover:bg-primary-600 text-white text-center px-4 py-3 rounded-md font-medium transition duration-300 mt-6">
                                    إتمام الطلب
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-6xl text-gray-300 mb-4">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-700 mb-4">سلة التسوق فارغة</h2>
                    <p class="text-gray-500 mb-8">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد.</p>
                    <a href="{{ route('products') }}"
                        class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                        تصفح المنتجات
                    </a>
                </div>
            @endif
        </div>
    </section>
</div>
