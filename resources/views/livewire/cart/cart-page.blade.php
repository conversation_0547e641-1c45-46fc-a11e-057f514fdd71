<div>
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">سلة التسوق</span>
            </div>
        </div>
    </div>

    <section class="py-12">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold mb-8 text-center">سلة التسوق</h1>

            @if ($cart->items->count() > 0)
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Cart Items -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-4">
                            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                                <h2 class="text-xl font-bold">المنتجات ({{ $cart->items->count() }})</h2>
                                <button wire:click="clearCart"
                                    class="text-red-500 hover:text-red-700 text-sm font-medium">
                                    <i class="fas fa-trash-alt ml-1"></i>
                                    تفريغ السلة
                                </button>
                            </div>

                            <div class="divide-y divide-gray-200">
                                @foreach ($cart->items as $item)
                                    @livewire('cart.cart-item', ['item' => $item], key('cart-item-' . $item->id))
                                @endforeach
                            </div>
                        </div>

                        <div class="flex justify-between mt-6">
                            <a href="{{ route('products') }}"
                                class="inline-flex items-center text-primary-500 hover:text-primary-600 font-medium">
                                <i class="fas fa-arrow-right ml-2"></i>
                                متابعة التسوق
                            </a>
                        </div>
                    </div>

                    <!-- Cart Summary -->
                    <div class="lg:col-span-1">
                        @livewire('cart.cart-summary')
                    </div>
                </div>
            @else
                <div class="text-center py-12">
                    <div class="text-6xl text-gray-300 mb-4">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h2 class="text-2xl font-bold text-gray-700 mb-4">سلة التسوق فارغة</h2>
                    <p class="text-gray-500 mb-8">لم تقم بإضافة أي منتجات إلى سلة التسوق بعد.</p>
                    <a href="{{ route('products') }}"
                        class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                        تصفح المنتجات
                    </a>
                </div>
            @endif
        </div>
    </section>
</div>
