<div class="cart-manager">
    @if (!$isDisplayOnlyMode)
        @if ($isInCart)
            <!-- زر إزالة من السلة -->
            <button wire:click="removeFromCart" wire:loading.attr="disabled"
                class="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-md hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed">
                <i class="fas fa-trash-alt" wire:loading.remove></i>
                <i class="fas fa-spinner fa-spin" wire:loading></i>
                <span wire:loading.remove>إزالة من السلة</span>
                <span wire:loading>جاري الإزالة...</span>
            </button>
        @else
            <!-- زر إضافة إلى السلة -->
            <button wire:click="addToCart" wire:loading.attr="disabled"
                class="w-full bg-white text-primary-600 font-bold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-md hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed hover:bg-primary-50">
                <i class="fas fa-cart-plus" wire:loading.remove></i>
                <i class="fas fa-spinner fa-spin" wire:loading></i>
                <span wire:loading.remove>إضافة إلى السلة</span>
                <span wire:loading>جاري الإضافة...</span>
            </button>
        @endif
    @endif


</div>
