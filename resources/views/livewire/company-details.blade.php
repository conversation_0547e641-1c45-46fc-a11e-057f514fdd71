{{-- مكون تفاصيل الشركة --}}
<div>
    {{-- شريط التنقل --}}
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-amber-600">
            <li><a href="{{ route('home') }}" class="hover:text-amber-800">الرئيسية</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li><a href="{{ route('companies') }}" class="hover:text-amber-800">الشركات</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li class="text-amber-800 font-medium">{{ $company->name }}</li>
        </ol>
    </nav>

    {{-- معلومات الشركة الرئيسية --}}
    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
        <div class="md:flex">
            {{-- لوجو الشركة --}}
            <div class="md:w-1/3 bg-gradient-to-br from-amber-100 to-yellow-200 flex items-center justify-center p-8">
                @if ($company->logo)
                    <img src="{{ $company->logo_url }}" alt="{{ $company->name }}"
                        class="max-w-full max-h-64 object-contain">
                @else
                    <div class="text-center">
                        <i class="fas fa-building text-8xl text-amber-400 mb-4"></i>
                        <p class="text-amber-600 font-medium text-lg">{{ $company->name }}</p>
                    </div>
                @endif
            </div>

            {{-- معلومات الشركة --}}
            <div class="md:w-2/3 p-8">
                <h1 class="text-3xl font-bold text-amber-800 mb-4">{{ $company->name }}</h1>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    {{-- مصنعية الجرام --}}
                    <div class="bg-amber-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-hammer text-amber-600 mr-2"></i>
                            <h3 class="font-semibold text-amber-800">مصنعية الجرام</h3>
                        </div>
                        <p class="text-2xl font-bold text-amber-700">
                            {{ number_format($company->manufacturing_cost_per_gram, 2) }} ج.م
                        </p>
                    </div>

                    {{-- قيمة الاستردادة --}}
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-undo text-green-600 mr-2"></i>
                            <h3 class="font-semibold text-green-800">قيمة الاستردادة</h3>
                        </div>
                        <p class="text-2xl font-bold text-green-700">
                            {{ number_format($company->refund_value, 2) }} ج.م
                        </p>
                    </div>

                    {{-- عدد المنتجات --}}
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-box text-blue-600 mr-2"></i>
                            <h3 class="font-semibold text-blue-800">عدد المنتجات</h3>
                        </div>
                        <p class="text-2xl font-bold text-blue-700">{{ $stats['total_products'] }} منتج</p>
                    </div>

                    {{-- تاريخ الإنشاء --}}
                    <div class="bg-purple-50 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-calendar text-purple-600 mr-2"></i>
                            <h3 class="font-semibold text-purple-800">تاريخ الإنشاء</h3>
                        </div>
                        <p class="text-lg font-medium text-purple-700">
                            {{ $company->created_at->format('d/m/Y') }}
                        </p>
                    </div>
                </div>

                {{-- أزرار العمل --}}
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="{{ route('companies.products', $company->id) }}"
                        class="flex-1 bg-amber-500 hover:bg-amber-600 text-white text-center py-3 px-6 rounded-lg transition-colors duration-200 font-medium">
                        <i class="fas fa-box mr-2"></i>
                        عرض جميع المنتجات ({{ $stats['total_products'] }})
                    </a>

                    <a href="{{ route('companies') }}"
                        class="flex-1 bg-amber-100 hover:bg-amber-200 text-amber-800 text-center py-3 px-6 rounded-lg transition-colors duration-200 font-medium">
                        <i class="fas fa-arrow-right mr-2"></i>
                        العودة للشركات
                    </a>
                </div>
            </div>
        </div>
    </div>

    {{-- إحصائيات تفصيلية --}}
    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
        <h2 class="text-2xl font-bold text-amber-800 mb-6">
            <i class="fas fa-chart-bar text-amber-600 mr-2"></i>
            إحصائيات المنتجات
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {{-- إحصائيات الأنواع --}}
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-green-800 mb-4">السبائك</h3>
                <p class="text-3xl font-bold text-green-600">{{ $stats['bars_count'] }}</p>
                <p class="text-sm text-green-600 mt-1">منتج</p>
            </div>

            <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4">العملات</h3>
                <p class="text-3xl font-bold text-blue-600">{{ $stats['coins_count'] }}</p>
                <p class="text-sm text-blue-600 mt-1">منتج</p>
            </div>

            <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-purple-800 mb-4">متوسط الوزن</h3>
                <p class="text-3xl font-bold text-purple-600">{{ number_format($stats['avg_weight'], 2) }}</p>
                <p class="text-sm text-purple-600 mt-1">جرام</p>
            </div>

            <div class="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-orange-800 mb-4">إجمالي الوزن</h3>
                <p class="text-3xl font-bold text-orange-600">{{ number_format($stats['total_weight'], 2) }}</p>
                <p class="text-sm text-orange-600 mt-1">جرام</p>
            </div>
        </div>

        {{-- توزيع العيارات --}}
        @if (count($stats['metal_purities']) > 0)
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-amber-800 mb-4">توزيع العيارات</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    @foreach ($stats['metal_purities'] as $purity => $count)
                        <div class="bg-amber-50 rounded-lg p-4 text-center">
                            <p class="text-lg font-bold text-amber-800">عيار {{ $purity }}</p>
                            <p class="text-2xl font-bold text-amber-600">{{ $count }}</p>
                            <p class="text-sm text-amber-600">منتج</p>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif
    </div>

    {{-- المنتجات الحديثة --}}
    @if ($recentProducts->count() > 0)
        <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-amber-800">
                    <i class="fas fa-star text-amber-600 mr-2"></i>
                    أحدث المنتجات
                </h2>

                @if ($stats['total_products'] > 6)
                    <a href="{{ route('companies.products', $company->id) }}"
                        class="text-amber-600 hover:text-amber-800 font-medium">
                        عرض الكل ({{ $stats['total_products'] }})
                        <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                @endif
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach ($recentProducts as $product)
                    <div class="bg-amber-50 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
                        <h3 class="text-lg font-semibold text-amber-800 mb-3">{{ $product->name }}</h3>

                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-amber-600">الوزن:</span>
                                <span class="font-medium text-amber-800">{{ $product->formatted_weight }}</span>
                            </div>

                            <div class="flex justify-between items-center text-sm">
                                <span class="text-amber-600">التكلفة الإجمالية:</span>
                                <span class="font-bold text-green-700">{{ number_format($product->total_cost, 2) }}
                                    ج.م</span>
                            </div>
                        </div>

                        @if ($product->description)
                            <p class="text-sm text-amber-700 line-clamp-2">{{ $product->description }}</p>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    {{-- إحصائيات إضافية --}}
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        {{-- متوسط المصنعية --}}
        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <i class="fas fa-tools text-3xl text-amber-600 mb-3"></i>
            <h3 class="text-lg font-semibold text-amber-800 mb-2">متوسط المصنعية</h3>
            <p class="text-2xl font-bold text-amber-700">
                {{ number_format($company->products()->avg('manufacturing_cost_per_gram') ?? 0, 2) }} ج.م/جرام
            </p>
        </div>

        {{-- متوسط قيمة الاسترداد --}}
        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <i class="fas fa-undo text-3xl text-green-600 mb-3"></i>
            <h3 class="text-lg font-semibold text-green-800 mb-2">متوسط قيمة الاسترداد</h3>
            <p class="text-2xl font-bold text-green-700">
                {{ number_format($company->products()->avg('refund_value_per_gram') ?? 0, 2) }} ج.م/جرام
            </p>
        </div>

        {{-- عدد المنتجات --}}
        <div class="bg-white rounded-xl shadow-lg p-6 text-center">
            <i class="fas fa-list text-3xl text-blue-600 mb-3"></i>
            <h3 class="text-lg font-semibold text-blue-800 mb-2">عدد المنتجات</h3>
            <p class="text-2xl font-bold text-blue-700">
                {{ $company->products()->count() }} منتج
            </p>
        </div>
    </div>
</div>
