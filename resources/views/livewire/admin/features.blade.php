<div>
    <div class="container mx-auto py-6 px-4">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">إدارة ميزات الموقع</h1>
            <div class="flex items-center">
                <input type="text" wire:model.live.debounce.300ms="search" placeholder="بحث..." class="border border-gray-300 rounded-lg px-4 py-2 ml-4 focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
        </div>

        @if (session()->has('message'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('message') }}</span>
            </div>
        @endif

        <!-- Form for adding/editing features -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-bold mb-4">{{ $isEditing ? 'تعديل ميزة' : 'إضافة ميزة جديدة' }}</h2>
            <form wire:submit.prevent="save" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="title_ar" class="block text-sm font-medium text-gray-700 mb-1">العنوان (عربي)</label>
                    <input type="text" id="title_ar" wire:model="title_ar" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    @error('title_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="title_en" class="block text-sm font-medium text-gray-700 mb-1">العنوان (إنجليزي)</label>
                    <input type="text" id="title_en" wire:model="title_en" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    @error('title_en') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="description_ar" class="block text-sm font-medium text-gray-700 mb-1">الوصف (عربي)</label>
                    <textarea id="description_ar" wire:model="description_ar" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                    @error('description_ar') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="description_en" class="block text-sm font-medium text-gray-700 mb-1">الوصف (إنجليزي)</label>
                    <textarea id="description_en" wire:model="description_en" rows="3" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                    @error('description_en') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="icon" class="block text-sm font-medium text-gray-700 mb-1">أيقونة (Font Awesome)</label>
                    <input type="text" id="icon" wire:model="icon" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    <p class="text-sm text-gray-500 mt-1">مثال: fas fa-gem, fas fa-truck, fas fa-shield-alt</p>
                    @error('icon') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div>
                    <label for="order" class="block text-sm font-medium text-gray-700 mb-1">الترتيب</label>
                    <input type="number" id="order" wire:model="order" min="1" class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    @error('order') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="is_active" wire:model="is_active" class="ml-2">
                    <label for="is_active" class="text-sm font-medium text-gray-700">نشط</label>
                </div>

                <div class="md:col-span-2 flex justify-end">
                    @if ($isEditing)
                        <button type="button" wire:click="resetForm" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg ml-2">
                            إلغاء
                        </button>
                    @endif
                    <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white font-bold py-2 px-4 rounded-lg">
                        {{ $isEditing ? 'تحديث' : 'إضافة' }}
                    </button>
                </div>
            </form>
        </div>

        <!-- Features Table -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الترتيب
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الأيقونة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            العنوان (عربي)
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            العنوان (إنجليزي)
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse ($features as $feature)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $feature->order }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <i class="{{ $feature->icon }} text-primary-500 text-xl"></i>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $feature->title_ar }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ $feature->title_en }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <button wire:click="toggleActive({{ $feature->id }})" class="focus:outline-none">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $feature->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                        {{ $feature->is_active ? 'نشط' : 'غير نشط' }}
                                    </span>
                                </button>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button wire:click="edit({{ $feature->id }})" class="text-indigo-600 hover:text-indigo-900 ml-3">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button wire:click="confirmDelete({{ $feature->id }})" class="text-red-600 hover:text-red-900">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="6" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                                لا توجد ميزات متاحة
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <div class="mt-4">
            {{ $features->links() }}
        </div>

        <!-- Delete Confirmation Modal -->
        @if ($confirmingDelete)
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                <div class="bg-white rounded-lg p-6 max-w-md mx-auto">
                    <h3 class="text-lg font-bold mb-4">تأكيد الحذف</h3>
                    <p class="mb-4">هل أنت متأكد من رغبتك في حذف هذه الميزة؟</p>
                    <div class="flex justify-end">
                        <button wire:click="cancelDelete" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg ml-2">
                            إلغاء
                        </button>
                        <button wire:click="delete" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-lg">
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
