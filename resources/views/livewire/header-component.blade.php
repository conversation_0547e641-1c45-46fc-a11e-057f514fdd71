<div>
    <!-- User Account -->
    {{-- @if (auth()->check() || $registrationEnabled) --}}
    @if ($registrationEnabled)
        <div class="relative group dropdown-container">
            <a href="{{ auth()->check() ? route('account.dashboard') : route('login') }}"
                class="flex items-center text-gray-700 hover:text-primary-500 dropdown-toggle">
                <i class="fas fa-user-circle text-xl ml-1"></i>
                <span class="hidden md:inline">
                    @auth
                        {{ __('My Account') }}
                    @else
                        {{ __('Login') }}
                    @endauth
                </span>
            </a>
            <div class="absolute z-10 right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden dropdown-menu">
                @auth
                    <a href="{{ route('account.dashboard') }}"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('Dashboard') }}</a>

                    @if (!$displayOnlyMode)
                        <a href="{{ route('account.orders') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('My Orders') }}</a>
                    @endif

                    <a href="{{ route('account.settings') }}"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('Account Settings') }}</a>
                    <hr class="my-1">
                    <form method="POST" action="{{ route('logout') }}">
                        @csrf
                        <button type="submit"
                            class="block w-full text-right px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('Logout') }}</button>
                    </form>
                @else
                    <a href="{{ route('login') }}"
                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('Login') }}</a>
                    @if ($registrationEnabled)
                        <a href="{{ route('register') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">{{ __('Register') }}</a>
                    @endif
                @endauth
            </div>
        </div>
    @endif

    @if ($showWishlist && (auth()->check() || $registrationEnabled))
        <!-- Wishlist -->
        <a href="{{ route('account.wishlist') }}" class="relative text-gray-700 hover:text-primary-500">
            <i class="fas fa-heart text-xl"></i>
            <livewire:wishlist-counter />
        </a>
    @endif

    @if (!$displayOnlyMode)
        <!-- Cart -->
        <a href="{{ route('cart') }}" class="relative text-gray-700 hover:text-primary-500">
            <i class="fas fa-shopping-cart text-xl"></i>
            <livewire:cart-counter />
        </a>
    @endif
</div>
