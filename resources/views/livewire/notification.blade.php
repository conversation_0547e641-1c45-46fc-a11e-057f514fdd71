<div class="fixed top-4 right-4 z-50 space-y-2">
    @foreach ($notifications as $notification)
        <div
            x-data="{ show: true }"
            x-show="show"
            x-init="setTimeout(() => { show = false; $wire.remove('{{ $notification['id'] }}') }, {{ $notification['timeout'] }})"
            x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 transform translate-x-4"
            x-transition:enter-end="opacity-100 transform translate-x-0"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform translate-x-0"
            x-transition:leave-end="opacity-0 transform translate-x-4"
            class="p-4 rounded-md shadow-md max-w-sm {{ $notification['type'] === 'success' ? 'bg-green-500' : ($notification['type'] === 'error' ? 'bg-red-500' : ($notification['type'] === 'warning' ? 'bg-yellow-500' : 'bg-blue-500')) }} text-white"
        >
            <div class="flex items-center">
                <div class="flex-shrink-0 ml-2">
                    @if ($notification['type'] === 'success')
                        <i class="fas fa-check-circle"></i>
                    @elseif ($notification['type'] === 'error')
                        <i class="fas fa-exclamation-circle"></i>
                    @elseif ($notification['type'] === 'warning')
                        <i class="fas fa-exclamation-triangle"></i>
                    @else
                        <i class="fas fa-info-circle"></i>
                    @endif
                </div>
                <div class="flex-1">
                    {{ $notification['message'] }}
                </div>
                <div class="flex-shrink-0">
                    <button wire:click="remove('{{ $notification['id'] }}')" class="text-white hover:text-gray-200 focus:outline-none">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
    @endforeach
</div>
