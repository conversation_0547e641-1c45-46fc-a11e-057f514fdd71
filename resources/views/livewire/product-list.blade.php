<div>
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-1/4">
            @livewire('product-filters', [
                'targetComponent' => 'product-list',
                'currentPage' => 'products',
                'showSearch' => true,
                'showPriceFilter' => $showPriceFilter,
                'showCategoryFilter' => true,
                'showMetalFilter' => true,
                'showPurityFilter' => true,
                'initialFilters' => [
                    'search' => $search,
                    'category' => $category,
                    'minPrice' => $minPrice,
                    'maxPrice' => $maxPrice,
                    'metalType' => $metalType,
                    'purity' => $purity,
                    'sort' => $sort,
                ],
            ])
        </div>

        <!-- Products Grid -->
        <div class="lg:w-3/4">
            <!-- Results Header -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <!-- Results Info -->
                    <div class="flex items-center gap-4">
                        <div class="text-gray-600">
                            <span class="font-medium text-gray-900">{{ number_format($totalProducts) }}</span>
                            منتج
                            @if ($filterStats['has_filters'])
                                <span class="text-sm text-gray-500">(مفلتر)</span>
                            @endif
                        </div>
                        @if ($currentPage > 1)
                            <div class="text-sm text-gray-500">
                                صفحة {{ $currentPage }} من {{ $lastPage }}
                            </div>
                        @endif
                        <div class="text-sm text-gray-500">
                            عرض {{ $products->firstItem() ?? 0 }}-{{ $products->lastItem() ?? 0 }}
                        </div>
                    </div>

                    <!-- Debug info - remove in production -->
                    @if (config('app.debug'))
                        <div class="text-xs text-gray-500 mb-2">
                            <details>
                                <summary>معلومات التصفية (للمطورين فقط)</summary>
                                <div class="mt-2 p-2 bg-gray-100 rounded">
                                    <ul>
                                        <li>الترتيب: {{ $sort }}</li>
                                        <li>الفئة: {{ $category }}</li>
                                        <li>البحث: {{ $search }}</li>
                                        <li>السعر من: {{ $minPrice }}</li>
                                        <li>السعر إلى: {{ $maxPrice }}</li>
                                        <li>نوع المعدن: {{ $metalType }}</li>
                                        <li>العيار: {{ $purity }}</li>
                                    </ul>
                                </div>
                            </details>
                        </div>
                    @endif

                    <!-- Sort Options -->
                    <div class="flex items-center gap-4">
                        <span class="text-gray-600">ترتيب حسب:</span>
                        <select value="{{ $sort }}" onchange="setFilter('product-list', 'sort', this.value)"
                            class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                            <option value="newest" {{ $sort === 'newest' ? 'selected' : '' }}>الأحدث</option>
                            <option value="oldest" {{ $sort === 'oldest' ? 'selected' : '' }}>الأقدم</option>
                            <option value="name_asc" {{ $sort === 'name_asc' ? 'selected' : '' }}>الاسم (أ-ي)</option>
                            <option value="name_desc" {{ $sort === 'name_desc' ? 'selected' : '' }}>الاسم (ي-أ)
                            </option>
                            @if ($showPriceFilter)
                                <option value="price_asc" {{ $sort === 'price_asc' ? 'selected' : '' }}>السعر (منخفض
                                    إلى مرتفع)</option>
                                <option value="price_desc" {{ $sort === 'price_desc' ? 'selected' : '' }}>السعر (مرتفع
                                    إلى منخفض)</option>
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Loading State -->
                <div wire:loading.delay class="bg-white rounded-lg shadow-sm p-8 text-center mb-6">
                    <div class="inline-flex items-center justify-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
                        <span class="mr-3 text-gray-600">جاري تحميل المنتجات...</span>
                    </div>
                    <div class="mt-4 text-sm text-gray-500">
                        يرجى الانتظار بينما نجلب أحدث المنتجات لك
                    </div>
                </div>

                <div wire:loading.delay.remove>
                    @if ($products->count() > 0)
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach ($products as $product)
                                <x-product-card :product="$product" :show-ratings="$showRatings && ($product->site_show_ratings ?? true)" :show-wishlist="$showWishlist && ($product->site_show_wishlist ?? true)" :show-whatsapp="$showWhatsapp && ($product->site_show_whatsapp ?? true)"
                                    :show-metal-info="true" :show-category="true" :badge="$product->show_price ? 'discount' : null" :size="'normal'"
                                    :layout="'grid'" :whatsapp-phone="$whatsappPhone" />
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-8">
                            {{ $products->links() }}
                        </div>
                    @else
                        <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                            <div class="text-gray-400 mb-4">
                                <i class="fas fa-search fa-3x"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 mb-2">لم يتم العثور على منتجات</h3>
                            <p class="text-gray-600 mb-4">لم نتمكن من العثور على أي منتجات تطابق معايير البحث الخاصة
                                بك.
                            </p>
                            <button onclick="callMethod('product-list', 'resetFilters')"
                                class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                                عرض جميع المنتجات
                            </button>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
