<div class="space-y-6">
    <!-- نموذج إضافة قطعة مجوهرات -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 flex items-center">
            <i class="fas fa-gem text-blue-500 mr-3"></i>
            {{ __('jewelry_value.form.title') }}
        </h2>

        <form wire:submit.prevent="addJewelryItem" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- نوع المعدن -->
                <div>
                    <label for="metal_type" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('jewelry_value.form.metal_type') }} <span class="text-red-500">*</span>
                    </label>
                    <select wire:model.live="currentItem.metal_type" id="metal_type"
                        wire:key="metal-type-{{ count($jewelryItems) }}"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="">{{ __('jewelry_value.form.select_metal') }}</option>
                        @foreach ($metalTypes as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @if ($errors->has('currentItem.metal_type'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.metal_type') }}</p>
                    @endif
                </div>

                <!-- العيار -->
                <div>
                    <label for="purity" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('jewelry_value.form.purity') }} <span class="text-red-500">*</span>
                    </label>
                    <select wire:model.live="currentItem.purity" id="purity"
                        wire:key="purity-{{ $currentItem['metal_type'] ?? 'empty' }}-{{ count($jewelryItems) }}"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        {{ empty($currentItem['metal_type']) ? 'disabled' : '' }}>
                        <option value="">{{ __('jewelry_value.form.select_purity') }}</option>
                        @foreach ($availablePurities as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @if ($errors->has('currentItem.purity'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.purity') }}</p>
                    @endif

                    @if (!empty($currentItem['metal_type']) && !empty($currentItem['purity']))
                        @php
                            $price = $latestPrices[$currentItem['metal_type']][$currentItem['purity']] ?? null;
                        @endphp
                        @if ($price)
                            <p class="mt-1 text-sm text-green-600">
                                {{ __('jewelry_value.form.current_price') }}:
                                {{ number_format($price->price_per_gram, 2) }}
                                {{ __('jewelry_value.units.egp') }}/{{ __('jewelry_value.units.grams') }}
                            </p>
                        @else
                            <p class="mt-1 text-sm text-red-600">
                                {{ __('jewelry_value.form.price_not_available') }}
                            </p>
                        @endif
                    @endif
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- الوزن -->
                <div>
                    <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('jewelry_value.form.weight') }} <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="number" wire:model.live="currentItem.weight" id="weight" step="0.01"
                            min="0.01" max="10000"
                            class="w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="{{ __('jewelry_value.form.weight_placeholder') }}">
                        <span class="absolute right-3 top-3 text-gray-500">{{ __('jewelry_value.units.grams') }}</span>
                    </div>
                    @if ($errors->has('currentItem.weight'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.weight') }}</p>
                    @endif
                </div>

                <!-- الوصف (اختياري) -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('jewelry_value.form.description') }} <span
                            class="text-gray-400">({{ __('Optional') }})</span>
                    </label>
                    <input type="text" wire:model.live="currentItem.description" id="description" maxlength="100"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="{{ __('jewelry_value.form.description_placeholder') }}">
                    @if ($errors->has('currentItem.description'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.description') }}</p>
                    @endif
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="flex flex-col sm:flex-row gap-4">
                <button type="submit"
                    class="flex-1 bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-plus mr-2"></i>
                    {{ __('jewelry_value.form.add_item') }}
                </button>

                @if (!empty($jewelryItems))
                    <button type="button" wire:click="clearAllItems"
                        wire:confirm="{{ __('jewelry_value.form.confirm_clear_all') }}"
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition duration-300 flex items-center justify-center">
                        <i class="fas fa-trash mr-2"></i>
                        {{ __('jewelry_value.form.clear_all') }}
                    </button>
                @endif
            </div>
        </form>
    </div>

    <!-- قائمة المجوهرات المضافة -->
    @if (!empty($jewelryItems))
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-list text-green-500 mr-3"></i>
                {{ __('jewelry_value.results.items_list') }} ({{ count($jewelryItems) }}
                {{ __('jewelry_value.results.items') }})
            </h3>

            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.results.item_number') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.form.metal_type') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.form.purity') }}
                            </th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.form.weight') }}
                            </th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.results.price_per_gram') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.results.item_value') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('jewelry_value.form.description') }}</th>
                            <th class="px-4 py-3 text-center font-medium text-gray-700">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        @foreach ($jewelryItems as $index => $item)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-gray-900">{{ $index + 1 }}</td>
                                <td class="px-4 py-3">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $item['metal_type'] === 'gold' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $item['metal_type'] === 'gold' ? '🥇 ذهب' : '🥈 فضة' }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-gray-900 font-medium">{{ $item['purity'] }}</td>
                                <td class="px-4 py-3 text-gray-900">{{ number_format($item['weight'], 2) }}
                                    {{ __('jewelry_value.units.grams') }}</td>
                                <td class="px-4 py-3 text-gray-900">{{ number_format($item['price_per_gram'], 2) }}
                                    {{ __('jewelry_value.units.egp') }}</td>
                                <td class="px-4 py-3 text-gray-900 font-bold">
                                    {{ number_format($item['total_value'], 2) }} {{ __('jewelry_value.units.egp') }}
                                </td>
                                <td class="px-4 py-3 text-gray-600">{{ $item['description'] ?: '-' }}</td>
                                <td class="px-4 py-3 text-center">
                                    <button wire:click="removeJewelryItem('{{ $item['id'] }}')"
                                        wire:confirm="{{ __('jewelry_value.form.confirm_remove') }}"
                                        class="text-red-600 hover:text-red-800 transition duration-200">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif

    <!-- النتائج والحسابات -->
    @if ($showResults && !empty($jewelryItems))
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-calculator text-blue-500 mr-3"></i>
                {{ __('jewelry_value.results.title') }}
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- إجمالي الوزن - الذهب -->
                @if ($totalGoldWeight > 0)
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span
                                class="text-sm font-medium text-yellow-800">{{ __('jewelry_value.results.total_gold_weight') }}</span>
                            <span
                                class="text-lg font-bold text-yellow-600">{{ number_format($totalGoldWeight, 2) }}</span>
                        </div>
                        <p class="text-xs text-yellow-600 mt-1">{{ __('jewelry_value.units.grams') }}</p>
                    </div>
                @endif

                <!-- إجمالي الوزن - الفضة -->
                @if ($totalSilverWeight > 0)
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span
                                class="text-sm font-medium text-gray-800">{{ __('jewelry_value.results.total_silver_weight') }}</span>
                            <span
                                class="text-lg font-bold text-gray-600">{{ number_format($totalSilverWeight, 2) }}</span>
                        </div>
                        <p class="text-xs text-gray-600 mt-1">{{ __('jewelry_value.units.grams') }}</p>
                    </div>
                @endif

                <!-- إجمالي القيمة -->
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <span
                            class="text-sm font-medium text-green-800">{{ __('jewelry_value.results.total_value') }}</span>
                        <span class="text-lg font-bold text-green-600">{{ number_format($totalValue, 2) }}</span>
                    </div>
                    <p class="text-xs text-green-600 mt-1">{{ __('jewelry_value.units.egp') }}</p>
                </div>
            </div>

            <!-- تفاصيل القيمة الإجمالية -->
            <div class="bg-gradient-to-r from-blue-50 to-green-50 border-2 border-blue-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="text-lg font-bold text-blue-800 flex items-center">
                        <i class="fas fa-coins mr-2"></i>
                        {{ __('jewelry_value.results.total_jewelry_value') }}
                    </h4>
                    <span class="text-3xl font-bold text-blue-600">{{ number_format($totalValue, 2) }}
                        {{ __('jewelry_value.units.egp') }}</span>
                </div>

                <div class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-800 mb-2">
                        {{ __('jewelry_value.results.calculation_breakdown') }}:</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        @if ($totalGoldWeight > 0)
                            <li>• {{ __('jewelry_value.results.gold_items') }}:
                                {{ count(array_filter($jewelryItems, fn($item) => $item['metal_type'] === 'gold')) }}
                                {{ __('jewelry_value.results.items') }}</li>
                        @endif
                        @if ($totalSilverWeight > 0)
                            <li>• {{ __('jewelry_value.results.silver_items') }}:
                                {{ count(array_filter($jewelryItems, fn($item) => $item['metal_type'] === 'silver')) }}
                                {{ __('jewelry_value.results.items') }}</li>
                        @endif
                        <li>• {{ __('jewelry_value.results.total_items') }}: {{ count($jewelryItems) }}
                            {{ __('jewelry_value.results.items') }}</li>
                        <li>• {{ __('jewelry_value.results.calculation_method') }}:
                            {{ __('jewelry_value.results.weight_times_price') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    @endif
</div>

@script
    <script>
        console.log('تحميل JavaScript للحاسبة');
    </script>
@endscript
