<div>
    <ul class="space-y-2">
        <li>
            <a href="{{ route('account.dashboard') }}" 
               class="flex items-center px-4 py-2 rounded-md {{ request()->routeIs('account.dashboard') ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                <i class="fas fa-tachometer-alt ml-3 w-5 text-center"></i>
                <span>لوحة التحكم</span>
            </a>
        </li>
        @if (!$displayOnlyMode)
        <li>
            <a href="{{ route('account.orders') }}"
               class="flex items-center px-4 py-2 rounded-md {{ request()->routeIs('account.orders') || request()->routeIs('account.orders.show') ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                <i class="fas fa-shopping-bag ml-3 w-5 text-center"></i>
                <span>طلباتي</span>
            </a>
        </li>
        @endif
        @if ($showWishlist)
        <li>
            <a href="{{ route('account.wishlist') }}"
               class="flex items-center px-4 py-2 rounded-md {{ request()->routeIs('account.wishlist') ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                <i class="fas fa-heart ml-3 w-5 text-center"></i>
                <span>المفضلة</span>
            </a>
        </li>
        @endif
        <li>
            <a href="{{ route('account.settings') }}"
               class="flex items-center px-4 py-2 rounded-md {{ request()->routeIs('account.settings') ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                <i class="fas fa-cog ml-3 w-5 text-center"></i>
                <span>إعدادات الحساب</span>
            </a>
        </li>
        <li>
            <form action="{{ route('logout') }}" method="POST">
                @csrf
                <button type="submit" class="flex items-center px-4 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-red-500 transition duration-300 w-full text-right">
                    <i class="fas fa-sign-out-alt ml-3 w-5 text-center"></i>
                    <span>تسجيل الخروج</span>
                </button>
            </form>
        </li>
    </ul>
</div>
