<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-4">
    <!-- عنوان الفلاتر -->
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-800 flex items-center">
            <i class="fas fa-filter text-primary-500 ml-2"></i>
            فلاتر البحث
        </h3>

        @if ($activeFiltersCount > 0)
            <div class="flex items-center gap-2">
                <span
                    class="bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 text-xs px-3 py-1 rounded-full font-medium shadow-sm">
                    <i class="fas fa-filter mr-1"></i>
                    {{ $activeFiltersCount }} فلتر نشط
                </span>
                <button wire:click="resetAllFilters"
                    class="text-xs text-gray-500 hover:text-red-500 underline transition duration-200 hover:bg-red-50 px-2 py-1 rounded">
                    <i class="fas fa-times mr-1"></i>
                    مسح الكل
                </button>
            </div>
        @endif
    </div>

    <!-- مؤشر التحميل المحسن -->
    <div wire:loading class="mb-4">
        <div
            class="flex items-center justify-center py-3 bg-gradient-to-r from-primary-50 to-blue-50 rounded-lg border border-primary-200">
            <div class="relative">
                <div class="animate-spin rounded-full h-5 w-5 border-2 border-primary-200"></div>
                <div class="animate-spin rounded-full h-5 w-5 border-t-2 border-primary-500 absolute top-0 left-0">
                </div>
            </div>
            <span class="mr-3 text-sm text-primary-700 font-medium">جاري التحديث...</span>
        </div>
    </div>

    <!-- مؤشر عدد النتائج -->
    <div wire:loading.remove class="mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">
        <div class="flex items-center justify-between text-sm">
            <span class="text-gray-600">
                <i class="fas fa-list mr-1"></i>
                النتائج المتاحة
            </span>
            <span class="font-semibold text-primary-600" id="results-count">
                جاري الحساب...
            </span>
        </div>
    </div>

    <!-- البحث -->
    @if ($showSearch)
        <div class="mb-6">
            <h4 class="font-medium mb-3 flex items-center text-gray-700">
                <i class="fas fa-search text-gray-400 ml-2"></i>
                بحث
                @if (!empty($search))
                    <span
                        class="bg-gradient-to-r from-green-100 to-green-200 text-green-800 text-xs px-2 py-1 rounded-full mr-2 font-medium shadow-sm">
                        <i class="fas fa-check-circle mr-1"></i>نشط
                    </span>
                @endif
            </h4>
            <div class="relative group">
                <input type="text" wire:model.live.debounce.300ms="search" placeholder="ابحث عن منتج..."
                    class="w-full border border-gray-300 bg-white text-gray-900 rounded-md px-3 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 group-hover:border-primary-400
                    @if (!empty($search)) border-primary-500 bg-primary-50 @endif">
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                    @if (!empty($search))
                        <button wire:click="$set('search', '')"
                            class="text-gray-400 hover:text-gray-600 transition duration-200 p-1 rounded-full hover:bg-gray-100">
                            <i class="fas fa-times text-xs"></i>
                        </button>
                    @endif
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
    @endif

    <!-- الفئات -->
    @if ($showCategoryFilter && count($availableCategories) > 0)
        <div class="mb-6">
            <h4 class="font-medium mb-3 flex items-center">
                <i class="fas fa-tags text-gray-400 ml-2"></i>
                الفئات
                @if (!empty($category))
                    <span
                        class="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full mr-2 font-medium shadow-sm">
                        <i class="fas fa-check-circle mr-1"></i>نشط
                    </span>
                @endif
            </h4>
            <div class="space-y-2 max-h-48 overflow-y-auto">
                <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                    <input type="radio" wire:model.live="category" name="category_filter" value=""
                        class="ml-2 text-primary-500 focus:ring-primary-500">
                    <span class="@if (empty($category)) text-primary-700 font-medium @endif">
                        جميع المنتجات
                    </span>
                </label>
                @foreach ($availableCategories as $cat)
                    <label
                        class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                        <input type="radio" wire:model.live="category" name="category_filter"
                            value="{{ $cat->slug }}" class="ml-2 text-primary-500 focus:ring-primary-500">
                        <span class="@if ($category === $cat->slug) text-primary-700 font-medium @endif">
                            {{ $cat->name_ar }}
                        </span>
                    </label>
                @endforeach
            </div>
        </div>
    @endif

    <!-- نطاق السعر -->
    @if ($showPriceFilter && !$displaySettings['displayOnlyMode'])
        <div class="mb-6">
            <h4 class="font-medium mb-3 flex items-center">
                <i class="fas fa-dollar-sign text-gray-400 ml-2"></i>
                نطاق السعر
                @if ($minPrice !== null || $maxPrice !== null)
                    <span
                        class="bg-gradient-to-r from-yellow-100 to-yellow-200 text-yellow-800 text-xs px-2 py-1 rounded-full mr-2 font-medium shadow-sm">
                        <i class="fas fa-check-circle mr-1"></i>نشط
                    </span>
                @endif
            </h4>
            <div class="space-y-3">
                <div>
                    <label class="block text-sm text-gray-600 mb-1">الحد الأدنى</label>
                    <input type="number" wire:model.live.debounce.500ms="minPrice" placeholder="0" min="0"
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary-500">
                </div>
                <div>
                    <label class="block text-sm text-gray-600 mb-1">الحد الأقصى</label>
                    <input type="number" wire:model.live.debounce.500ms="maxPrice" placeholder="بدون حد" min="0"
                        class="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-1 focus:ring-primary-500">
                </div>
                @if ($minPrice !== null || $maxPrice !== null)
                    <button wire:click="resetPriceFilter"
                        class="text-xs text-gray-500 hover:text-primary-500 underline">
                        مسح نطاق السعر
                    </button>
                @endif
                @if ($priceRange['min'] > 0 || $priceRange['max'] > 0)
                    <div class="text-xs text-gray-500 mt-2">
                        النطاق المتاح: {{ number_format($priceRange['min']) }} - {{ number_format($priceRange['max']) }}
                        ج.م
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- نوع المعدن -->
    @if ($showMetalFilter && count($availableMetals) > 0)
        <div class="mb-6">
            <h4 class="font-medium mb-3 flex items-center">
                <i class="fas fa-gem text-gray-400 ml-2"></i>
                نوع المعدن
                @if (!empty($metalType))
                    <span
                        class="bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 text-xs px-2 py-1 rounded-full mr-2 font-medium shadow-sm">
                        <i class="fas fa-check-circle mr-1"></i>نشط
                    </span>
                @endif
            </h4>
            <div class="space-y-2">
                <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                    <input type="radio" wire:model.live="metalType" name="metal_filter" value=""
                        class="ml-2 text-primary-500 focus:ring-primary-500">
                    <span class="@if (empty($metalType)) text-primary-700 font-medium @endif">
                        جميع المعادن
                    </span>
                </label>
                @foreach ($availableMetals as $metal)
                    <label
                        class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                        <input type="radio" wire:model.live="metalType" name="metal_filter"
                            value="{{ $metal->name }}" class="ml-2 text-primary-500 focus:ring-primary-500">
                        <span class="@if ($metalType === $metal->name) text-primary-700 font-medium @endif">
                            {{ $metal->name_ar }}
                        </span>
                    </label>
                @endforeach
            </div>
        </div>
    @endif

    <!-- عيار المعدن -->
    @if ($showPurityFilter && count($availablePurities) > 0)
        <div class="mb-6">
            <h4 class="font-medium mb-3 flex items-center">
                <i class="fas fa-certificate text-gray-400 ml-2"></i>
                عيار المعدن
                @if (!empty($purity))
                    <span
                        class="bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800 text-xs px-2 py-1 rounded-full mr-2 font-medium shadow-sm">
                        <i class="fas fa-check-circle mr-1"></i>نشط
                    </span>
                @endif
            </h4>
            <div class="space-y-2">
                <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                    <input type="radio" wire:model.live="purity" name="purity_filter" value=""
                        class="ml-2 text-primary-500 focus:ring-primary-500">
                    <span class="@if (empty($purity)) text-primary-700 font-medium @endif">
                        جميع العيارات
                    </span>
                </label>
                @foreach ($availablePurities as $metalPurity)
                    <label
                        class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                        <input type="radio" wire:model.live="purity" name="purity_filter"
                            value="{{ $metalPurity->name }}" class="ml-2 text-primary-500 focus:ring-primary-500">
                        <span class="@if ($purity === $metalPurity->name) text-primary-700 font-medium @endif">
                            {{ $metalPurity->name_ar }}
                        </span>
                    </label>
                @endforeach
            </div>
        </div>
    @endif

    <!-- فلاتر سريعة للترتيب -->
    <div class="border-t border-gray-200 pt-4">
        <h4 class="font-medium mb-3 text-gray-700">ترتيب النتائج</h4>
        <div class="space-y-2">
            <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                <input type="radio" wire:model.live="sort" name="sort_filter" value="newest"
                    class="ml-2 text-primary-500 focus:ring-primary-500">
                <span class="@if ($sort === 'newest') text-primary-700 font-medium @endif">
                    الأحدث
                </span>
            </label>
            @if (!$displaySettings['displayOnlyMode'])
                <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                    <input type="radio" wire:model.live="sort" name="sort_filter" value="price_asc"
                        class="ml-2 text-primary-500 focus:ring-primary-500">
                    <span class="@if ($sort === 'price_asc') text-primary-700 font-medium @endif">
                        الأرخص أولاً
                    </span>
                </label>
                <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                    <input type="radio" wire:model.live="sort" name="sort_filter" value="price_desc"
                        class="ml-2 text-primary-500 focus:ring-primary-500">
                    <span class="@if ($sort === 'price_desc') text-primary-700 font-medium @endif">
                        الأغلى أولاً
                    </span>
                </label>
            @endif
            <label class="flex items-center cursor-pointer hover:bg-gray-50 p-2 rounded transition duration-200">
                <input type="radio" wire:model.live="sort" name="sort_filter" value="name"
                    class="ml-2 text-primary-500 focus:ring-primary-500">
                <span class="@if ($sort === 'name') text-primary-700 font-medium @endif">
                    الاسم
                </span>
            </label>
        </div>
    </div>


</div>
