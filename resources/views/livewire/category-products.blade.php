<div>
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-1/4">
            @livewire('product-filters', [
                'targetComponent' => 'category-products',
                'currentPage' => 'category',
                'showSearch' => true,
                'showPriceFilter' => $showPrice,
                'showCategoryFilter' => false,
                'showMetalFilter' => true,
                'showPurityFilter' => true,
                'initialFilters' => [
                    'search' => $search,
                    'category' => $category->slug,
                    'minPrice' => $minPrice,
                    'maxPrice' => $maxPrice,
                    'metalType' => $metalType,
                    'purity' => $purity,
                    'sort' => $sort,
                ],
            ])
        </div>

        <!-- Products Grid -->
        <div class="lg:w-3/4">
            <!-- Toolbar -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6 flex flex-wrap justify-between items-center">
                <div class="mb-2 sm:mb-0">
                    <span class="text-gray-600">{{ __('عرض') }}
                        {{ $products->firstItem() ?? 0 }}-{{ $products->lastItem() ?? 0 }}
                        {{ __('من') }} {{ $products->total() ?? 0 }} {{ __('منتج') }}</span>
                </div>

                <!-- Debug info - remove in production -->
                @if (config('app.debug'))
                    <div class="text-xs text-gray-500 mb-2">
                        <details>
                            <summary>معلومات التصفية (للمطورين فقط)</summary>
                            <div class="mt-2 p-2 bg-gray-100 rounded">
                                <ul>
                                    <li>الترتيب: {{ $sort }}</li>
                                    <li>الفئة: {{ $category->name_ar }} ({{ $category->slug }})</li>
                                    <li>البحث: {{ $search }}</li>
                                    <li>السعر من: {{ $minPrice }}</li>
                                    <li>السعر إلى: {{ $maxPrice }}</li>
                                    <li>نوع المعدن: {{ $metalType }}</li>
                                    <li>العيار: {{ $purity }}</li>
                                    <li>أنواع المعادن المتاحة: {{ implode(', ', $availableMetalTypes) }}</li>
                                    <li>العيارات المتاحة: {{ implode(', ', $availablePurities) }}</li>
                                    <li>نطاق السعر:
                                        {{ isset($priceRange['min']) ? number_format($priceRange['min']) : 0 }} -
                                        {{ isset($priceRange['max']) ? number_format($priceRange['max']) : 0 }}</li>
                                </ul>
                            </div>
                        </details>
                    </div>
                @endif

                <div class="flex items-center">
                    <label for="sort" class="text-gray-600 ml-2">{{ __('ترتيب حسب:') }}</label>
                    <select value="{{ $sort }}" onchange="setFilter('category-products', 'sort', this.value)"
                        class="border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-1 focus:ring-primary-500">
                        <option value="newest" {{ $sort === 'newest' ? 'selected' : '' }}>{{ __('الأحدث') }}</option>
                        @if ($showPrice)
                            <option value="price_asc" {{ $sort === 'price_asc' ? 'selected' : '' }}>
                                {{ __('السعر: من الأقل إلى الأعلى') }}</option>
                            <option value="price_desc" {{ $sort === 'price_desc' ? 'selected' : '' }}>
                                {{ __('السعر: من الأعلى إلى الأقل') }}</option>
                        @endif
                        <option value="oldest" {{ $sort === 'oldest' ? 'selected' : '' }}>{{ __('الأقدم') }}</option>
                    </select>
                </div>
            </div>

            <div wire:loading.delay class="w-full text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                </div>
                <p class="mt-2 text-gray-500">{{ __('جاري تحميل المنتجات...') }}</p>
            </div>

            <div wire:loading.delay.remove>
                @if ($products->count() > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach ($products as $product)
                            <x-product-card :product="$product" :show-ratings="$showRatings ?? true" :show-wishlist="$showWishlist ?? true" :show-metal-info="true"
                                :show-category="false" :badge="'discount'" :size="'normal'" :layout="'grid'"
                                :whatsapp-phone="$whatsappPhone" />
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8">
                        {{ $products->links() }}
                    </div>
                @else
                    <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-search fa-3x"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">{{ __('لم يتم العثور على منتجات') }}</h3>
                        <p class="text-gray-600 mb-4">{{ __('لم نتمكن من العثور على أي منتجات في هذه الفئة.') }}</p>
                        <a href="{{ route('products') }}"
                            class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                            {{ __('عرض جميع المنتجات') }}
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
