<div class="bg-white rounded-lg shadow-sm p-6">
    <h2 class="text-xl font-bold mb-6">معلومات الشحن</h2>

    <form wire:submit.prevent="continue">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
                <label for="name" class="block text-gray-700 font-medium mb-2">الاسم الكامل <span
                        class="text-red-500">*</span></label>
                <input type="text" wire:model="name" id="name" required
                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                @error('name')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="email" class="block text-gray-700 font-medium mb-2">البريد الإلكتروني <span
                        class="text-red-500">*</span></label>
                <input type="email" wire:model="email" id="email" required
                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                @error('email')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div class="mb-6">
            <label for="phone" class="block text-gray-700 font-medium mb-2">رقم الهاتف <span
                    class="text-red-500">*</span></label>
            <input type="tel" wire:model="phone" id="phone" required
                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
            @error('phone')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="mb-6">
            <label for="address" class="block text-gray-700 font-medium mb-2">العنوان <span
                    class="text-red-500">*</span></label>
            <input type="text" wire:model="address" id="address" required
                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
            @error('address')
                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
            @enderror
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
                <label for="city" class="block text-gray-700 font-medium mb-2">المدينة <span
                        class="text-red-500">*</span></label>
                <input type="text" wire:model="city" id="city" required
                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                @error('city')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="country" class="block text-gray-700 font-medium mb-2">البلد <span
                        class="text-red-500">*</span></label>
                <input type="text" wire:model="country" id="country" required
                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                @error('country')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>

            <div>
                <label for="postalCode" class="block text-gray-700 font-medium mb-2">الرمز البريدي</label>
                <input type="text" wire:model="postalCode" id="postalCode"
                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                @error('postalCode')
                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <div class="flex justify-end">
            <button type="submit"
                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                متابعة للدفع
            </button>
        </div>
    </form>
</div>
