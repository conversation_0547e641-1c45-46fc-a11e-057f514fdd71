<div class="bg-white rounded-lg shadow-sm p-6">
    <h2 class="text-xl font-bold mb-6">طريقة الدفع</h2>

    <form wire:submit.prevent="continue">
        <div class="space-y-4">
            @foreach ($availableMethods as $key => $method)
                <div
                    class="border border-gray-200 rounded-md p-4 {{ $paymentMethod == $key ? 'border-primary-500 bg-primary-50' : '' }}">
                    <label class="flex items-center cursor-pointer">
                        <input type="radio" wire:model.live="paymentMethod" value="{{ $key }}"
                            class="ml-3 text-primary-500 focus:ring-primary-500">
                        <div>
                            <span class="font-medium">{{ $method['name'] }}</span>
                            <p class="text-gray-600 text-sm">{{ $method['description'] }}</p>
                        </div>
                    </label>

                    @if ($key == 'bank_transfer' && $paymentMethod == 'bank_transfer')
                        <div class="mt-4 pr-6">
                            <p class="text-gray-600 text-sm mb-2">يرجى استخدام رقم الطلب الخاص بك كمرجع للدفع. لن يتم
                                شحن طلبك حتى يتم تأكيد استلام الأموال في حسابنا.</p>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p class="text-sm"><span class="font-medium">اسم البنك:</span> البنك الأهلي المصري</p>
                                <p class="text-sm"><span class="font-medium">اسم الحساب:</span> شركة مكة جولد للمجوهرات
                                </p>
                                <p class="text-sm"><span class="font-medium">رقم الحساب:</span> *********</p>
                                <p class="text-sm"><span class="font-medium">رقم IBAN:</span> **************************
                                </p>
                            </div>
                        </div>
                    @endif

                    @if ($key == 'credit_card' && $paymentMethod == 'credit_card')
                        <div class="mt-4 pr-6">
                            <p class="text-gray-600 text-sm mb-2">سيتم تحويلك إلى صفحة الدفع الآمنة بعد تقديم الطلب.</p>
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <img src="{{ asset('images/payment/visa.png') }}" alt="Visa" class="h-8">
                                <img src="{{ asset('images/payment/mastercard.png') }}" alt="Mastercard" class="h-8">
                                <img src="{{ asset('images/payment/amex.png') }}" alt="American Express" class="h-8">
                            </div>
                        </div>
                    @endif

                    @if ($key == 'paypal' && $paymentMethod == 'paypal')
                        <div class="mt-4 pr-6">
                            <p class="text-gray-600 text-sm mb-2">سيتم تحويلك إلى صفحة PayPal الآمنة بعد تقديم الطلب.
                            </p>
                            <div class="flex items-center">
                                <img src="{{ asset('images/payment/paypal.png') }}" alt="PayPal" class="h-10">
                            </div>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>

        <div class="flex justify-between mt-8">
            <button type="button" onclick="window.history.back()"
                class="text-gray-600 hover:text-gray-800 font-medium">
                <i class="fas fa-arrow-right ml-1"></i>
                العودة
            </button>

            <button type="submit"
                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                متابعة للمراجعة
            </button>
        </div>
    </form>
</div>
