<div class="bg-white rounded-lg shadow-sm p-6 sticky top-6">
    <h2 class="text-xl font-bold mb-6">ملخص الطلب</h2>

    <div class="space-y-4 mb-6">
        @foreach ($cart->items as $item)
            <div class="flex items-start">
                <img src="{{ $item->product->image ? asset('storage/' . $item->product->image) : asset('images/products/default.jpg') }}"
                    alt="{{ $item->product->name_ar }}" class="w-16 h-16 object-cover rounded-md ml-4">
                <div>
                    <h4 class="font-medium text-gray-800">{{ $item->product->name_ar }}</h4>
                    <div class="flex items-center text-sm text-gray-500">
                        <span>{{ $item->quantity }} x {{ number_format($item->price, 2) }} ج.م</span>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <div class="border-t border-gray-200 pt-4 space-y-3 mb-6">
        <div class="flex justify-between">
            <span class="text-gray-600">المجموع الفرعي</span>
            <span class="text-gray-800 font-medium">{{ number_format($cart->subtotal, 2) }} ج.م</span>
        </div>

        <div class="flex justify-between">
            <span class="text-gray-600">الضريبة (14%)</span>
            <span class="text-gray-800 font-medium">{{ number_format($cart->tax, 2) }} ج.م</span>
        </div>

        <div class="flex justify-between">
            <span class="text-gray-600">الشحن</span>
            @if ($cart->shipping > 0)
                <span class="text-gray-800 font-medium">{{ number_format($cart->shipping, 2) }} ج.م</span>
            @else
                <span class="text-green-500 font-medium">مجاني</span>
            @endif
        </div>

        @if ($cart->discount > 0)
            <div class="flex justify-between text-green-500">
                <span>الخصم</span>
                <span class="font-medium">- {{ number_format($cart->discount, 2) }} ج.م</span>
            </div>
        @endif

        <div class="pt-3 border-t border-gray-200 flex justify-between">
            <span class="text-lg font-bold">الإجمالي</span>
            <span class="text-lg text-primary-500 font-bold">{{ number_format($cart->total, 2) }} ج.م</span>
        </div>
    </div>

    @if ($step == 3)
        <div class="mb-6">
            <div class="flex items-center mb-4">
                <input type="checkbox" id="terms" wire:model.live="termsAccepted"
                    class="ml-2 text-primary-500 focus:ring-primary-500">
                <label for="terms" class="text-gray-600 text-sm">لقد قرأت ووافقت على <a href="{{ route('terms') }}"
                        class="text-primary-500 hover:text-primary-600">الشروط والأحكام</a></label>
            </div>

            <button wire:click="placeOrder"
                class="w-full bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                إتمام الطلب
            </button>
        </div>
    @endif

    <div class="text-center">
        <p class="text-gray-600 text-sm mb-4">طرق الدفع المقبولة</p>
        <div class="flex items-center justify-center space-x-4 space-x-reverse">
            <img src="{{ asset('images/payment/visa.png') }}" alt="Visa" class="h-8">
            <img src="{{ asset('images/payment/mastercard.png') }}" alt="Mastercard" class="h-8">
            <img src="{{ asset('images/payment/paypal.png') }}" alt="PayPal" class="h-8">
            <img src="{{ asset('images/payment/fawry.png') }}" alt="Fawry" class="h-8">
        </div>
    </div>
</div>
