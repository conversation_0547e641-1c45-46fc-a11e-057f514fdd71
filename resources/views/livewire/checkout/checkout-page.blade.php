<div>
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('cart.index') }}" class="text-gray-600 hover:text-primary-500">سلة التسوق</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">الدفع</span>
            </div>
        </div>
    </div>

    <!-- Checkout Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold mb-8 text-center">الدفع</h1>

            <!-- Checkout Steps -->
            <div class="flex justify-center mb-12">
                <div class="flex items-center">
                    <div class="flex flex-col items-center">
                        <div
                            class="w-10 h-10 rounded-full flex items-center justify-center {{ $step >= 1 ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-500' }}">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <span
                            class="text-sm mt-2 {{ $step >= 1 ? 'text-primary-500 font-medium' : 'text-gray-500' }}">الشحن</span>
                    </div>
                    <div class="w-16 h-1 {{ $step >= 2 ? 'bg-primary-500' : 'bg-gray-200' }} mx-2"></div>
                    <div class="flex flex-col items-center">
                        <div
                            class="w-10 h-10 rounded-full flex items-center justify-center {{ $step >= 2 ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-500' }}">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <span
                            class="text-sm mt-2 {{ $step >= 2 ? 'text-primary-500 font-medium' : 'text-gray-500' }}">الدفع</span>
                    </div>
                    <div class="w-16 h-1 {{ $step >= 3 ? 'bg-primary-500' : 'bg-gray-200' }} mx-2"></div>
                    <div class="flex flex-col items-center">
                        <div
                            class="w-10 h-10 rounded-full flex items-center justify-center {{ $step >= 3 ? 'bg-primary-500 text-white' : 'bg-gray-200 text-gray-500' }}">
                            <i class="fas fa-check"></i>
                        </div>
                        <span
                            class="text-sm mt-2 {{ $step >= 3 ? 'text-primary-500 font-medium' : 'text-gray-500' }}">المراجعة</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Checkout Form -->
                <div class="lg:col-span-2">
                    @if ($step == 1)
                        @livewire('checkout.shipping-form', ['shippingData' => $shippingData])
                    @elseif($step == 2)
                        @livewire('checkout.payment-form', ['paymentMethod' => $paymentMethod])
                    @elseif($step == 3)
                        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-xl font-bold">معلومات الشحن</h2>
                                <button wire:click="goToStep(1)"
                                    class="text-primary-500 hover:text-primary-600 text-sm font-medium">
                                    <i class="fas fa-edit ml-1"></i>
                                    تعديل
                                </button>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <p class="text-gray-600 text-sm mb-1">الاسم الكامل</p>
                                    <p class="font-medium">{{ $shippingData['name'] ?? '' }}</p>
                                </div>

                                <div>
                                    <p class="text-gray-600 text-sm mb-1">البريد الإلكتروني</p>
                                    <p class="font-medium">{{ $shippingData['email'] ?? '' }}</p>
                                </div>

                                <div>
                                    <p class="text-gray-600 text-sm mb-1">رقم الهاتف</p>
                                    <p class="font-medium">{{ $shippingData['phone'] ?? '' }}</p>
                                </div>

                                <div>
                                    <p class="text-gray-600 text-sm mb-1">العنوان</p>
                                    <p class="font-medium">{{ $shippingData['address'] ?? '' }}</p>
                                </div>

                                <div>
                                    <p class="text-gray-600 text-sm mb-1">المدينة</p>
                                    <p class="font-medium">{{ $shippingData['city'] ?? '' }}</p>
                                </div>

                                <div>
                                    <p class="text-gray-600 text-sm mb-1">البلد</p>
                                    <p class="font-medium">{{ $shippingData['country'] ?? '' }}</p>
                                </div>

                                @if (!empty($shippingData['postal_code']))
                                    <div>
                                        <p class="text-gray-600 text-sm mb-1">الرمز البريدي</p>
                                        <p class="font-medium">{{ $shippingData['postal_code'] }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                            <div class="flex justify-between items-center mb-6">
                                <h2 class="text-xl font-bold">طريقة الدفع</h2>
                                <button wire:click="goToStep(2)"
                                    class="text-primary-500 hover:text-primary-600 text-sm font-medium">
                                    <i class="fas fa-edit ml-1"></i>
                                    تعديل
                                </button>
                            </div>

                            <div class="flex items-center">
                                @if ($paymentMethod == 'cash')
                                    <i class="fas fa-money-bill-wave text-primary-500 text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium">الدفع عند الاستلام</p>
                                        <p class="text-gray-600 text-sm">ادفع نقدًا عند استلام طلبك</p>
                                    </div>
                                @elseif($paymentMethod == 'credit_card')
                                    <i class="fas fa-credit-card text-primary-500 text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium">بطاقة ائتمان</p>
                                        <p class="text-gray-600 text-sm">ادفع باستخدام بطاقة الائتمان الخاصة بك</p>
                                    </div>
                                @elseif($paymentMethod == 'paypal')
                                    <i class="fab fa-paypal text-primary-500 text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium">PayPal</p>
                                        <p class="text-gray-600 text-sm">ادفع باستخدام حساب PayPal الخاص بك</p>
                                    </div>
                                @elseif($paymentMethod == 'bank_transfer')
                                    <i class="fas fa-university text-primary-500 text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium">تحويل بنكي</p>
                                        <p class="text-gray-600 text-sm">قم بالتحويل البنكي مباشرة إلى حسابنا</p>
                                    </div>
                                @endif
                            </div>

                            @if ($paymentMethod == 'bank_transfer')
                                <div class="mt-4 bg-gray-50 p-4 rounded-md">
                                    <p class="text-gray-600 text-sm mb-2">يرجى استخدام رقم الطلب الخاص بك كمرجع للدفع.
                                        لن يتم شحن طلبك حتى يتم تأكيد استلام الأموال في حسابنا.</p>
                                    <div>
                                        <p class="text-sm"><span class="font-medium">اسم البنك:</span> البنك الأهلي
                                            المصري</p>
                                        <p class="text-sm"><span class="font-medium">اسم الحساب:</span> شركة مكة جولد
                                            للمجوهرات</p>
                                        <p class="text-sm"><span class="font-medium">رقم الحساب:</span> *********</p>
                                        <p class="text-sm"><span class="font-medium">رقم IBAN:</span>
                                            EG*********0*********01234</p>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-xl font-bold mb-6">ملاحظات الطلب</h2>

                            <div class="mb-6">
                                <label for="order_notes" class="block text-gray-700 font-medium mb-2">ملاحظات إضافية
                                    (اختياري)</label>
                                <textarea wire:model="orderNotes" id="order_notes" rows="3"
                                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                            </div>

                            <div class="flex items-center mb-6">
                                <input type="checkbox" id="terms" wire:model.live="termsAccepted"
                                    class="ml-2 text-primary-500 focus:ring-primary-500">
                                <label for="terms" class="text-gray-600 text-sm">لقد قرأت ووافقت على <a
                                        href="{{ route('terms') }}"
                                        class="text-primary-500 hover:text-primary-600">الشروط والأحكام</a></label>
                            </div>

                            <button wire:click="placeOrder"
                                class="w-full bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                إتمام الطلب
                            </button>
                        </div>
                    @endif
                </div>

                <!-- Order Summary -->
                <div class="lg:col-span-1">
                    @livewire('checkout.order-summary', [
                        'cart' => $cart,
                        'step' => $step,
                        'shippingData' => $shippingData,
                        'paymentMethod' => $paymentMethod,
                    ])
                </div>
            </div>
        </div>
    </section>
</div>
