<div class="relative" x-data="{ open: false }">
    <form wire:submit.prevent="search" class="relative">
        <div class="flex">
            <input type="text" wire:model.live.debounce.300ms="query" placeholder="ابحث عن منتجات..."
                class="w-full py-2 px-4 border border-gray-300 rounded-r-full focus:outline-none focus:ring-2 focus:ring-primary-500"
                autocomplete="off" @focus="open = true" @click.away="open = false">
            <button type="submit"
                class="bg-primary-500 text-white px-4 rounded-l-full hover:bg-primary-600 transition duration-300">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </form>

    <!-- Dropdown Results -->
    <div x-show="open && $wire.products.length > 0" x-cloak
        class="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-60 overflow-y-auto">
        <ul class="py-1">
            @forelse($products as $product)
                <li wire:key="search-result-{{ $product->id }}">
                    <button wire:click="selectProduct('{{ $product->slug }}')"
                        class="w-full text-right block px-4 py-2 text-sm hover:bg-gray-100">
                        <div class="flex items-center">
                            @if ($product->image)
                                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name_ar }}"
                                    class="w-10 h-10 object-cover rounded-md ml-3">
                            @else
                                <div class="w-10 h-10 bg-gray-200 rounded-md ml-3 flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                            @endif
                            <div class="flex-1">
                                <div class="font-medium text-gray-800">{{ $product->name_ar }}</div>
                                <div class="text-gray-500 text-xs">{{ $product->category->name_ar ?? 'غير محدد' }}</div>
                                @if ($product->show_price)
                                    <div class="text-primary-600 text-sm font-medium">
                                        {{ number_format($product->price, 2) }} ج.م</div>
                                @endif
                            </div>
                        </div>
                    </button>
                </li>
            @empty
                <li class="px-4 py-2 text-sm text-gray-500">لا توجد نتائج</li>
            @endforelse
        </ul>
    </div>

    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>
</div>
