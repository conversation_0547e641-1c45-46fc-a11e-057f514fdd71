<div class="relative">
    <button wire:click="toggleDropdown" class="relative p-1 text-gray-600 hover:text-primary-500 focus:outline-none">
        <i class="fas fa-bell text-xl"></i>
        @if ($unreadCount > 0)
            <span
                class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
                {{ $unreadCount > 9 ? '9+' : $unreadCount }}
            </span>
        @endif
    </button>

    @if ($showDropdown)
        <div class="absolute left-0 mt-2 w-80 bg-white rounded-md shadow-lg overflow-hidden z-50">
            <div class="py-2 px-3 bg-gray-100 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-sm font-medium text-gray-700">الإشعارات</h3>
                @if ($unreadCount > 0)
                    <button wire:click="markAllAsRead" class="text-xs text-primary-500 hover:text-primary-600">
                        تعيين الكل كمقروء
                    </button>
                @endif
            </div>

            <div class="max-h-64 overflow-y-auto">
                @forelse($notifications as $notification)
                    <div class="p-3 border-b border-gray-100 {{ $notification->read ? 'bg-white' : 'bg-blue-50' }}">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 ml-3">
                                @if ($notification->type == 'info')
                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                        <i class="fas fa-info text-blue-500"></i>
                                    </div>
                                @elseif($notification->type == 'success')
                                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                        <i class="fas fa-check text-green-500"></i>
                                    </div>
                                @elseif($notification->type == 'warning')
                                    <div class="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                                        <i class="fas fa-exclamation text-yellow-500"></i>
                                    </div>
                                @elseif($notification->type == 'error')
                                    <div class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center">
                                        <i class="fas fa-times text-red-500"></i>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $notification->title }}</h4>
                                    <span
                                        class="text-xs text-gray-500">{{ $notification->created_at->diffForHumans() }}</span>
                                </div>
                                <p class="text-xs text-gray-600 mt-1">{{ $notification->message }}</p>
                                <div class="mt-2 flex justify-between">
                                    @if ($notification->link)
                                        <a href="{{ $notification->link }}"
                                            class="text-xs text-primary-500 hover:text-primary-600">
                                            عرض التفاصيل
                                        </a>
                                    @endif
                                    @if (!$notification->read)
                                        <button wire:click="markAsRead({{ $notification->id }})"
                                            class="text-xs text-gray-500 hover:text-gray-700">
                                            تعيين كمقروء
                                        </button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="p-4 text-center text-gray-500">
                        لا توجد إشعارات
                    </div>
                @endforelse
            </div>

            <div class="py-2 px-3 bg-gray-100 text-center">
                <a href="{{ route('account.notifications') }}" class="text-xs text-primary-500 hover:text-primary-600">
                    عرض كل الإشعارات
                </a>
            </div>
        </div>
    @endif
</div>
