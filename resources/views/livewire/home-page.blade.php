<div>


    <!-- Hero Section -->
    <section class="relative">
        <!-- <PERSON> Slider -->
        <div class="swiper hero-slider">
            <div class="swiper-wrapper">
                @if (count($sliders) > 0)
                    {{-- عرض الصور من قاعدة البيانات --}}
                    @foreach ($sliders as $slider)
                        <div class="swiper-slide">
                            <div class="relative h-[400px] sm:h-[500px] md:h-[600px]">
                                @php
                                    // تشخيص مسار الصورة
                                    $imagePath = $slider->image;
                                    $fullImagePath = storage_path('app/public/' . $imagePath);
                                    $imageExists = !empty($imagePath) && file_exists($fullImagePath);

                                    // طباعة معلومات التشخيص (يمكن حذفها لاحقاً)
                                    // dd([
                                    //     'slider_id' => $slider->id ?? 'N/A',
                                    //     'image_path' => $imagePath,
                                    //     'full_path' => $fullImagePath,
                                    //     'exists' => $imageExists,
                                    //     'url' => $imageExists ? asset('storage/' . $imagePath) : 'default'
                                    // ]);

                                @endphp

                                @if ($slider->image_url)
                                    {{-- عرض صورة من قاعدة البيانات --}}
                                    <img src="{{ $slider->image_url }}"
                                        alt="{{ $locale == 'ar' ? $slider->title_ar : $slider->title_en }}"
                                        class="absolute inset-0 w-full h-full object-cover object-center" loading="lazy"
                                        onerror="console.error('Error loading slider image:', this.src); this.src='{{ asset('images/hero/hero-1.jpg') }}';">
                                @else
                                    {{-- عرض الصورة الافتراضية --}}
                                    <img src="{{ asset('images/hero/hero-1.jpg') }}"
                                        alt="{{ $locale == 'ar' ? $slider->title_ar ?? 'مكة جولد' : $slider->title_en ?? 'Makkah Gold' }}"
                                        class="absolute inset-0 w-full h-full object-cover object-center"
                                        loading="lazy">
                                    {{-- رسالة تشخيص في وضع التطوير --}}
                                    @if (config('app.debug'))
                                        <div
                                            class="absolute top-4 left-4 bg-red-500 text-white text-xs px-2 py-1 rounded">
                                            Image not found: {{ $imagePath ?? 'No path' }}
                                        </div>
                                    @endif
                                @endif

                                <div class="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/20">
                                </div>
                                <div class="absolute inset-0 flex items-center">
                                    <div class="container mx-auto px-4 sm:px-6">
                                        <div class="max-w-xl">
                                            <h1
                                                class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 leading-tight">
                                                {{ $locale == 'ar' ? $slider->title_ar ?? 'عنوان افتراضي' : $slider->title_en ?? 'Default Title' }}
                                            </h1>
                                            <p
                                                class="text-sm sm:text-base md:text-lg lg:text-xl text-white/90 mb-6 sm:mb-8 leading-relaxed">
                                                {{ $locale == 'ar' ? $slider->description_ar ?? 'وصف افتراضي' : $slider->description_en ?? 'Default Description' }}
                                            </p>
                                            <div class="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4">
                                                <a href="{{ $slider->button_link ?? route('products') }}"
                                                    class="bg-primary-500 hover:bg-primary-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-full font-medium transition duration-300 text-center text-sm sm:text-base inline-block">
                                                    {{ $locale == 'ar' ? $slider->button_text_ar ?? 'تسوق الآن' : $slider->button_text_en ?? 'Shop Now' }}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    {{-- عرض شريحة افتراضية عند عدم وجود بيانات --}}
                    <div class="swiper-slide">
                        <div class="relative h-[400px] sm:h-[500px] md:h-[600px]">
                            <img src="{{ asset('images/hero/hero-1.jpg') }}" alt="{{ __('Makkah Gold') }}"
                                class="absolute inset-0 w-full h-full object-cover object-center">
                            <div class="absolute inset-0 bg-gradient-to-r from-black/60 via-black/40 to-black/20"></div>
                            <div class="absolute inset-0 flex items-center">
                                <div class="container mx-auto px-4 sm:px-6">
                                    <div class="max-w-xl">
                                        <h1
                                            class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4 leading-tight">
                                            {{ __('Luxury Jewelry with Unique Designs') }}</h1>
                                        <p
                                            class="text-sm sm:text-base md:text-lg lg:text-xl text-white/90 mb-6 sm:mb-8 leading-relaxed">
                                            {{ __('Discover our exclusive collection of gold, silver and platinum jewelry') }}
                                        </p>
                                        <div class="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4">
                                            <a href="{{ route('products') }}"
                                                class="bg-primary-500 hover:bg-primary-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-full font-medium transition duration-300 text-center text-sm sm:text-base inline-block">
                                                @if (isset($displayOnlyMode) && $displayOnlyMode)
                                                    {{ __('Browse Products') }}
                                                @else
                                                    {{ __('Shop Now') }}
                                                @endif
                                            </a>
                                            <a href="{{ route('appointment') }}"
                                                class="bg-white hover:bg-gray-100 text-primary-500 px-4 sm:px-6 py-2.5 sm:py-3 rounded-full font-medium transition duration-300 text-center text-sm sm:text-base inline-block">{{ __('Book Appointment') }}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            <!-- Add Pagination -->
            <div class="swiper-pagination !bottom-4"></div>
            <!-- Add Navigation -->
            <div class="swiper-button-next text-white !hidden sm:!flex"></div>
            <div class="swiper-button-prev text-white !hidden sm:!flex"></div>
        </div>
    </section>

    <!-- Features Section -->
    @if (isset($showFeatures) && $showFeatures && isset($this->features) && count($this->features) > 0)
        <section class="py-8 sm:py-12 bg-white">
            <div class="container mx-auto px-4 sm:px-6">
                <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-8">
                    @foreach ($this->features as $feature)
                        <div
                            class="flex flex-col items-center text-center p-3 sm:p-6 rounded-lg hover:shadow-lg transition duration-300">
                            <div class="text-primary-500 mb-2 sm:mb-4">
                                <i class="{{ $feature->icon ?? 'fas fa-gem' }} text-2xl sm:text-3xl md:text-4xl"></i>
                            </div>
                            <h3 class="text-sm sm:text-lg md:text-xl font-bold mb-1 sm:mb-2 leading-tight">
                                {{ $locale == 'ar' ? $feature->title_ar : $feature->title_en }}</h3>
                            <p class="text-xs sm:text-sm md:text-base text-gray-600 leading-relaxed">
                                {{ $locale == 'ar' ? $feature->description_ar : $feature->description_en }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Categories Section -->
    @if (count($categories) > 0)
        <section class="py-8 sm:py-12 bg-gray-50">
            <div class="container mx-auto px-4 sm:px-6">
                <div class="text-center mb-8 sm:mb-12">
                    <h2 class="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">{{ __('Shop by Category') }}</h2>
                    <p class="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-4">
                        {{ __('Discover our diverse collection of jewelry categorized to make your shopping experience easier') }}
                    </p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-8">
                    @foreach ($categories as $category)
                        <a href="{{ route('category', $category->slug) }}" class="group">
                            <div class="relative h-64 sm:h-72 md:h-80 rounded-lg overflow-hidden">
                                <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}"
                                    class="w-full h-full object-cover transition duration-500 group-hover:scale-110">
                                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                                <div class="absolute bottom-0 left-0 right-0 p-4 sm:p-6">
                                    <h3 class="text-lg sm:text-xl md:text-2xl font-bold text-white mb-1 sm:mb-2">
                                        {{ $category->name }}</h3>
                                    <p class="text-sm sm:text-base text-white/80 mb-3 sm:mb-4">
                                        {{ $category->products_count ?? 0 }}
                                        {{ __('products') }}</p>
                                    <span
                                        class="inline-block bg-white text-primary-500 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full text-sm font-medium transition duration-300 group-hover:bg-primary-500 group-hover:text-white">
                                        @if (isset($displayOnlyMode) && $displayOnlyMode)
                                            {{ __('Browse Products') }}
                                        @else
                                            {{ __('Shop Now') }}
                                        @endif
                                    </span>
                                </div>
                            </div>
                        </a>
                    @endforeach
                </div>

                <div class="text-center mt-8 sm:mt-10">
                    <a href="{{ route('products') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-full font-medium transition duration-300 text-sm sm:text-base">{{ __('View All Categories') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Featured Products Section -->
    @if (count($featuredProducts) > 0)
        <section class="py-8 sm:py-12 bg-white">
            <div class="container mx-auto px-4 sm:px-6">
                <div class="text-center mb-8 sm:mb-12">
                    <h2 class="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">{{ __('Featured Products') }}</h2>
                    <p class="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-4">
                        {{ __('Discover our featured collection of luxury jewelry carefully selected to suit your refined taste') }}
                    </p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500 text-sm sm:text-base">{{ __('Loading products...') }}</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-6 lg:gap-8 product-grid">
                    @foreach ($featuredProducts as $product)
                        <x-product-card :product="$product" :show-ratings="$showRatings ?? true" :show-wishlist="$showWishlist ?? true" :show-metal-info="true"
                            :show-category="true" :badge="'featured'" :size="'normal'" :layout="'grid'" :whatsapp-phone="$whatsappPhone" />
                    @endforeach
                </div>

                <div class="text-center mt-8 sm:mt-10">
                    <a href="{{ route('products') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-full font-medium transition duration-300 text-sm sm:text-base">{{ __('View All Products') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Gold Prices Section -->
    @if (count($goldPrices) > 0)
        <section class="py-8 sm:py-12 bg-gray-50">
            <div class="container mx-auto px-4 sm:px-6">
                <div class="text-center mb-8 sm:mb-12">
                    <h2 class="text-2xl sm:text-3xl font-bold mb-2 sm:mb-4">{{ __('Today\'s Gold Prices') }}</h2>
                    <p class="text-sm sm:text-base text-gray-600 max-w-2xl mx-auto px-4">
                        {{ __('Follow the latest gold prices in Egypt in Egyptian pounds') }}</p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500 text-sm sm:text-base">{{ __('Loading prices...') }}</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4">
                    @foreach ($goldPrices as $price)
                        <div
                            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300 border border-primary-200">
                            <div class="gold-gradient p-2 sm:p-3 text-center">
                                <h3 class="text-sm sm:text-base font-bold text-gray-800">ذهب عيار {{ $price->purity }}
                                </h3>
                                <p class="text-xs text-gray-600 mt-1">{{ __('Karat') }}</p>
                            </div>
                            <div class="p-2 sm:p-3 space-y-2">
                                <!-- سعر البيع -->
                                <div class="bg-green-50 rounded-lg p-1.5 sm:p-2 border border-green-200">
                                    <div class="flex items-center justify-center mb-1">
                                        <span class="text-xs sm:text-sm mr-1">🏷️</span>
                                        <span class="text-xs font-semibold text-green-700">سعر البيع</span>
                                    </div>
                                    <div class="text-sm sm:text-lg font-bold text-green-800 text-center">
                                        {{ number_format($price->price_per_gram, 0) }} <span
                                            class="text-xs">ج.م</span>
                                    </div>
                                </div>

                                <!-- سعر الشراء -->
                                @if ($price->purchase_price_per_gram)
                                    <div class="bg-red-50 rounded-lg p-1.5 sm:p-2 border border-red-200">
                                        <div class="flex items-center justify-center mb-1">
                                            <span class="text-xs sm:text-sm mr-1">🛒</span>
                                            <span class="text-xs font-semibold text-red-700">سعر الشراء</span>
                                        </div>
                                        <div class="text-sm sm:text-lg font-bold text-red-800 text-center">
                                            {{ number_format($price->purchase_price_per_gram, 0) }} <span
                                                class="text-xs">ج.م</span>
                                        </div>
                                    </div>
                                @else
                                    <div class="bg-gray-50 rounded-lg p-1.5 sm:p-2 border border-gray-200">
                                        <div class="flex items-center justify-center mb-1">
                                            <span class="text-xs sm:text-sm mr-1">🛒</span>
                                            <span class="text-xs font-semibold text-gray-500">سعر الشراء</span>
                                        </div>
                                        <div class="text-xs text-gray-500 text-center">
                                            غير متاح
                                        </div>
                                    </div>
                                @endif

                                <!-- مؤشر التغيير -->
                                @php
                                    $changePercent = rand(-2, 3) / 10; // تغيير تجريبي
                                    $isPositive = $changePercent > 0;
                                    $isNegative = $changePercent < 0;
                                @endphp
                                <div
                                    class="flex items-center justify-center space-x-2 space-x-reverse pt-1 sm:pt-2 border-t border-gray-100">
                                    @if ($isPositive)
                                        <span class="text-green-600 text-xs sm:text-sm">↗️
                                            +{{ number_format(abs($changePercent), 1) }}%</span>
                                    @elseif($isNegative)
                                        <span class="text-red-600 text-xs sm:text-sm">↘️
                                            -{{ number_format(abs($changePercent), 1) }}%</span>
                                    @else
                                        <span class="text-gray-600 text-xs sm:text-sm">➡️ 0.0%</span>
                                    @endif
                                </div>

                                <!-- آخر تحديث -->
                                <div class="text-xs text-gray-500 text-center pt-1 hidden sm:block">
                                    آخر تحديث: {{ $price->updated_at->format('d/m/Y - g:i') }}
                                    {{ $price->updated_at->format('A') == 'AM' ? 'صباحاً' : 'مساءً' }}
                                </div>
                                <div class="text-xs text-gray-500 text-center pt-1 sm:hidden">
                                    {{ $price->updated_at->format('d/m') }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-6 sm:mt-10">
                    <a href="{{ route('metal-prices') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-4 sm:px-6 py-2.5 sm:py-3 rounded-full font-medium transition duration-300 text-sm sm:text-base">{{ __('View All Prices') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Silver Prices Section -->
    @if (count($silverPrices) > 0)
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">أسعار الفضة اليوم</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        تابع أحدث أسعار الفضة في مصر بالجنيه المصري</p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500">جاري تحميل الأسعار...</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 md:gap-4">
                    @foreach ($silverPrices as $price)
                        <div
                            class="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300 border border-gray-300">
                            <div class="silver-gradient p-3 text-center">
                                <h3 class="text-base font-bold text-gray-800">فضة عيار {{ $price->purity }}</h3>
                                <p class="text-xs text-gray-600 mt-1">عيار</p>
                            </div>
                            <div class="p-3 space-y-2">
                                <!-- سعر البيع -->
                                <div class="bg-green-50 rounded-lg p-2 border border-green-200">
                                    <div class="flex items-center justify-center mb-1">
                                        <span class="text-sm mr-1">🏷️</span>
                                        <span class="text-xs font-semibold text-green-700">سعر البيع</span>
                                    </div>
                                    <div class="text-lg font-bold text-green-800 text-center">
                                        {{ number_format($price->price_per_gram, 0) }} <span
                                            class="text-xs">ج.م</span>
                                    </div>
                                </div>

                                <!-- سعر الشراء -->
                                @if ($price->purchase_price_per_gram)
                                    <div class="bg-red-50 rounded-lg p-2 border border-red-200">
                                        <div class="flex items-center justify-center mb-1">
                                            <span class="text-sm mr-1">🛒</span>
                                            <span class="text-xs font-semibold text-red-700">سعر الشراء</span>
                                        </div>
                                        <div class="text-lg font-bold text-red-800 text-center">
                                            {{ number_format($price->purchase_price_per_gram, 0) }} <span
                                                class="text-xs">ج.م</span>
                                        </div>
                                    </div>
                                @else
                                    <div class="bg-gray-50 rounded-lg p-2 border border-gray-200">
                                        <div class="flex items-center justify-center mb-1">
                                            <span class="text-sm mr-1">🛒</span>
                                            <span class="text-xs font-semibold text-gray-500">سعر الشراء</span>
                                        </div>
                                        <div class="text-xs text-gray-500 text-center">
                                            غير متاح
                                        </div>
                                    </div>
                                @endif

                                <!-- مؤشر التغيير -->
                                @php
                                    $silverChangePercent = rand(-1, 2) / 10; // تغيير تجريبي للفضة
                                    $silverIsPositive = $silverChangePercent > 0;
                                    $silverIsNegative = $silverChangePercent < 0;
                                @endphp
                                <div
                                    class="flex items-center justify-center space-x-2 space-x-reverse pt-2 border-t border-gray-100">
                                    @if ($silverIsPositive)
                                        <span class="text-green-600 text-sm">↗️
                                            +{{ number_format(abs($silverChangePercent), 1) }}%</span>
                                    @elseif($silverIsNegative)
                                        <span class="text-red-600 text-sm">↘️
                                            -{{ number_format(abs($silverChangePercent), 1) }}%</span>
                                    @else
                                        <span class="text-gray-600 text-sm">➡️ 0.0%</span>
                                    @endif
                                </div>

                                <!-- آخر تحديث -->
                                <div class="text-xs text-gray-500 text-center pt-1">
                                    آخر تحديث: {{ $price->updated_at->format('d/m/Y - g:i') }}
                                    {{ $price->updated_at->format('A') == 'AM' ? 'صباحاً' : 'مساءً' }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <div class="text-center mt-10">
                    <a href="{{ route('metal-prices') }}"
                        class="inline-block bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-full font-medium transition duration-300">عرض
                        جميع أسعار الفضة</a>
                </div>
            </div>
        </section>
    @endif

    <!-- New Arrivals Section -->
    @if (count($newArrivals) > 0)
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('New Arrivals') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Discover the latest additions to our luxury jewelry collection') }}</p>
                </div>

                <div wire:loading.delay class="w-full text-center py-4">
                    <div
                        class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                    </div>
                    <p class="mt-2 text-gray-500">{{ __('Loading products...') }}</p>
                </div>

                <div wire:loading.delay.remove
                    class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-8 product-grid">
                    @foreach ($newArrivals as $product)
                        <x-product-card :product="$product" :show-ratings="$showRatings ?? true" :show-wishlist="$showWishlist ?? true" :show-metal-info="true"
                            :show-category="true" :badge="'new'" :size="'normal'" :layout="'grid'"
                            :whatsapp-phone="$whatsappPhone" />
                    @endforeach
                </div>

                <div class="text-center mt-10">
                    <a href="{{ route('products') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">{{ __('View All New Products') }}</a>
                </div>
            </div>
        </section>
    @endif

    <!-- Testimonials Section -->
    @if (isset($showTestimonials) && $showTestimonials && isset($this->testimonials) && count($this->testimonials) > 0)
        <section class="py-12 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-12">
                    <h2 class="text-3xl font-bold mb-4">{{ __('Customer Reviews') }}</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto">
                        {{ __('Read what our customers say about their experience with Makkah Gold') }}</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
                    @foreach ($this->testimonials as $testimonial)
                        <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition duration-300">
                            @if (isset($showRatings) && $showRatings)
                                <div class="flex text-yellow-400 mb-4">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= $testimonial->rating)
                                            <i class="fas fa-star"></i>
                                        @elseif($i - 0.5 <= $testimonial->rating)
                                            <i class="fas fa-star-half-alt"></i>
                                        @else
                                            <i class="far fa-star"></i>
                                        @endif
                                    @endfor
                                </div>
                            @endif
                            <p class="text-gray-600 mb-6">
                                {{ $locale == 'ar' ? $testimonial->content_ar : $testimonial->content_en }}</p>
                            <div class="flex items-center">
                                @if ($testimonial->image)
                                    <img src="{{ asset('storage/' . $testimonial->image) }}"
                                        alt="{{ $locale == 'ar' ? $testimonial->client_name_ar : $testimonial->client_name_en }}"
                                        class="w-12 h-12 rounded-full object-cover ml-4">
                                @else
                                    <div
                                        class="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center ml-4">
                                        <i class="fas fa-user text-primary-500"></i>
                                    </div>
                                @endif
                                <div>
                                    <h4 class="font-bold text-gray-800">
                                        {{ $locale == 'ar' ? $testimonial->client_name_ar : $testimonial->client_name_en }}
                                    </h4>
                                    @if ($testimonial->location_ar || $testimonial->location_en)
                                        <p class="text-gray-500 text-sm">
                                            {{ $locale == 'ar' ? $testimonial->location_ar : $testimonial->location_en }}
                                        </p>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif



    <!-- Instagram Feed Section -->
    {{-- <section class="py-12 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">{{ __('Follow Us on Instagram') }}</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">{{ __('See our latest designs and products on our Instagram account') }}</p>
            </div>

            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2 sm:gap-4">
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-1.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-2.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-3.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-4.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-5.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="block relative group overflow-hidden">
                    <img src="{{ asset('images/instagram/post-6.jpg') }}" alt="Instagram Post"
                        class="w-full aspect-square object-cover transition duration-500 group-hover:scale-110">
                    <div
                        class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center transition duration-300 opacity-0 group-hover:opacity-100">
                        <i class="fab fa-instagram text-white text-2xl"></i>
                    </div>
                </a>
            </div>

            <div class="text-center mt-8">
                <a href="https://www.instagram.com/makkahgold" target="_blank"
                    class="inline-flex items-center text-primary-500 hover:text-primary-600 font-medium">
                    <span>@makkahgold</span>
                    <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
        </div>
    </section> --}}

    <!-- Initialize Swiper - Backup -->
    <script>
        document.addEventListener('livewire:initialized', function() {
            console.log('Livewire initialized - checking for slider...');

            // انتظار لضمان تحميل المحتوى
            setTimeout(function() {
                const sliderElement = document.querySelector('.hero-slider');

                // تحقق إذا لم يتم تهيئة السلايدر بالفعل
                if (sliderElement && !sliderElement.swiper && !window.heroSliderInstance) {
                    console.log('Initializing backup slider from home-page...');

                    try {
                        const heroSlider = new Swiper('.hero-slider', {
                            loop: true,
                            autoplay: {
                                delay: 5000,
                                disableOnInteraction: false,
                            },
                            speed: 800,
                            grabCursor: true,

                            pagination: {
                                el: '.swiper-pagination',
                                clickable: true,
                            },

                            navigation: {
                                nextEl: '.swiper-button-next',
                                prevEl: '.swiper-button-prev',
                            },

                            on: {
                                init: function() {
                                    console.log('✅ Backup slider initialized!');
                                    this.autoplay.start();
                                }
                            }
                        });

                        window.heroSliderInstance = heroSlider;

                    } catch (error) {
                        console.error('❌ Error in backup slider:', error);
                    }
                }
            }, 1500);
        });
    </script>
</div>
