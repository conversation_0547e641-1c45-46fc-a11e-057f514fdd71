{{-- مكون منتجات الشركة --}}
<div>
    {{-- شريط التنقل --}}
    <nav class="mb-8">
        <ol class="flex items-center space-x-2 text-sm text-amber-600">
            <li><a href="{{ route('home') }}" class="hover:text-amber-800">الرئيسية</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li><a href="{{ route('companies') }}" class="hover:text-amber-800">الشركات</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li><a href="{{ route('companies.show', $company->id) }}"
                    class="hover:text-amber-800">{{ $company->name }}</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li class="text-amber-800 font-medium">المنتجات</li>
        </ol>
    </nav>

    {{-- معلومات الشركة المختصرة --}}
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex flex-col md:flex-row items-center justify-between">
            <div class="flex items-center mb-4 md:mb-0">
                @if ($company->logo)
                    <img src="{{ $company->logo_url }}" alt="{{ $company->name }}"
                        class="w-16 h-16 object-contain rounded-lg mr-4">
                @else
                    <div class="w-16 h-16 bg-amber-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-building text-2xl text-amber-600"></i>
                    </div>
                @endif

                <div>
                    <h1 class="text-2xl font-bold text-amber-800">منتجات {{ $company->name }}</h1>
                    <p class="text-amber-600">مصنعية الجرام:
                        {{ number_format($company->manufacturing_cost_per_gram, 2) }} ج.م</p>
                </div>
            </div>

            <a href="{{ route('companies.show', $company->id) }}"
                class="bg-amber-100 hover:bg-amber-200 text-amber-800 px-4 py-2 rounded-lg transition-colors duration-200">
                <i class="fas fa-arrow-right mr-1"></i>
                عودة لتفاصيل الشركة
            </a>
        </div>
    </div>

    {{-- تبويبات نوع المنتج --}}
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex flex-wrap justify-center gap-2 mb-6">
            <button wire:click="setActiveTab('الكل')"
                class="px-6 py-3 rounded-lg font-medium transition-all duration-200 {{ $activeTab === 'الكل' ? 'bg-amber-500 text-white shadow-lg' : 'bg-amber-100 text-amber-700 hover:bg-amber-200' }}">
                <i class="fas fa-list mr-2"></i>
                جميع المنتجات
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs mr-2">
                    {{ $productStats['total'] }}
                </span>
            </button>

            <button wire:click="setActiveTab('سبائك')"
                class="px-6 py-3 rounded-lg font-medium transition-all duration-200 {{ $activeTab === 'سبائك' ? 'bg-yellow-500 text-white shadow-lg' : 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200' }}">
                <i class="fas fa-square mr-2"></i>
                سبائك عيار 24
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs mr-2">
                    {{ $productStats['سبائك'] }}
                </span>
            </button>

            <button wire:click="setActiveTab('عملات')"
                class="px-6 py-3 rounded-lg font-medium transition-all duration-200 {{ $activeTab === 'عملات' ? 'bg-green-500 text-white shadow-lg' : 'bg-green-100 text-green-700 hover:bg-green-200' }}">
                <i class="fas fa-coins mr-2"></i>
                عملات عيار 21
                <span class="bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs mr-2">
                    {{ $productStats['عملات'] }}
                </span>
            </button>
        </div>
    </div>

    {{-- شريط البحث والفلاتر --}}
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            {{-- مربع البحث --}}
            <div class="lg:col-span-2">
                <label class="block text-sm font-medium text-amber-700 mb-2">البحث في المنتجات</label>
                <div class="relative">
                    <input type="text" wire:model.live.debounce.300ms="search" placeholder="ابحث عن منتج..."
                        class="w-full pl-10 pr-4 py-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-400"></i>
                </div>
            </div>

            {{-- فلتر نوع المنتج --}}
            <div>
                <label class="block text-sm font-medium text-amber-700 mb-2">نوع المنتج</label>
                <select wire:model.live="productType"
                    class="w-full px-4 py-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                    <option value="all">جميع الأنواع</option>
                    <option value="سبيكة">🟨 سبائك</option>
                    <option value="عملة">🪙 عملات</option>
                </select>
            </div>

            {{-- فلتر العيار --}}
            <div>
                <label class="block text-sm font-medium text-amber-700 mb-2">العيار</label>
                <select wire:model.live="metalPurity"
                    class="w-full px-4 py-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                    <option value="all">جميع العيارات</option>
                    <option value="24">عيار 24</option>
                    <option value="21">عيار 21</option>
                    <option value="18">عيار 18</option>
                </select>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            {{-- فلتر الوزن الأدنى --}}
            <div>
                <label class="block text-sm font-medium text-amber-700 mb-2">الوزن الأدنى (جرام)</label>
                <input type="number" wire:model.live.debounce.500ms="minWeight" step="0.001" min="0"
                    placeholder="0.000"
                    class="w-full px-4 py-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
            </div>

            {{-- فلتر الوزن الأعلى --}}
            <div>
                <label class="block text-sm font-medium text-amber-700 mb-2">الوزن الأعلى (جرام)</label>
                <input type="number" wire:model.live.debounce.500ms="maxWeight" step="0.001" min="0"
                    placeholder="1000.000"
                    class="w-full px-4 py-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
            </div>
        </div>

        {{-- خيارات الترتيب والإجراءات --}}
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            {{-- خيارات الترتيب --}}
            <div class="flex gap-2">
                <button wire:click="sortBy('name')"
                    class="px-4 py-2 rounded-lg border transition-colors duration-200
                               {{ $sortBy === 'name' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-amber-700 border-amber-200 hover:bg-amber-50' }}">
                    <i
                        class="fas fa-sort-alpha-{{ $sortBy === 'name' && $sortDirection === 'asc' ? 'down' : 'up' }} mr-1"></i>
                    الاسم
                </button>

                <button wire:click="sortBy('weight')"
                    class="px-4 py-2 rounded-lg border transition-colors duration-200
                               {{ $sortBy === 'weight' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-amber-700 border-amber-200 hover:bg-amber-50' }}">
                    <i class="fas fa-weight mr-1"></i>
                    الوزن
                </button>

                <button wire:click="sortBy('created_at')"
                    class="px-4 py-2 rounded-lg border transition-colors duration-200
                               {{ $sortBy === 'created_at' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-amber-700 border-amber-200 hover:bg-amber-50' }}">
                    <i class="fas fa-calendar mr-1"></i>
                    التاريخ
                </button>
            </div>

            {{-- مسح الفلاتر --}}
            @if ($search || $minWeight || $maxWeight)
                <button wire:click="clearFilters"
                    class="bg-red-100 hover:bg-red-200 text-red-800 px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i>
                    مسح الفلاتر
                </button>
            @endif
        </div>

        {{-- عرض نتائج البحث --}}
        @if ($search || $minWeight || $maxWeight)
            <div class="mt-4 text-sm text-amber-600">
                <i class="fas fa-info-circle mr-1"></i>
                عدد النتائج: {{ $products->total() }} منتج
                @if ($search)
                    - البحث: "<strong>{{ $search }}</strong>"
                @endif
                @if ($minWeight || $maxWeight)
                    - الوزن:
                    @if ($minWeight && $maxWeight)
                        من {{ $minWeight }} إلى {{ $maxWeight }} جرام
                    @elseif($minWeight)
                        أكبر من {{ $minWeight }} جرام
                    @elseif($maxWeight)
                        أصغر من {{ $maxWeight }} جرام
                    @endif
                @endif
            </div>
        @endif
    </div>

    {{-- شبكة المنتجات --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @forelse($products as $product)
            <div
                class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                {{-- رأس البطاقة --}}
                <div class="bg-gradient-to-br from-amber-100 to-yellow-200 p-6 text-center">
                    @if ($product->productType?->type === 'سبيكة')
                        <i class="fas fa-square text-4xl text-yellow-600 mb-3"></i>
                    @else
                        <i class="fas fa-coins text-4xl text-green-600 mb-3"></i>
                    @endif
                    <h3 class="text-lg font-bold text-amber-800">{{ $product->name }}</h3>

                    {{-- شارات نوع المنتج والعيار --}}
                    <div class="flex justify-center gap-2 mt-3">
                        <span
                            class="px-3 py-1 rounded-full text-xs font-medium
                            {{ $product->productType?->type === 'سبيكة' ? 'bg-yellow-500 text-white' : 'bg-green-500 text-white' }}">
                            {{ $product->product_type_with_icon }}
                        </span>
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-blue-500 text-white">
                            {{ $product->formatted_purity }}
                        </span>
                    </div>
                </div>

                {{-- محتوى البطاقة --}}
                <div class="p-6">
                    {{-- معلومات المنتج --}}
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between items-center">
                            <span class="text-amber-600 font-medium">الوزن:</span>
                            <span class="bg-amber-100 text-amber-800 px-3 py-1 rounded-full font-bold">
                                {{ $product->formatted_weight }}
                            </span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-amber-600 font-medium">مصنعية الجرام:</span>
                            <span class="text-orange-700 font-bold">
                                {{ number_format($product->manufacturing_cost_per_gram, 2) }} ج.م
                            </span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-amber-600 font-medium">المصنعية الإجمالية:</span>
                            <span class="text-green-700 font-bold text-lg">
                                {{ number_format($product->total_cost, 2) }} ج.م
                            </span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-amber-600 font-medium">استرداد الجرام:</span>
                            <span class="text-purple-700 font-bold">
                                {{ number_format($product->refund_value_per_gram, 2) }} ج.م
                            </span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-amber-600 font-medium">الاسترداد الإجمالي:</span>
                            <span class="text-blue-700 font-bold text-lg">
                                {{ number_format($product->total_refund_value, 2) }} ج.م
                            </span>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-amber-600 font-medium">تاريخ الإضافة:</span>
                            <span class="text-amber-700 text-sm">
                                {{ $product->created_at->format('d/m/Y') }}
                            </span>
                        </div>
                    </div>

                    {{-- وصف المنتج --}}
                    @if ($product->description)
                        <div class="mb-4">
                            <p class="text-sm text-amber-700 line-clamp-3">{{ $product->description }}</p>
                        </div>
                    @endif

                    {{-- تفاصيل نوع المنتج --}}
                    <div class="bg-amber-50 rounded-lg p-3 text-xs text-amber-700">
                        <div class="flex justify-between">
                            <span>نوع المنتج:</span>
                            <span>{{ $product->productType?->name ?? 'غير محدد' }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>الوزن:</span>
                            <span>{{ $product->formatted_weight }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>العيار:</span>
                            <span>{{ $product->formatted_purity }}</span>
                        </div>
                        <hr class="my-1 border-amber-200">
                        <div class="flex justify-between text-xs">
                            <span>مصنعية الجرام:</span>
                            <span>{{ number_format($product->manufacturing_cost_per_gram, 2) }} ج.م</span>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span>استرداد الجرام:</span>
                            <span>{{ number_format($product->refund_value_per_gram, 2) }} ج.م</span>
                        </div>
                        <hr class="my-1 border-amber-200">
                        <div class="flex justify-between font-bold">
                            <span>المصنعية الإجمالية:</span>
                            <span>{{ number_format($product->total_cost, 2) }} ج.م</span>
                        </div>
                        <div class="flex justify-between font-bold">
                            <span>الاسترداد الإجمالي:</span>
                            <span>{{ number_format($product->total_refund_value, 2) }} ج.م</span>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            {{-- رسالة عدم وجود نتائج --}}
            <div class="col-span-full">
                <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                    <i class="fas fa-search text-6xl text-amber-300 mb-4"></i>
                    <h3 class="text-2xl font-bold text-amber-800 mb-2">لا توجد منتجات</h3>
                    <p class="text-amber-600 mb-4">
                        @if ($search || $minWeight || $maxWeight)
                            لم يتم العثور على منتجات تطابق معايير البحث المحددة
                        @else
                            لم تقم هذه الشركة بإضافة أي منتجات بعد
                        @endif
                    </p>
                    @if ($search || $minWeight || $maxWeight)
                        <button wire:click="clearFilters"
                            class="bg-amber-500 hover:bg-amber-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-1"></i>
                            مسح جميع الفلاتر
                        </button>
                    @endif
                </div>
            </div>
        @endforelse
    </div>

    {{-- التنقل بين الصفحات --}}
    @if ($products->hasPages())
        <div class="bg-white rounded-xl shadow-lg p-6">
            {{ $products->links() }}
        </div>
    @endif

    {{-- إحصائيات سريعة --}}
    @if ($products->count() > 0)
        <div class="bg-white rounded-xl shadow-lg p-6 mt-8">
            <h3 class="text-lg font-bold text-amber-800 mb-4">
                <i class="fas fa-chart-bar text-amber-600 mr-2"></i>
                إحصائيات سريعة
            </h3>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-amber-50 rounded-lg p-4">
                    <p class="text-2xl font-bold text-amber-700">{{ $products->total() }}</p>
                    <p class="text-sm text-amber-600">إجمالي المنتجات</p>
                </div>

                <div class="bg-green-50 rounded-lg p-4">
                    <p class="text-2xl font-bold text-green-700">
                        {{ number_format($company->products()->with('productType')->get()->avg(function ($product) {return $product->productType?->weight ?? 0;}),2) }}
                    </p>
                    <p class="text-sm text-green-600">متوسط الوزن (جرام)</p>
                </div>

                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-2xl font-bold text-blue-700">
                        {{ number_format($company->products()->with('productType')->get()->sum(function ($product) {return $product->productType?->weight ?? 0;}),2) }}
                    </p>
                    <p class="text-sm text-blue-600">إجمالي الوزن (جرام)</p>
                </div>

                <div class="bg-purple-50 rounded-lg p-4">
                    <p class="text-2xl font-bold text-purple-700">
                        {{ number_format($company->products()->with('productType')->get()->avg(function ($product) {return $product->productType?->weight ?? 0;}) * $company->manufacturing_cost_per_gram,2) }}
                    </p>
                    <p class="text-sm text-purple-600">متوسط التكلفة (ج.م)</p>
                </div>
            </div>
        </div>
    @endif
</div>
