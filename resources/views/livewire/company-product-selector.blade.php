<div class="space-y-6">
    <!-- Selection Card -->
    <div class="bg-gradient-to-br from-yellow-400 via-yellow-300 to-yellow-200 rounded-2xl p-6 shadow-xl">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">

            <!-- Company Selection (Right) -->
            <div class="relative order-1 md:order-1">
                <label class="block text-gray-800 font-semibold text-lg mb-2">الشركة المصنعة</label>
                <select wire:model.live="selectedCompanyId"
                    class="w-full bg-white border-2 border-gray-300 rounded-xl px-4 py-3 text-gray-800 font-medium focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all appearance-none">
                    <option value="">اختر شركة</option>
                    @foreach ($companies as $company)
                        <option value="{{ $company->id }}">{{ $company->name }}</option>
                    @endforeach
                </select>
                <div class="absolute inset-y-0 left-3 top-8 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>

            <!-- Product Selection (Center) -->
            <div class="relative order-2 md:order-2">
                <label class="block text-gray-800 font-semibold text-lg mb-2">المنتج</label>
                <select wire:model.live="selectedProductTypeId"
                    class="w-full bg-white border-2 border-gray-300 rounded-xl px-4 py-3 text-gray-800 font-medium focus:border-yellow-500 focus:ring-2 focus:ring-yellow-200 transition-all appearance-none"
                    {{ !$selectedCompanyId ? 'disabled' : '' }}>
                    <option value="">اختر المنتج</option>
                    @foreach ($productTypes as $productType)
                        <option value="{{ $productType->id }}">{{ $productType->name }}</option>
                    @endforeach
                </select>
                <div class="absolute inset-y-0 left-3 top-8 flex items-center pointer-events-none">
                    <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>

            <!-- Logo Section (Left) -->
            <div class="flex justify-center order-3 md:order-3">
                <div class="w-24 h-24 bg-white rounded-2xl p-3 shadow-lg flex items-center justify-center">
                    @if ($selectedCompanyId && $companies->find($selectedCompanyId) && $companies->find($selectedCompanyId)->logo)
                        <img src="{{ Storage::url($companies->find($selectedCompanyId)->logo) }}"
                            alt="{{ $companies->find($selectedCompanyId)->name }}" class="w-full h-full object-contain">
                    @elseif($defaultLogo)
                        <img src="{{ Storage::url($defaultLogo) }}" alt="شعار الموقع"
                            class="w-full h-full object-contain">
                    @else
                        <!-- Default fallback logo -->
                        <div
                            class="w-full h-full bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center">
                            <span class="text-white font-bold text-lg">مكة</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Results -->
    @if ($selectedProduct && $calculatedPrices)
        <div class="space-y-4">
            <!-- Info Note -->
            <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
                <p class="text-gray-300 text-sm leading-relaxed">
                    <span class="text-yellow-400 font-semibold">ملحوظة هامة:</span>
                    أسعار المصنعيات بالأدنى حسب آخر تحديث معلن من الشركة المنتجة.
                    سعر جرام الذهب محدث حسب آخر سعر للسوق الصاغة، وليس مستمد من الشركة المنتجة نفسها وغير ملزم لها.
                </p>
            </div>

            <!-- Price Cards -->
            <div class="space-y-3">
                <!-- Gold Price per Gram -->
                <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
                    <div class="flex justify-between items-center">
                        <span class="text-white text-lg">سعر الجرام عيار
                            {{ $selectedProduct->productType->metal_purity }}</span>
                        <span
                            class="text-white font-bold text-lg">{{ number_format($calculatedPrices['raw_gold_price_per_gram'], 0, '.', ',') }}
                            جنيه</span>
                    </div>
                </div>

                <!-- Manufacturing Cost -->
                <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
                    <div class="flex justify-between items-center">
                        <span class="text-white text-lg">مصنعية الجرام:</span>
                        <span
                            class="text-white font-bold text-lg">{{ number_format($calculatedPrices['manufacturing_per_gram'], 1, '.', ',') }}
                            جنيه</span>
                    </div>
                </div>

                <!-- Total Price Including Manufacturing and Tax -->
                <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
                    <div class="flex justify-between items-center">
                        <span class="text-white text-lg">السعر شامل المصنعية والضريبة:</span>
                        <span
                            class="text-white font-bold text-lg">{{ number_format($calculatedPrices['total_price'], 0, '.', ',') }}
                            جنيه</span>
                    </div>
                </div>

                <!-- Import Value per Gram -->
                <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
                    <div class="flex justify-between items-center">
                        <span class="text-white text-lg">قيمة الاستيراد للجرام:</span>
                        <span
                            class="text-white font-bold text-lg">{{ number_format($calculatedPrices['refund_per_gram'], 1, '.', ',') }}
                            جنيه</span>
                    </div>
                </div>

                <!-- Resale Price -->
                <div class="bg-gray-800 rounded-xl p-4 border border-gray-700">
                    <div class="flex justify-between items-center">
                        <span class="text-white text-lg">سعر إعادة البيع</span>
                        <span
                            class="text-white font-bold text-lg">{{ number_format($calculatedPrices['resale_price'], 0, '.', ',') }}
                            جنيه</span>
                    </div>
                </div>
            </div>
        </div>
    @elseif($selectedCompanyId && $selectedProductTypeId)
        <div class="bg-gray-800 rounded-xl p-6 text-center border border-gray-700">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400 mx-auto mb-4"></div>
            <p class="text-gray-300">جاري تحميل البيانات...</p>
        </div>
    @endif
</div>
