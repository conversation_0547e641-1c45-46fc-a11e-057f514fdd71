<div class="space-y-8">
    <!-- نموذج إضافة قطعة مجوهرات -->
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
            <i class="fas fa-gem text-yellow-500 mr-3"></i>
            {{ __('zakat.jewelry.add_item_title') }}
        </h3>

        <form wire:submit.prevent="addJewelryItem" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- نوع المعدن -->
                <div>
                    <label for="metal_type" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('zakat.jewelry.metal_type') }} <span class="text-red-500">*</span>
                    </label>
                    <select wire:model.live="currentItem.metal_type" id="metal_type"
                        wire:key="metal-type-{{ count($jewelryItems) }}"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent">
                        <option value="">{{ __('zakat.jewelry.select_metal_type') }}</option>
                        @foreach ($metalTypes as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @if ($errors->has('currentItem.metal_type'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.metal_type') }}</p>
                    @endif
                </div>

                <!-- العيار -->
                <div>
                    <label for="purity" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('zakat.jewelry.purity') }} <span class="text-red-500">*</span>
                    </label>
                    <select wire:model.live="currentItem.purity" id="purity"
                        wire:key="purity-{{ $currentItem['metal_type'] ?? 'empty' }}-{{ count($jewelryItems) }}"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        {{ empty($currentItem['metal_type']) ? 'disabled' : '' }}>
                        <option value="">{{ __('zakat.jewelry.select_purity') }}</option>
                        @foreach ($availablePurities as $value => $label)
                            <option value="{{ $value }}">{{ $label }}</option>
                        @endforeach
                    </select>
                    @if ($errors->has('currentItem.purity'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.purity') }}</p>
                    @endif

                    @if (!empty($currentItem['metal_type']) && !empty($currentItem['purity']))
                        @php
                            $price = $latestPrices[$currentItem['metal_type']][$currentItem['purity']] ?? null;
                        @endphp
                        @if ($price)
                            <p class="mt-1 text-sm text-green-600">
                                {{ __('zakat.jewelry.current_price') }}:
                                {{ number_format($price->price_per_gram, 2) }}
                                {{ __('zakat.units.egp') }}/{{ __('zakat.units.grams') }}
                            </p>
                        @else
                            <p class="mt-1 text-sm text-red-600">
                                {{ __('zakat.jewelry.price_not_available') }}
                            </p>
                        @endif
                    @endif
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- الوزن -->
                <div>
                    <label for="weight" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('zakat.jewelry.weight') }} <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="number" wire:model.live="currentItem.weight" id="weight" step="0.01"
                            min="0.01" max="10000"
                            class="w-full px-4 py-3 pr-16 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                            placeholder="{{ __('zakat.jewelry.weight_placeholder') }}">
                        <span class="absolute right-3 top-3 text-gray-500">{{ __('zakat.units.grams') }}</span>
                    </div>
                    @if ($errors->has('currentItem.weight'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.weight') }}</p>
                    @endif
                </div>

                <!-- الوصف (اختياري) -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        {{ __('zakat.jewelry.description') }} <span
                            class="text-gray-400">({{ __('Optional') }})</span>
                    </label>
                    <input type="text" wire:model.live="currentItem.description" id="description" maxlength="100"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                        placeholder="{{ __('zakat.jewelry.description_placeholder') }}">
                    @if ($errors->has('currentItem.description'))
                        <p class="mt-1 text-sm text-red-600">{{ $errors->first('currentItem.description') }}</p>
                    @endif
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="flex flex-col sm:flex-row gap-4">
                <button type="submit"
                    class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-lg transition duration-300 flex items-center justify-center">
                    <i class="fas fa-plus mr-2"></i>
                    {{ __('zakat.jewelry.add_item') }}
                </button>

                @if (!empty($jewelryItems))
                    <button type="button" wire:click="clearAllItems"
                        wire:confirm="{{ __('zakat.jewelry.confirm_clear_all') }}"
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition duration-300 flex items-center justify-center">
                        <i class="fas fa-trash mr-2"></i>
                        {{ __('zakat.jewelry.clear_all') }}
                    </button>
                @endif
            </div>
        </form>
    </div>

    <!-- قائمة المجوهرات المضافة -->
    @if (!empty($jewelryItems))
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-list text-blue-500 mr-3"></i>
                {{ __('zakat.jewelry.items_list') }} ({{ count($jewelryItems) }} {{ __('zakat.jewelry.items') }})
            </h3>

            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('zakat.jewelry.item_number') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('zakat.jewelry.metal_type') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">{{ __('zakat.jewelry.purity') }}
                            </th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">{{ __('zakat.jewelry.weight') }}
                            </th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('zakat.jewelry.price_per_gram') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('zakat.jewelry.total_value') }}</th>
                            <th class="px-4 py-3 text-right font-medium text-gray-700">
                                {{ __('zakat.jewelry.description') }}</th>
                            <th class="px-4 py-3 text-center font-medium text-gray-700">{{ __('Actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        @foreach ($jewelryItems as $index => $item)
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-gray-900">{{ $index + 1 }}</td>
                                <td class="px-4 py-3">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {{ $item['metal_type'] === 'gold' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800' }}">
                                        {{ $item['metal_type'] === 'gold' ? '🥇 ذهب' : '🥈 فضة' }}
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-gray-900 font-medium">{{ $item['purity'] }}</td>
                                <td class="px-4 py-3 text-gray-900">{{ number_format($item['weight'], 2) }}
                                    {{ __('zakat.units.grams') }}</td>
                                <td class="px-4 py-3 text-gray-900">{{ number_format($item['price_per_gram'], 2) }}
                                    {{ __('zakat.units.egp') }}</td>
                                <td class="px-4 py-3 text-gray-900 font-bold">
                                    {{ number_format($item['total_value'], 2) }} {{ __('zakat.units.egp') }}</td>
                                <td class="px-4 py-3 text-gray-600">{{ $item['description'] ?: '-' }}</td>
                                <td class="px-4 py-3 text-center">
                                    <button wire:click="removeJewelryItem('{{ $item['id'] }}')"
                                        wire:confirm="{{ __('zakat.jewelry.confirm_remove') }}"
                                        class="text-red-600 hover:text-red-800 transition duration-200">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif

    <!-- النتائج والحسابات -->
    @if ($showResults && !empty($jewelryItems))
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-calculator text-green-500 mr-3"></i>
                {{ __('zakat.jewelry.calculation_results') }}
            </h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- إجمالي الوزن - الذهب -->
                @if ($totalGoldWeight > 0)
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span
                                class="text-sm font-medium text-yellow-800">{{ __('zakat.jewelry.total_gold_weight') }}</span>
                            <span
                                class="text-lg font-bold text-yellow-600">{{ number_format($totalGoldWeight, 2) }}</span>
                        </div>
                        <p class="text-xs text-yellow-600 mt-1">{{ __('zakat.units.grams') }}</p>
                    </div>
                @endif

                <!-- إجمالي الوزن - الفضة -->
                @if ($totalSilverWeight > 0)
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <span
                                class="text-sm font-medium text-gray-800">{{ __('zakat.jewelry.total_silver_weight') }}</span>
                            <span
                                class="text-lg font-bold text-gray-600">{{ number_format($totalSilverWeight, 2) }}</span>
                        </div>
                        <p class="text-xs text-gray-600 mt-1">{{ __('zakat.units.grams') }}</p>
                    </div>
                @endif

                <!-- إجمالي القيمة -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-blue-800">{{ __('zakat.jewelry.total_value') }}</span>
                        <span class="text-lg font-bold text-blue-600">{{ number_format($totalValue, 2) }}</span>
                    </div>
                    <p class="text-xs text-blue-600 mt-1">{{ __('zakat.units.egp') }}</p>
                </div>

                <!-- النصاب المعتبر (نصاب الذهب) -->
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-purple-800">نصاب الذهب (المعتبر شرعياً)</span>
                        <span
                            class="text-lg font-bold text-purple-600">{{ $nisabInfo['applicable']['formatted_value'] ?? '0' }}</span>
                    </div>
                    <p class="text-xs text-purple-600 mt-1">{{ __('zakat.units.egp') }}
                        ({{ $nisabInfo['applicable']['weight'] ?? 85 }} جرام ذهب عيار 24)</p>
                </div>
            </div>

            <!-- معلومات الحساب الشرعي -->
            @if (isset($nisabInfo['calculation']))
                <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 mb-6">
                    <h4 class="text-lg font-semibold text-indigo-800 mb-3 flex items-center">
                        <i class="fas fa-balance-scale mr-2"></i>
                        تفاصيل الحساب الشرعي
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-indigo-600">
                                {{ $nisabInfo['calculation']['formatted_gold_equivalent'] ?? '0' }}</div>
                            <div class="text-sm text-indigo-700">جرام ذهب عيار 24 مكافئ</div>
                            <div class="text-xs text-indigo-600">(مجموع جميع المعادن محولة)</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">
                                {{ $nisabInfo['calculation']['nisab_weight'] ?? 85 }}</div>
                            <div class="text-sm text-purple-700">جرام ذهب عيار 24</div>
                            <div class="text-xs text-purple-600">(النصاب المطلوب شرعياً)</div>
                        </div>
                        <div class="text-center">
                            @if ($nisabInfo['calculation']['nisab_reached'] ?? false)
                                <div class="text-2xl font-bold text-green-600">✓ تم بلوغ النصاب</div>
                                <div class="text-sm text-green-700">الزكاة واجبة</div>
                            @else
                                <div class="text-2xl font-bold text-orange-600">✗ لم يبلغ النصاب</div>
                                <div class="text-sm text-orange-700">نقص:
                                    {{ $nisabInfo['calculation']['formatted_shortage'] ?? '0' }} جرام</div>
                            @endif
                        </div>
                    </div>

                    <div class="mt-4 p-3 bg-white rounded-lg border border-indigo-200">
                        <p class="text-sm text-indigo-800">
                            <strong>طريقة الحساب الشرعية:</strong> يتم تحويل جميع المعادن (ذهب وفضة) إلى قيمة الذهب عيار
                            24،
                            ثم مقارنة المجموع بنصاب الذهب (85 جرام). إذا بلغ المجموع النصاب أو زاد عنه، تجب الزكاة على
                            كامل القيمة بنسبة 2.5%.
                        </p>
                    </div>
                </div>
            @endif

            <!-- نتيجة الزكاة -->
            <div class="border-t border-gray-200 pt-6">
                @if ($nisabReached)
                    <div class="bg-green-50 border-2 border-green-200 rounded-lg p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="text-lg font-bold text-green-800 flex items-center">
                                <i class="fas fa-check-circle mr-2"></i>
                                {{ __('zakat.jewelry.zakat_due') }}
                            </h4>
                            <span class="text-3xl font-bold text-green-600">{{ number_format($zakatAmount, 2) }}
                                {{ __('zakat.units.egp') }}</span>
                        </div>

                        <div class="bg-white rounded-lg p-4 mb-4">
                            <h5 class="font-semibold text-gray-800 mb-3">تفاصيل الحساب الشرعي:</h5>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h6 class="font-medium text-gray-700 mb-2">المعلومات الأساسية:</h6>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• إجمالي قيمة المجوهرات: {{ number_format($totalValue, 2) }}
                                            {{ __('zakat.units.egp') }}</li>
                                        <li>• المكافئ بالذهب عيار 24:
                                            {{ $nisabInfo['calculation']['formatted_gold_equivalent'] ?? '0' }}
                                            جرام</li>
                                        <li>• نصاب الذهب المطلوب:
                                            {{ $nisabInfo['calculation']['nisab_weight'] ?? 85 }} جرام</li>
                                        <li>• نسبة الزكاة الشرعية: 2.5%</li>
                                    </ul>
                                </div>
                                <div>
                                    <h6 class="font-medium text-gray-700 mb-2">نتيجة الحساب:</h6>
                                    <ul class="text-sm text-gray-700 space-y-1">
                                        <li>• ✅ تم بلوغ النصاب
                                            ({{ $nisabInfo['calculation']['formatted_gold_equivalent'] ?? '0' }} ≥
                                            {{ $nisabInfo['calculation']['nisab_weight'] ?? 85 }})</li>
                                        <li>• الزكاة واجبة على كامل القيمة</li>
                                        <li>• حساب الزكاة: {{ number_format($totalValue, 2) }} × 2.5%</li>
                                        <li>• <strong>المبلغ الواجب: {{ number_format($zakatAmount, 2) }}
                                                {{ __('zakat.units.egp') }}</strong></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p class="text-sm text-blue-800">
                                <i class="fas fa-info-circle mr-1"></i>
                                {{ __('zakat.jewelry.zakat_conditions_reminder') }}
                            </p>
                        </div>
                    </div>
                @else
                    <div class="bg-orange-50 border-2 border-orange-200 rounded-lg p-6">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-exclamation-triangle text-orange-500 mr-3"></i>
                            <h4 class="text-lg font-bold text-orange-800">لا تجب عليك الزكاة</h4>
                        </div>

                        <div class="bg-white rounded-lg p-4 mb-4">
                            <p class="text-sm text-orange-700 mb-3">
                                <strong>السبب الشرعي:</strong> لم تبلغ قيمة مجوهراتك النصاب المقرر شرعياً (85 جرام
                                ذهب عيار 24).
                            </p>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <h5 class="font-semibold text-orange-800 mb-2">الوضع الحالي:</h5>
                                    <ul class="text-sm text-orange-700 space-y-1">
                                        <li>• إجمالي القيمة: {{ number_format($totalValue, 2) }}
                                            {{ __('zakat.units.egp') }}</li>
                                        <li>• المكافئ بالذهب عيار 24:
                                            {{ $nisabInfo['calculation']['formatted_gold_equivalent'] ?? '0' }}
                                            جرام</li>
                                        <li>• النصاب المطلوب: {{ $nisabInfo['calculation']['nisab_weight'] ?? 85 }}
                                            جرام ذهب عيار 24</li>
                                    </ul>
                                </div>
                                <div>
                                    <h5 class="font-semibold text-orange-800 mb-2">ما تحتاجه لبلوغ النصاب:</h5>
                                    <ul class="text-sm text-orange-700 space-y-1">
                                        <li>• نقص في الوزن:
                                            {{ $nisabInfo['calculation']['formatted_shortage'] ?? '0' }} جرام ذهب
                                            عيار 24</li>
                                        <li>• نقص في القيمة:
                                            {{ number_format(($nisabInfo['applicable']['value'] ?? 0) - $totalValue, 2) }}
                                            {{ __('zakat.units.egp') }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <p class="text-sm text-blue-800">
                                <i class="fas fa-info-circle mr-1"></i>
                                <strong>ملاحظة شرعية:</strong> الزكاة تجب فقط عند بلوغ النصاب. إذا زادت مجوهراتك
                                مستقبلاً وبلغت النصاب،
                                فستجب الزكاة على كامل القيمة وليس على الزيادة فقط.
                            </p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- رسائل التنبيه -->
    @if (!empty($jewelryItems))
        <div class="bg-amber-50 border-l-4 border-amber-400 p-6 rounded-lg">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-amber-400 mt-1 mr-3"></i>
                <div>
                    <h4 class="text-lg font-bold text-amber-800 mb-2">{{ __('zakat.jewelry.important_notes') }}</h4>
                    <ul class="text-sm text-amber-700 space-y-2">
                        <li>• {{ __('zakat.jewelry.note_hawl') }}</li>
                        <li>• {{ __('zakat.jewelry.note_personal_use') }}</li>
                        <li>• {{ __('zakat.jewelry.note_consult_scholar') }}</li>
                        <li>• {{ __('zakat.jewelry.note_prices_current') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    @endif
</div>

@script
    <script>
        // استمع لأحداث Livewire
        $wire.on('jewelry-added', (event) => {
            // يمكن إضافة تأثيرات بصرية هنا
            console.log('تم إضافة قطعة مجوهرات');
        });

        $wire.on('jewelry-removed', (event) => {
            console.log('تم حذف قطعة مجوهرات');
        });

        $wire.on('all-jewelry-cleared', (event) => {
            console.log('تم مسح جميع القطع');
        });
    </script>
@endscript
