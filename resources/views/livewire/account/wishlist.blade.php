<div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold">المفضلة</h2>
    </div>

    @if ($wishlistItems->isEmpty())
        <div class="text-center py-12">
            <i class="fas fa-heart text-gray-300 text-5xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-700 mb-2">المفضلة فارغة</h3>
            <p class="text-gray-500 mb-6">لم تقم بإضافة أي منتجات إلى المفضلة بعد</p>
            <a href="{{ route('products') }}"
                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                @if ($displayOnlyMode)
                استعرض المنتجات
                @else
                تسوق الآن
                @endif
            </a>
        </div>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @foreach ($wishlistItems as $item)
                <div class="border border-gray-200 rounded-lg overflow-hidden group">
                    <div class="relative">
                        <a href="{{ route('product.show', $item->product->slug) }}">
                            <img src="{{ $item->product->image ? asset('storage/' . $item->product->image) : asset('images/products/default.jpg') }}"
                                alt="{{ $item->product->name_ar }}"
                                class="w-full h-48 object-cover group-hover:opacity-90 transition duration-300">
                        </a>

                        <button wire:click="removeFromWishlist({{ $item->id }})"
                            class="absolute top-2 right-2 bg-white rounded-full p-2 shadow-sm hover:bg-gray-100 transition duration-300">
                            <i class="fas fa-times text-gray-500"></i>
                        </button>
                    </div>

                    <div class="p-4">
                        <a href="{{ route('product.show', $item->product->slug) }}"
                            class="text-lg font-medium text-gray-800 hover:text-primary-500 mb-2 block">{{ $item->product->name_ar }}</a>

                        <div class="flex justify-between items-center mb-4">
                            <div>
                                @if ($item->product->discount_price)
                                    <span
                                        class="text-primary-500 font-bold">{{ number_format($item->product->discount_price, 2) }}
                                        ج.م</span>
                                    <span
                                        class="text-gray-500 line-through text-sm mr-2">{{ number_format($item->product->price, 2) }}
                                        ج.م</span>
                                @else
                                    <span
                                        class="text-primary-500 font-bold">{{ number_format($item->product->price, 2) }}
                                        ج.م</span>
                                @endif
                            </div>

                            @if ($item->product->in_stock)
                                <span class="text-green-500 text-sm">متوفر</span>
                            @else
                                <span class="text-red-500 text-sm">غير متوفر</span>
                            @endif
                        </div>

                        <div class="flex space-x-2 space-x-reverse">
                            @if (!$displayOnlyMode)
                            <button wire:click="addToCart({{ $item->product->id }})"
                                @if (!$item->product->in_stock) disabled @endif
                                class="flex-1 bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
                                <i class="fas fa-shopping-cart ml-2"></i>
                                إضافة للسلة
                            </button>
                            @endif

                            <button wire:click="removeFromWishlist({{ $item->id }})"
                                class="@if (!$displayOnlyMode) bg-gray-200 hover:bg-gray-300 text-gray-700 @else flex-1 bg-red-500 hover:bg-red-600 text-white @endif px-4 py-2 rounded-md font-medium transition duration-300">
                                <i class="fas fa-trash-alt @if ($displayOnlyMode) ml-2 @endif"></i>
                                @if ($displayOnlyMode)
                                إزالة من المفضلة
                                @endif
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>
