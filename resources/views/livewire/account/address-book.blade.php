<div>
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold">دفتر العناوين</h2>

        <button wire:click="showForm"
            class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition duration-300">
            <i class="fas fa-plus ml-2"></i>
            إضافة عنوان جديد
        </button>
    </div>

    @if ($showAddressForm)
        <div class="bg-gray-50 rounded-lg p-6 mb-8 border border-gray-200">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium">{{ $editAddressId ? 'تعديل العنوان' : 'إضافة عنوان جديد' }}</h3>
                <button wire:click="hideForm" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form wire:submit.prevent="saveAddress">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="name" class="block text-gray-700 font-medium mb-2">الاسم الكامل <span
                                class="text-red-500">*</span></label>
                        <input type="text" wire:model="name" id="name" required
                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="phone" class="block text-gray-700 font-medium mb-2">رقم الهاتف <span
                                class="text-red-500">*</span></label>
                        <input type="tel" wire:model="phone" id="phone" required
                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        @error('phone')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mb-6">
                    <label for="address" class="block text-gray-700 font-medium mb-2">العنوان <span
                            class="text-red-500">*</span></label>
                    <input type="text" wire:model="address" id="address" required
                        class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                    @error('address')
                        <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                    @enderror
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div>
                        <label for="city" class="block text-gray-700 font-medium mb-2">المدينة <span
                                class="text-red-500">*</span></label>
                        <input type="text" wire:model="city" id="city" required
                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        @error('city')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="country" class="block text-gray-700 font-medium mb-2">البلد <span
                                class="text-red-500">*</span></label>
                        <input type="text" wire:model="country" id="country" required
                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        @error('country')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="postalCode" class="block text-gray-700 font-medium mb-2">الرمز البريدي</label>
                        <input type="text" wire:model="postalCode" id="postalCode"
                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        @error('postalCode')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" wire:model="isDefault"
                            class="ml-2 text-primary-500 focus:ring-primary-500">
                        <span class="text-gray-700">تعيين كعنوان افتراضي</span>
                    </label>
                </div>

                <div class="flex justify-end space-x-4 space-x-reverse">
                    <button type="button" wire:click="hideForm"
                        class="border border-gray-300 bg-white text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-50 transition duration-300">
                        إلغاء
                    </button>

                    <button type="submit"
                        class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                        {{ $editAddressId ? 'تحديث العنوان' : 'إضافة العنوان' }}
                    </button>
                </div>
            </form>
        </div>
    @endif

    @if ($addresses->isEmpty())
        <div class="text-center py-12">
            <i class="fas fa-map-marker-alt text-gray-300 text-5xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-700 mb-2">لا توجد عناوين</h3>
            <p class="text-gray-500 mb-6">لم تقم بإضافة أي عناوين بعد</p>
            <button wire:click="showForm"
                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                إضافة عنوان جديد
            </button>
        </div>
    @else
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            @foreach ($addresses as $address)
                <div
                    class="border {{ $address->is_default ? 'border-primary-500' : 'border-gray-200' }} rounded-lg p-4 relative">
                    @if ($address->is_default)
                        <div
                            class="absolute top-0 left-0 bg-primary-500 text-white text-xs px-2 py-1 rounded-bl-lg rounded-tr-lg">
                            افتراضي
                        </div>
                    @endif

                    <div class="mb-4">
                        <h3 class="font-medium text-gray-800">{{ $address->name }}</h3>
                        <p class="text-gray-600">{{ $address->phone }}</p>
                    </div>

                    <div class="mb-4">
                        <p class="text-gray-700">{{ $address->address }}</p>
                        <p class="text-gray-700">{{ $address->city }}, {{ $address->country }}</p>
                        @if ($address->postal_code)
                            <p class="text-gray-700">{{ $address->postal_code }}</p>
                        @endif
                    </div>

                    <div class="flex justify-end space-x-2 space-x-reverse">
                        @if (!$address->is_default)
                            <button wire:click="setDefaultAddress({{ $address->id }})"
                                class="text-gray-600 hover:text-primary-500 font-medium text-sm">
                                تعيين كافتراضي
                            </button>
                        @endif

                        <button wire:click="editAddress({{ $address->id }})"
                            class="text-primary-500 hover:text-primary-600 font-medium text-sm">
                            تعديل
                        </button>

                        <button wire:click="deleteAddress({{ $address->id }})"
                            class="text-red-500 hover:text-red-600 font-medium text-sm">
                            حذف
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
</div>
