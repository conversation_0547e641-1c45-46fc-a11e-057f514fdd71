<div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="p-6 border-b border-gray-200">
        <div class="flex items-center">
            <div class="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center ml-4">
                <i class="fas fa-user text-primary-500 text-2xl"></i>
            </div>
            <div>
                <h3 class="text-xl font-bold text-gray-800">{{ $user->name }}</h3>
                <p class="text-gray-600">{{ $user->email }}</p>
            </div>
        </div>
    </div>

    <div class="p-4">
        <ul class="space-y-2">
            <li>
                <a href="{{ route('account.dashboard') }}"
                   class="flex items-center px-4 py-2 rounded-md {{ $currentRoute == 'account.dashboard' ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                    <i class="fas fa-tachometer-alt ml-3 w-5 text-center"></i>
                    <span>لوحة التحكم</span>
                </a>
            </li>
            @if (!$displayOnlyMode)
            <li>
                <a href="{{ route('account.orders') }}"
                   class="flex items-center px-4 py-2 rounded-md {{ $currentRoute == 'account.orders' || $currentRoute == 'account.orders.show' ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                    <i class="fas fa-shopping-bag ml-3 w-5 text-center"></i>
                    <span>طلباتي</span>
                </a>
            </li>
            @endif
            @if ($showWishlist)
            <li>
                <a href="{{ route('account.wishlist') }}"
                   class="flex items-center px-4 py-2 rounded-md {{ $currentRoute == 'account.wishlist' ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                    <i class="fas fa-heart ml-3 w-5 text-center"></i>
                    <span>المفضلة</span>
                </a>
            </li>
            @endif
            <li>
                <a href="{{ route('account.settings') }}"
                   class="flex items-center px-4 py-2 rounded-md {{ $currentRoute == 'account.settings' ? 'bg-primary-50 text-primary-500 font-medium' : 'text-gray-700 hover:bg-gray-50 hover:text-primary-500 transition duration-300' }}">
                    <i class="fas fa-cog ml-3 w-5 text-center"></i>
                    <span>إعدادات الحساب</span>
                </a>
            </li>
            <li>
                <form action="{{ route('logout') }}" method="POST">
                    @csrf
                    <button type="submit" class="flex items-center px-4 py-2 rounded-md text-gray-700 hover:bg-gray-50 hover:text-red-500 transition duration-300 w-full text-right">
                        <i class="fas fa-sign-out-alt ml-3 w-5 text-center"></i>
                        <span>تسجيل الخروج</span>
                    </button>
                </form>
            </li>
        </ul>
    </div>
</div>
