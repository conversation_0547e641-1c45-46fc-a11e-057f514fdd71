<div class="bg-white rounded-lg shadow-sm p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-xl font-bold">طلباتي</h2>

        <div class="flex items-center space-x-4 space-x-reverse">
            <div class="relative">
                <input type="text" wire:model.live.debounce.300ms="search" placeholder="بحث برقم الطلب..."
                    class="border border-gray-300 rounded-md px-4 py-2 pl-10 focus:outline-none focus:ring-2 focus:ring-primary-500">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>

            <select wire:model.live="status"
                class="border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">جميع الحالات</option>
                <option value="pending">قيد الانتظار</option>
                <option value="processing">قيد المعالجة</option>
                <option value="shipped">تم الشحن</option>
                <option value="delivered">تم التسليم</option>
                <option value="cancelled">ملغي</option>
            </select>
        </div>
    </div>

    @if ($orders->isEmpty())
        <div class="text-center py-12">
            <i class="fas fa-shopping-bag text-gray-300 text-5xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-700 mb-2">لا توجد طلبات</h3>
            <p class="text-gray-500 mb-6">لم تقم بإجراء أي طلبات بعد</p>
            <a href="{{ route('shop') }}"
                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                تسوق الآن
            </a>
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="py-3 px-4 text-right font-medium text-gray-500">رقم الطلب</th>
                        <th class="py-3 px-4 text-right font-medium text-gray-500">التاريخ</th>
                        <th class="py-3 px-4 text-right font-medium text-gray-500">المجموع</th>
                        <th class="py-3 px-4 text-right font-medium text-gray-500">الحالة</th>
                        <th class="py-3 px-4 text-right font-medium text-gray-500">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    @foreach ($orders as $order)
                        <tr>
                            <td class="py-4 px-4 text-gray-800">{{ $order->order_number }}</td>
                            <td class="py-4 px-4 text-gray-600">{{ $order->created_at->format('Y-m-d') }}</td>
                            <td class="py-4 px-4 text-gray-800">{{ number_format($order->total, 2) }} ج.م</td>
                            <td class="py-4 px-4">
                                @if ($order->status == 'pending')
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        قيد الانتظار
                                    </span>
                                @elseif($order->status == 'processing')
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        قيد المعالجة
                                    </span>
                                @elseif($order->status == 'shipped')
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                        تم الشحن
                                    </span>
                                @elseif($order->status == 'delivered')
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        تم التسليم
                                    </span>
                                @elseif($order->status == 'cancelled')
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        ملغي
                                    </span>
                                @endif
                            </td>
                            <td class="py-4 px-4">
                                <a href="{{ route('account.orders.show', $order->id) }}"
                                    class="text-primary-500 hover:text-primary-600 font-medium">
                                    عرض التفاصيل
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <div class="mt-6">
            {{ $orders->links() }}
        </div>
    @endif
</div>
