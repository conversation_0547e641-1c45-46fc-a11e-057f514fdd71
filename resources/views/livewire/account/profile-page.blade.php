<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row gap-8">
        <!-- Sidebar -->
        <div class="md:w-1/4">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <h2 class="text-xl font-bold mb-6">حسابي</h2>
                @livewire('account-menu')
            </div>
        </div>

        <!-- Main Content -->
        <div class="md:w-3/4">
            <div class="bg-white rounded-lg shadow-sm p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">إعدادات الحساب</h2>
                </div>

                <!-- Tabs -->
                <div class="border-b border-gray-200 mb-6">
                    <div class="flex space-x-8 space-x-reverse -mb-px">
                        <button wire:click="setActiveTab('profile')"
                            class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'profile' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                            المعلومات الشخصية
                        </button>

                        <button wire:click="setActiveTab('password')"
                            class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'password' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                            تغيير كلمة المرور
                        </button>

                        @if (!$displayOnlyMode)
                        <button wire:click="setActiveTab('addresses')"
                            class="py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'addresses' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                            دفتر العناوين
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Profile Tab -->
                <div x-show="$wire.activeTab === 'profile'">
                    <form wire:submit.prevent="updateProfile">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="name" class="block text-gray-700 font-medium mb-2">الاسم الكامل <span
                                        class="text-red-500">*</span></label>
                                <input type="text" wire:model="name" id="name" required
                                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                @error('name')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="email" class="block text-gray-700 font-medium mb-2">البريد الإلكتروني
                                    <span class="text-red-500">*</span></label>
                                <input type="email" wire:model="email" id="email" required
                                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                @error('email')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="phone" class="block text-gray-700 font-medium mb-2">رقم الهاتف</label>
                            <input type="tel" wire:model="phone" id="phone"
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            @error('phone')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="address" class="block text-gray-700 font-medium mb-2">العنوان</label>
                            <input type="text" wire:model="address" id="address"
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            @error('address')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div>
                                <label for="city" class="block text-gray-700 font-medium mb-2">المدينة</label>
                                <input type="text" wire:model="city" id="city"
                                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                @error('city')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="country" class="block text-gray-700 font-medium mb-2">البلد</label>
                                <input type="text" wire:model="country" id="country"
                                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                @error('country')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="postalCode" class="block text-gray-700 font-medium mb-2">الرمز
                                    البريدي</label>
                                <input type="text" wire:model="postalCode" id="postalCode"
                                    class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                @error('postalCode')
                                    <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                                حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Password Tab -->
                <div x-show="$wire.activeTab === 'password'">
                    <form wire:submit.prevent="updatePassword">
                        <div class="mb-6">
                            <label for="currentPassword" class="block text-gray-700 font-medium mb-2">كلمة المرور
                                الحالية <span class="text-red-500">*</span></label>
                            <input type="password" wire:model="currentPassword" id="currentPassword" required
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            @error('currentPassword')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="password" class="block text-gray-700 font-medium mb-2">كلمة المرور الجديدة
                                <span class="text-red-500">*</span></label>
                            <input type="password" wire:model="password" id="password" required
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            @error('password')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="passwordConfirmation" class="block text-gray-700 font-medium mb-2">تأكيد كلمة
                                المرور الجديدة <span class="text-red-500">*</span></label>
                            <input type="password" wire:model="passwordConfirmation" id="passwordConfirmation"
                                required
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            @error('passwordConfirmation')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button type="submit"
                                class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                                تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Addresses Tab -->
                @if (!$displayOnlyMode)
                <div x-show="$wire.activeTab === 'addresses'">
                    @livewire('account.address-book')
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
