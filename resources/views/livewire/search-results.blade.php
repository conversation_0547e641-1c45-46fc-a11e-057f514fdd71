<div>
    <div class="mb-6">
        <h1 class="text-2xl font-bold mb-2">نتائج البحث عن: "{{ $query }}"</h1>
        <p class="text-gray-600">تم العثور على {{ $products->total() }} منتج</p>
    </div>

    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Sidebar Filters -->
        <div class="lg:w-1/4">
            @livewire('product-filters', [
                'targetComponent' => 'search-results',
                'currentPage' => 'search',
                'showSearch' => true,
                'showPriceFilter' => true,
                'showCategoryFilter' => true,
                'showMetalFilter' => true,
                'showPurityFilter' => true,
                'initialFilters' => [
                    'search' => $query,
                    'category' => $category,
                    'minPrice' => $minPrice,
                    'maxPrice' => $maxPrice,
                    'metalType' => $metalType,
                    'purity' => $purity,
                    'sort' => $sort,
                ],
            ])
        </div>

        <!-- Products Grid -->
        <div class="lg:w-3/4">
            <!-- Toolbar -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6 flex flex-wrap justify-between items-center">
                <div class="mb-2 sm:mb-0">
                    <span class="text-gray-600">عرض
                        {{ $products->firstItem() ?? 0 }}-{{ $products->lastItem() ?? 0 }}
                        من {{ $products->total() ?? 0 }} منتج</span>
                </div>

                <div class="flex items-center">
                    <label for="sort" class="text-gray-600 ml-2">ترتيب حسب:</label>
                    <select value="{{ $sort }}" onchange="setFilter('search-results', 'sort', this.value)"
                        class="border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-1 focus:ring-primary-500">
                        <option value="newest" {{ $sort === 'newest' ? 'selected' : '' }}>الأحدث</option>
                        <option value="price_asc" {{ $sort === 'price_asc' ? 'selected' : '' }}>السعر: من الأقل إلى
                            الأعلى</option>
                        <option value="price_desc" {{ $sort === 'price_desc' ? 'selected' : '' }}>السعر: من الأعلى إلى
                            الأقل</option>
                        <option value="oldest" {{ $sort === 'oldest' ? 'selected' : '' }}>الأقدم</option>
                    </select>
                </div>
            </div>

            <div wire:loading.delay class="w-full text-center py-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500">
                </div>
                <p class="mt-2 text-gray-500">جاري تحميل المنتجات...</p>
            </div>

            <div wire:loading.delay.remove>
                @if ($products->count() > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach ($products as $product)
                            <x-product-card :product="$product" :show-ratings="$showRatings && ($product->site_show_ratings ?? true)" :show-wishlist="$showWishlist && ($product->site_show_wishlist ?? true)" :show-whatsapp="$showWhatsapp && ($product->site_show_whatsapp ?? true)"
                                :show-metal-info="true" :show-category="true" :badge="$product->show_price ? 'discount' : null" :size="'normal'"
                                :layout="'grid'" :whatsapp-phone="$whatsappPhone" />
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8">
                        {{ $products->links() }}
                    </div>
                @else
                    <div class="bg-white rounded-lg shadow-sm p-8 text-center">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-search fa-3x"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-800 mb-2">لم يتم العثور على منتجات</h3>
                        <p class="text-gray-600 mb-4">لم نتمكن من العثور على أي منتجات تطابق معايير البحث الخاصة بك.
                        </p>
                        <a href="{{ route('products') }}"
                            class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-md font-medium transition duration-300">
                            عرض جميع المنتجات
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>
