{{-- مكون عرض قائمة الشركات --}}
<div>

    {{-- شريط البحث والفلاتر --}}
    <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
        <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
            {{-- مربع البحث --}}
            <div class="flex-1 max-w-md">
                <div class="relative">
                    <input type="text" wire:model.live.debounce.300ms="search" placeholder="ابحث عن شركة..."
                        class="w-full pl-10 pr-4 py-3 border border-amber-200 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-400"></i>
                </div>
            </div>

            {{-- خيارات الترتيب --}}
            <div class="flex gap-2">
                <button wire:click="sortBy('name')"
                    class="px-4 py-2 rounded-lg border transition-colors duration-200
                               {{ $sortBy === 'name' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-amber-700 border-amber-200 hover:bg-amber-50' }}">
                    <i
                        class="fas fa-sort-alpha-{{ $sortBy === 'name' && $sortDirection === 'asc' ? 'down' : 'up' }} mr-1"></i>
                    الاسم
                </button>

                <button wire:click="sortBy('created_at')"
                    class="px-4 py-2 rounded-lg border transition-colors duration-200
                               {{ $sortBy === 'created_at' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-amber-700 border-amber-200 hover:bg-amber-50' }}">
                    <i class="fas fa-calendar mr-1"></i>
                    التاريخ
                </button>
            </div>
        </div>

        {{-- عرض نتائج البحث --}}
        @if ($search)
            <div class="mt-4 text-sm text-amber-600">
                <i class="fas fa-info-circle mr-1"></i>
                نتائج البحث عن: "<strong>{{ $search }}</strong>" - {{ $companies->total() }} نتيجة
            </div>
        @endif
    </div>

    {{-- شبكة الشركات --}}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
        @forelse($companies as $company)
            <div
                class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                {{-- لوجو الشركة --}}
                <div class="h-48 bg-gradient-to-br from-amber-100 to-yellow-200 flex items-center justify-center p-6">
                    @if ($company->logo)
                        <img src="{{ $company->logo_url }}" alt="{{ $company->name }}"
                            class="max-h-full max-w-full object-contain">
                    @else
                        <div class="text-center">
                            <i class="fas fa-building text-6xl text-amber-400 mb-2"></i>
                            <p class="text-amber-600 font-medium">{{ $company->name }}</p>
                        </div>
                    @endif
                </div>

                {{-- معلومات الشركة --}}
                <div class="p-6">
                    <h3 class="text-xl font-bold text-amber-800 mb-3 line-clamp-2">
                        {{ $company->name }}
                    </h3>

                    {{-- الإحصائيات --}}
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between items-center text-sm">
                            <span class="text-amber-600">إجمالي المنتجات:</span>
                            <span class="bg-amber-100 text-amber-800 px-2 py-1 rounded-full font-medium">
                                {{ $company->total_products }}
                            </span>
                        </div>

                        <div class="grid grid-cols-2 gap-2 text-xs">
                            <div class="flex justify-between items-center">
                                <span class="text-green-600">السبائك:</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full font-medium">
                                    {{ $company->bars_count }}
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-blue-600">العملات:</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                                    {{ $company->coins_count }}
                                </span>
                            </div>
                        </div>

                        <div class="flex justify-between items-center text-sm">
                            <span class="text-amber-600">مصنعية الجرام:</span>
                            <span class="text-amber-800 font-medium">
                                {{ number_format($company->manufacturing_cost_per_gram, 2) }} ج.م
                            </span>
                        </div>

                        <div class="flex justify-between items-center text-sm">
                            <span class="text-amber-600">قيمة الاستردادة:</span>
                            <span class="text-amber-800 font-medium">
                                {{ number_format($company->refund_value, 2) }} ج.م
                            </span>
                        </div>
                    </div>

                    {{-- أزرار العمل --}}
                    <div class="flex gap-2">
                        <a href="{{ route('companies.show', $company->id) }}"
                            class="flex-1 bg-amber-500 hover:bg-amber-600 text-white text-center py-2 px-4 rounded-lg transition-colors duration-200">
                            <i class="fas fa-eye mr-1"></i>
                            عرض التفاصيل
                        </a>

                        <a href="{{ route('companies.products', $company->id) }}"
                            class="flex-1 bg-amber-100 hover:bg-amber-200 text-amber-800 text-center py-2 px-4 rounded-lg transition-colors duration-200">
                            <i class="fas fa-box mr-1"></i>
                            المنتجات
                        </a>
                    </div>
                </div>
            </div>
        @empty
            {{-- رسالة عدم وجود نتائج --}}
            <div class="col-span-full">
                <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                    <i class="fas fa-search text-6xl text-amber-300 mb-4"></i>
                    <h3 class="text-2xl font-bold text-amber-800 mb-2">لا توجد شركات</h3>
                    <p class="text-amber-600 mb-4">
                        @if ($search)
                            لم يتم العثور على شركات تطابق البحث "{{ $search }}"
                        @else
                            لم يتم إضافة أي شركات بعد
                        @endif
                    </p>
                    @if ($search)
                        <button wire:click="$set('search', '')"
                            class="bg-amber-500 hover:bg-amber-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-times mr-1"></i>
                            مسح البحث
                        </button>
                    @endif
                </div>
            </div>
        @endforelse
    </div>

    {{-- التنقل بين الصفحات --}}
    @if ($companies->hasPages())
        <div class="bg-white rounded-xl shadow-lg p-6">
            {{ $companies->links() }}
        </div>
    @endif
</div>
