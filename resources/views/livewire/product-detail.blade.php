<div>


    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('products') }}" class="text-gray-600 hover:text-primary-500">المنتجات</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('category', $product->category->slug) }}"
                    class="text-gray-600 hover:text-primary-500">{{ $product->category->name }}</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ $product->name }}</span>
            </div>
        </div>
    </div>

    <!-- Product Details -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
                <!-- Product Images -->
                <div>
                    @if ($product->images && $product->images->count() > 0)
                        <!-- Main Image Slider -->
                        <div class="swiper product-main-slider mb-4 relative">
                            <div class="swiper-wrapper">
                                @forelse ($product->images as $image)
                                    <div class="swiper-slide">
                                        <div class="relative group">
                                            <img src="{{ asset('storage/' . $image->image_path) }}"
                                                alt="{{ $image->alt_text_ar ?? $product->name_ar }}"
                                                class="w-full h-96 md:h-[500px] object-cover rounded-lg cursor-zoom-in zoom-image"
                                                loading="lazy"
                                                data-zoom-src="{{ asset('storage/' . $image->image_path) }}"
                                                data-zoom-alt="{{ $image->alt_text_ar ?? $product->name_ar }}"
                                                data-zoom-index="{{ $loop->index }}"
                                                onerror="this.src='{{ asset('images/placeholder-product.jpg') }}'; this.onerror=null;">

                                            <!-- Primary Badge -->
                                            @if ($image->is_primary)
                                                <div
                                                    class="absolute top-3 right-3 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                                                    الصورة الرئيسية
                                                </div>
                                            @endif

                                            <!-- Zoom Icon -->
                                            <div
                                                class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
                                                <i
                                                    class="fas fa-search-plus text-white text-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
                                            </div>

                                            <!-- Zoom Button (Alternative) -->
                                            <button
                                                class="absolute top-3 left-3 bg-black bg-opacity-50 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-70 transition-all duration-300 zoom-btn"
                                                onclick="event.stopPropagation(); openImageModal('{{ asset('storage/' . $image->image_path) }}', '{{ $image->alt_text_ar ?? $product->name_ar }}', {{ $loop->index }})"
                                                title="تكبير الصورة">
                                                <i class="fas fa-search-plus text-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                @empty
                                    <!-- Default image when no images available -->
                                    <div class="swiper-slide">
                                        <div class="relative group">
                                            <div
                                                class="w-full h-96 md:h-[500px] bg-gray-200 rounded-lg flex items-center justify-center">
                                                <div class="text-center text-gray-500">
                                                    <i class="fas fa-image text-6xl mb-4"></i>
                                                    <p class="text-lg">لا توجد صور متاحة</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforelse
                            </div>

                            <!-- Navigation Buttons -->
                            @if ($product->images->count() > 1)
                                <div
                                    class="swiper-button-next !text-yellow-500 !bg-white !bg-opacity-80 !rounded-full !w-10 !h-10 !mt-0 !top-1/2 !transform !-translate-y-1/2">
                                </div>
                                <div
                                    class="swiper-button-prev !text-yellow-500 !bg-white !bg-opacity-80 !rounded-full !w-10 !h-10 !mt-0 !top-1/2 !transform !-translate-y-1/2">
                                </div>

                                <!-- Pagination -->
                                <div class="swiper-pagination !bottom-4"></div>
                            @endif
                        </div>

                        <!-- Thumbnail Slider -->
                        @if ($product->images && $product->images->count() > 1)
                            <div class="swiper product-thumbs-slider">
                                <div class="swiper-wrapper">
                                    @foreach ($product->images as $image)
                                        <div class="swiper-slide">
                                            <div
                                                class="product-thumb border-2 border-gray-200 rounded-md overflow-hidden cursor-pointer hover:border-yellow-500 transition-colors duration-300">
                                                <img src="{{ asset('storage/' . $image->image_path) }}"
                                                    alt="{{ $image->alt_text_ar ?? $product->name_ar }}"
                                                    class="w-full h-20 object-cover" loading="lazy"
                                                    onerror="this.src='{{ asset('images/placeholder-product.jpg') }}'; this.onerror=null;">
                                                @if ($image->is_primary)
                                                    <div
                                                        class="absolute inset-0 border-2 border-yellow-500 bg-yellow-500 bg-opacity-10">
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @else
                        <!-- Default Image -->
                        <div class="bg-gray-100 rounded-lg h-96 md:h-[500px] flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-image text-gray-400 text-6xl mb-4"></i>
                                <p class="text-gray-500">لا توجد صور متاحة</p>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Product Info -->
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ $product->name_ar }}</h1>

                    @if (isset($showRatings) && $showRatings)
                        <div class="flex items-center mb-4">
                            <div class="flex text-yellow-400 ml-2">
                                @for ($i = 1; $i <= 5; $i++)
                                    @if ($i <= $product->rating)
                                        <i class="fas fa-star"></i>
                                    @elseif($i - 0.5 <= $product->rating)
                                        <i class="fas fa-star-half-alt"></i>
                                    @else
                                        <i class="far fa-star"></i>
                                    @endif
                                @endfor
                            </div>
                            <span class="text-gray-500 text-sm">{{ $product->reviews_count ?? 0 }} تقييم</span>
                        </div>
                    @endif

                    {{-- السعر سيتم عرضه في shopping-controls component --}}

                    <div class="mb-6">
                        <p class="text-gray-600">{{ $product->short_description }}</p>
                    </div>

                    <div class="mb-6">
                        @if ($product->metal || $product->metal_type_id)
                            <div class="flex items-center mb-2">
                                <span class="text-gray-700 font-medium ml-2">نوع المعدن:</span>
                                <span class="text-gray-600">
                                    {{ $product->metal ? $product->metal->name_ar : ($product->material_type == 'gold' ? 'ذهب' : ($product->material_type == 'silver' ? 'فضة' : 'بلاتين')) }}
                                </span>
                            </div>
                        @endif

                        @if ($product->metalPurity || $product->metal_purity)
                            <div class="flex items-center mb-2">
                                <span class="text-gray-700 font-medium ml-2">العيار:</span>
                                <span class="text-gray-600">
                                    {{ $product->metalPurity ? $product->metalPurity->name_ar : $product->metal_purity }}
                                </span>
                            </div>
                        @endif

                        @if ($product->weight)
                            <div class="flex items-center mb-2">
                                <span class="text-gray-700 font-medium ml-2">الوزن:</span>
                                <span class="text-gray-600">{{ number_format($product->weight, 3) }} جرام</span>
                            </div>
                        @endif

                        @if ($metalPrice)
                            <div class="flex items-center mb-2">
                                <span class="text-gray-700 font-medium ml-2">سعر الجرام اليوم:</span>
                                <span class="text-gray-600">{{ number_format($metalPrice->price_per_gram, 2) }}
                                    ج.م</span>
                            </div>
                        @endif

                        <div class="flex items-center mb-2">
                            <span class="text-gray-700 font-medium ml-2">الحالة:</span>
                            <span class="{{ $product->stock_quantity > 0 ? 'text-green-500' : 'text-red-500' }}">
                                {{ $product->stock_quantity > 0 ? 'متوفر' : 'غير متوفر' }}
                            </span>
                        </div>

                        @if ($product->stock_quantity > 0)
                            <div class="flex items-center mb-2">
                                <span class="text-gray-700 font-medium ml-2">الكمية المتوفرة:</span>
                                <span class="text-gray-600">{{ $product->stock_quantity }} قطعة</span>
                            </div>
                        @endif

                        @if ($product->category)
                            <div class="flex items-center">
                                <span class="text-gray-700 font-medium ml-2">الفئة:</span>
                                <a href="{{ route('category', $product->category->slug) }}"
                                    class="text-yellow-600 hover:text-yellow-700 transition-colors">
                                    {{ $product->category->name_ar }}
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- استخدام Shopping Controls Component -->
                    <x-shopping-controls :product="$product" :show-wishlist="$showWishlist" :whatsapp-phone="$whatsappPhone" :quantity="$quantity"
                        :show-quantity-controls="true" />

                    {{-- <div class="border-t border-gray-200 pt-6">
                        <div class="flex flex-wrap gap-6">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt text-primary-500 ml-2"></i>
                                <span class="text-gray-600">ضمان لمدة عام</span>
                            </div>

                            <div class="flex items-center">
                                <i class="fas fa-truck text-primary-500 ml-2"></i>
                                <span class="text-gray-600">شحن سريع</span>
                            </div>

                            <div class="flex items-center">
                                <i class="fas fa-exchange-alt text-primary-500 ml-2"></i>
                                <span class="text-gray-600">إرجاع خلال 14 يوم</span>
                            </div>
                        </div>
                    </div> --}}
                </div>
            </div>
        </div>
    </section>

    <!-- Product Description Tabs -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="border-b border-gray-200">
                    <div class="flex flex-wrap" x-data="{ activeTab: 'description' }">
                        <button @click="activeTab = 'description'"
                            :class="{ 'text-primary-500 border-b-2 border-primary-500': activeTab === 'description', 'text-gray-500': activeTab !== 'description' }"
                            class="px-6 py-4 font-medium focus:outline-none">الوصف</button>
                        <button @click="activeTab = 'specifications'"
                            :class="{ 'text-primary-500 border-b-2 border-primary-500': activeTab === 'specifications', 'text-gray-500': activeTab !== 'specifications' }"
                            class="px-6 py-4 font-medium hover:text-primary-500 focus:outline-none">المواصفات</button>
                        @if (isset($showRatings) && $showRatings)
                            <button @click="activeTab = 'reviews'"
                                :class="{ 'text-primary-500 border-b-2 border-primary-500': activeTab === 'reviews', 'text-gray-500': activeTab !== 'reviews' }"
                                class="px-6 py-4 font-medium hover:text-primary-500 focus:outline-none">التقييمات</button>
                        @endif
                    </div>
                </div>

                <div class="p-6" x-data="{ activeTab: 'description' }">
                    <div x-show="activeTab === 'description'" class="prose max-w-none">
                        {!! $product->description_ar !!}
                    </div>

                    <div x-show="activeTab === 'specifications'" x-cloak>
                        <table class="w-full border-collapse">
                            <tbody>
                                <tr class="border-b border-gray-200">
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">النوع</td>
                                    <td class="py-3 px-6">
                                        {{ $product->metal_type == 'gold' ? 'ذهب' : ($product->metal_type == 'silver' ? 'فضة' : 'بلاتين') }}
                                    </td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">العيار</td>
                                    <td class="py-3 px-6">{{ $product->purity }}</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">الوزن</td>
                                    <td class="py-3 px-6">{{ $product->weight }} جرام</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">الأبعاد</td>
                                    <td class="py-3 px-6">{{ $product->dimensions ?? 'غير محدد' }}</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">الفئة</td>
                                    <td class="py-3 px-6">{{ $product->category->name_ar }}</td>
                                </tr>
                                <tr class="border-b border-gray-200">
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">الضمان</td>
                                    <td class="py-3 px-6">عام واحد</td>
                                </tr>
                                <tr>
                                    <td class="py-3 pl-6 pr-4 font-medium text-gray-700 bg-gray-50">بلد المنشأ</td>
                                    <td class="py-3 px-6">مصر</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    @if (isset($showRatings) && $showRatings)
                        <div x-show="activeTab === 'reviews'" x-cloak>
                            <div class="mb-8">
                                <h3 class="text-xl font-bold mb-4">التقييمات ({{ $product->reviews_count ?? 0 }})</h3>

                                @if (($product->reviews_count ?? 0) > 0)
                                    <div class="space-y-6">
                                        <!-- Sample reviews - replace with actual reviews from database -->
                                        <div class="border-b border-gray-200 pb-6">
                                            <div class="flex items-center mb-2">
                                                <div class="flex text-yellow-400 ml-2">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                </div>
                                                <span class="font-medium text-gray-800">منتج رائع</span>
                                            </div>
                                            <p class="text-gray-600 mb-2">جودة ممتازة وتصميم جميل. أنا سعيدة جدًا بهذا
                                                المنتج.</p>
                                            <div class="flex items-center text-sm text-gray-500">
                                                <span class="font-medium">سارة أحمد</span>
                                                <span class="mx-2">•</span>
                                                <span>منذ 3 أيام</span>
                                            </div>
                                        </div>

                                        <div class="border-b border-gray-200 pb-6">
                                            <div class="flex items-center mb-2">
                                                <div class="flex text-yellow-400 ml-2">
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="fas fa-star"></i>
                                                    <i class="far fa-star"></i>
                                                </div>
                                                <span class="font-medium text-gray-800">منتج جيد</span>
                                            </div>
                                            <p class="text-gray-600 mb-2">المنتج جيد ولكن كنت أتوقع أن يكون أثقل
                                                قليلاً.
                                            </p>
                                            <div class="flex items-center text-sm text-gray-500">
                                                <span class="font-medium">محمد علي</span>
                                                <span class="mx-2">•</span>
                                                <span>منذ أسبوع</span>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <p class="text-gray-600">لا توجد تقييمات لهذا المنتج حتى الآن.</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    @if ($relatedProducts && $relatedProducts->count() > 0)
        <section class="py-16 bg-gradient-to-b from-gray-50 to-white">
            <div class="container mx-auto px-4">
                <!-- Header Section -->
                <div class="text-center mb-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-4">
                        <i class="fas fa-gem text-primary-500 text-2xl"></i>
                    </div>
                    <h2 class="text-3xl font-bold mb-4 text-gray-800">منتجات ذات صلة</h2>
                    <p class="text-gray-600 max-w-2xl mx-auto text-lg">
                        اكتشف منتجات مشابهة قد تعجبك من نفس الفئة والمعدن
                    </p>
                    <div class="w-24 h-1 bg-gradient-to-r from-primary-400 to-primary-600 mx-auto mt-4 rounded-full">
                    </div>
                </div>

                <!-- Products Grid -->
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-3 gap-6 mb-12">
                    @foreach ($relatedProducts as $relatedProduct)
                        <div class="transform hover:scale-105 transition duration-300">
                            <x-product-card :product="$relatedProduct" :show-ratings="$showRatings && ($relatedProduct->site_show_ratings ?? true)" :show-wishlist="$showWishlist && ($relatedProduct->site_show_wishlist ?? true)" :show-whatsapp="$showWhatsapp && ($relatedProduct->site_show_whatsapp ?? true)"
                                :show-metal-info="true" :show-category="false" :badge="$relatedProduct->show_price ? 'discount' : null" :size="'normal'"
                                :layout="'grid'" :whatsapp-phone="$whatsappPhone" />
                        </div>
                    @endforeach
                </div>

                <!-- Action Buttons -->
                <div class="text-center space-y-4">
                    <a href="{{ route('category', $product->category->slug) }}"
                        class="inline-flex items-center bg-primary-500 hover:bg-primary-600 text-white px-8 py-3 rounded-full font-medium transition duration-300 shadow-lg hover:shadow-xl">
                        <i class="fas fa-th-large ml-2"></i>
                        عرض المزيد من {{ $product->category->name_ar }}
                    </a>

                    @if ($product->metal)
                        <div class="block">
                            <a href="{{ route('products', ['metal' => $product->metal->id]) }}"
                                class="inline-flex items-center bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-2 rounded-full font-medium transition duration-300">
                                <i class="fas fa-filter ml-2"></i>
                                جميع منتجات {{ $product->metal->name_ar }}
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </section>
    @endif

    <!-- Image Modal for Zoom -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden items-center justify-center p-4"
        style="backdrop-filter: blur(5px);">
        <div class="relative max-w-6xl max-h-full w-full h-full flex items-center justify-center">
            <!-- Close Button -->
            <button onclick="closeImageModal()"
                class="absolute top-4 right-4 text-white text-3xl hover:text-gray-300 z-20 bg-black bg-opacity-50 rounded-full w-12 h-12 flex items-center justify-center transition-all duration-300">
                <i class="fas fa-times"></i>
            </button>

            <!-- Navigation Buttons -->
            <button id="prevImageBtn" onclick="previousImage()"
                class="absolute left-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300 z-20 bg-black bg-opacity-50 rounded-full w-12 h-12 flex items-center justify-center transition-all duration-300">
                <i class="fas fa-chevron-left"></i>
            </button>

            <button id="nextImageBtn" onclick="nextImage()"
                class="absolute right-4 top-1/2 transform -translate-y-1/2 text-white text-2xl hover:text-gray-300 z-20 bg-black bg-opacity-50 rounded-full w-12 h-12 flex items-center justify-center transition-all duration-300">
                <i class="fas fa-chevron-right"></i>
            </button>

            <!-- Image Container -->
            <div class="relative w-full h-full flex items-center justify-center">
                <img id="modalImage" src="" alt=""
                    class="max-w-full max-h-full object-contain transition-all duration-300 cursor-zoom-in"
                    onclick="toggleZoom(this)">
            </div>

            <!-- Image Counter -->
            <div
                class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-4 py-2 rounded-full">
                <span id="imageCounter">1 / 1</span>
            </div>
        </div>
    </div>

    <style>
        /* تحسينات إضافية للصور والسليدر */
        .product-main-slider .swiper-slide img {
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .product-main-slider .swiper-slide img:hover {
            transform: scale(1.02);
        }

        .product-thumbs-slider .swiper-slide {
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .product-thumbs-slider .swiper-slide-thumb-active {
            opacity: 1;
        }

        .product-thumbs-slider .swiper-slide:hover {
            opacity: 1;
        }

        /* تحسينات للمودال */
        #imageModal {
            backdrop-filter: blur(10px);
        }

        #modalImage {
            max-width: 90vw;
            max-height: 90vh;
            transition: transform 0.3s ease;
        }

        /* تحسينات للأزرار */
        #imageModal button {
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        #imageModal button:hover {
            background-color: rgba(0, 0, 0, 0.8);
            transform: scale(1.1);
        }

        /* تحسين عداد الصور */
        #imageCounter {
            backdrop-filter: blur(5px);
            font-family: 'Cairo', sans-serif;
        }

        /* إخفاء أزرار التنقل عند عدم وجود صور متعددة */
        .single-image #prevImageBtn,
        .single-image #nextImageBtn {
            display: none !important;
        }

        /* تحسين زر الزووم */
        .zoom-btn {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s ease;
            z-index: 10;
        }

        .group:hover .zoom-btn {
            opacity: 1;
            transform: scale(1);
        }

        .zoom-btn:hover {
            transform: scale(1.1);
            background-color: rgba(0, 0, 0, 0.8) !important;
        }

        /* تحسين مؤشر الماوس للصور */
        .zoom-image {
            transition: transform 0.3s ease;
        }

        .zoom-image:hover {
            transform: scale(1.02);
        }
    </style>

    <script>
        // Global variables for image modal
        let currentImageIndex = 0;
        let productImages = [];
        let isZoomed = false;

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing zoom functionality...');
            initializeZoomFunctionality();
        });

        // Also initialize after Livewire loads
        document.addEventListener('livewire:initialized', function() {
            console.log('Livewire initialized, re-initializing zoom functionality...');
            setTimeout(() => {
                initializeZoomFunctionality();
            }, 500);
        });

        function initializeZoomFunctionality() {
            // Get all zoom images
            const zoomImages = document.querySelectorAll('.zoom-image');
            console.log('Found zoom images:', zoomImages.length);

            // Build product images array
            productImages = Array.from(zoomImages).map(img => ({
                src: img.dataset.zoomSrc || img.src,
                alt: img.dataset.zoomAlt || img.alt
            }));

            console.log('Product images array:', productImages);

            // Remove existing event listeners to avoid duplicates
            zoomImages.forEach(img => {
                img.removeEventListener('click', handleZoomClick);
            });

            // Add click event listeners that work with Swiper
            zoomImages.forEach((img, index) => {
                img.addEventListener('click', handleZoomClick);
                img.style.cursor = 'zoom-in';
            });

            console.log('Zoom functionality initialized');
        }

        function handleZoomClick(event) {
            // Prevent Swiper from interfering
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();

            const img = event.target;
            const imageSrc = img.dataset.zoomSrc || img.src;
            const imageAlt = img.dataset.zoomAlt || img.alt;
            const imageIndex = parseInt(img.dataset.zoomIndex) || 0;

            console.log('Zoom click handled:', imageSrc, imageIndex);

            // Small delay to ensure Swiper doesn't interfere
            setTimeout(() => {
                openImageModal(imageSrc, imageAlt, imageIndex);
            }, 50);
        }

        // Image Modal Functions
        function openImageModal(imageSrc, imageAlt, imageIndex = 0) {
            console.log('Opening modal for image:', imageSrc, 'at index:', imageIndex);

            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const imageCounter = document.getElementById('imageCounter');
            const prevBtn = document.getElementById('prevImageBtn');
            const nextBtn = document.getElementById('nextImageBtn');

            if (!modal || !modalImage) {
                console.error('Modal elements not found');
                return;
            }

            // Set current image index
            currentImageIndex = imageIndex;

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt;
            modalImage.style.transform = 'scale(1)';
            modalImage.style.cursor = 'zoom-in';
            isZoomed = false;

            // Update counter
            imageCounter.textContent = `${currentImageIndex + 1} / ${productImages.length}`;

            // Show/hide navigation buttons
            if (productImages.length > 1) {
                prevBtn.style.display = 'flex';
                nextBtn.style.display = 'flex';
            } else {
                prevBtn.style.display = 'none';
                nextBtn.style.display = 'none';
            }

            modal.classList.remove('hidden');
            modal.classList.add('flex');
            document.body.style.overflow = 'hidden';

            console.log('Modal opened successfully');
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');

            modal.classList.add('hidden');
            modal.classList.remove('flex');
            document.body.style.overflow = 'auto';

            // Reset zoom
            modalImage.style.transform = 'scale(1)';
            modalImage.style.cursor = 'zoom-in';
            isZoomed = false;
        }

        function previousImage() {
            if (productImages.length > 1) {
                currentImageIndex = (currentImageIndex - 1 + productImages.length) % productImages.length;
                updateModalImage();
            }
        }

        function nextImage() {
            if (productImages.length > 1) {
                currentImageIndex = (currentImageIndex + 1) % productImages.length;
                updateModalImage();
            }
        }

        function updateModalImage() {
            const modalImage = document.getElementById('modalImage');
            const imageCounter = document.getElementById('imageCounter');

            modalImage.src = productImages[currentImageIndex].src;
            modalImage.alt = productImages[currentImageIndex].alt;
            modalImage.style.transform = 'scale(1)';
            modalImage.style.cursor = 'zoom-in';
            isZoomed = false;

            imageCounter.textContent = `${currentImageIndex + 1} / ${productImages.length}`;
        }

        function toggleZoom(img) {
            console.log('Toggle zoom called, current state:', isZoomed);

            if (isZoomed) {
                img.style.transform = 'scale(1)';
                img.style.cursor = 'zoom-in';
                img.style.transformOrigin = 'center center';
                isZoomed = false;
                console.log('Zoomed out');
            } else {
                img.style.transform = 'scale(2)';
                img.style.cursor = 'zoom-out';
                img.style.transformOrigin = 'center center';
                isZoomed = true;
                console.log('Zoomed in');
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('imageModal');
            if (!modal.classList.contains('hidden')) {
                switch (e.key) {
                    case 'Escape':
                        closeImageModal();
                        break;
                    case 'ArrowLeft':
                        previousImage();
                        break;
                    case 'ArrowRight':
                        nextImage();
                        break;
                    case ' ':
                        e.preventDefault();
                        const modalImage = document.getElementById('modalImage');
                        toggleZoom(modalImage);
                        break;
                }
            }
        });

        // Close modal on background click
        document.addEventListener('DOMContentLoaded', function() {
            const modal = document.getElementById('imageModal');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeImageModal();
                    }
                });
            }
        });

        document.addEventListener('livewire:initialized', function() {
            // Initialize sliders only if images exist
            const mainSliderElement = document.querySelector('.product-main-slider');
            const thumbsSliderElement = document.querySelector('.product-thumbs-slider');

            if (mainSliderElement) {
                let thumbsSlider = null;

                // Initialize thumbs slider if it exists
                if (thumbsSliderElement) {
                    thumbsSlider = new Swiper('.product-thumbs-slider', {
                        spaceBetween: 10,
                        slidesPerView: 4,
                        freeMode: true,
                        watchSlidesProgress: true,
                        breakpoints: {
                            320: {
                                slidesPerView: 3
                            },
                            640: {
                                slidesPerView: 4
                            },
                            768: {
                                slidesPerView: 5
                            }
                        }
                    });
                }

                // Initialize main slider
                const mainSlider = new Swiper('.product-main-slider', {
                    spaceBetween: 10,
                    loop: false, // Disable loop to prevent duplication
                    autoplay: false, // Disable autoplay
                    allowTouchMove: true, // Allow touch/swipe
                    simulateTouch: true, // Allow mouse drag
                    touchRatio: 1,
                    touchAngle: 45,
                    grabCursor: false, // Disable grab cursor to avoid conflicts
                    preventClicks: false, // Allow clicks
                    preventClicksPropagation: false, // Allow click propagation
                    slideToClickedSlide: false, // Disable slide to clicked slide
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    thumbs: thumbsSlider ? {
                        swiper: thumbsSlider,
                    } : undefined,
                    keyboard: {
                        enabled: true,
                    },
                    on: {
                        init: function() {
                            console.log('Swiper initialized');
                            // Re-initialize zoom after Swiper is ready
                            setTimeout(() => {
                                initializeZoomFunctionality();
                            }, 100);
                        },
                        slideChange: function() {
                            console.log('Slide changed');
                        }
                    }
                });

                // Add loading states for images
                const images = document.querySelectorAll('.product-main-slider img');
                images.forEach((img, index) => {
                    img.style.opacity = '0';
                    img.style.transition = 'opacity 0.3s ease';

                    img.addEventListener('load', function() {
                        this.style.opacity = '1';
                    });

                    // If image is already loaded
                    if (img.complete) {
                        img.style.opacity = '1';
                    }

                    // Add error handling
                    img.addEventListener('error', function() {
                        console.warn(`Failed to load image: ${this.src}`);
                        this.style.opacity = '0.5';
                        // Try to load placeholder image
                        if (this.src !== '{{ asset('images/placeholder-product.jpg') }}') {
                            this.src = '{{ asset('images/placeholder-product.jpg') }}';
                        }
                    });
                });

                // Update product images array after slider initialization
                setTimeout(() => {
                    const imageElements = document.querySelectorAll('.product-main-slider img');
                    productImages = Array.from(imageElements).map(img => ({
                        src: img.src,
                        alt: img.alt
                    }));
                }, 100);
            } else {
                console.warn('No product images found for slider initialization');
            }
        });
    </script>
</div>
