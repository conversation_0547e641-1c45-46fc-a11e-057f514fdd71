<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name') }} - لوحة التحكم</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=tajawal:400,500,700&display=swap" rel="stylesheet" />
        <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
        
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
        
        <!-- Bootstrap Icons -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

        <!-- Styles -->
        <style>
            body {
                font-family: {{ app()->getLocale() == 'ar' ? 'Tajawal' : 'Instrument Sans' }}, sans-serif;
                background-color: #f8f9fa;
                color: #333;
            }
            .sidebar {
                background-color: #343a40;
                color: white;
                min-height: 100vh;
            }
            .sidebar .nav-link {
                color: rgba(255, 255, 255, 0.8);
                padding: 0.5rem 1rem;
                margin: 0.2rem 0;
                border-radius: 0.25rem;
            }
            .sidebar .nav-link:hover {
                color: white;
                background-color: rgba(255, 255, 255, 0.1);
            }
            .sidebar .nav-link.active {
                color: white;
                background-color: #D4AF37;
            }
            .sidebar .nav-link i {
                margin-left: 0.5rem;
            }
            .gold-color {
                color: #D4AF37;
            }
            .btn-gold {
                background-color: #D4AF37;
                border-color: #D4AF37;
                color: white;
            }
            .btn-gold:hover {
                background-color: #C5A028;
                border-color: #C5A028;
                color: white;
            }
            .card {
                border: none;
                border-radius: 0.5rem;
                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                margin-bottom: 1.5rem;
            }
            .card-header {
                background-color: #fff;
                border-bottom: 1px solid rgba(0, 0, 0, 0.125);
                padding: 1rem;
            }
            .card-body {
                padding: 1.5rem;
            }
            .stat-card {
                border-right: 4px solid #D4AF37;
            }
            .stat-icon {
                font-size: 2.5rem;
                color: #D4AF37;
            }
            .stat-value {
                font-size: 2rem;
                font-weight: 700;
            }
            .stat-label {
                font-size: 1rem;
                color: #6c757d;
            }
        </style>
    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            <img src="https://via.placeholder.com/100x100?text=MGG" alt="{{ config('app.name') }}" class="img-fluid rounded-circle mb-2" width="80">
                            <h5 class="mb-0">{{ config('app.name') }}</h5>
                            <p class="small text-muted">لوحة التحكم</p>
                        </div>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link active" href="#">
                                    <i class="bi bi-speedometer2"></i>
                                    الرئيسية
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-tag"></i>
                                    الفئات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-gem"></i>
                                    المنتجات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-cart"></i>
                                    الطلبات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-shop"></i>
                                    المتاجر
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-calendar-check"></i>
                                    المواعيد
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-star"></i>
                                    التقييمات
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-people"></i>
                                    المستخدمين
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#">
                                    <i class="bi bi-gear"></i>
                                    الإعدادات
                                </a>
                            </li>
                            <li class="nav-item mt-3">
                                <a class="nav-link text-danger" href="#">
                                    <i class="bi bi-box-arrow-right"></i>
                                    تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Main content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2">لوحة التحكم</h1>
                        <div class="btn-toolbar mb-2 mb-md-0">
                            <div class="btn-group me-2">
                                <button type="button" class="btn btn-sm btn-outline-secondary">تصدير</button>
                                <button type="button" class="btn btn-sm btn-outline-secondary">مشاركة</button>
                            </div>
                            <button type="button" class="btn btn-sm btn-gold">
                                <i class="bi bi-calendar"></i>
                                اليوم
                            </button>
                        </div>
                    </div>

                    <!-- Stats cards -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon me-3">
                                        <i class="bi bi-cart-check"></i>
                                    </div>
                                    <div>
                                        <div class="stat-value">{{ rand(10, 100) }}</div>
                                        <div class="stat-label">الطلبات</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon me-3">
                                        <i class="bi bi-gem"></i>
                                    </div>
                                    <div>
                                        <div class="stat-value">{{ rand(50, 200) }}</div>
                                        <div class="stat-label">المنتجات</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon me-3">
                                        <i class="bi bi-people"></i>
                                    </div>
                                    <div>
                                        <div class="stat-value">{{ rand(100, 500) }}</div>
                                        <div class="stat-label">العملاء</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="card-body d-flex align-items-center">
                                    <div class="stat-icon me-3">
                                        <i class="bi bi-currency-dollar"></i>
                                    </div>
                                    <div>
                                        <div class="stat-value">{{ number_format(rand(10000, 50000)) }}</div>
                                        <div class="stat-label">المبيعات (ر.س)</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent orders -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أحدث الطلبات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th scope="col">#</th>
                                            <th scope="col">العميل</th>
                                            <th scope="col">المنتجات</th>
                                            <th scope="col">المبلغ</th>
                                            <th scope="col">الحالة</th>
                                            <th scope="col">التاريخ</th>
                                            <th scope="col">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @for ($i = 1; $i <= 5; $i++)
                                            <tr>
                                                <th scope="row">{{ 1000 + $i }}</th>
                                                <td>عميل {{ $i }}</td>
                                                <td>{{ rand(1, 5) }}</td>
                                                <td>{{ number_format(rand(1000, 10000)) }} ر.س</td>
                                                <td>
                                                    @php
                                                        $statuses = ['قيد المعالجة', 'مكتمل', 'ملغي', 'قيد الشحن'];
                                                        $status = $statuses[array_rand($statuses)];
                                                        $badgeClass = match($status) {
                                                            'قيد المعالجة' => 'bg-warning',
                                                            'مكتمل' => 'bg-success',
                                                            'ملغي' => 'bg-danger',
                                                            'قيد الشحن' => 'bg-info',
                                                            default => 'bg-secondary'
                                                        };
                                                    @endphp
                                                    <span class="badge {{ $badgeClass }}">{{ $status }}</span>
                                                </td>
                                                <td>{{ date('Y-m-d', strtotime('-' . rand(1, 30) . ' days')) }}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-primary">عرض</button>
                                                        <button type="button" class="btn btn-outline-secondary">تعديل</button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endfor
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Recent products -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">أحدث المنتجات</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @for ($i = 1; $i <= 4; $i++)
                                    <div class="col-md-3">
                                        <div class="card h-100">
                                            <img src="https://via.placeholder.com/300x200?text=Product+{{ $i }}" class="card-img-top" alt="منتج {{ $i }}">
                                            <div class="card-body">
                                                <h5 class="card-title">منتج {{ $i }}</h5>
                                                <p class="card-text text-muted small">{{ rand(10, 50) }} جرام - عيار {{ ['18', '21', '24'][array_rand(['18', '21', '24'])] }}</p>
                                                <p class="card-text gold-color fw-bold">{{ number_format(rand(1000, 5000)) }} ر.س</p>
                                            </div>
                                            <div class="card-footer bg-white border-top-0">
                                                <div class="d-flex justify-content-between">
                                                    <span class="text-muted small">المخزون: {{ rand(0, 20) }}</span>
                                                    <a href="#" class="btn btn-sm btn-outline-secondary">تعديل</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endfor
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    </body>
</html>
