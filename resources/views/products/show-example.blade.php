@extends('layouts.app-with-settings')

@section('title', $product->name . ' - ' . config('app.name'))
@section('description', $product->description)

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Product Images -->
        <div class="space-y-4">
            <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img src="{{ $product->image_url }}" alt="{{ $product->name }}" class="w-full h-full object-cover">
            </div>
        </div>

        <!-- Product Info -->
        <div class="space-y-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">{{ $product->name }}</h1>
                <p class="text-2xl font-semibold text-blue-600 mt-2">{{ number_format($product->price) }} {{ __('ج.م') }}</p>
            </div>

            <!-- Product Description -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ __('الوصف') }}</h3>
                <p class="text-gray-600">{{ $product->description }}</p>
            </div>

            <!-- Product Rating (إذا كان مفعلاً) -->
            <x-feature-check feature="ratings" :show-message="false">
                <div class="border-t pt-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('التقييمات') }}</h3>
                    <div class="flex items-center space-x-2">
                        <div class="flex items-center">
                            @for($i = 1; $i <= 5; $i++)
                                <svg class="w-5 h-5 {{ $i <= $product->average_rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                </svg>
                            @endfor
                        </div>
                        <span class="text-sm text-gray-600">({{ $product->reviews_count }} {{ __('تقييم') }})</span>
                    </div>
                </div>
            </x-feature-check>

            <!-- Add to Cart Section -->
            <x-feature-check feature="display_only" :show-message="false">
                <div class="border-t pt-6">
                    <div class="flex space-x-4">
                        <!-- Add to Cart Button -->
                        <button type="button" 
                                onclick="addToCart({{ $product->id }})"
                                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition duration-200">
                            {{ __('إضافة إلى السلة') }}
                        </button>

                        <!-- Add to Wishlist (إذا كان مفعلاً) -->
                        <x-feature-check feature="wishlist" :show-message="false">
                            <button type="button" 
                                    onclick="toggleWishlist({{ $product->id }})"
                                    class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition duration-200">
                                <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </button>
                        </x-feature-check>
                    </div>
                </div>
            </x-feature-check>

            <!-- Social Share -->
            <div class="border-t pt-6">
                <x-social-share 
                    :url="request()->url()" 
                    :title="$product->name" 
                    :description="$product->description"
                    :image="$product->image_url" />
            </div>

            <!-- Shipping Info -->
            <x-feature-check feature="local_pickup" :show-message="false">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span class="text-sm text-green-800">{{ __('متاح للاستلام من المتجر') }}</span>
                    </div>
                </div>
            </x-feature-check>
        </div>
    </div>

    <!-- Product Reviews (إذا كان مفعلاً) -->
    <x-feature-check feature="ratings" :show-message="false">
        <div class="mt-16">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">{{ __('آراء العملاء') }}</h2>
            
            <div class="space-y-6">
                @forelse($product->reviews as $review)
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center space-x-2">
                            <h4 class="font-semibold text-gray-900">{{ $review->user->name }}</h4>
                            <div class="flex items-center">
                                @for($i = 1; $i <= 5; $i++)
                                    <svg class="w-4 h-4 {{ $i <= $review->rating ? 'text-yellow-400' : 'text-gray-300' }}" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                @endfor
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">{{ $review->created_at->diffForHumans() }}</span>
                    </div>
                    <p class="text-gray-600">{{ $review->comment }}</p>
                </div>
                @empty
                <div class="text-center py-8">
                    <p class="text-gray-500">{{ __('لا توجد تقييمات بعد. كن أول من يقيم هذا المنتج!') }}</p>
                </div>
                @endforelse
            </div>
        </div>
    </x-feature-check>
</div>

@push('scripts')
<script>
function addToCart(productId) {
    // إضافة المنتج إلى السلة
    fetch(`/cart/add/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('{{ __("تم إضافة المنتج إلى السلة بنجاح") }}');
        } else {
            alert('{{ __("حدث خطأ أثناء إضافة المنتج") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("حدث خطأ أثناء إضافة المنتج") }}');
    });
}

function toggleWishlist(productId) {
    // إضافة/إزالة المنتج من المفضلة
    fetch(`/wishlist/toggle/${productId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.added) {
                alert('{{ __("تم إضافة المنتج إلى المفضلة") }}');
            } else {
                alert('{{ __("تم إزالة المنتج من المفضلة") }}');
            }
        } else {
            alert('{{ __("حدث خطأ أثناء تحديث المفضلة") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("حدث خطأ أثناء تحديث المفضلة") }}');
    });
}
</script>
@endpush
@endsection
