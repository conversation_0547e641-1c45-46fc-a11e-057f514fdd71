@extends('layouts.frontend')

@section('title', 'اتصل بنا')

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">اتصل بنا</span>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="relative py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center">
                <h1 class="text-3xl md:text-4xl font-bold mb-4">اتصل بنا</h1>
                <p class="text-gray-600 mb-0">نحن هنا للإجابة على جميع استفساراتك ومساعدتك في اختيار المجوهرات المناسبة</p>
            </div>
        </div>
    </section>

    <!-- Contact Info Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                    <div class="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-map-marker-alt text-primary-500 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">العنوان</h3>
                    <p class="text-gray-600">المقر الرئيسي: القاهرة، مصر</p>
                    <a href="#stores" class="text-primary-500 hover:text-primary-600 font-medium inline-block mt-2">عرض جميع الفروع</a>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                    <div class="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-phone-alt text-primary-500 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">الهاتف</h3>
                    <p class="text-gray-600">+20 ************</p>
                    <p class="text-gray-600">+20 ************</p>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm p-6 text-center">
                    <div class="w-16 h-16 bg-primary-50 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-envelope text-primary-500 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold mb-2">البريد الإلكتروني</h3>
                    <p class="text-gray-600"><EMAIL></p>
                    <p class="text-gray-600"><EMAIL></p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Contact Form -->
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-2xl font-bold mb-6">أرسل لنا رسالة</h2>
                    
                    <form action="#" method="POST">
                        @csrf
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="name" class="block text-gray-700 font-medium mb-2">الاسم</label>
                                <input type="text" name="name" id="name" required class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                            
                            <div>
                                <label for="email" class="block text-gray-700 font-medium mb-2">البريد الإلكتروني</label>
                                <input type="email" name="email" id="email" required class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="phone" class="block text-gray-700 font-medium mb-2">رقم الهاتف</label>
                            <input type="tel" name="phone" id="phone" class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        
                        <div class="mb-6">
                            <label for="subject" class="block text-gray-700 font-medium mb-2">الموضوع</label>
                            <input type="text" name="subject" id="subject" required class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        
                        <div class="mb-6">
                            <label for="message" class="block text-gray-700 font-medium mb-2">الرسالة</label>
                            <textarea name="message" id="message" rows="5" required class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                        </div>
                        
                        <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                            إرسال الرسالة
                        </button>
                    </form>
                </div>
                
                <!-- Map -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3453.6730457217247!2d31.24683491511566!3d30.044419981884906!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1458409f7f0f9a91%3A0x6e3c6cb7a28db598!2sCairo%2C%20Egypt!5e0!3m2!1sen!2sus!4v1625000000000!5m2!1sen!2sus" width="100%" height="100%" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                </div>
            </div>
        </div>
    </section>

    <!-- Stores Section -->
    <section id="stores" class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">فروعنا</h2>
                <p class="text-gray-600">تفضل بزيارة أقرب فرع إليك واستمتع بتجربة تسوق فريدة</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-48">
                        <img src="{{ asset('images/stores/store-1.jpg') }}" alt="فرع القاهرة" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">فرع القاهرة</h3>
                        <div class="flex items-start mb-2">
                            <i class="fas fa-map-marker-alt mt-1 ml-2 text-primary-500"></i>
                            <span class="text-gray-600">123 شارع التحرير، وسط البلد، القاهرة</span>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-phone-alt ml-2 text-primary-500"></i>
                            <span class="text-gray-600">+20 ************</span>
                        </div>
                        <div class="flex items-center mb-4">
                            <i class="fas fa-clock ml-2 text-primary-500"></i>
                            <div>
                                <p class="text-gray-600">السبت - الخميس: 10 ص - 10 م</p>
                                <p class="text-gray-600">الجمعة: 2 م - 10 م</p>
                            </div>
                        </div>
                        <a href="https://goo.gl/maps/1234" target="_blank" class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition duration-300">
                            <i class="fas fa-directions ml-1"></i>
                            الاتجاهات
                        </a>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-48">
                        <img src="{{ asset('images/stores/store-2.jpg') }}" alt="فرع الإسكندرية" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">فرع الإسكندرية</h3>
                        <div class="flex items-start mb-2">
                            <i class="fas fa-map-marker-alt mt-1 ml-2 text-primary-500"></i>
                            <span class="text-gray-600">456 طريق الكورنيش، سان ستيفانو، الإسكندرية</span>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-phone-alt ml-2 text-primary-500"></i>
                            <span class="text-gray-600">+20 ************</span>
                        </div>
                        <div class="flex items-center mb-4">
                            <i class="fas fa-clock ml-2 text-primary-500"></i>
                            <div>
                                <p class="text-gray-600">السبت - الخميس: 10 ص - 10 م</p>
                                <p class="text-gray-600">الجمعة: 2 م - 10 م</p>
                            </div>
                        </div>
                        <a href="https://goo.gl/maps/5678" target="_blank" class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition duration-300">
                            <i class="fas fa-directions ml-1"></i>
                            الاتجاهات
                        </a>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="h-48">
                        <img src="{{ asset('images/stores/store-3.jpg') }}" alt="فرع الجيزة" class="w-full h-full object-cover">
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2">فرع الجيزة</h3>
                        <div class="flex items-start mb-2">
                            <i class="fas fa-map-marker-alt mt-1 ml-2 text-primary-500"></i>
                            <span class="text-gray-600">789 شارع الهرم، الجيزة</span>
                        </div>
                        <div class="flex items-center mb-2">
                            <i class="fas fa-phone-alt ml-2 text-primary-500"></i>
                            <span class="text-gray-600">+20 ************</span>
                        </div>
                        <div class="flex items-center mb-4">
                            <i class="fas fa-clock ml-2 text-primary-500"></i>
                            <div>
                                <p class="text-gray-600">السبت - الخميس: 10 ص - 10 م</p>
                                <p class="text-gray-600">الجمعة: 2 م - 10 م</p>
                            </div>
                        </div>
                        <a href="https://goo.gl/maps/9012" target="_blank" class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition duration-300">
                            <i class="fas fa-directions ml-1"></i>
                            الاتجاهات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-2xl font-bold mb-8 text-center">الأسئلة الشائعة</h2>
                
                <div class="space-y-4">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <button class="faq-btn w-full flex justify-between items-center p-6 focus:outline-none">
                            <span class="text-lg font-medium text-gray-800">ما هي طرق الدفع المتاحة؟</span>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                        </button>
                        <div class="faq-content hidden px-6 pb-6">
                            <p class="text-gray-600">نقبل الدفع بالطرق التالية: بطاقات الائتمان (فيزا، ماستركارد)، الدفع عند الاستلام، التحويل البنكي، وخدمة فوري.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <button class="faq-btn w-full flex justify-between items-center p-6 focus:outline-none">
                            <span class="text-lg font-medium text-gray-800">ما هي سياسة الإرجاع والاستبدال؟</span>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                        </button>
                        <div class="faq-content hidden px-6 pb-6">
                            <p class="text-gray-600">يمكنك إرجاع المنتج خلال 14 يومًا من تاريخ الاستلام إذا كان في حالته الأصلية وبدون استخدام. يرجى الاطلاع على صفحة سياسة الإرجاع للمزيد من التفاصيل.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <button class="faq-btn w-full flex justify-between items-center p-6 focus:outline-none">
                            <span class="text-lg font-medium text-gray-800">هل يمكنني حجز موعد لزيارة المتجر؟</span>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                        </button>
                        <div class="faq-content hidden px-6 pb-6">
                            <p class="text-gray-600">نعم، يمكنك حجز موعد لزيارة أي من فروعنا من خلال صفحة حجز موعد على موقعنا أو بالاتصال بالفرع مباشرة.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <button class="faq-btn w-full flex justify-between items-center p-6 focus:outline-none">
                            <span class="text-lg font-medium text-gray-800">هل تقدمون خدمة التوصيل لجميع المحافظات؟</span>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                        </button>
                        <div class="faq-content hidden px-6 pb-6">
                            <p class="text-gray-600">نعم، نقدم خدمة التوصيل لجميع محافظات مصر. تختلف رسوم الشحن ومدة التوصيل حسب المحافظة.</p>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <button class="faq-btn w-full flex justify-between items-center p-6 focus:outline-none">
                            <span class="text-lg font-medium text-gray-800">هل تقدمون شهادة ضمان للمجوهرات؟</span>
                            <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                        </button>
                        <div class="faq-content hidden px-6 pb-6">
                            <p class="text-gray-600">نعم، جميع منتجاتنا مرفقة بشهادة ضمان تؤكد أصالة المعدن ونقاوته. كما نقدم ضمانًا لمدة عام ضد عيوب الصناعة.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
<script>
    // FAQ Accordion
    document.querySelectorAll('.faq-btn').forEach(button => {
        button.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const icon = this.querySelector('i');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('transform', 'rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('transform', 'rotate-180');
            }
        });
    });
</script>
@endsection
