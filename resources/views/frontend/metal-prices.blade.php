@extends('layouts.frontend')

@section('title', 'أسعار المعادن الثمينة بالجنيه المصري')

@section('styles')
    <style>
        .price-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .price-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .price-change-up {
            color: #10b981;
            background-color: #d1fae5;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .price-change-down {
            color: #ef4444;
            background-color: #fee2e2;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .price-change-stable {
            color: #6b7280;
            background-color: #f3f4f6;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .metal-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-left: 1rem;
        }

        .gold-icon {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #92400e;
        }

        .silver-icon {
            background: linear-gradient(135deg, #c0c0c0, #e5e7eb);
            color: #374151;
        }

        .platinum-icon {
            background: linear-gradient(135deg, #e5e4e2, #f3f4f6);
            color: #374151;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .chart-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-top: 2rem;
        }

        /* Modern Metal Type Buttons */
        .metal-type-btn,
        .gold-metal-btn,
        .silver-metal-btn,
        .gold-period-btn,
        .silver-period-btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 600;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .metal-type-btn:hover,
        .gold-metal-btn:hover,
        .silver-metal-btn:hover,
        .gold-period-btn:hover,
        .silver-period-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .metal-type-btn.active,
        .gold-metal-btn.active,
        .silver-metal-btn.active,
        .gold-period-btn.active,
        .silver-period-btn.active {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Period Buttons */
        .gold-period-btn,
        .silver-period-btn {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            color: #374151;
        }

        .gold-period-btn.active {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
        }

        .silver-period-btn.active {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
        }

        /* Statistics Cards Animation */
        .statistics-card {
            transition: all 0.3s ease;
        }

        .statistics-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .metal-price-table {
            width: 100%;
            border-collapse: collapse;
        }

        .metal-price-table th {
            background-color: #f9fafb;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }

        .metal-price-table td {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            vertical-align: middle;
        }

        .metal-price-table tr:last-child td {
            border-bottom: none;
        }

        .metal-price-table tr:hover {
            background-color: #f9fafb;
        }

        .price-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
            font-weight: bold;
            font-size: 1.25rem;
        }

        .gold-header {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #92400e;
        }

        .silver-header {
            background: linear-gradient(135deg, #c0c0c0, #9ca3af);
            color: #374151;
        }

        .platinum-header {
            background: linear-gradient(135deg, #e5e4e2, #d1d5db);
            color: #374151;
        }

        .update-time {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            display: inline-block;
            margin-top: 1rem;
        }

        .currency-badge {
            background: #10b981;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 600;
            font-size: 0.875rem;
            display: inline-block;
            margin-bottom: 1rem;
        }

        .period-btn {
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 2px solid transparent;
            background: #f3f4f6;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
        }

        .period-btn:hover:not(.active) {
            background-color: #e5e7eb;
            color: #374151;
        }

        .period-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .metal-type-btn {
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: 2px solid transparent;
            background: #f3f4f6;
            color: #6b7280;
            font-weight: 500;
            cursor: pointer;
        }

        .metal-type-btn:hover:not(.active) {
            background-color: #e5e7eb;
            color: #374151;
        }

        .metal-type-btn.active {
            background: linear-gradient(135deg, #ffd700, #ffb300);
            color: #92400e;
            border-color: #ffd700;
        }

        @media (max-width: 768px) {

            .metal-price-table th,
            .metal-price-table td {
                padding: 0.75rem 0.5rem;
                font-size: 0.875rem;
            }

            .metal-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .price-card {
                margin-bottom: 1rem;
            }
        }
    </style>
@endsection

@section('content')
    <div class="min-h-screen bg-gray-50">
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-primary-600 to-primary-800 text-white py-16">
            <div class="container mx-auto px-4 text-center">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">أسعار المعادن الثمينة</h1>
                <p class="text-xl md:text-2xl mb-6">تابع أحدث أسعار الذهب والفضة والبلاتين بالجنيه المصري</p>

                <div class="currency-badge">
                    <i class="fas fa-coins ml-2"></i>
                    الأسعار بالجنيه المصري فقط
                </div>

                <div class="update-time">
                    <div class="flex items-center justify-center space-x-4 space-x-reverse">
                        <i class="fas fa-clock text-2xl"></i>
                        <div>
                            <p class="text-lg font-semibold">آخر تحديث</p>
                            <p class="text-sm">
                                {{ \Carbon\Carbon::parse($priceDate)->locale('ar')->isoFormat('dddd، D MMMM YYYY - h:mm A') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Breadcrumb -->
        <div class="bg-white py-4 border-b">
            <div class="container mx-auto px-4">
                <div class="flex items-center text-sm">
                    <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                    <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                    <span class="text-gray-800">أسعار المعادن</span>
                </div>
            </div>
        </div>

        <!-- Modern Statistics Cards -->
        @if (isset($statistics) && !empty($statistics))
            <section class="py-12">
                <div class="container mx-auto px-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                        <!-- Gold Statistics -->
                        @if (isset($statistics['gold']))
                            <div
                                class="relative overflow-hidden rounded-2xl shadow-xl transform hover:scale-105 transition-all duration-300">
                                <!-- Gold Gradient Background -->
                                <div
                                    class="absolute inset-0 bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600">
                                </div>
                                <div class="relative p-6 text-white">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="p-3 bg-white/20 rounded-full backdrop-blur-sm ml-4">
                                                <i class="fas fa-coins text-2xl"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-bold">إحصائيات الذهب</h3>
                                                <p class="text-yellow-100 text-sm">{{ $statistics['gold']['count'] ?? 0 }}
                                                    عيار</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-3">
                                        <div class="flex justify-between items-center">
                                            <span class="text-yellow-100">أعلى سعر:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['gold']['highest'], 2) }}
                                                ج.م</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-yellow-100">أقل سعر:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['gold']['lowest'], 2) }}
                                                ج.م</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-yellow-100">المتوسط:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['gold']['average'], 2) }}
                                                ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Silver Statistics -->
                        @if (isset($statistics['silver']))
                            <div
                                class="relative overflow-hidden rounded-2xl shadow-xl transform hover:scale-105 transition-all duration-300">
                                <!-- Silver Gradient Background -->
                                <div class="absolute inset-0 bg-gradient-to-br from-gray-400 via-gray-500 to-gray-600">
                                </div>
                                <div class="relative p-6 text-white">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="p-3 bg-white/20 rounded-full backdrop-blur-sm ml-4">
                                                <i class="fas fa-coins text-2xl"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-bold">إحصائيات الفضة</h3>
                                                <p class="text-gray-100 text-sm">{{ $statistics['silver']['count'] ?? 0 }}
                                                    عيار</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-3">
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-100">أعلى سعر:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['silver']['highest'], 2) }}
                                                ج.م</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-100">أقل سعر:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['silver']['lowest'], 2) }}
                                                ج.م</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-gray-100">المتوسط:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['silver']['average'], 2) }}
                                                ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <!-- Gold Coins Statistics -->
                        @if (isset($statistics['gold_coins']))
                            <div
                                class="relative overflow-hidden rounded-2xl shadow-xl transform hover:scale-105 transition-all duration-300">
                                <!-- Gold Coins Gradient Background -->
                                <div
                                    class="absolute inset-0 bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600">
                                </div>
                                <div class="relative p-6 text-white">
                                    <div class="flex items-center justify-between mb-4">
                                        <div class="flex items-center">
                                            <div class="p-3 bg-white/20 rounded-full backdrop-blur-sm ml-4">
                                                <i class="fas fa-coins text-2xl"></i>
                                            </div>
                                            <div>
                                                <h3 class="text-xl font-bold">إحصائيات الجنيهات الذهبية</h3>
                                                <p class="text-orange-100 text-sm">
                                                    {{ $statistics['gold_coins']['count'] ?? 0 }} نوع</p>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="space-y-3">
                                        <div class="flex justify-between items-center">
                                            <span class="text-orange-100">أعلى سعر:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['gold_coins']['highest'], 2) }}
                                                ج.م</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-orange-100">أقل سعر:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['gold_coins']['lowest'], 2) }}
                                                ج.م</span>
                                        </div>
                                        <div class="flex justify-between items-center">
                                            <span class="text-orange-100">المتوسط:</span>
                                            <span
                                                class="font-bold text-lg">{{ number_format($statistics['gold_coins']['average'], 2) }}
                                                ج.م</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </section>
        @endif

        <!-- Main Content -->
        <section class="py-8">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <!-- Gold Prices Card -->
                    <div class="price-card">
                        <div class="gold-header">
                            <i class="fas fa-coins ml-2"></i>
                            {{ $translations['gold_prices_title'] }}
                        </div>
                        <div class="p-6">
                            <div class="overflow-x-auto">
                                <table class="metal-price-table">
                                    <thead>
                                        <tr>
                                            <th>{{ $translations['table_headers']['purity'] }}</th>
                                            <th>سعر البيع/جرام</th>
                                            <th>سعر الشراء/جرام</th>
                                            <th>التاريخ والوقت</th>
                                            <th>{{ $translations['table_headers']['change'] }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($goldPrices as $priceRecord)
                                            <tr>
                                                <td>
                                                    <div class="flex items-center">
                                                        <div class="gold-icon">
                                                            <i class="fas fa-coins"></i>
                                                        </div>
                                                        <span
                                                            class="font-semibold">{{ $priceRecord['purity_label'] }}</span>
                                                    </div>
                                                </td>
                                                <td class="font-bold text-lg text-green-600">
                                                    {{ $priceRecord['formatted_price_per_gram'] }}
                                                    {{ $priceRecord['currency_symbol'] }}</td>
                                                <td class="font-bold text-lg text-orange-600">
                                                    @if (isset($priceRecord['formatted_purchase_price_per_gram']) && $priceRecord['formatted_purchase_price_per_gram'])
                                                        {{ $priceRecord['formatted_purchase_price_per_gram'] }}
                                                        {{ $priceRecord['currency_symbol'] }}
                                                    @else
                                                        <span class="text-gray-400 text-sm">غير محدد</span>
                                                    @endif
                                                </td>
                                                <td class="text-sm text-gray-600">
                                                    <div class="flex flex-col">
                                                        <span
                                                            class="font-medium">{{ $priceRecord['formatted_date'] }}</span>
                                                        <span
                                                            class="text-xs opacity-75">{{ $priceRecord['formatted_time_ago'] }}</span>
                                                    </div>
                                                </td>
                                                <td>
                                                    @if ($priceRecord['direction'] === 'up')
                                                        <span class="price-change-up">
                                                            <i class="fas fa-arrow-up"></i>
                                                            +{{ $priceRecord['formatted_change_percent'] }}%
                                                        </span>
                                                    @elseif($priceRecord['direction'] === 'down')
                                                        <span class="price-change-down">
                                                            <i class="fas fa-arrow-down"></i>
                                                            {{ $priceRecord['formatted_change_percent'] }}%
                                                        </span>
                                                    @else
                                                        <span class="price-change-stable">
                                                            <i class="fas fa-minus"></i>
                                                            {{ $priceRecord['formatted_change_percent'] }}%
                                                        </span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="5" class="text-center py-8 text-gray-500">
                                                    <i class="fas fa-info-circle text-2xl mb-2"></i>
                                                    <p>{{ $translations['messages']['no_gold_prices'] }}</p>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Gold Coins Prices Card -->
                    @if ($goldCoinPrices->count() > 0)
                        <div class="price-card">
                            <div class="gold-header">
                                <i class="fas fa-coins ml-2"></i>
                                {{ __('metal_prices.gold_coins_title') }}
                            </div>
                            <div class="p-6">
                                <div class="overflow-x-auto">
                                    <table class="metal-price-table">
                                        <thead>
                                            <tr>
                                                <th>{{ __('metal_prices.table_headers.type') }}</th>
                                                <th>{{ __('metal_prices.table_headers.price_per_gram') }}</th>
                                                <th>{{ __('metal_prices.table_headers.price_per_piece') }}</th>
                                                <th>{{ __('metal_prices.table_headers.date_time') }}</th>
                                                <th>{{ __('metal_prices.table_headers.change') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($goldCoinPrices as $priceRecord)
                                                <tr>
                                                    <td>
                                                        <div class="flex items-center">
                                                            <div class="gold-icon">
                                                                <i class="fas fa-coins"></i>
                                                            </div>
                                                            <span
                                                                class="font-semibold">{{ $priceRecord['purity_label'] }}</span>
                                                        </div>
                                                    </td>
                                                    <td class="font-bold text-lg">
                                                        {{ $priceRecord['formatted_price_per_gram'] }}
                                                        {{ $priceRecord['currency_symbol'] }}</td>
                                                    <td class="font-bold text-lg text-green-600">
                                                        {{ $priceRecord['formatted_price_per_piece'] }}
                                                        {{ $priceRecord['currency_symbol'] }}</td>
                                                    <td class="text-sm text-gray-600">
                                                        <div class="flex flex-col">
                                                            <span
                                                                class="font-medium">{{ $priceRecord['formatted_date'] }}</span>
                                                            <span
                                                                class="text-xs opacity-75">{{ $priceRecord['formatted_time_ago'] }}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if ($priceRecord['direction'] === 'up')
                                                            <span class="price-change-up">
                                                                <i class="fas fa-arrow-up"></i>
                                                                +{{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @elseif($priceRecord['direction'] === 'down')
                                                            <span class="price-change-down">
                                                                <i class="fas fa-arrow-down"></i>
                                                                {{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @else
                                                            <span class="price-change-stable">
                                                                <i class="fas fa-minus"></i>
                                                                {{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center py-8 text-gray-500">
                                                        <i class="fas fa-info-circle text-2xl mb-2"></i>
                                                        <p>{{ __('metal_prices.messages.no_gold_coins') }}</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Silver Prices Card -->
                    @if ($silverPrices->count() > 0)
                        <div class="price-card">
                            <div class="silver-header">
                                <i class="fas fa-coins ml-2"></i>
                                {{ $translations['silver_prices_title'] }}
                            </div>
                            <div class="p-6">
                                <div class="overflow-x-auto">
                                    <table class="metal-price-table">
                                        <thead>
                                            <tr>
                                                <th>{{ __('metal_prices.table_headers.type') }}</th>
                                                <th>سعر البيع/جرام</th>
                                                <th>سعر الشراء/جرام</th>
                                                <th>{{ __('metal_prices.table_headers.date_time') }}</th>
                                                <th>{{ __('metal_prices.table_headers.change') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($silverPrices as $priceRecord)
                                                <tr>
                                                    <td>
                                                        <div class="flex items-center">
                                                            <div class="silver-icon">
                                                                <i class="fas fa-coins"></i>
                                                            </div>
                                                            <span
                                                                class="font-semibold">{{ $priceRecord['purity_label'] }}</span>
                                                        </div>
                                                    </td>
                                                    <td class="font-bold text-lg text-green-600">
                                                        {{ $priceRecord['formatted_price_per_gram'] }}
                                                        {{ $priceRecord['currency_symbol'] }}</td>
                                                    <td class="font-bold text-lg text-orange-600">
                                                        @if (isset($priceRecord['formatted_purchase_price_per_gram']) && $priceRecord['formatted_purchase_price_per_gram'])
                                                            {{ $priceRecord['formatted_purchase_price_per_gram'] }}
                                                            {{ $priceRecord['currency_symbol'] }}
                                                        @else
                                                            <span class="text-gray-400 text-sm">غير محدد</span>
                                                        @endif
                                                    </td>
                                                    <td class="text-sm text-gray-600">
                                                        <div class="flex flex-col">
                                                            <span
                                                                class="font-medium">{{ $priceRecord['formatted_date'] }}</span>
                                                            <span
                                                                class="text-xs opacity-75">{{ $priceRecord['formatted_time_ago'] }}</span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        @if ($priceRecord['direction'] === 'up')
                                                            <span class="price-change-up">
                                                                <i class="fas fa-arrow-up"></i>
                                                                +{{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @elseif($priceRecord['direction'] === 'down')
                                                            <span class="price-change-down">
                                                                <i class="fas fa-arrow-down"></i>
                                                                {{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @else
                                                            <span class="price-change-stable">
                                                                <i class="fas fa-minus"></i>
                                                                {{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="5" class="text-center py-8 text-gray-500">
                                                        <i class="fas fa-info-circle text-2xl mb-2"></i>
                                                        <p>{{ $translations['messages']['no_silver_prices'] }}</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Platinum Prices Card -->
                    @if ($platinumPrices->count() > 0)
                        <div class="price-card">
                            <div class="platinum-header">
                                <i class="fas fa-coins ml-2"></i>
                                {{ $translations['platinum_prices_title'] }}
                            </div>
                            <div class="p-6">
                                <div class="overflow-x-auto">
                                    <table class="metal-price-table">
                                        <thead>
                                            <tr>
                                                <th>النوع</th>
                                                <th>سعر الجرام</th>
                                                <th>سعر الأوقية</th>
                                                <th>التغير</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($platinumPrices as $priceRecord)
                                                <tr>
                                                    <td>
                                                        <div class="flex items-center">
                                                            <div class="platinum-icon">
                                                                <i class="fas fa-coins"></i>
                                                            </div>
                                                            <span
                                                                class="font-semibold">{{ $priceRecord['purity_label'] }}</span>
                                                        </div>
                                                    </td>
                                                    <td class="font-bold text-lg">
                                                        {{ $priceRecord['formatted_price_per_gram'] }}
                                                        {{ $priceRecord['currency_symbol'] }}</td>
                                                    <td class="font-semibold">
                                                        {{ $priceRecord['formatted_price_per_ounce'] }}
                                                        {{ $priceRecord['currency_symbol'] }}</td>
                                                    <td>
                                                        @if ($priceRecord['direction'] === 'up')
                                                            <span class="price-change-up">
                                                                <i class="fas fa-arrow-up"></i>
                                                                +{{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @elseif($priceRecord['direction'] === 'down')
                                                            <span class="price-change-down">
                                                                <i class="fas fa-arrow-down"></i>
                                                                {{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @else
                                                            <span class="price-change-stable">
                                                                <i class="fas fa-minus"></i>
                                                                {{ $priceRecord['formatted_change_percent'] }}%
                                                            </span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="4" class="text-center py-8 text-gray-500">
                                                        <i class="fas fa-info-circle text-2xl mb-2"></i>
                                                        <p>{{ $translations['messages']['no_platinum_prices'] }}</p>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Gold Price Chart -->
                    <div class="chart-container mb-12">
                        <h2 class="text-2xl font-bold mb-6 text-center text-gray-800">
                            <i class="fas fa-chart-line ml-2 text-yellow-600"></i>
                            رسم بياني لأسعار الذهب
                        </h2>

                        <!-- Gold Metal Type Buttons -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-yellow-700 mb-3 text-center">
                                اختر عيار الذهب</h3>
                            <div class="flex flex-wrap justify-center gap-2">
                                @if (isset($chartData['7']['datasets']['gold_24k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-400 to-yellow-600 text-white"
                                        data-metal="gold_24k">{{ $translations['chart']['metals']['gold_24k'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['gold_22k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-300 to-yellow-500 text-white"
                                        data-metal="gold_22k">{{ $translations['chart']['metals']['gold_22k'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['gold_21k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-300 to-yellow-500 text-white active"
                                        data-metal="gold_21k">{{ $translations['chart']['metals']['gold_21k'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['gold_18k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-300 to-yellow-500 text-white"
                                        data-metal="gold_18k">{{ $translations['chart']['metals']['gold_18k'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['gold_14k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-300 to-yellow-500 text-white"
                                        data-metal="gold_14k">{{ $translations['chart']['metals']['gold_14k'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['gold_12k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-300 to-yellow-500 text-white"
                                        data-metal="gold_12k">{{ $translations['chart']['metals']['gold_12k'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['gold_9k']))
                                    <button
                                        class="gold-metal-btn bg-gradient-to-r from-yellow-300 to-yellow-500 text-white"
                                        data-metal="gold_9k">{{ $translations['chart']['metals']['gold_9k'] }}</button>
                                @endif
                            </div>
                        </div>

                        <!-- Gold Period Buttons -->
                        <div class="flex flex-wrap justify-center gap-2 mb-6">
                            <button class="gold-period-btn"
                                data-period="7">{{ $translations['chart']['periods']['7'] }}</button>
                            <button class="gold-period-btn active"
                                data-period="30">{{ $translations['chart']['periods']['30'] }}</button>
                            <button class="gold-period-btn"
                                data-period="90">{{ $translations['chart']['periods']['90'] }}</button>
                            <button class="gold-period-btn"
                                data-period="180">{{ $translations['chart']['periods']['180'] }}</button>
                            <button class="gold-period-btn"
                                data-period="365">{{ $translations['chart']['periods']['365'] }}</button>
                        </div>

                        <div class="h-80">
                            <canvas id="goldPriceChart"></canvas>
                        </div>

                        <div class="text-sm text-gray-500 mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                            <p class="font-semibold mb-2 text-yellow-800">ملاحظة حول أسعار الذهب:</p>
                            <p class="text-yellow-700">الأسعار المعروضة هي أسعار استرشادية وقد تختلف حسب السوق والتاجر.
                                يُنصح بالتحقق من الأسعار الحالية قبل اتخاذ أي قرار شراء.</p>
                        </div>
                    </div>

                    <!-- Silver Price Chart -->
                    <div class="chart-container">
                        <h2 class="text-2xl font-bold mb-6 text-center text-gray-800">
                            <i class="fas fa-chart-line ml-2 text-gray-600"></i>
                            رسم بياني لأسعار الفضة
                        </h2>

                        <!-- Silver Metal Type Buttons -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-700 mb-3 text-center">
                                اختر عيار الفضة</h3>
                            <div class="flex flex-wrap justify-center gap-2">
                                @if (isset($chartData['7']['datasets']['silver_999']))
                                    <button class="silver-metal-btn bg-gradient-to-r from-gray-400 to-gray-600 text-white"
                                        data-metal="silver_999">{{ $translations['chart']['metals']['silver_999'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['silver_925']))
                                    <button
                                        class="silver-metal-btn bg-gradient-to-r from-gray-400 to-gray-600 text-white active"
                                        data-metal="silver_925">{{ $translations['chart']['metals']['silver_925'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['silver_900']))
                                    <button class="silver-metal-btn bg-gradient-to-r from-gray-400 to-gray-600 text-white"
                                        data-metal="silver_900">{{ $translations['chart']['metals']['silver_900'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['silver_800']))
                                    <button class="silver-metal-btn bg-gradient-to-r from-gray-400 to-gray-600 text-white"
                                        data-metal="silver_800">{{ $translations['chart']['metals']['silver_800'] }}</button>
                                @endif
                                @if (isset($chartData['7']['datasets']['silver_600']))
                                    <button class="silver-metal-btn bg-gradient-to-r from-gray-400 to-gray-600 text-white"
                                        data-metal="silver_600">{{ $translations['chart']['metals']['silver_600'] }}</button>
                                @endif
                            </div>
                        </div>

                        <!-- Silver Period Buttons -->
                        <div class="flex flex-wrap justify-center gap-2 mb-6">
                            <button class="silver-period-btn"
                                data-period="7">{{ $translations['chart']['periods']['7'] }}</button>
                            <button class="silver-period-btn active"
                                data-period="30">{{ $translations['chart']['periods']['30'] }}</button>
                            <button class="silver-period-btn"
                                data-period="90">{{ $translations['chart']['periods']['90'] }}</button>
                            <button class="silver-period-btn"
                                data-period="180">{{ $translations['chart']['periods']['180'] }}</button>
                            <button class="silver-period-btn"
                                data-period="365">{{ $translations['chart']['periods']['365'] }}</button>
                        </div>

                        <div class="h-80">
                            <canvas id="silverPriceChart"></canvas>
                        </div>

                        <div class="text-sm text-gray-500 mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <p class="font-semibold mb-2 text-gray-800">ملاحظة حول أسعار الفضة:</p>
                            <p class="text-gray-700">الأسعار المعروضة هي أسعار استرشادية وقد تختلف حسب السوق والتاجر. يُنصح
                                بالتحقق من الأسعار الحالية قبل اتخاذ أي قرار شراء.</p>
                        </div>
                    </div>


                </div>
            </div>
        </section>
    </div>
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // بيانات الرسم البياني من الخادم
        const chartData = @json($chartData);

        // متغيرات للذهب
        let currentGoldMetal = 'gold_21k'; // الافتراضي عيار 21
        let currentGoldPeriod = '30';

        // متغيرات للفضة
        let currentSilverMetal = 'silver_925'; // الافتراضي عيار 925
        let currentSilverPeriod = '30';

        // تهيئة الرسوم البيانية
        const goldCtx = document.getElementById('goldPriceChart').getContext('2d');
        const silverCtx = document.getElementById('silverPriceChart').getContext('2d');
        let goldChart;
        let silverChart;

        // دالة إنشاء الرسم البياني
        function createChart(ctx, metal, period, chartType = 'gold') {
            if (chartData[period] && chartData[period].datasets && chartData[period].datasets[metal]) {
                const borderColor = chartType === 'gold' ? '#F59E0B' : '#6B7280';
                const backgroundColor = chartType === 'gold' ? 'rgba(245, 158, 11, 0.1)' : 'rgba(107, 114, 128, 0.1)';

                return new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: chartData[period].labels,
                        datasets: [{
                            label: chartData[period].datasets[metal].label + ' (جنيه/جرام)',
                            data: chartData[period].datasets[metal].data,
                            borderColor: borderColor,
                            backgroundColor: backgroundColor,
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                                labels: {
                                    font: {
                                        family: 'Tajawal',
                                        size: 14
                                    },
                                    padding: 20
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.dataset.label + ': ' + context.parsed.y.toFixed(2) +
                                            ' ج.م';
                                    }
                                },
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleColor: 'white',
                                bodyColor: 'white',
                                borderColor: chartType === 'gold' ? '#F59E0B' : '#6B7280',
                                borderWidth: 1
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value.toFixed(0) + ' ج.م';
                                    },
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                },
                                ticks: {
                                    font: {
                                        family: 'Tajawal'
                                    }
                                }
                            }
                        }
                    }
                });
            }
            return null;
        }

        // تهيئة الرسوم البيانية
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة رسم الذهب
            goldChart = createChart(goldCtx, currentGoldMetal, currentGoldPeriod, 'gold');
            if (!goldChart) {
                document.getElementById('goldPriceChart').parentElement.innerHTML =
                    '<div class="flex items-center justify-center h-full text-gray-500"><div class="text-center"><i class="fas fa-chart-line text-4xl mb-4"></i><p class="text-lg">لا توجد بيانات متاحة</p><p class="text-sm">لعيار الذهب المحدد</p></div></div>';
            }

            // تهيئة رسم الفضة
            silverChart = createChart(silverCtx, currentSilverMetal, currentSilverPeriod, 'silver');
            if (!silverChart) {
                document.getElementById('silverPriceChart').parentElement.innerHTML =
                    '<div class="flex items-center justify-center h-full text-gray-500"><div class="text-center"><i class="fas fa-chart-line text-4xl mb-4"></i><p class="text-lg">لا توجد بيانات متاحة</p><p class="text-sm">لعيار الفضة المحدد</p></div></div>';
            }
        });

        // أزرار الذهب - العيارات
        document.querySelectorAll('.gold-metal-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.gold-metal-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentGoldMetal = this.dataset.metal;
                updateGoldChart();
            });
        });

        // أزرار الذهب - الفترات
        document.querySelectorAll('.gold-period-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.gold-period-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentGoldPeriod = this.dataset.period;
                updateGoldChart();
            });
        });

        // أزرار الفضة - العيارات
        document.querySelectorAll('.silver-metal-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.silver-metal-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentSilverMetal = this.dataset.metal;
                updateSilverChart();
            });
        });

        // أزرار الفضة - الفترات
        document.querySelectorAll('.silver-period-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.silver-period-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentSilverPeriod = this.dataset.period;
                updateSilverChart();
            });
        });

        // دالة إعادة إنشاء canvas
        function recreateCanvas(containerId, canvasId) {
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `<canvas id="${canvasId}"></canvas>`;
                return document.getElementById(canvasId).getContext('2d');
            }
            return null;
        }

        // دالة تحديث رسم الذهب
        function updateGoldChart() {
            if (goldChart) {
                goldChart.destroy();
                goldChart = null;
            }

            // إعادة إنشاء canvas إذا لزم الأمر
            const goldContainer = document.getElementById('goldPriceChart').parentElement;
            if (!document.getElementById('goldPriceChart')) {
                goldContainer.innerHTML = '<canvas id="goldPriceChart"></canvas>';
                goldCtx = document.getElementById('goldPriceChart').getContext('2d');
            }

            goldChart = createChart(goldCtx, currentGoldMetal, currentGoldPeriod, 'gold');
            if (!goldChart) {
                goldContainer.innerHTML =
                    '<div class="flex items-center justify-center h-full text-gray-500"><div class="text-center"><i class="fas fa-chart-line text-4xl mb-4"></i><p class="text-lg">لا توجد بيانات متاحة</p><p class="text-sm">لهذا العيار في الفترة المحددة</p></div></div>';
            }
        }

        // دالة تحديث رسم الفضة
        function updateSilverChart() {
            if (silverChart) {
                silverChart.destroy();
                silverChart = null;
            }

            // إعادة إنشاء canvas إذا لزم الأمر
            const silverContainer = document.getElementById('silverPriceChart').parentElement;
            if (!document.getElementById('silverPriceChart')) {
                silverContainer.innerHTML = '<canvas id="silverPriceChart"></canvas>';
                silverCtx = document.getElementById('silverPriceChart').getContext('2d');
            }

            silverChart = createChart(silverCtx, currentSilverMetal, currentSilverPeriod, 'silver');
            if (!silverChart) {
                silverContainer.innerHTML =
                    '<div class="flex items-center justify-center h-full text-gray-500"><div class="text-center"><i class="fas fa-chart-line text-4xl mb-4"></i><p class="text-lg">لا توجد بيانات متاحة</p><p class="text-sm">لهذا العيار في الفترة المحددة</p></div></div>';
            }
        }

        // تحديث الصفحة كل 5 دقائق
        setInterval(function() {
            location.reload();
        }, 300000); // 5 دقائق

        // دالة إظهار/إخفاء تفاصيل الإحصائيات
        function toggleDetails(elementId) {
            const element = document.getElementById(elementId);
            const button = element.previousElementSibling.querySelector('i');

            if (element.classList.contains('hidden')) {
                element.classList.remove('hidden');
                button.classList.remove('fa-eye');
                button.classList.add('fa-eye-slash');
                element.previousElementSibling.querySelector('span').textContent =
                    '{{ __('metal_prices.statistics.hide_details') }}';
            } else {
                element.classList.add('hidden');
                button.classList.remove('fa-eye-slash');
                button.classList.add('fa-eye');
                element.previousElementSibling.querySelector('span').textContent =
                    '{{ __('metal_prices.statistics.view_details') }}';
            }
        }
    </script>
@endsection
