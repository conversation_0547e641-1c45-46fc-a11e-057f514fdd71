@extends('layouts.frontend')

@section('title', 'تم إتمام الطلب بنجاح')

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('cart') }}" class="text-gray-600 hover:text-primary-500">سلة المشتريات</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('checkout') }}" class="text-gray-600 hover:text-primary-500">الدفع</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">تأكيد الطلب</span>
            </div>
        </div>
    </div>

    <!-- Success Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto">
                <div class="bg-white rounded-lg shadow-sm p-8 text-center mb-8">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-check text-green-500 text-3xl"></i>
                    </div>
                    
                    <h1 class="text-3xl font-bold text-gray-800 mb-4">تم إتمام الطلب بنجاح!</h1>
                    <p class="text-gray-600 mb-6">شكرًا لك على طلبك. تم استلام طلبك وسيتم معالجته في أقرب وقت ممكن.</p>
                    
                    <div class="bg-gray-50 rounded-lg p-6 text-right mb-6">
                        <h2 class="text-xl font-bold mb-4">تفاصيل الطلب</h2>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">رقم الطلب:</span>
                                <span class="font-medium">{{ $order->order_number }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">تاريخ الطلب:</span>
                                <span>{{ $order->created_at->format('Y-m-d H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">طريقة الدفع:</span>
                                <span>
                                    @if($order->payment_method == 'cash')
                                        الدفع عند الاستلام
                                    @elseif($order->payment_method == 'credit_card')
                                        بطاقة ائتمان
                                    @elseif($order->payment_method == 'bank_transfer')
                                        تحويل بنكي
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">حالة الدفع:</span>
                                <span>
                                    @if($order->payment_status == 'pending')
                                        <span class="text-yellow-600">قيد الانتظار</span>
                                    @elseif($order->payment_status == 'paid')
                                        <span class="text-green-600">تم الدفع</span>
                                    @elseif($order->payment_status == 'failed')
                                        <span class="text-red-600">فشل الدفع</span>
                                    @endif
                                </span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">المجموع:</span>
                                <span class="font-medium">{{ number_format($order->total_amount, 2) }} ج.م</span>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-4">
                            <h3 class="font-bold mb-2">عنوان الشحن</h3>
                            <p class="text-gray-600">{{ $order->shipping_name }}</p>
                            <p class="text-gray-600">{{ $order->shipping_address }}</p>
                            <p class="text-gray-600">{{ $order->shipping_city }}، {{ $order->shipping_country }}</p>
                            <p class="text-gray-600">{{ $order->shipping_phone }}</p>
                        </div>
                    </div>
                    
                    <div class="space-y-2">
                        <p class="text-gray-600">تم إرسال تفاصيل الطلب إلى بريدك الإلكتروني {{ $order->shipping_email }}</p>
                        <p class="text-gray-600">إذا كان لديك أي أسئلة، يرجى <a href="{{ route('contact') }}" class="text-primary-500 hover:text-primary-600">الاتصال بنا</a>.</p>
                    </div>
                </div>
                
                <div class="flex flex-wrap justify-center gap-4">
                    <a href="{{ route('account.orders.show', $order->id) }}" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                        عرض تفاصيل الطلب
                    </a>
                    <a href="{{ route('products') }}" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-full font-medium transition duration-300">
                        متابعة التسوق
                    </a>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Recommended Products Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl font-bold mb-8 text-center">قد يعجبك أيضًا</h2>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @foreach(\App\Models\Product::where('is_featured', true)->where('is_active', true)->inRandomOrder()->take(4)->get() as $product)
                <div class="product-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300">
                    <a href="{{ route('product.show', $product->slug) }}" class="block relative">
                        <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/products/default.jpg') }}" alt="{{ $product->name_ar }}" class="w-full h-64 object-cover">
                        @if($product->discount_percentage > 0)
                        <span class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">خصم {{ $product->discount_percentage }}%</span>
                        @endif
                    </a>
                    <div class="p-4">
                        <a href="{{ route('category', $product->category->slug) }}" class="text-xs text-primary-500 hover:text-primary-600 mb-2 inline-block">{{ $product->category->name_ar }}</a>
                        <h3 class="text-lg font-bold mb-2">
                            <a href="{{ route('product.show', $product->slug) }}" class="text-gray-800 hover:text-primary-500">{{ $product->name_ar }}</a>
                        </h3>
                        <div class="flex items-center mb-3">
                            <div class="flex text-yellow-400">
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $product->rating)
                                        <i class="fas fa-star"></i>
                                    @elseif($i - 0.5 <= $product->rating)
                                        <i class="fas fa-star-half-alt"></i>
                                    @else
                                        <i class="far fa-star"></i>
                                    @endif
                                @endfor
                            </div>
                            <span class="text-gray-500 text-xs mr-1">({{ $product->reviews_count ?? 0 }})</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                @if($product->old_price > 0)
                                <span class="text-gray-400 line-through text-sm">{{ number_format($product->old_price, 2) }} ج.م</span>
                                @endif
                                <span class="text-lg font-bold text-gray-800">{{ number_format($product->price, 2) }} ج.م</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ route('account.wishlist.add', $product->id) }}" class="text-gray-400 hover:text-red-500 transition duration-300">
                                    <i class="far fa-heart"></i>
                                </a>
                                <a href="{{ route('product.show', $product->slug) }}" class="text-gray-400 hover:text-primary-500 transition duration-300">
                                    <i class="fas fa-shopping-cart"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
@endsection
