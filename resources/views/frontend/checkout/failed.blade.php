@extends('layouts.frontend')

@section('title', app()->getLocale() == 'ar' ? 'فشل الدفع' : 'Payment Failed')

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('cart') }}" class="text-gray-600 hover:text-primary-500">{{ __('Cart') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('checkout') }}" class="text-gray-600 hover:text-primary-500">{{ __('Checkout') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ app()->getLocale() == 'ar' ? 'فشل الدفع' : 'Payment Failed' }}</span>
            </div>
        </div>
    </div>

    <!-- Failed Payment Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <div class="bg-red-100 rounded-full h-24 w-24 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-times text-red-500 text-4xl"></i>
                </div>
                
                <h1 class="text-3xl font-bold text-gray-800 mb-4">{{ app()->getLocale() == 'ar' ? 'فشل عملية الدفع' : 'Payment Failed' }}</h1>
                
                <p class="text-gray-600 mb-8">
                    {{ app()->getLocale() == 'ar' ? 'للأسف، لم تتم عملية الدفع بنجاح. يرجى المحاولة مرة أخرى أو اختيار طريقة دفع أخرى.' : 'Unfortunately, your payment was not successful. Please try again or choose another payment method.' }}
                </p>
                
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h2 class="text-xl font-bold mb-4">{{ __('Order Details') }}</h2>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Order Number') }}:</span>
                        <span class="font-medium">{{ $order->order_number }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Date') }}:</span>
                        <span class="font-medium">{{ $order->created_at->format('Y-m-d') }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Total') }}:</span>
                        <span class="font-medium">{{ number_format($order->total_amount + $order->tax_amount + $order->shipping_amount, 2) }} {{ $order->currency }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2">
                        <span class="text-gray-600">{{ __('Status') }}:</span>
                        <span class="text-red-500 font-medium">{{ app()->getLocale() == 'ar' ? 'فشل الدفع' : 'Payment Failed' }}</span>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="{{ route('checkout') }}" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                        {{ app()->getLocale() == 'ar' ? 'إعادة المحاولة' : 'Try Again' }}
                    </a>
                    
                    <a href="{{ route('products') }}" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-full font-medium transition duration-300">
                        {{ __('Continue Shopping') }}
                    </a>
                </div>
                
                <p class="mt-8 text-gray-500 text-sm">
                    {{ app()->getLocale() == 'ar' ? 'إذا كنت تواجه مشكلة في إتمام عملية الدفع، يرجى التواصل مع خدمة العملاء للمساعدة.' : 'If you are experiencing issues with payment, please contact our customer service for assistance.' }}
                </p>
            </div>
        </div>
    </section>
@endsection
