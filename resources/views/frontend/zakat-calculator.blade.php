@extends('layouts.frontend')

@section('title', $translations['page_title'])

@push('styles')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .zakat-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #fefce8 100%);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .zakat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .nisab-badge {
            background: linear-gradient(135deg, #FFC107 0%, #FFB300 100%);
            color: #92400e;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .zakat-button {
            background: linear-gradient(135deg, #FFC107 0%, #FF8F00 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
        }

        .zakat-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
        }

        .gold-nisab {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
        }

        .silver-nisab {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border: 1px solid #9ca3af;
            border-radius: 8px;
            padding: 1rem;
        }

        .applicable-nisab {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border: 2px solid #22c55e;
            border-radius: 8px;
            padding: 1rem;
            position: relative;
            overflow: hidden;
        }

        .applicable-nisab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #22c55e, #16a34a, #15803d);
        }

        .condition-item {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid #0ea5e9;
        }

        .islamic-accent {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #f59e0b;
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section -->
    <section class="relative overflow-hidden py-20"
        style="background: linear-gradient(135deg, #FFC107 0%, #FFB300 50%, #FF8F00 100%);">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative container mx-auto px-4">
            <div class="text-center text-white">
                <div
                    class="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-6 backdrop-blur-sm">
                    <i class="fas fa-balance-scale text-3xl text-white"></i>
                </div>

                <h1 class="text-4xl md:text-6xl font-bold mb-4 leading-tight">
                    {{ $translations['page_title'] }}
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
                    {{ $translations['page_subtitle'] }}
                </p>

                <!-- Islamic Badge -->
                <div class="inline-flex items-center bg-white bg-opacity-20 backdrop-blur-sm rounded-full px-6 py-3 mb-8">
                    <i class="fas fa-mosque text-yellow-200 ml-2"></i>
                    <span class="font-semibold">{{ __('zakat.info.zakat_rate') }}</span>
                </div>

                <!-- Breadcrumb -->
                <nav class="text-sm opacity-80">
                    <a href="{{ route('home') }}"
                        class="hover:underline transition duration-300">{{ __('zakat.breadcrumb.home') }}</a>
                    <span class="mx-2">›</span>
                    <span>{{ __('zakat.breadcrumb.zakat_calculator') }}</span>
                </nav>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 bg-yellow-300 bg-opacity-20 rounded-full animate-pulse"></div>
        <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-white bg-opacity-5 rounded-full"></div>
    </section>

    <!-- Main Content -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Personal Jewelry Calculator Section -->
                <div class="lg:col-span-2">
                    <!-- Livewire Component -->
                    @livewire('personal-jewelry-calculator')
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">

                    <!-- Current Nisab -->
                    <div class="bg-white rounded-lg shadow-lg p-6 border-t-4 border-yellow-500">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-balance-scale text-yellow-500 mr-3"></i>
                            {{ __('zakat.nisab.title') }}
                        </h3>

                        <div class="space-y-4">
                            <!-- Gold Nisab -->
                            <div class="p-4 bg-yellow-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <span
                                        class="text-sm font-medium text-gray-700">{{ __('zakat.nisab.gold_nisab') }}</span>
                                    <span class="text-lg font-bold text-yellow-600">
                                        {{ $nisabCalculations['gold']['formatted_value'] }} {{ __('zakat.units.egp') }}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-600 mt-1">
                                    ({{ $nisabCalculations['gold']['weight'] }} {{ __('zakat.units.grams') }})
                                </p>
                            </div>

                            <!-- Silver Nisab -->
                            <div class="p-4 bg-gray-50 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <span
                                        class="text-sm font-medium text-gray-700">{{ __('zakat.nisab.silver_nisab') }}</span>
                                    <span class="text-lg font-bold text-gray-600">
                                        {{ $nisabCalculations['silver']['formatted_value'] }} {{ __('zakat.units.egp') }}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-600 mt-1">
                                    ({{ $nisabCalculations['silver']['weight'] }} {{ __('zakat.units.grams') }})
                                </p>
                            </div>

                            <!-- Applicable Nisab -->
                            <div
                                class="p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border-2 border-green-200 shadow-sm">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm font-medium text-gray-700 flex items-center">
                                        <i class="fas fa-star text-green-500 mr-2"></i>
                                        {{ __('zakat.nisab.applicable_nisab') }}
                                    </span>
                                    <span class="text-lg font-bold text-green-600">
                                        {{ $nisabCalculations['applicable']['formatted_value'] }}
                                        {{ __('zakat.units.egp') }}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-600 mt-1">
                                    {{ $nisabCalculations['applicable']['type'] == 'gold' ? __('zakat.nisab.based_on_gold') : __('zakat.nisab.based_on_silver') }}
                                </p>
                            </div>

                            @if ($latestPrices['last_update'])
                                <p class="text-xs text-gray-500 text-center">
                                    {{ __('zakat.nisab.last_update') }}: {{ $latestPrices['last_update'] }}
                                </p>
                            @endif
                        </div>
                    </div>

                    <!-- Zakat Conditions -->
                    <div class="bg-white rounded-lg shadow-lg p-6 border-t-4 border-blue-500">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-list-check text-blue-500 mr-3"></i>
                            {{ $translations['conditions_title'] }}
                        </h3>

                        <ul class="space-y-3">
                            @foreach ($zakatInfo['conditions'] as $condition)
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-green-500 mt-1 mr-3 flex-shrink-0"></i>
                                    <span class="text-sm text-gray-700">{{ $condition }}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>

                    <!-- Related Calculator -->
                    <div class="bg-blue-50 border-l-4 border-blue-400 p-6 rounded-lg">
                        <h4 class="text-lg font-bold text-blue-800 mb-2 flex items-center">
                            <i class="fas fa-gem mr-2"></i>
                            {{ __('zakat.related_calculator.title') }}
                        </h4>
                        <p class="text-sm text-blue-700 mb-3">
                            {{ __('zakat.related_calculator.description') }}
                        </p>
                        <a href="{{ route('jewelry-value-calculator') }}"
                            class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition duration-300">
                            <i class="fas fa-calculator mr-2"></i>
                            {{ __('zakat.related_calculator.link_text') }}
                        </a>
                    </div>

                    <!-- Important Notice -->
                    <div class="bg-orange-50 border-l-4 border-orange-400 p-6 rounded-lg">
                        <h4 class="text-lg font-bold text-orange-800 mb-2 flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            {{ $translations['disclaimer_title'] }}
                        </h4>
                        <p class="text-sm text-orange-700">
                            {{ $translations['disclaimer_content'] }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
