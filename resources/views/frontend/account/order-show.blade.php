@extends('layouts.frontend')

@section('title', 'تفاصيل الطلب #' . $order->order_number)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('account.dashboard') }}" class="text-gray-600 hover:text-primary-500">حسابي</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('account.orders') }}" class="text-gray-600 hover:text-primary-500">طلباتي</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">تفاصيل الطلب #{{ $order->order_number }}</span>
            </div>
        </div>
    </div>

    <!-- Account Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Sidebar -->
                <div class="lg:w-1/4">
                    @livewire('account.sidebar')
                </div>

                <!-- Main Content -->
                <div class="lg:w-3/4">
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
                        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                            <h1 class="text-2xl font-bold">تفاصيل الطلب #{{ $order->order_number }}</h1>

                            <div>
                                @if($order->status == 'pending')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">قيد الانتظار</span>
                                @elseif($order->status == 'processing')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">قيد التجهيز</span>
                                @elseif($order->status == 'shipped')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">تم الشحن</span>
                                @elseif($order->status == 'delivered')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">تم التسليم</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">ملغي</span>
                                @endif
                            </div>
                        </div>

                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                                <div>
                                    <h3 class="text-lg font-bold mb-3">معلومات الطلب</h3>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">رقم الطلب:</span>
                                            <span class="font-medium">{{ $order->order_number }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">تاريخ الطلب:</span>
                                            <span>{{ $order->created_at->format('Y-m-d H:i') }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">طريقة الدفع:</span>
                                            <span>
                                                @if($order->payment_method == 'cash')
                                                    الدفع عند الاستلام
                                                @elseif($order->payment_method == 'credit_card')
                                                    بطاقة ائتمان
                                                @elseif($order->payment_method == 'bank_transfer')
                                                    تحويل بنكي
                                                @endif
                                            </span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">حالة الدفع:</span>
                                            <span>
                                                @if($order->payment_status == 'pending')
                                                    <span class="text-yellow-600">قيد الانتظار</span>
                                                @elseif($order->payment_status == 'paid')
                                                    <span class="text-green-600">تم الدفع</span>
                                                @elseif($order->payment_status == 'failed')
                                                    <span class="text-red-600">فشل الدفع</span>
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-bold mb-3">معلومات الشحن</h3>
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">الاسم:</span>
                                            <span>{{ $order->shipping_name }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">العنوان:</span>
                                            <span>{{ $order->shipping_address }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">المدينة:</span>
                                            <span>{{ $order->shipping_city }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">البلد:</span>
                                            <span>{{ $order->shipping_country }}</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">الهاتف:</span>
                                            <span>{{ $order->shipping_phone }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h3 class="text-lg font-bold mb-3">المنتجات</h3>
                            <div class="overflow-x-auto">
                                <table class="w-full min-w-[600px]">
                                    <thead class="bg-gray-50 border-b border-gray-200">
                                        <tr>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200">
                                        @foreach($order->items as $item)
                                        <tr>
                                            <td class="px-6 py-4">
                                                <div class="flex items-center">
                                                    <img src="{{ $item->product->image ? asset('storage/' . $item->product->image) : asset('images/products/default.jpg') }}" alt="{{ $item->product->name_ar }}" class="w-16 h-16 object-cover rounded-md ml-4">
                                                    <div>
                                                        <h4 class="font-medium text-gray-800">{{ $item->product->name_ar }}</h4>
                                                        <p class="text-gray-500 text-sm">{{ $item->product->metal_type == 'gold' ? 'ذهب' : ($item->product->metal_type == 'silver' ? 'فضة' : 'بلاتين') }} {{ $item->product->purity }}</p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="text-gray-800">{{ number_format($item->price, 2) }} ج.م</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="text-gray-800">{{ $item->quantity }}</span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="text-gray-800 font-medium">{{ number_format($item->total, 2) }} ج.م</span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-8 border-t border-gray-200 pt-6">
                                <div class="flex justify-end">
                                    <div class="w-full md:w-1/2 lg:w-1/3">
                                        <div class="space-y-3">
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">المجموع الفرعي:</span>
                                                <span class="text-gray-800">{{ number_format($order->total_amount - $order->tax_amount - $order->shipping_amount + $order->discount_amount, 2) }} ج.م</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">الضريبة (14%):</span>
                                                <span class="text-gray-800">{{ number_format($order->tax_amount, 2) }} ج.م</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">الشحن:</span>
                                                <span class="text-gray-800">{{ number_format($order->shipping_amount, 2) }} ج.م</span>
                                            </div>
                                            @if($order->discount_amount > 0)
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">الخصم:</span>
                                                <span class="text-green-600">-{{ number_format($order->discount_amount, 2) }} ج.م</span>
                                            </div>
                                            @endif
                                            <div class="flex justify-between pt-3 border-t border-gray-200 font-bold">
                                                <span>المجموع:</span>
                                                <span class="text-primary-500">{{ number_format($order->total_amount, 2) }} ج.م</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <a href="{{ route('account.orders') }}" class="flex items-center text-primary-500 hover:text-primary-600 font-medium">
                            <i class="fas fa-arrow-right ml-2"></i>
                            العودة إلى الطلبات
                        </a>

                        @if($order->status == 'pending')
                        <form action="#" method="POST" onsubmit="return confirm('هل أنت متأكد من إلغاء هذا الطلب؟')">
                            @csrf
                            <button type="submit" class="flex items-center text-red-500 hover:text-red-600 font-medium">
                                <i class="fas fa-times ml-2"></i>
                                إلغاء الطلب
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
