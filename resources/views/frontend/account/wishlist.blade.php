@extends('layouts.frontend')

@section('title', 'المفضلة')

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('account.dashboard') }}" class="text-gray-600 hover:text-primary-500">حسابي</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">المفضلة</span>
            </div>
        </div>
    </div>

    <!-- Wishlist Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row gap-8">
                <!-- Sidebar -->
                <div class="md:w-1/4">
                    <div class="bg-white rounded-lg shadow-sm p-6">
                        <h2 class="text-xl font-bold mb-6">حسابي</h2>

                        <nav class="space-y-2">
                            <a href="{{ route('account.dashboard') }}"
                                class="flex items-center py-2 px-4 rounded-md {{ request()->routeIs('account.dashboard') ? 'bg-primary-50 text-primary-600' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-tachometer-alt ml-3 w-5 text-center"></i>
                                <span>لوحة التحكم</span>
                            </a>

                            <a href="{{ route('account.orders') }}"
                                class="flex items-center py-2 px-4 rounded-md {{ request()->routeIs('account.orders') ? 'bg-primary-50 text-primary-600' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-shopping-bag ml-3 w-5 text-center"></i>
                                <span>طلباتي</span>
                            </a>

                            <a href="{{ route('account.wishlist') }}"
                                class="flex items-center py-2 px-4 rounded-md {{ request()->routeIs('account.wishlist') ? 'bg-primary-50 text-primary-600' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-heart ml-3 w-5 text-center"></i>
                                <span>المفضلة</span>
                            </a>

                            <a href="{{ route('account.settings') }}"
                                class="flex items-center py-2 px-4 rounded-md {{ request()->routeIs('account.settings') ? 'bg-primary-50 text-primary-600' : 'text-gray-700 hover:bg-gray-100' }}">
                                <i class="fas fa-user-cog ml-3 w-5 text-center"></i>
                                <span>إعدادات الحساب</span>
                            </a>

                            <form method="POST" action="{{ route('logout') }}" class="w-full">
                                @csrf
                                <button type="submit"
                                    class="flex items-center w-full text-right py-2 px-4 rounded-md text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-sign-out-alt ml-3 w-5 text-center"></i>
                                    <span>تسجيل الخروج</span>
                                </button>
                            </form>
                        </nav>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="md:w-3/4">
                    @livewire('account.wishlist')
                </div>
            </div>
        </div>
    </section>
@endsection
