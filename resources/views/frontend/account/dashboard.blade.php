@extends('layouts.frontend')

@section('title', 'لوحة التحكم')

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">حسابي</span>
            </div>
        </div>
    </div>

    <!-- Account Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row gap-8">
                <!-- Sidebar -->
                <div class="lg:w-1/4">
                    @livewire('account.sidebar')
                </div>

                <!-- Main Content -->
                <div class="lg:w-3/4">
                    <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                        <h1 class="text-2xl font-bold mb-6">مرحبًا، {{ $user->name }}!</h1>
                        <p class="text-gray-600 mb-4">
                            من خلال لوحة التحكم الخاصة بحسابك، يمكنك
                            @if (!$displayOnlyMode)
                            عرض <a href="{{ route('account.orders') }}" class="text-primary-500 hover:text-primary-600">طلباتك الأخيرة</a>،
                            @endif
                            @if ($showWishlist)
                            إدارة <a href="{{ route('account.wishlist') }}" class="text-primary-500 hover:text-primary-600">المفضلة</a>،
                            @endif
                            إدارة <a href="{{ route('account.settings') }}" class="text-primary-500 hover:text-primary-600">تفاصيل حسابك</a>، وتعديل كلمة المرور الخاصة بك.
                        </p>
                    </div>

                    <!-- Account Summary -->
                    <div class="grid grid-cols-1 md:grid-cols-{{ (!$displayOnlyMode && $showWishlist) ? '3' : ((!$displayOnlyMode || $showWishlist) ? '2' : '1') }} gap-6 mb-8">
                        @if (!$displayOnlyMode)
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center ml-4">
                                    <i class="fas fa-shopping-bag text-primary-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-bold text-gray-800">الطلبات</h3>
                                    <p class="text-gray-600">{{ $orders->count() }} طلب</p>
                                </div>
                            </div>
                            <a href="{{ route('account.orders') }}" class="text-primary-500 hover:text-primary-600 font-medium flex items-center">
                                <span>عرض جميع الطلبات</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                        </div>
                        @endif

                        @if ($showWishlist)
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center ml-4">
                                    <i class="fas fa-heart text-primary-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-bold text-gray-800">المفضلة</h3>
                                    <p class="text-gray-600">{{ $user->wishlist()->count() }} منتج</p>
                                </div>
                            </div>
                            <a href="{{ route('account.wishlist') }}" class="text-primary-500 hover:text-primary-600 font-medium flex items-center">
                                <span>عرض المفضلة</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                        </div>
                        @endif

                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center ml-4">
                                    <i class="fas fa-cog text-primary-500"></i>
                                </div>
                                <div>
                                    <h3 class="text-lg font-bold text-gray-800">الإعدادات</h3>
                                    <p class="text-gray-600">تعديل بيانات الحساب</p>
                                </div>
                            </div>
                            <a href="{{ route('account.settings') }}" class="text-primary-500 hover:text-primary-600 font-medium flex items-center">
                                <span>تعديل الإعدادات</span>
                                <i class="fas fa-arrow-left mr-2"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Recent Orders -->
                    @if (!$displayOnlyMode)
                    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-bold">آخر الطلبات</h2>
                        </div>

                        @if($orders->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="w-full min-w-[800px]">
                                <thead class="bg-gray-50 border-b border-gray-200">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">رقم الطلب</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المجموع</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"></th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    @foreach($orders as $order)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="font-medium text-gray-800">{{ $order->order_number }}</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-gray-600">{{ $order->created_at->format('Y-m-d') }}</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="text-gray-800">{{ number_format($order->total_amount, 2) }} ج.م</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if($order->status == 'pending')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">قيد الانتظار</span>
                                            @elseif($order->status == 'processing')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">قيد التجهيز</span>
                                            @elseif($order->status == 'shipped')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">تم الشحن</span>
                                            @elseif($order->status == 'delivered')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">تم التسليم</span>
                                            @elseif($order->status == 'cancelled')
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">ملغي</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-left">
                                            <a href="{{ route('account.orders.show', $order->id) }}" class="text-primary-500 hover:text-primary-600 font-medium">
                                                عرض التفاصيل
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @else
                        <div class="p-6 text-center">
                            <p class="text-gray-600">لا توجد طلبات حتى الآن.</p>
                            <a href="{{ route('products') }}" class="inline-block mt-4 bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md font-medium transition duration-300">
                                تسوق الآن
                            </a>
                        </div>
                        @endif
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </section>
@endsection
