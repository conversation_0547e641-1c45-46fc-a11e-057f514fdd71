<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $settings->site_name ?? __('<PERSON>kkah Gold') }} - {{ __('Maintenance') }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/favicon.png') }}" type="image/png">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap"
        rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            500: '#FFC107', // Amber
                        },
                        gold: '#D4AF37',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }
        .gold-gradient {
            background: linear-gradient(135deg, #D4AF37 0%, #F9F295 50%, #D4AF37 100%);
        }
        .maintenance-bg {
            background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none" fill="%23D4AF37" opacity="0.05"><path d="M0 0 L100 100 L0 100 Z"></path></svg>'), url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none" fill="%23D4AF37" opacity="0.05"><path d="M100 0 L0 100 L100 100 Z"></path></svg>');
            background-size: 100% 100%;
        }
        .animate-pulse-slow {
            animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }
        .countdown {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 1.5rem 0;
        }
        .countdown-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            background-color: rgba(212, 175, 55, 0.1);
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            min-width: 4rem;
        }
        .countdown-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #D4AF37;
        }
        .countdown-label {
            font-size: 0.75rem;
            color: #6B7280;
        }
        .social-icons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        .social-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 9999px;
            background-color: rgba(212, 175, 55, 0.1);
            color: #D4AF37;
            transition: all 0.3s ease;
        }
        .social-icon:hover {
            background-color: #D4AF37;
            color: white;
        }
    </style>
</head>

<body class="bg-gray-100 min-h-screen flex flex-col items-center justify-center p-4 maintenance-bg">
    <div class="max-w-2xl w-full bg-white rounded-lg shadow-xl overflow-hidden">
        <div class="gold-gradient p-6 text-center">
            <h1 class="text-3xl font-bold text-white">{{ $settings->site_name ?? __('Makkah Gold') }}</h1>
        </div>
        <div class="p-8 text-center">
            <div class="text-6xl text-gold mb-6 animate-pulse-slow">
                <i class="fas fa-tools"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4">{{ __('Site Under Maintenance') }}</h2>

            <div class="bg-amber-50 border-l-4 border-amber-500 p-4 mb-6 text-right">
                <p class="text-gray-700 text-lg">{{ $message }}</p>
            </div>

            <p class="text-gray-600 mb-4">{{ __('We are working hard to improve our website and will be back soon.') }}</p>

            <div class="countdown">
                <div class="countdown-item">
                    <span class="countdown-value" id="hours">00</span>
                    <span class="countdown-label">{{ __('Hours') }}</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-value" id="minutes">00</span>
                    <span class="countdown-label">{{ __('Minutes') }}</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-value" id="seconds">00</span>
                    <span class="countdown-label">{{ __('Seconds') }}</span>
                </div>
            </div>

            <div class="mt-6">
                <p class="text-gray-600 mb-2">{{ __('Need assistance? Contact us:') }}</p>
                @if(isset($settings) && $settings->contact_email)
                    <p class="text-gray-700 mb-1">
                        <i class="fas fa-envelope ml-2 text-gold"></i> {{ $settings->contact_email }}
                    </p>
                @endif
                @if(isset($settings) && $settings->contact_phone)
                    <p class="text-gray-700">
                        <i class="fas fa-phone ml-2 text-gold"></i> {{ $settings->contact_phone }}
                    </p>
                @endif
            </div>

            <div class="social-icons">
                @if(isset($settings) && $settings->facebook_url)
                    <a href="{{ $settings->facebook_url }}" target="_blank" class="social-icon">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                @endif
                @if(isset($settings) && $settings->instagram_url)
                    <a href="{{ $settings->instagram_url }}" target="_blank" class="social-icon">
                        <i class="fab fa-instagram"></i>
                    </a>
                @endif
                @if(isset($settings) && $settings->twitter_url)
                    <a href="{{ $settings->twitter_url }}" target="_blank" class="social-icon">
                        <i class="fab fa-twitter"></i>
                    </a>
                @endif
                @if(isset($settings) && $settings->whatsapp_number)
                    <a href="https://wa.me/{{ preg_replace('/[^0-9]/', '', $settings->whatsapp_number) }}" target="_blank" class="social-icon">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                @endif
            </div>

            <div class="mt-8 border-t border-gray-200 pt-6">
                <a href="{{ route('login') }}" class="inline-flex items-center justify-center px-6 py-3 bg-primary-500 text-white rounded-full hover:bg-amber-600 transition duration-300">
                    <i class="fas fa-sign-in-alt ml-2"></i> {{ __('Admin Login') }}
                </a>
            </div>
        </div>
    </div>
    <div class="mt-8 text-center text-gray-500 text-sm">
        &copy; {{ date('Y') }} {{ $settings->site_name ?? __('Makkah Gold') }}. {{ __('All Rights Reserved') }}.
    </div>

    <script>
        // عداد تنازلي بسيط (يعرض وقت عشوائي للعودة)
        function startCountdown() {
            // وقت عشوائي للعودة (بين 1-4 ساعات)
            const hours = Math.floor(Math.random() * 4) + 1;
            let totalSeconds = hours * 3600;

            function updateCountdown() {
                const hoursLeft = Math.floor(totalSeconds / 3600);
                const minutesLeft = Math.floor((totalSeconds % 3600) / 60);
                const secondsLeft = totalSeconds % 60;

                document.getElementById('hours').textContent = hoursLeft.toString().padStart(2, '0');
                document.getElementById('minutes').textContent = minutesLeft.toString().padStart(2, '0');
                document.getElementById('seconds').textContent = secondsLeft.toString().padStart(2, '0');

                if (totalSeconds > 0) {
                    totalSeconds--;
                    setTimeout(updateCountdown, 1000);
                } else {
                    // عند انتهاء العد التنازلي، أعد تحميل الصفحة
                    location.reload();
                }
            }

            updateCountdown();
        }

        // بدء العد التنازلي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', startCountdown);
    </script>
</body>

</html>
