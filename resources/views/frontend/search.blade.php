@extends('layouts.frontend')

@section('title', 'نتائج البحث: ' . $query)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">الرئيسية</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">نتائج البحث</span>
            </div>
        </div>
    </div>

    <!-- Search Results Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            @livewire('search-results', ['query' => $query])
        </div>
    </section>

    <!-- Popular Searches Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-5xl mx-auto">
                <h2 class="text-2xl font-bold mb-6">عمليات البحث الشائعة</h2>

                <div class="flex flex-wrap gap-2">
                    @if (isset($popularSearches) && $popularSearches->count() > 0)
                        @foreach ($popularSearches as $popularSearch)
                            <a href="{{ route('search', ['query' => $popularSearch]) }}"
                                class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                                {{ $popularSearch }}
                            </a>
                        @endforeach
                    @else
                        <!-- البحثات الافتراضية إذا لم توجد بحثات شائعة -->
                        <a href="{{ route('search', ['query' => 'خاتم']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            خاتم
                        </a>
                        <a href="{{ route('search', ['query' => 'سلسلة']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            سلسلة
                        </a>
                        <a href="{{ route('search', ['query' => 'أساور']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            أساور
                        </a>
                        <a href="{{ route('search', ['query' => 'أقراط']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            أقراط
                        </a>
                        <a href="{{ route('search', ['query' => 'فضة']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            فضة
                        </a>
                        <a href="{{ route('search', ['query' => 'هدايا']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            هدايا
                        </a>
                        <a href="{{ route('search', ['query' => 'خاتم خطوبة']) }}"
                            class="bg-white hover:bg-primary-50 border border-gray-300 hover:border-primary-500 text-gray-700 hover:text-primary-500 px-4 py-2 rounded-full transition duration-300">
                            خاتم خطوبة
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Categories Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-5xl mx-auto">
                <h2 class="text-2xl font-bold mb-6">تصفح حسب الفئة</h2>

                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    @foreach (\App\Models\Category::where('is_active', true)->where('parent_id', null)->take(6)->get() as $category)
                        <a href="{{ route('category', $category->slug) }}" class="group">
                            <div
                                class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition duration-300 text-center p-4">
                                <div
                                    class="w-16 h-16 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-gem text-primary-500 text-xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800 group-hover:text-primary-500 transition duration-300">
                                    {{ $category->name_ar }}</h3>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
@endsection
