@extends('layouts.frontend')

@section('title', $translations['page_title'])

@push('styles')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .calculator-card {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .calculator-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .price-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .modern-button {
            background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
        }

        .modern-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }

        .gold-accent {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #92400e;
        }

        .silver-accent {
            background: linear-gradient(135deg, #c0c0c0, #e5e7eb);
            color: #374151;
        }

        .info-card {
            background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
            border-left: 4px solid #8b5cf6;
            border-radius: 8px;
            padding: 1rem;
        }

        .success-card {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border-left: 4px solid #22c55e;
            border-radius: 8px;
            padding: 1rem;
        }

        .warning-card {
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
            border-left: 4px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
        }
    </style>
@endpush

@section('content')
    <!-- Hero Section -->
    <section class="relative overflow-hidden py-20"
        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #D4AF37 100%);">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative container mx-auto px-4">
            <div class="text-center text-white">
                <div
                    class="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-6 backdrop-blur-sm">
                    <i class="fas fa-gem text-3xl text-white"></i>
                </div>

                <h1 class="text-4xl md:text-6xl font-bold mb-4 leading-tight">
                    {{ $translations['page_title'] }}
                </h1>
                <p class="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto leading-relaxed">
                    {{ $translations['page_description'] }}
                </p>

                <!-- Currency Badge -->
                <div class="inline-flex items-center bg-white bg-opacity-20 backdrop-blur-sm rounded-full px-6 py-3 mb-8">
                    <i class="fas fa-coins text-gold ml-2"></i>
                    <span class="font-semibold">{{ __('jewelry_value.units.egp') }} - {{ __('Egyptian Pound') }}</span>
                </div>

                <!-- Breadcrumb -->
                <nav class="text-sm opacity-80">
                    <a href="{{ route('home') }}"
                        class="hover:underline transition duration-300">{{ $translations['breadcrumb_home'] }}</a>
                    <span class="mx-2">›</span>
                    <span>{{ $translations['breadcrumb_calculator'] }}</span>
                </nav>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 bg-white bg-opacity-10 rounded-full animate-pulse"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 bg-gold bg-opacity-20 rounded-full animate-pulse"></div>
        <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-white bg-opacity-5 rounded-full"></div>
    </section>

    <!-- Main Content -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                <!-- Jewelry Value Calculator Section -->
                <div class="lg:col-span-2">
                    <!-- Livewire Component -->
                    @livewire('jewelry-value-calculator')
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">

                    <!-- Current Prices -->
                    <div class="bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-chart-line text-purple-500 mr-3"></i>
                            {{ $translations['sidebar']['current_prices_title'] }}
                        </h3>

                        <div class="space-y-4">
                            <!-- Gold Prices -->
                            @if (!empty($latestPrices['gold']))
                                <div
                                    class="p-4 bg-gradient-to-br from-yellow-50 to-amber-50 rounded-lg border border-yellow-200">
                                    <h4 class="font-bold text-yellow-800 mb-3 flex items-center">
                                        🥇 {{ $translations['sidebar']['gold_prices'] }}
                                    </h4>
                                    <div class="space-y-2">
                                        @foreach ($latestPrices['gold'] as $purity => $price)
                                            <div
                                                class="flex justify-between items-center text-sm bg-white/50 rounded px-2 py-1.5 hover:bg-white/80 transition-colors">
                                                <span class="font-medium text-gray-800">
                                                    {{ __('metal_prices.purities.' . $purity, [], 'ar', $purity) }}
                                                </span>
                                                <span class="font-bold text-yellow-700">
                                                    {{ number_format($price->price_per_gram, 2) }}
                                                    <span
                                                        class="text-xs text-gray-600">{{ $translations['units']['egp'] }}</span>
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <!-- Silver Prices -->
                            @if (!empty($latestPrices['silver']))
                                <div
                                    class="p-4 bg-gradient-to-br from-slate-50 to-gray-50 rounded-lg border border-slate-200">
                                    <h4 class="font-bold text-slate-800 mb-3 flex items-center">
                                        🥈 {{ $translations['sidebar']['silver_prices'] }}
                                    </h4>
                                    <div class="space-y-2">
                                        @foreach ($latestPrices['silver'] as $purity => $price)
                                            <div
                                                class="flex justify-between items-center text-sm bg-white/50 rounded px-2 py-1.5 hover:bg-white/80 transition-colors">
                                                <span class="font-medium text-gray-800">
                                                    {{ __('metal_prices.purities.' . $purity, [], 'ar', $purity) }}
                                                </span>
                                                <span class="font-bold text-slate-700">
                                                    {{ number_format($price->price_per_gram, 2) }}
                                                    <span
                                                        class="text-xs text-gray-600">{{ $translations['units']['egp'] }}</span>
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            @if ($latestPrices['last_update'])
                                <div class="p-3 bg-blue-50 rounded-lg border border-blue-200 text-center">
                                    <p class="text-xs text-blue-700 font-medium">
                                        <i class="fas fa-clock mr-1"></i>
                                        {{ $translations['sidebar']['last_update'] }}: {{ $latestPrices['last_update'] }}
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Information -->
                    <div class="bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-info-circle text-purple-500 mr-3"></i>
                            {{ $translations['sidebar']['info_title'] }}
                        </h3>

                        <div class="text-sm text-gray-700 space-y-3">
                            <p>{{ $translations['sidebar']['info_content'] }}</p>

                            <ul class="space-y-2">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-purple-500 mt-1 mr-2 flex-shrink-0"></i>
                                    <span>{{ __('jewelry_value.info.real_time_prices') }}</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-purple-500 mt-1 mr-2 flex-shrink-0"></i>
                                    <span>{{ __('jewelry_value.info.accurate_calculations') }}</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-purple-500 mt-1 mr-2 flex-shrink-0"></i>
                                    <span>{{ __('jewelry_value.info.multiple_items') }}</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-purple-500 mt-1 mr-2 flex-shrink-0"></i>
                                    <span>{{ __('jewelry_value.info.session_storage') }}</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Related Calculator -->
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-6 rounded-lg">
                        <h4 class="text-lg font-bold text-yellow-800 mb-2 flex items-center">
                            <i class="fas fa-calculator mr-2"></i>
                            {{ __('jewelry_value.related_calculator.title') }}
                        </h4>
                        <p class="text-sm text-yellow-700 mb-3">
                            {{ __('jewelry_value.related_calculator.description') }}
                        </p>
                        <a href="{{ route('zakat-calculator') }}"
                            class="inline-flex items-center px-4 py-2 bg-yellow-500 hover:bg-yellow-600 text-white text-sm font-medium rounded-lg transition duration-300">
                            <i class="fas fa-balance-scale mr-2"></i>
                            {{ __('jewelry_value.related_calculator.link_text') }}
                        </a>
                    </div>

                    <!-- Important Notice -->
                    <div class="bg-purple-50 border-l-4 border-purple-400 p-6 rounded-lg">
                        <h4 class="text-lg font-bold text-purple-800 mb-2 flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ __('jewelry_value.notice.title') }}
                        </h4>
                        <p class="text-sm text-purple-700">
                            {{ __('jewelry_value.notice.content') }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
