@extends('layouts.frontend')

@section('title', 'تفاصيل المنتج')

@section('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" />
    <style>
        .swiper-button-next,
        .swiper-button-prev {
            color: #FFC107 !important;
            background: rgba(255, 255, 255, 0.8) !important;
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
        }

        .swiper-button-next:after,
        .swiper-button-prev:after {
            font-size: 16px !important;
            font-weight: bold;
        }

        .swiper-pagination-bullet {
            background: rgba(255, 193, 7, 0.5) !important;
            opacity: 1 !important;
        }

        .swiper-pagination-bullet-active {
            background: #FFC107 !important;
            transform: scale(1.2);
        }

        .product-thumb {
            opacity: 0.6;
            transition: all 0.3s ease;
            position: relative;
        }

        .product-thumb.active,
        .product-thumb:hover {
            opacity: 1;
            border-color: #FFC107 !important;
            transform: scale(1.05);
        }

        .product-thumb.swiper-slide-thumb-active {
            opacity: 1;
            border-color: #FFC107 !important;
        }

        /* Loading skeleton */
        .image-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }

            100% {
                background-position: -200% 0;
            }
        }

        /* Zoom cursor */
        .cursor-zoom-in {
            cursor: zoom-in;
        }

        /* RTL Support */
        [dir="rtl"] .swiper-button-next {
            left: 10px;
            right: auto;
        }

        [dir="rtl"] .swiper-button-prev {
            right: 10px;
            left: auto;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {

            .swiper-button-next,
            .swiper-button-prev {
                width: 35px !important;
                height: 35px !important;
            }

            .swiper-button-next:after,
            .swiper-button-prev:after {
                font-size: 14px !important;
            }
        }
    </style>
@endsection

@section('content')
    @livewire('product-detail', ['slug' => $slug])
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
@endsection
