@extends('layouts.frontend')

@section('title', __('Book an Appointment'))

@section('styles')
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_gold.css">
    <style>
        .appointment-card {
            background-image: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5)), url('{{ asset('images/appointment-bg.jpg') }}');
            background-size: cover;
            background-position: center;
        }
    </style>
@endsection

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Book an Appointment') }}</span>
            </div>
        </div>
    </div>



    <!-- Appointment Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h1 class="text-3xl font-bold mb-8 text-center">{{ __('Book an Appointment') }}</h1>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="md:col-span-1">
                        <div class="appointment-card text-white rounded-lg p-6 h-full">
                            <h2 class="text-xl font-bold mb-4">{{ __('Why Book an Appointment?') }}</h2>

                            <ul class="space-y-4">
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-primary-400 mt-1 ml-2"></i>
                                    <span>{{ app()->getLocale() == 'ar' ? 'خدمة شخصية مخصصة من خبراء المجوهرات لدينا' : 'Personalized service from our jewelry experts' }}</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-primary-400 mt-1 ml-2"></i>
                                    <span>{{ app()->getLocale() == 'ar' ? 'تجنب الانتظار وازدحام المتجر' : 'Avoid waiting and store crowds' }}</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-primary-400 mt-1 ml-2"></i>
                                    <span>{{ app()->getLocale() == 'ar' ? 'استشارة خاصة لاختيار المجوهرات المناسبة' : 'Private consultation to choose the right jewelry' }}</span>
                                </li>
                                <li class="flex items-start">
                                    <i class="fas fa-check-circle text-primary-400 mt-1 ml-2"></i>
                                    <span>{{ app()->getLocale() == 'ar' ? 'عرض حصري للمنتجات الجديدة والمميزة' : 'Exclusive preview of new and featured products' }}</span>
                                </li>
                            </ul>

                            <div class="mt-8">
                                <h3 class="font-bold mb-2">{{ __('Contact Us') }}</h3>
                                <p class="mb-4">
                                    {{ app()->getLocale() == 'ar' ? 'لديك أسئلة؟ اتصل بنا مباشرة:' : 'Have questions? Contact us directly:' }}
                                </p>

                                <div class="flex items-center mb-2">
                                    <i class="fas fa-phone-alt text-primary-400 ml-2"></i>
                                    <a href="tel:{{ $contactInfo['phone'] }}"
                                        class="hover:text-primary-300 transition duration-200">
                                        {{ $contactInfo['phone'] }}
                                    </a>
                                </div>

                                <div class="flex items-center mb-2">
                                    <i class="fas fa-envelope text-primary-400 ml-2"></i>
                                    <a href="mailto:{{ $contactInfo['email'] }}"
                                        class="hover:text-primary-300 transition duration-200">
                                        {{ $contactInfo['email'] }}
                                    </a>
                                </div>

                                @if ($contactInfo['whatsapp'])
                                    <div class="flex items-center mb-2">
                                        <i class="fab fa-whatsapp text-primary-400 ml-2"></i>
                                        <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $contactInfo['whatsapp']) }}"
                                            target="_blank" class="hover:text-primary-300 transition duration-200">
                                            {{ $contactInfo['whatsapp'] }}
                                        </a>
                                    </div>
                                @endif

                                @if ($contactInfo['address'])
                                    <div class="flex items-start mt-3">
                                        <i class="fas fa-map-marker-alt text-primary-400 ml-2 mt-1"></i>
                                        <span class="text-sm">{{ $contactInfo['address'] }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="md:col-span-2">
                        <div class="bg-white rounded-lg shadow-sm p-6">
                            <h2 class="text-xl font-bold mb-6">{{ __('Appointment Form') }}</h2>

                            <form action="{{ route('appointment.store') }}" method="POST" id="appointment-form">
                                @csrf

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <label for="name"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Name') }} <span
                                                class="text-red-500">*</span></label>
                                        <input type="text" id="name" name="name" required
                                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>

                                    <div>
                                        <label for="email"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Email') }} <span
                                                class="text-red-500">*</span></label>
                                        <input type="email" id="email" name="email" required
                                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>

                                    <div>
                                        <label for="phone"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Phone') }} <span
                                                class="text-red-500">*</span></label>
                                        <input type="tel" id="phone" name="phone" required
                                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>

                                    <div>
                                        <label for="store"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Select Store') }}
                                            <span class="text-red-500">*</span></label>
                                        <select id="store" name="store" required
                                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                            <option value="">
                                                {{ app()->getLocale() == 'ar' ? '-- اختر الفرع --' : '-- Select Store --' }}
                                            </option>
                                            <option value="cairo"
                                                {{ request()->get('store') == 'cairo' ? 'selected' : '' }}>
                                                {{ app()->getLocale() == 'ar' ? 'فرع القاهرة - وسط البلد' : 'Cairo Downtown Branch' }}
                                            </option>
                                            <option value="alexandria"
                                                {{ request()->get('store') == 'alexandria' ? 'selected' : '' }}>
                                                {{ app()->getLocale() == 'ar' ? 'فرع الإسكندرية - سان ستيفانو' : 'Alexandria San Stefano Branch' }}
                                            </option>
                                            <option value="citystars"
                                                {{ request()->get('store') == 'citystars' ? 'selected' : '' }}>
                                                {{ app()->getLocale() == 'ar' ? 'فرع القاهرة - سيتي ستارز' : 'Cairo City Stars Branch' }}
                                            </option>
                                            <option value="maadi"
                                                {{ request()->get('store') == 'maadi' ? 'selected' : '' }}>
                                                {{ app()->getLocale() == 'ar' ? 'فرع القاهرة - المعادي' : 'Cairo Maadi Branch' }}
                                            </option>
                                            <option value="mallofegypt"
                                                {{ request()->get('store') == 'mallofegypt' ? 'selected' : '' }}>
                                                {{ app()->getLocale() == 'ar' ? 'فرع القاهرة - مول مصر' : 'Cairo Mall of Egypt Branch' }}
                                            </option>
                                            <option value="hurghada"
                                                {{ request()->get('store') == 'hurghada' ? 'selected' : '' }}>
                                                {{ app()->getLocale() == 'ar' ? 'فرع الغردقة' : 'Hurghada Branch' }}
                                            </option>
                                        </select>
                                    </div>

                                    <div>
                                        <label for="date"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Select Date') }}
                                            <span class="text-red-500">*</span></label>
                                        <input type="text" id="date" name="date" required
                                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                                            placeholder="{{ app()->getLocale() == 'ar' ? 'اختر التاريخ' : 'Select Date' }}">
                                    </div>

                                    <div>
                                        <label for="time"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Select Time') }}
                                            <span class="text-red-500">*</span></label>
                                        <input type="text" id="time" name="time" required
                                            class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                                            placeholder="{{ app()->getLocale() == 'ar' ? 'اختر الوقت' : 'Select Time' }}">
                                    </div>
                                </div>

                                <div class="mb-6">
                                    <label for="purpose"
                                        class="block text-gray-700 text-sm font-medium mb-2">{{ __('Purpose of Visit') }}
                                        <span class="text-red-500">*</span></label>
                                    <select id="purpose" name="purpose" required
                                        class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                        <option value="">
                                            {{ app()->getLocale() == 'ar' ? '-- اختر الغرض --' : '-- Select Purpose --' }}
                                        </option>
                                        <option value="browse">
                                            {{ app()->getLocale() == 'ar' ? 'تصفح المجوهرات' : 'Browse Jewelry' }}</option>
                                        <option value="engagement">
                                            {{ app()->getLocale() == 'ar' ? 'خواتم الخطوبة والزفاف' : 'Engagement & Wedding Rings' }}
                                        </option>
                                        <option value="custom">
                                            {{ app()->getLocale() == 'ar' ? 'تصميم مجوهرات مخصصة' : 'Custom Jewelry Design' }}
                                        </option>
                                        <option value="repair">
                                            {{ app()->getLocale() == 'ar' ? 'إصلاح المجوهرات' : 'Jewelry Repair' }}
                                        </option>
                                        <option value="appraisal">
                                            {{ app()->getLocale() == 'ar' ? 'تقييم المجوهرات' : 'Jewelry Appraisal' }}
                                        </option>
                                        <option value="other">{{ app()->getLocale() == 'ar' ? 'أخرى' : 'Other' }}
                                        </option>
                                    </select>
                                </div>

                                <div class="mb-6">
                                    <label for="notes"
                                        class="block text-gray-700 text-sm font-medium mb-2">{{ __('Additional Notes') }}</label>
                                    <textarea id="notes" name="notes" rows="3"
                                        class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                                </div>

                                <div class="flex justify-end">
                                    <button type="submit"
                                        class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                        {{ __('Book Appointment') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize date picker
            flatpickr("#date", {
                minDate: "today",
                maxDate: new Date().fp_incr(30), // 30 days from now
                disable: [
                    function(date) {
                        // Disable Fridays before 2 PM
                        return date.getDay() === 5 && date.getHours() < 14;
                    }
                ],
                locale: {
                    firstDayOfWeek: 6 // Start with Saturday
                }
            });

            // Initialize time picker
            flatpickr("#time", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                minTime: "10:00",
                maxTime: "21:00",
                minuteIncrement: 30,
                // Disable times based on selected date (Friday)
                onChange: function(selectedDates, dateStr, instance) {
                    const dateInput = document.getElementById('date');
                    if (dateInput.value) {
                        const selectedDate = new Date(dateInput.value);
                        if (selectedDate.getDay() === 5) { // Friday
                            instance.set('minTime', "14:00");
                        } else {
                            instance.set('minTime', "10:00");
                        }
                    }
                }
            });

            // Update time picker when date changes
            document.getElementById('date').addEventListener('change', function() {
                const timeInput = document.getElementById('time');
                const timePicker = timeInput._flatpickr;

                if (this.value) {
                    const selectedDate = new Date(this.value);
                    if (selectedDate.getDay() === 5) { // Friday
                        timePicker.set('minTime', "14:00");

                        // If current selected time is before 14:00, reset it
                        if (timeInput.value && timeInput.value < "14:00") {
                            timePicker.setDate("14:00");
                        }
                    } else {
                        timePicker.set('minTime', "10:00");
                    }
                }
            });
        });
    </script>
@endsection
