@extends('layouts.frontend')

@section('title', __('Appointment Confirmation'))

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('appointment') }}" class="text-gray-600 hover:text-primary-500">{{ __('Book an Appointment') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Appointment Confirmation') }}</span>
            </div>
        </div>
    </div>

    <!-- Confirmation Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto text-center">
                <div class="bg-green-100 rounded-full h-24 w-24 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check text-green-500 text-4xl"></i>
                </div>
                
                <h1 class="text-3xl font-bold text-gray-800 mb-4">{{ __('Appointment Confirmation') }}</h1>
                
                <p class="text-gray-600 mb-8">
                    {{ __('Your appointment has been scheduled') }}. {{ __('We look forward to seeing you on') }} 
                    <span class="font-medium">{{ $appointment->appointment_date->format('l, F j, Y') }}</span> {{ __('at') }} 
                    <span class="font-medium">{{ date('h:i A', strtotime($appointment->appointment_time)) }}</span>.
                </p>
                
                <div class="bg-white rounded-lg shadow-sm p-6 mb-8">
                    <h2 class="text-xl font-bold mb-4">{{ __('Appointment Details') }}</h2>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Confirmation Code') }}:</span>
                        <span class="font-medium">{{ $appointment->confirmation_code }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Name') }}:</span>
                        <span class="font-medium">{{ $appointment->name }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Store') }}:</span>
                        <span class="font-medium">{{ $appointment->store_name }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Date') }}:</span>
                        <span class="font-medium">{{ $appointment->appointment_date->format('Y-m-d') }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2 border-b border-gray-100">
                        <span class="text-gray-600">{{ __('Time') }}:</span>
                        <span class="font-medium">{{ date('h:i A', strtotime($appointment->appointment_time)) }}</span>
                    </div>
                    
                    <div class="flex justify-between py-2">
                        <span class="text-gray-600">{{ __('Purpose') }}:</span>
                        <span class="font-medium">{{ $appointment->purpose_name }}</span>
                    </div>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-4 mb-8">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-yellow-500 mt-1 ml-2"></i>
                        <div>
                            <p class="text-gray-700 text-sm">
                                {{ app()->getLocale() == 'ar' ? 'تم إرسال تفاصيل الموعد إلى بريدك الإلكتروني. يرجى الاحتفاظ برمز التأكيد الخاص بك في حالة الحاجة إلى تعديل أو إلغاء موعدك.' : 'Appointment details have been sent to your email. Please keep your confirmation code in case you need to modify or cancel your appointment.' }}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="flex flex-col sm:flex-row justify-center gap-4">
                    <a href="{{ route('home') }}" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                        {{ __('Return to Home') }}
                    </a>
                    
                    <button type="button" id="cancel-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-6 py-3 rounded-full font-medium transition duration-300">
                        {{ __('Cancel Appointment') }}
                    </button>
                </div>
                
                <!-- Cancel Appointment Form (Hidden) -->
                <div id="cancel-form" class="mt-8 hidden">
                    <form action="{{ route('appointment.cancel', $appointment->id) }}" method="POST" class="bg-white rounded-lg shadow-sm p-6">
                        @csrf
                        <h3 class="text-lg font-bold mb-4">{{ app()->getLocale() == 'ar' ? 'تأكيد إلغاء الموعد' : 'Confirm Appointment Cancellation' }}</h3>
                        
                        <p class="text-gray-600 mb-4">
                            {{ app()->getLocale() == 'ar' ? 'يرجى إدخال رمز التأكيد الخاص بك لإلغاء هذا الموعد:' : 'Please enter your confirmation code to cancel this appointment:' }}
                        </p>
                        
                        <div class="mb-4">
                            <input type="text" name="code" required class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500" placeholder="{{ app()->getLocale() == 'ar' ? 'رمز التأكيد' : 'Confirmation Code' }}">
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="button" id="cancel-back-btn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-full font-medium transition duration-300 ml-2">
                                {{ app()->getLocale() == 'ar' ? 'رجوع' : 'Back' }}
                            </button>
                            <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-full font-medium transition duration-300 ml-2">
                                {{ app()->getLocale() == 'ar' ? 'تأكيد الإلغاء' : 'Confirm Cancellation' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const cancelBtn = document.getElementById('cancel-btn');
        const cancelForm = document.getElementById('cancel-form');
        const cancelBackBtn = document.getElementById('cancel-back-btn');
        
        cancelBtn.addEventListener('click', function() {
            cancelForm.classList.remove('hidden');
            cancelBtn.classList.add('hidden');
        });
        
        cancelBackBtn.addEventListener('click', function() {
            cancelForm.classList.add('hidden');
            cancelBtn.classList.remove('hidden');
        });
    });
</script>
@endsection
