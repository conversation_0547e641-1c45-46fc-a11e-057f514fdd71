@extends('layouts.frontend')

@section('title', $page->translated_title)

@section('meta_description', $page->translated_meta_description)
@section('meta_keywords', $page->meta_keywords)

@push('styles')
<style>
    .blog-card {
        transition: all 0.3s ease;
        transform: translateY(0);
    }
    .blog-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    .featured-post {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    .skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
    }
    @keyframes skeleton-loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    .fade-in {
        animation: fadeIn 0.6s ease-in;
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .lazy-image {
        opacity: 0;
        transition: opacity 0.3s;
    }
    .lazy-image.loaded {
        opacity: 1;
    }
</style>
@endpush

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-50 py-4">
        <div class="container mx-auto px-4">
            <nav class="flex items-center text-sm" aria-label="Breadcrumb">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500 transition-colors">
                    <i class="fas fa-home {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                    {{ __('messages.home') }}
                </a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800 font-medium">{{ __('messages.Blog') }}</span>
            </nav>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="py-16 bg-gradient-to-br from-gray-50 to-white">
        <div class="container mx-auto px-4">
            <div class="text-center max-w-3xl mx-auto">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">{{ $page->translated_title }}</h1>
                <p class="text-xl text-gray-600 leading-relaxed">{!! $page->translated_content !!}</p>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="flex flex-col lg:flex-row gap-12">
                <!-- Main Content Area -->
                <div class="lg:w-2/3">
                    @if ($featuredPosts->count() > 0)
                        <!-- Featured Posts Section -->
                        <div class="mb-16">
                            <div class="flex items-center justify-between mb-8">
                                <h2 class="text-3xl font-bold text-gray-900">{{ __('messages.Featured Posts') }}</h2>
                                <div class="w-24 h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded"></div>
                            </div>
                            
                            <div class="grid gap-8">
                                <!-- Main Featured Post -->
                                <div class="featured-post rounded-2xl overflow-hidden shadow-2xl fade-in">
                                    <div class="md:flex">
                                        <div class="md:w-1/2 relative overflow-hidden">
                                            <img src="{{ asset('storage/' . $featuredPosts->first()->featured_image) }}"
                                                alt="{{ $featuredPosts->first()->title }}" 
                                                class="lazy-image w-full h-80 md:h-full object-cover transition-transform duration-700 hover:scale-110"
                                                loading="lazy">
                                            <div class="absolute top-4 {{ app()->getLocale() == 'ar' ? 'right-4' : 'left-4' }}">
                                                <span class="bg-white bg-opacity-90 text-primary-600 px-3 py-1 rounded-full text-sm font-semibold">
                                                    {{ __('messages.Featured') }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="md:w-1/2 p-8 text-white relative">
                                            <div class="h-full flex flex-col justify-center">
                                                <div class="flex items-center text-sm text-white text-opacity-80 mb-4">
                                                    <i class="fas fa-calendar-alt {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                                    <span>{{ $featuredPosts->first()->published_at->locale(app()->getLocale())->format('d F Y') }}</span>
                                                    <span class="mx-3">•</span>
                                                    <i class="fas fa-folder {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                                    <span>{{ $featuredPosts->first()->category->{'name_' . app()->getLocale()} ?? '' }}</span>
                                                    <span class="mx-3">•</span>
                                                    <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                                    <span>{{ number_format($featuredPosts->first()->views) }}</span>
                                                </div>
                                                <h2 class="text-3xl font-bold mb-4 leading-tight">
                                                    {{ app()->getLocale() == 'ar' ? $featuredPosts->first()->title : $featuredPosts->first()->translations['en']['title'] ?? $featuredPosts->first()->title }}
                                                </h2>
                                                <p class="text-white text-opacity-90 mb-6 text-lg leading-relaxed">
                                                    {{ Str::limit(app()->getLocale() == 'ar' ? $featuredPosts->first()->excerpt : $featuredPosts->first()->translations['en']['excerpt'] ?? $featuredPosts->first()->excerpt, 120) }}
                                                </p>
                                                <a href="{{ route('blog.show', $featuredPosts->first()->slug) }}"
                                                    class="inline-flex items-center bg-white text-gray-900 px-6 py-3 rounded-full font-semibold transition-all duration-300 hover:shadow-lg hover:transform hover:scale-105">
                                                    {{ __('messages.Read More') }}
                                                    <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} {{ app()->getLocale() == 'ar' ? 'mr-2' : 'ml-2' }}"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                @if($featuredPosts->count() > 1)
                                    <!-- Additional Featured Posts -->
                                    <div class="grid md:grid-cols-2 gap-6">
                                        @foreach($featuredPosts->skip(1) as $post)
                                            <div class="blog-card bg-white rounded-xl shadow-lg overflow-hidden">
                                                <div class="relative">
                                                    <img src="{{ asset('storage/' . $post->featured_image) }}" 
                                                        alt="{{ $post->title }}"
                                                        class="lazy-image w-full h-48 object-cover"
                                                        loading="lazy">
                                                    <div class="absolute top-3 {{ app()->getLocale() == 'ar' ? 'right-3' : 'left-3' }}">
                                                        <span class="bg-primary-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                                            {{ __('messages.Featured') }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="p-6">
                                                    <div class="flex items-center text-sm text-gray-500 mb-3">
                                                        <span>{{ $post->published_at->locale(app()->getLocale())->format('d M Y') }}</span>
                                                        <span class="mx-2">•</span>
                                                        <span>{{ $post->category->{'name_' . app()->getLocale()} ?? '' }}</span>
                                                        <span class="mx-2">•</span>
                                                        <span>{{ number_format($post->views) }} {{ __('messages.views') }}</span>
                                                    </div>
                                                    <h3 class="text-lg font-bold mb-3 line-clamp-2">
                                                        {{ app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title }}
                                                    </h3>
                                                    <p class="text-gray-600 mb-4 line-clamp-2">
                                                        {{ Str::limit(app()->getLocale() == 'ar' ? $post->excerpt : $post->translations['en']['excerpt'] ?? $post->excerpt, 80) }}
                                                    </p>
                                                    <a href="{{ route('blog.show', $post->slug) }}"
                                                        class="inline-flex items-center text-primary-500 hover:text-primary-600 font-semibold transition-colors">
                                                        {{ __('messages.Read More') }}
                                                        <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} {{ app()->getLocale() == 'ar' ? 'mr-2' : 'ml-2' }} text-sm"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endif

                    <!-- All Posts Section -->
                    <div class="mb-12">
                        <div class="flex items-center justify-between mb-8">
                            <h2 class="text-3xl font-bold text-gray-900">{{ __('messages.All Posts') }}</h2>
                            <div class="text-sm text-gray-600">
                                {{ __('messages.Showing') }} {{ $posts->firstItem() ?? 0 }} - {{ $posts->lastItem() ?? 0 }} {{ __('messages.of') }} {{ $posts->total() }} {{ __('messages.posts') }}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            @forelse($posts as $post)
                                <article class="blog-card bg-white rounded-xl shadow-lg overflow-hidden fade-in">
                                    <div class="relative">
                                        <img src="{{ asset('storage/' . $post->featured_image) }}" 
                                            alt="{{ $post->title }}"
                                            class="lazy-image w-full h-56 object-cover"
                                            loading="lazy">
                                        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                                        @if($post->is_featured)
                                            <div class="absolute top-3 {{ app()->getLocale() == 'ar' ? 'right-3' : 'left-3' }}">
                                                <span class="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                                                    {{ __('messages.Featured') }}
                                                </span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="p-6">
                                        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                            <div class="flex items-center">
                                                <i class="fas fa-calendar-alt {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                                <span>{{ $post->published_at->locale(app()->getLocale())->format('d F Y') }}</span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                                <span>{{ number_format($post->views) }}</span>
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-center mb-3">
                                            <span class="bg-primary-100 text-primary-600 px-3 py-1 rounded-full text-xs font-semibold">
                                                {{ $post->category->{'name_' . app()->getLocale()} ?? '' }}
                                            </span>
                                        </div>

                                        <h3 class="text-xl font-bold mb-3 line-clamp-2 hover:text-primary-600 transition-colors">
                                            <a href="{{ route('blog.show', $post->slug) }}">
                                                {{ app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title }}
                                            </a>
                                        </h3>
                                        
                                        <p class="text-gray-600 mb-4 line-clamp-3">
                                            {{ app()->getLocale() == 'ar' ? $post->excerpt : $post->translations['en']['excerpt'] ?? $post->excerpt }}
                                        </p>
                                        
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-8 h-8 bg-gray-300 rounded-full {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }}"></div>
                                                <div>
                                                    <p class="text-sm font-semibold text-gray-900">{{ $post->author->name ?? __('Admin') }}</p>
                                                    <p class="text-xs text-gray-500">{{ __('messages.Author') }}</p>
                                                </div>
                                            </div>
                                            <a href="{{ route('blog.show', $post->slug) }}"
                                                class="inline-flex items-center text-primary-500 hover:text-primary-600 font-semibold transition-colors">
                                                {{ __('messages.Read More') }}
                                                <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} {{ app()->getLocale() == 'ar' ? 'mr-2' : 'ml-2' }} text-sm"></i>
                                            </a>
                                        </div>
                                    </div>
                                </article>
                            @empty
                                <div class="col-span-2 text-center py-16">
                                    <div class="max-w-md mx-auto">
                                        <i class="fas fa-newspaper text-6xl text-gray-300 mb-4"></i>
                                        <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ __('messages.No posts found') }}</h3>
                                        <p class="text-gray-500">{{ __('No blog posts found. Please check back later.') }}</p>
                                    </div>
                                </div>
                            @endforelse
                        </div>

                        <!-- Pagination -->
                        @if($posts->hasPages())
                            <div class="mt-12">
                                <div class="flex justify-center">
                                    {{ $posts->links('pagination::tailwind') }}
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Sidebar -->
                <aside class="lg:w-1/3">
                    <div class="space-y-8">
                        <!-- Search Box -->
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-4 flex items-center">
                                <i class="fas fa-search {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-primary-500"></i>
                                {{ __('Search Posts') }}
                            </h3>
                            <form method="GET" action="{{ route('blog') }}" class="space-y-3">
                                <div class="relative">
                                    <input type="text" name="search" value="{{ request('search') }}" 
                                        placeholder="{{ __('Search posts...') }}"
                                        class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                                    <button type="submit" class="absolute {{ app()->getLocale() == 'ar' ? 'left-3' : 'right-3' }} top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-500">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Latest Posts -->
                        @if($latestPosts->count() > 0)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-6 flex items-center">
                                <i class="fas fa-clock {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-primary-500"></i>
                                {{ __('Latest Posts') }}
                            </h3>
                            <div class="space-y-4">
                                @foreach($latestPosts as $post)
                                <div class="flex items-start space-x-4 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }} pb-4 border-b border-gray-100 last:border-b-0 last:pb-0">
                                    <img src="{{ asset('storage/' . $post->featured_image) }}" 
                                        alt="{{ $post->title }}"
                                        class="w-16 h-16 rounded-lg object-cover flex-shrink-0">
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-semibold text-sm line-clamp-2 hover:text-primary-600 transition-colors">
                                            <a href="{{ route('blog.show', $post->slug) }}">
                                                {{ app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title }}
                                            </a>
                                        </h4>
                                        <div class="flex items-center text-xs text-gray-500 mt-2">
                                            <span>{{ $post->published_at->locale(app()->getLocale())->format('d M Y') }}</span>
                                            <span class="mx-2">•</span>
                                            <span>{{ number_format($post->views) }} {{ __('messages.views') }}</span>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Popular Posts -->
                        @if($popularPosts->count() > 0)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-6 flex items-center">
                                <i class="fas fa-fire {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-red-500"></i>
                                {{ __('Popular Posts') }}
                            </h3>
                            <div class="space-y-4">
                                @foreach($popularPosts as $index => $post)
                                <div class="flex items-start space-x-4 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }} pb-4 border-b border-gray-100 last:border-b-0 last:pb-0">
                                    <div class="flex-shrink-0 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                                        {{ $index + 1 }}
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-semibold text-sm line-clamp-2 hover:text-primary-600 transition-colors">
                                            <a href="{{ route('blog.show', $post->slug) }}">
                                                {{ app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title }}
                                            </a>
                                        </h4>
                                        <div class="flex items-center text-xs text-gray-500 mt-2">
                                            <i class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ml-1' : 'mr-1' }}"></i>
                                            <span>{{ number_format($post->views) }} {{ __('messages.views') }}</span>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Categories -->
                        @if($categories->count() > 0)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-6 flex items-center">
                                <i class="fas fa-folder {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-primary-500"></i>
                                {{ __('Categories') }}
                            </h3>
                            <div class="space-y-3">
                                @foreach($categories as $category)
                                <a href="{{ route('blog', ['category' => $category->id]) }}"
                                    class="flex justify-between items-center p-3 rounded-lg hover:bg-gray-50 transition-colors group">
                                    <span class="text-gray-700 group-hover:text-primary-600 font-medium">
                                        {{ $category->{'name_' . app()->getLocale()} }}
                                    </span>
                                    <span class="bg-gray-100 group-hover:bg-primary-100 text-gray-600 group-hover:text-primary-600 text-xs px-3 py-1 rounded-full font-semibold">
                                        {{ $category->posts_count }}
                                    </span>
                                </a>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Archive -->
                        @if($archives->count() > 0)
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-6 flex items-center">
                                <i class="fas fa-archive {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-primary-500"></i>
                                {{ __('Archive') }}
                            </h3>
                            <div class="space-y-2">
                                @foreach($archives as $archive)
                                <a href="{{ route('blog', ['year' => $archive->year, 'month' => $archive->month]) }}"
                                    class="flex justify-between items-center p-2 rounded-lg hover:bg-gray-50 transition-colors group">
                                    <span class="text-gray-700 group-hover:text-primary-600">
                                        {{ \Carbon\Carbon::createFromDate($archive->year, $archive->month)->locale(app()->getLocale())->format('F Y') }}
                                    </span>
                                    <span class="text-gray-500 text-sm">
                                        ({{ $archive->count }})
                                    </span>
                                </a>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Popular Tags -->
                        <div class="bg-white rounded-xl shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-6 flex items-center">
                                <i class="fas fa-tags {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-primary-500"></i>
                                {{ __('Popular Tags') }}
                            </h3>
                            <div class="flex flex-wrap gap-2">
                                @php
                                $tags = [
                                    ['ar' => 'ذهب', 'en' => 'Gold'],
                                    ['ar' => 'فضة', 'en' => 'Silver'],
                                    ['ar' => 'ألماس', 'en' => 'Diamond'],
                                    ['ar' => 'خواتم', 'en' => 'Rings'],
                                    ['ar' => 'أساور', 'en' => 'Bracelets'],
                                    ['ar' => 'قلائد', 'en' => 'Necklaces'],
                                    ['ar' => 'أقراط', 'en' => 'Earrings'],
                                    ['ar' => 'تنظيف', 'en' => 'Cleaning'],
                                    ['ar' => 'هدايا', 'en' => 'Gifts'],
                                    ['ar' => 'زفاف', 'en' => 'Wedding'],
                                    ['ar' => 'خطوبة', 'en' => 'Engagement'],
                                    ['ar' => 'تصميم', 'en' => 'Design'],
                                    ['ar' => 'حرفية', 'en' => 'Craftsmanship'],
                                    ['ar' => 'أحجار كريمة', 'en' => 'Gemstones']
                                ];
                                @endphp
                                @foreach($tags as $tag)
                                <a href="{{ route('blog', ['tag' => $tag['en']]) }}"
                                    class="bg-gray-100 hover:bg-primary-50 hover:text-primary-600 text-gray-700 px-3 py-2 rounded-full text-sm transition-colors font-medium">
                                    {{ $tag[app()->getLocale()] }}
                                </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </section>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Lazy Loading Images
    const images = document.querySelectorAll('.lazy-image');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.classList.add('loaded');
                observer.unobserve(img);
            }
        });
    });

    images.forEach(img => {
        imageObserver.observe(img);
    });

    // Smooth animations for cards
    const cards = document.querySelectorAll('.blog-card');
    const cardObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = Math.random() * 0.3 + 's';
                entry.target.classList.add('fade-in');
            }
        });
    }, {
        threshold: 0.1
    });

    cards.forEach(card => {
        cardObserver.observe(card);
    });
});
</script>
@endpush
