@extends('layouts.frontend')

@section('title', app()->getLocale() == 'ar' ? $siteSettings->about_hero_title_ar ?? 'من نحن' :
    $siteSettings->about_hero_title_en ?? 'About Us')

@section('meta_description',
    app()->getLocale() == 'ar'
    ? $siteSettings->about_hero_subtitle_ar ??
    'تعرف على مجوهرات مكة
    جولد'
    : $siteSettings->about_hero_subtitle_en ?? 'Learn about Makkah Gold Jewelry')
@section('meta_keywords', 'من نحن، مجوهرات، ذهب، مكة جولد، تاريخنا، رؤيتنا')

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('About Us') }}</span>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="relative">
        <img src="{{ $siteSettings->about_hero_image ? asset('storage/' . $siteSettings->about_hero_image) : asset('images/about/hero.jpg') }}"
            alt="{{ app()->getLocale() == 'ar' ? $siteSettings->about_hero_title_ar ?? 'من نحن' : $siteSettings->about_hero_title_en ?? 'About Us' }}"
            class="w-full h-96 object-cover">
        <div class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div class="text-center text-white px-4">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    {{ app()->getLocale() == 'ar'
                        ? $siteSettings->about_hero_title_ar ?? 'مجوهرات مكة جولد'
                        : $siteSettings->about_hero_title_en ?? 'Makkah Gold Jewelry' }}
                </h1>
                <p class="text-xl md:text-2xl">
                    {{ app()->getLocale() == 'ar'
                        ? $siteSettings->about_hero_subtitle_ar ?? 'رحلة من التميز والجودة في عالم المجوهرات'
                        : $siteSettings->about_hero_subtitle_en ?? 'A journey of excellence and quality in the world of jewelry' }}
                </p>
            </div>
        </div>
    </section>

    <!-- Our Story Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-center mb-12">
                    {{ app()->getLocale() == 'ar' ? 'قصتنا' : 'Our Story' }}
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                    <div class="prose prose-lg max-w-none">
                        @if (app()->getLocale() == 'ar')
                            {!! $siteSettings->company_mission_ar ??
                                'نحن شركة رائدة في مجال المجوهرات والذهب، نسعى لتقديم أجود أنواع المجوهرات بأعلى معايير الجودة والحرفية.' !!}
                        @else
                            {!! $siteSettings->company_mission_en ??
                                'We are a leading company in the field of jewelry and gold, striving to provide the finest jewelry with the highest standards of quality and craftsmanship.' !!}
                        @endif

                        @if ($siteSettings->company_founded_year)
                            <p class="mt-4 text-primary-600 font-semibold">
                                {{ app()->getLocale() == 'ar' ? 'تأسست عام ' . $siteSettings->company_founded_year : 'Founded in ' . $siteSettings->company_founded_year }}
                            </p>
                        @endif
                    </div>
                    <div>
                        <img src="{{ $siteSettings->about_story_image ? asset('storage/' . $siteSettings->about_story_image) : asset('images/about/story.jpg') }}"
                            alt="{{ app()->getLocale() == 'ar' ? 'قصتنا' : 'Our Story' }}"
                            class="rounded-lg shadow-md w-full object-cover h-80">
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
