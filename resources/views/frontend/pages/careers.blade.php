@extends('layouts.frontend')

@section('title', $page->translated_title)

@section('meta_description', $page->translated_meta_description)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Careers') }}</span>
            </div>
        </div>
    </div>

    <!-- Careers Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold text-center mb-12">{{ $page->translated_title }}</h1>

            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-sm p-8 mb-12">
                    {!! $page->translated_content !!}
                </div>

                <h2 class="text-2xl font-bold mb-6">{{ __('Current Openings') }}</h2>

                @if ($jobs->count() > 0)
                    <div class="space-y-6 mb-12">
                        @foreach ($jobs as $job)
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                <div class="p-6 border-b border-gray-200">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <h3 class="text-xl font-bold mb-2">{{ $job->translated_title }}</h3>
                                            <div class="flex flex-wrap gap-2 mb-2">
                                                @if ($job->is_featured)
                                                    <span
                                                        class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">{{ __('Featured') }}</span>
                                                @endif
                                                <span
                                                    class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded">{{ $job->translated_type }}</span>
                                                <span
                                                    class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ $job->translated_location }}</span>
                                                @if ($job->department)
                                                    <span
                                                        class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ $job->translated_department }}</span>
                                                @endif
                                            </div>
                                            @if ($job->expires_at)
                                                <p class="text-sm text-gray-500">
                                                    {{ __('Application Deadline') }}:
                                                    {{ $job->expires_at->locale(app()->getLocale())->format('d F Y') }}
                                                </p>
                                            @endif
                                        </div>
                                        <button
                                            class="job-toggle bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-sm focus:outline-none">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="job-details hidden p-6 bg-gray-50">
                                    <div class="mb-4">
                                        <h4 class="font-bold mb-2">{{ __('Job Description') }}</h4>
                                        <div class="text-gray-700 mb-4 prose prose-sm max-w-none">
                                            {!! Str::limit(strip_tags($job->translated_description), 300) !!}
                                        </div>
                                    </div>

                                    @if ($job->salary_min || $job->salary_max)
                                        <div class="mb-4">
                                            <h4 class="font-bold mb-2">{{ __('Salary Range') }}</h4>
                                            <p class="text-gray-700">
                                                @if ($job->salary_min && $job->salary_max)
                                                    {{ number_format($job->salary_min) }} -
                                                    {{ number_format($job->salary_max) }} {{ __('EGP') }}
                                                @elseif($job->salary_min)
                                                    {{ __('From') }} {{ number_format($job->salary_min) }}
                                                    {{ __('EGP') }}
                                                @elseif($job->salary_max)
                                                    {{ __('Up to') }} {{ number_format($job->salary_max) }}
                                                    {{ __('EGP') }}
                                                @endif
                                            </p>
                                        </div>
                                    @endif

                                    <div class="flex justify-end">
                                        <a href="{{ route('careers.show', $job->slug) }}"
                                            class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium transition duration-300">
                                            {{ __('View Details & Apply') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    @if ($jobs->hasPages())
                        <div class="mb-12">
                            {{ $jobs->links() }}
                        </div>
                    @endif
                @else
                    <div class="bg-white rounded-lg shadow-sm p-8 text-center mb-12">
                        <div class="text-gray-500 mb-4">
                            <i class="fas fa-briefcase text-4xl"></i>
                        </div>
                        <h3 class="text-xl font-bold mb-2">{{ __('No Current Openings') }}</h3>
                        <p class="text-gray-600">
                            {{ __('We don\'t have any job openings at the moment. Please check back later or contact us directly.') }}
                        </p>
                    </div>
                @endif



                <h2 class="text-2xl font-bold mb-6">{{ __('Why Work With Us') }}</h2>

                <div class="bg-white rounded-lg shadow-sm p-8 mb-12">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-chart-line text-primary-500"></i>
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Career Growth') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'نقدم فرصًا للتطور المهني والترقيات الداخلية وبرامج التدريب المستمر.' : 'We offer opportunities for professional development, internal promotions, and continuous training programs.' }}
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-hand-holding-usd text-primary-500"></i>
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Competitive Compensation') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'نقدم رواتب تنافسية ومكافآت أداء وحوافز مبيعات.' : 'We offer competitive salaries, performance bonuses, and sales incentives.' }}
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-heartbeat text-primary-500"></i>
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Health Benefits') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'نقدم تأمينًا صحيًا شاملاً وبرامج رعاية صحية للموظفين وعائلاتهم.' : 'We provide comprehensive health insurance and wellness programs for employees and their families.' }}
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-users text-primary-500"></i>
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Team Environment') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'نحن نعزز بيئة عمل إيجابية وداعمة حيث يتم تقدير كل فرد وإسهاماته.' : 'We foster a positive and supportive work environment where each individual and their contributions are valued.' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <h2 class="text-2xl font-bold mb-6">{{ __('Application Process') }}</h2>

                <div class="bg-white rounded-lg shadow-sm p-8 mb-12">
                    <div class="space-y-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div
                                    class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold">
                                    1
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Online Application') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'قم بتقديم طلبك عبر الإنترنت من خلال النقر على زر "تقدم الآن" للوظيفة التي تهتم بها.' : 'Submit your application online by clicking the "Apply Now" button for the position you are interested in.' }}
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div
                                    class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold">
                                    2
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Initial Screening') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'سيقوم فريق الموارد البشرية لدينا بمراجعة طلبك والتواصل معك إذا كنت مؤهلاً للخطوة التالية.' : 'Our HR team will review your application and contact you if you qualify for the next step.' }}
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div
                                    class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold">
                                    3
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Interview Process') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'قد تتضمن عملية المقابلة مقابلة هاتفية أولية تليها مقابلة شخصية مع مدير التوظيف و/أو أعضاء الفريق.' : 'The interview process may include an initial phone interview followed by an in-person interview with the hiring manager and/or team members.' }}
                                </p>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="flex-shrink-0">
                                <div
                                    class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white font-bold">
                                    4
                                </div>
                            </div>
                            <div class="mr-4">
                                <h3 class="font-bold mb-2">{{ __('Job Offer') }}</h3>
                                <p class="text-gray-600">
                                    {{ app()->getLocale() == 'ar' ? 'إذا تم اختيارك، ستتلقى عرض عمل رسمي يتضمن تفاصيل الراتب والمزايا وتاريخ البدء.' : 'If selected, you will receive a formal job offer including salary details, benefits, and start date.' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-lg p-8 text-center">
                    <h3 class="text-xl font-bold mb-4">{{ __('Have Questions?') }}</h3>
                    <p class="text-gray-700 mb-6">
                        {{ app()->getLocale() == 'ar' ? 'إذا كانت لديك أي أسئلة حول فرص العمل لدينا أو عملية التقديم، يرجى الاتصال بفريق الموارد البشرية لدينا.' : 'If you have any questions about our job opportunities or application process, please contact our HR team.' }}
                    </p>
                    <a href="mailto:<EMAIL>" class="text-primary-500 hover:text-primary-600 font-medium">
                        <EMAIL>
                    </a>
                </div>
            </div>
        </div>
    </section>
@endsection

@section('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Job toggle functionality
            const jobToggles = document.querySelectorAll('.job-toggle');
            jobToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const jobDetails = this.closest('.bg-white').querySelector('.job-details');
                    const icon = this.querySelector('i');

                    if (jobDetails.classList.contains('hidden')) {
                        jobDetails.classList.remove('hidden');
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        jobDetails.classList.add('hidden');
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });
            });
        });
    </script>
@endsection
