@extends('layouts.frontend')

@section('title', $page->translated_title)

@section('meta_description', $page->translated_meta_description)
@section('meta_keywords', $page->meta_keywords)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Frequently Asked Questions') }}</span>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold text-center mb-12">
                {{ $page->translated_title ?? __('Frequently Asked Questions') }}</h1>

            @if ($page->translated_content)
                <div class="max-w-3xl mx-auto text-center mb-12">
                    <p class="text-lg text-gray-600">{!! $page->translated_content !!}</p>
                </div>
            @endif

            <div class="max-w-4xl mx-auto">
                @if ($faqs->count() > 0)
                    <!-- Categories Filter -->
                    @if ($faqsByCategory->count() > 1)
                        <div class="mb-8">
                            <div class="flex flex-wrap justify-center gap-2">
                                <button
                                    class="category-filter active px-4 py-2 rounded-full text-sm font-medium bg-primary-500 text-white"
                                    data-category="all">
                                    {{ __('الكل') }}
                                </button>
                                @foreach ($faqsByCategory as $category => $categoryFaqs)
                                    <button
                                        class="category-filter px-4 py-2 rounded-full text-sm font-medium bg-gray-200 text-gray-700 hover:bg-primary-100"
                                        data-category="{{ $category }}">
                                        {{ $categories[$category] ?? $category }} ({{ $categoryFaqs->count() }})
                                    </button>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <div class="space-y-6">
                        @foreach ($faqs as $faq)
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden faq-item"
                                data-category="{{ $faq->category }}">
                                <button class="faq-toggle w-full flex justify-between items-center p-5 focus:outline-none">
                                    <span class="text-lg font-medium text-gray-800">{{ $faq->translated_question }}</span>
                                    <i class="fas fa-chevron-down text-gray-500 transition-transform duration-300"></i>
                                </button>
                                <div class="faq-content hidden px-5 pb-5">
                                    <div class="text-gray-600">
                                        {!! nl2br(e($faq->translated_answer)) !!}
                                    </div>

                                    <!-- FAQ Actions -->
                                    <div class="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between">
                                        <div class="flex items-center space-x-4 space-x-reverse">
                                            <span
                                                class="text-sm text-gray-500">{{ __('هل كانت هذه الإجابة مفيدة؟') }}</span>
                                            <button onclick="markHelpful({{ $faq->id }})"
                                                class="text-sm text-primary-500 hover:text-primary-600">
                                                <i class="fas fa-thumbs-up mr-1"></i>
                                                {{ __('نعم') }} (<span
                                                    id="helpful-{{ $faq->id }}">{{ $faq->helpful_votes ?? 0 }}</span>)
                                            </button>
                                        </div>
                                        <div class="text-sm text-gray-400">
                                            <i class="fas fa-eye mr-1"></i>
                                            {{ $faq->views }} {{ __('مشاهدة') }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <!-- No FAQs Message -->
                    <div class="text-center py-12">
                        <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-question-circle text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-medium text-gray-900 mb-2">{{ __('لا توجد أسئلة شائعة حالياً') }}</h3>
                        <p class="text-gray-600 mb-6">{{ __('نحن نعمل على إضافة المزيد من الأسئلة الشائعة قريباً') }}</p>
                    </div>
                @endif

                <!-- Contact Section -->
                <div class="mt-12 bg-white rounded-lg shadow-sm p-8 text-center">
                    <h2 class="text-2xl font-bold mb-4">
                        {{ app()->getLocale() == 'ar' ? 'لم تجد إجابة لسؤالك؟' : 'Didn\'t find an answer to your question?' }}
                    </h2>
                    <p class="text-gray-600 mb-6">
                        {{ app()->getLocale() == 'ar' ? 'اتصل بفريق خدمة العملاء لدينا وسنكون سعداء بمساعدتك' : 'Contact our customer service team and we\'ll be happy to help you' }}
                    </p>
                    <a href="{{ route('contact') }}"
                        class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                        {{ app()->getLocale() == 'ar' ? 'اتصل بنا' : 'Contact Us' }}
                    </a>
                </div>
            </div>


        </div>
        </div>
    </section>
@endsection

@section('scripts')
    <script>
        // FAQ Toggle
        document.querySelectorAll('.faq-toggle').forEach(button => {
            button.addEventListener('click', () => {
                const content = button.nextElementSibling;
                const icon = button.querySelector('i');

                // Toggle content
                content.classList.toggle('hidden');

                // Toggle icon
                if (content.classList.contains('hidden')) {
                    icon.classList.remove('transform', 'rotate-180');
                } else {
                    icon.classList.add('transform', 'rotate-180');
                }

                // Close other FAQs
                document.querySelectorAll('.faq-toggle').forEach(otherButton => {
                    if (otherButton !== button) {
                        const otherContent = otherButton.nextElementSibling;
                        const otherIcon = otherButton.querySelector('i');

                        otherContent.classList.add('hidden');
                        otherIcon.classList.remove('transform', 'rotate-180');
                    }
                });
            });
        });

        // Category Filter
        document.querySelectorAll('.category-filter').forEach(button => {
            button.addEventListener('click', () => {
                const category = button.dataset.category;

                // Update active button
                document.querySelectorAll('.category-filter').forEach(btn => {
                    btn.classList.remove('active', 'bg-primary-500', 'text-white');
                    btn.classList.add('bg-gray-200', 'text-gray-700');
                });

                button.classList.add('active', 'bg-primary-500', 'text-white');
                button.classList.remove('bg-gray-200', 'text-gray-700');

                // Filter FAQ items
                document.querySelectorAll('.faq-item').forEach(item => {
                    if (category === 'all' || item.dataset.category === category) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });

        // Mark FAQ as helpful
        function markHelpful(faqId) {
            fetch('{{ route('faq.mark-helpful') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        faq_id: faqId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById(`helpful-${faqId}`).textContent = data.helpful_votes;
                        // إظهار رسالة شكر
                        const button = event.target.closest('button');
                        const originalText = button.innerHTML;
                        button.innerHTML = '<i class="fas fa-check mr-1"></i> {{ __('شكراً لك!') }}';
                        button.disabled = true;

                        setTimeout(() => {
                            button.innerHTML = originalText;
                            button.disabled = false;
                        }, 2000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }
    </script>
@endsection
