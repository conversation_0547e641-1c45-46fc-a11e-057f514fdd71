@extends('layouts.frontend')

@section('title', $page->translated_title)

@section('meta_title', $page->translated_meta_title)
@section('meta_description', $page->translated_meta_description)
@section('meta_keywords', $page->meta_keywords)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ $page->translated_title }}</span>
            </div>
        </div>
    </div>

    <!-- Terms Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto">
                <h1 class="text-3xl font-bold mb-8">{{ $page->translated_title }}</h1>

                @if($page->show_last_updated && $page->last_updated)
                    <div class="mb-6 text-sm text-gray-600">
                        {{ app()->getLocale() == 'ar' ? 'آخر تحديث:' : 'Last Updated:' }} 
                        {{ \Carbon\Carbon::parse($page->last_updated)->format(app()->getLocale() == 'ar' ? 'd/m/Y' : 'M d, Y') }}
                    </div>
                @endif

                <div class="bg-white rounded-lg shadow-sm p-8">
                    <div class="prose prose-lg max-w-none">
                        {!! $page->translated_content !!}
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
