@extends('layouts.frontend')

@section('title', __('Store Locator'))

@section('styles')
    <style>
        #map {
            height: 500px;
            width: 100%;
            border-radius: 0.5rem;
        }

        .store-card {
            transition: all 0.3s ease;
        }

        .store-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .store-card.active {
            border-color: #FFC107;
            background-color: #FFF8E1;
        }
    </style>
@endsection

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Store Locator') }}</span>
            </div>
        </div>
    </div>

    <!-- Stores Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold mb-8 text-center">{{ __('Our Locations') }}</h1>

            <div class="mb-10">
                <div id="map"></div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @forelse($stores as $index => $store)
                    <div class="store-card border border-gray-200 rounded-lg overflow-hidden"
                        data-lat="{{ $store->latitude }}" data-lng="{{ $store->longitude }}"
                        data-id="store{{ $store->id }}">
                        <div class="relative h-48">
                            <img src="{{ $store->image ? asset('storage/' . $store->image) : asset('images/stores/default.jpg') }}"
                                alt="{{ $store->name }}" class="w-full h-full object-cover">
                            @if ($index == 0)
                                <div class="absolute top-0 right-0 bg-primary-500 text-white px-3 py-1 rounded-bl-lg">
                                    {{ __('Main Branch') }}
                                </div>
                            @endif
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-bold mb-2">{{ $store->name }}</h3>
                            <p class="text-gray-600 mb-4">{{ $store->address }}</p>

                            <div class="flex items-center mb-3">
                                <i class="fas fa-phone-alt text-primary-500 ml-3"></i>
                                <span>{{ $store->phone }}</span>
                            </div>

                            <div class="flex items-center mb-3">
                                <i class="fas fa-clock text-primary-500 ml-3"></i>
                                <div>
                                    <p>{{ $store->working_hours }}</p>
                                </div>
                            </div>

                            <div class="flex items-center justify-between mt-6">
                                <a href="https://maps.google.com/?q={{ $store->latitude }},{{ $store->longitude }}"
                                    target="_blank" class="text-primary-500 hover:text-primary-600 font-medium">
                                    <i class="fas fa-directions ml-1"></i> {{ __('Directions') }}
                                </a>
                                {{-- <a href="{{ route('appointment') }}?store={{ $store->id }}"
                                   class="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-medium transition duration-300">
                                    {{ __('Book an Appointment') }}
                                </a> --}}
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-3 text-center py-12">
                        <p class="text-xl text-gray-500">{{ __('No stores found') }}</p>
                    </div>
                @endforelse
            </div>
        </div>
    </section>
@endsection

@section('scripts')
    @php
        $googleMapsApiKey = $siteSettings->google_maps_api_key ?? env('GOOGLE_MAPS_API_KEY');
    @endphp
    @if ($googleMapsApiKey)
        <script src="https://maps.googleapis.com/maps/api/js?key={{ $googleMapsApiKey }}&callback=initMap" async defer>
        </script>
        <script>
            // Store locations data from database
            const stores = [
                @foreach ($stores as $index => $store)
                    {
                        id: 'store{{ $store->id }}',
                        name: '{{ $store->name }}',
                        position: {
                            lat: {{ $store->latitude }},
                            lng: {{ $store->longitude }}
                        },
                        address: '{{ $store->address }}',
                        phone: '{{ $store->phone }}',
                        main: {{ $index == 0 ? 'true' : 'false' }}
                    }
                    {{ !$loop->last ? ',' : '' }}
                @endforeach
            ];

            let map;
            let markers = [];

            function initMap() {
                // Create map centered on Egypt
                map = new google.maps.Map(document.getElementById('map'), {
                    center: {
                        lat: 30.0444,
                        lng: 31.2357
                    },
                    zoom: 6,
                    mapTypeControl: false,
                    streetViewControl: false,
                    fullscreenControl: true,
                    zoomControl: true,
                    styles: [{
                        "featureType": "poi",
                        "stylers": [{
                            "visibility": "off"
                        }]
                    }]
                });

                // Create info window
                const infoWindow = new google.maps.InfoWindow();

                // Add markers for each store
                stores.forEach(store => {
                    const marker = new google.maps.Marker({
                        position: store.position,
                        map: map,
                        title: store.name,
                        // استخدام الأيقونات الافتراضية لـ Google Maps
                        icon: store.main ? {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 8,
                            fillColor: '#FFD700',
                            fillOpacity: 1,
                            strokeColor: '#B8860B',
                            strokeWeight: 2
                        } : {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 6,
                            fillColor: '#FF6B6B',
                            fillOpacity: 1,
                            strokeColor: '#CC5555',
                            strokeWeight: 2
                        },
                        animation: google.maps.Animation.DROP
                    });

                    markers.push(marker);

                    // Add click event to marker
                    marker.addListener('click', () => {
                        // Set content for info window
                        const content = `
                    <div class="p-3" style="max-width: 300px;">
                        <h3 class="font-bold text-lg mb-1">${store.name}</h3>
                        <p class="text-gray-600 mb-2">${store.address}</p>
                        <p class="text-gray-600 mb-2"><i class="fas fa-phone-alt mr-2"></i> ${store.phone}</p>
                        <div class="mt-3">
                            <a href="https://maps.google.com/?q=${store.position.lat},${store.position.lng}" target="_blank" class="text-blue-500 hover:text-blue-700">
                                <i class="fas fa-directions mr-1"></i> {{ __('Directions') }}
                            </a>
                        </div>
                    </div>
                `;

                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);

                        // Highlight the corresponding store card
                        highlightStoreCard(store.id);

                        // Pan to marker
                        map.panTo(marker.getPosition());
                        map.setZoom(15);
                    });
                });

                // Add click event to store cards
                document.querySelectorAll('.store-card').forEach(card => {
                    card.addEventListener('click', function() {
                        const storeId = this.getAttribute('data-id');
                        const lat = parseFloat(this.getAttribute('data-lat'));
                        const lng = parseFloat(this.getAttribute('data-lng'));

                        // Find the corresponding store
                        const store = stores.find(s => s.id === storeId);

                        if (store) {
                            // Find the marker
                            const marker = markers.find(m => m.getTitle() === store.name);

                            if (marker) {
                                // Trigger marker click
                                google.maps.event.trigger(marker, 'click');
                            }
                        }
                    });
                });
            }

            function highlightStoreCard(storeId) {
                // Remove active class from all cards
                document.querySelectorAll('.store-card').forEach(card => {
                    card.classList.remove('active');
                });

                // Add active class to selected card
                const selectedCard = document.querySelector(`.store-card[data-id="${storeId}"]`);
                if (selectedCard) {
                    selectedCard.classList.add('active');

                    // Scroll to the card
                    selectedCard.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            }
        </script>
    @endif
@endsection
