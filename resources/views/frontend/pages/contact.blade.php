@extends('layouts.frontend')

@section('title', $page->translated_title)

@section('meta_description', $page->translated_meta_description)
@section('meta_keywords', $page->meta_keywords)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Contact Us') }}</span>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <h1 class="text-3xl font-bold text-center mb-12">{{ $page->translated_title }}</h1>

            <!-- Page Content -->
            <div class="max-w-4xl mx-auto mb-12">
                {!! $page->translated_content !!}
            </div>

            <div class="max-w-6xl mx-auto">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Contact Information -->
                    <div class="lg:col-span-1">
                        <div class="bg-white rounded-lg shadow-sm p-8">
                            <h2 class="text-xl font-bold mb-6">{{ __('Contact Information') }}</h2>

                            <div class="space-y-6">
                                <!-- Headquarters -->
                                @if ($contactInfo['headquarters_address_ar'] || $contactInfo['headquarters_address_en'])
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <div
                                                class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-map-marker-alt text-primary-500"></i>
                                            </div>
                                        </div>
                                        <div class="mr-4">
                                            <h3 class="font-medium text-gray-900">{{ __('Headquarters') }}</h3>
                                            <p class="text-gray-600 mt-1">
                                                {{ app()->getLocale() == 'ar'
                                                    ? $contactInfo['headquarters_address_ar'] ?? $contactInfo['headquarters_address_en']
                                                    : $contactInfo['headquarters_address_en'] ?? $contactInfo['headquarters_address_ar'] }}
                                            </p>
                                        </div>
                                    </div>
                                @endif

                                <!-- Phone -->
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-phone-alt text-primary-500"></i>
                                        </div>
                                    </div>
                                    <div class="mr-4">
                                        <h3 class="font-medium text-gray-900">{{ __('Phone') }}</h3>
                                        @if ($contactInfo['phone'])
                                            @php
                                                $phone = $contactInfo['phone'];
                                                // تصحيح موضع علامة + في رقم الهاتف مثل التوبار
                                                if (!str_starts_with($phone, '+')) {
                                                    // إذا كان الرقم يبدأ بـ 0، نحوله إلى +2
                                                    if (str_starts_with($phone, '0')) {
                                                        $phone = '+2' . substr($phone, 1);
                                                    } else {
                                                        $phone = '+' . ltrim($phone, '+');
                                                    }
                                                }
                                                // التأكد من أن الرقم المصري يبدأ بـ +20
                                                if (str_starts_with($phone, '+2') && !str_starts_with($phone, '+20')) {
                                                    $phone = '+20' . substr($phone, 2);
                                                }
                                                // إنشاء رقم للعرض مع + في النهاية مثل التوبار
                                                $displayPhone = ltrim($phone, '+') . '+';
                                            @endphp
                                            <p class="text-gray-600 mt-1">
                                                <a href="tel:{{ $phone }}"
                                                    class="hover:text-primary-500 transition duration-200 flex items-center">
                                                    <i class="fas fa-phone-alt text-primary-500 mr-2"></i>
                                                    {{ $displayPhone }}
                                                </a>
                                            </p>
                                        @endif
                                        @if ($contactInfo['whatsapp'])
                                            @php
                                                $whatsapp = $contactInfo['whatsapp'];
                                                // تصحيح موضع علامة + في رقم الواتساب مثل التوبار
                                                if (!str_starts_with($whatsapp, '+')) {
                                                    // إذا كان الرقم يبدأ بـ 0، نحوله إلى +2
                                                    if (str_starts_with($whatsapp, '0')) {
                                                        $whatsapp = '+2' . substr($whatsapp, 1);
                                                    } else {
                                                        $whatsapp = '+' . ltrim($whatsapp, '+');
                                                    }
                                                }
                                                // التأكد من أن الرقم المصري يبدأ بـ +20
                                                if (
                                                    str_starts_with($whatsapp, '+2') &&
                                                    !str_starts_with($whatsapp, '+20')
                                                ) {
                                                    $whatsapp = '+20' . substr($whatsapp, 2);
                                                }
                                                // إنشاء رقم للعرض مع + في النهاية مثل التوبار
                                                $displayWhatsapp = ltrim($whatsapp, '+') . '+';
                                            @endphp
                                            <p class="text-gray-600 mt-2">
                                                <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $whatsapp) }}"
                                                    target="_blank"
                                                    class="hover:text-primary-500 transition duration-200 flex items-center">
                                                    <i class="fab fa-whatsapp text-green-500 mr-2"></i>
                                                    {{ $displayWhatsapp }}
                                                </a>
                                            </p>
                                        @endif
                                    </div>
                                </div>

                                <!-- Email -->
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-envelope text-primary-500"></i>
                                        </div>
                                    </div>
                                    <div class="mr-4">
                                        <h3 class="font-medium text-gray-900">{{ __('Email') }}</h3>
                                        <p class="text-gray-600 mt-1">
                                            <a href="mailto:{{ $contactInfo['email'] }}"
                                                class="hover:text-primary-500 transition duration-200">
                                                {{ $contactInfo['email'] }}
                                            </a>
                                        </p>
                                    </div>
                                </div>

                                <!-- Address -->
                                @if ($contactInfo['address'])
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <div
                                                class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-map-marker-alt text-primary-500"></i>
                                            </div>
                                        </div>
                                        <div class="mr-4">
                                            <h3 class="font-medium text-gray-900">{{ __('Address') }}</h3>
                                            <p class="text-gray-600 mt-1">{{ $contactInfo['address'] }}</p>
                                        </div>
                                    </div>
                                @endif

                                <!-- Working Hours -->
                                @if ($contactInfo['working_hours_ar'] || $contactInfo['working_hours_en'])
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <div
                                                class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-clock text-primary-500"></i>
                                            </div>
                                        </div>
                                        <div class="mr-4">
                                            <h3 class="font-medium text-gray-900">{{ __('Working Hours') }}</h3>
                                            <div class="text-gray-600 mt-1">
                                                {!! app()->getLocale() == 'ar'
                                                    ? nl2br(e($contactInfo['working_hours_ar'] ?? $contactInfo['working_hours_en']))
                                                    : nl2br(e($contactInfo['working_hours_en'] ?? $contactInfo['working_hours_ar'])) !!}
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <!-- Social Media -->
                            @if ($contactInfo['show_social_media'])
                                <div class="mt-8">
                                    <h3 class="font-medium text-gray-900 mb-4">{{ __('Follow Us') }}</h3>
                                    <div class="flex space-x-4 space-x-reverse">
                                        @if ($contactInfo['facebook'])
                                            <a href="{{ $contactInfo['facebook'] }}" target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-blue-600 hover:text-white transition duration-300">
                                                <i class="fab fa-facebook-f"></i>
                                            </a>
                                        @endif

                                        @if ($contactInfo['twitter'])
                                            <a href="{{ $contactInfo['twitter'] }}" target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-blue-400 hover:text-white transition duration-300">
                                                <i class="fab fa-twitter"></i>
                                            </a>
                                        @endif

                                        @if ($contactInfo['instagram'])
                                            <a href="{{ $contactInfo['instagram'] }}" target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-pink-500 hover:text-white transition duration-300">
                                                <i class="fab fa-instagram"></i>
                                            </a>
                                        @endif

                                        @if ($contactInfo['youtube'])
                                            <a href="{{ $contactInfo['youtube'] }}" target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-red-600 hover:text-white transition duration-300">
                                                <i class="fab fa-youtube"></i>
                                            </a>
                                        @endif

                                        @if ($contactInfo['tiktok'])
                                            <a href="{{ $contactInfo['tiktok'] }}" target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-black hover:text-white transition duration-300">
                                                <i class="fab fa-tiktok"></i>
                                            </a>
                                        @endif

                                        @if ($contactInfo['linkedin'])
                                            <a href="{{ $contactInfo['linkedin'] }}" target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-blue-700 hover:text-white transition duration-300">
                                                <i class="fab fa-linkedin-in"></i>
                                            </a>
                                        @endif

                                        @if ($contactInfo['whatsapp'])
                                            <a href="https://wa.me/{{ str_replace(['+', ' ', '-'], '', $contactInfo['whatsapp']) }}"
                                                target="_blank"
                                                class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 hover:bg-green-500 hover:text-white transition duration-300">
                                                <i class="fab fa-whatsapp"></i>
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Contact Form -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-lg shadow-sm p-8">
                            <h2 class="text-xl font-bold mb-6">{{ __('Send Us a Message') }}</h2>

                            <!-- Success/Error Messages -->
                            @if (session('success'))
                                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative"
                                    role="alert">
                                    <span class="block sm:inline">{{ session('success') }}</span>
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                                    role="alert">
                                    <span class="block sm:inline">{{ session('error') }}</span>
                                </div>
                            @endif

                            @if ($errors->any())
                                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                                    role="alert">
                                    <ul class="list-disc list-inside">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            <form action="{{ route('contact.store') }}" method="POST">
                                @csrf

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <label for="name"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Full Name') }}
                                            *</label>
                                        <input type="text" id="name" name="name" value="{{ old('name') }}"
                                            required
                                            class="w-full border @error('name') border-red-500 @else border-gray-300 @enderror rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>

                                    <div>
                                        <label for="email"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Email') }}
                                            *</label>
                                        <input type="email" id="email" name="email" value="{{ old('email') }}"
                                            required
                                            class="w-full border @error('email') border-red-500 @else border-gray-300 @enderror rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                    <div>
                                        <label for="phone"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Phone Number') }}</label>
                                        <input type="tel" id="phone" name="phone" value="{{ old('phone') }}"
                                            class="w-full border @error('phone') border-red-500 @else border-gray-300 @enderror rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>

                                    <div>
                                        <label for="subject"
                                            class="block text-gray-700 text-sm font-medium mb-2">{{ __('Subject') }}
                                            *</label>
                                        <input type="text" id="subject" name="subject"
                                            value="{{ old('subject') }}" required
                                            class="w-full border @error('subject') border-red-500 @else border-gray-300 @enderror rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                                    </div>
                                </div>

                                <div class="mb-6">
                                    <label for="message"
                                        class="block text-gray-700 text-sm font-medium mb-2">{{ __('Message') }} *</label>
                                    <textarea id="message" name="message" rows="6" required
                                        class="w-full border @error('message') border-red-500 @else border-gray-300 @enderror rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">{{ old('message') }}</textarea>
                                </div>

                                <div class="flex items-center mb-6">
                                    <input type="checkbox" id="privacy" name="privacy" required
                                        class="text-primary-500 focus:ring-primary-500 h-4 w-4">
                                    <label for="privacy" class="mr-2 text-gray-700 text-sm">
                                        {{ __('I agree to the') }} <a href="{{ route('privacy') }}"
                                            class="text-primary-500 hover:text-primary-600">{{ __('Privacy Policy') }}</a>
                                    </label>
                                </div>

                                <button type="submit"
                                    class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-full font-medium transition duration-300">
                                    {{ __('Send Message') }}
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Map -->
                @if ($contactInfo['show_map'] && ($contactInfo['map_latitude'] && $contactInfo['map_longitude']))
                    <div class="mt-12 bg-white rounded-lg shadow-sm overflow-hidden">
                        <div id="map" class="w-full h-96"></div>
                    </div>
                @endif


            </div>
        </div>
    </section>
@endsection

@section('scripts')
    @if ($contactInfo['show_map'] && ($contactInfo['map_latitude'] && $contactInfo['map_longitude']))
        @php
            $googleMapsApiKey = $siteSettings->google_maps_api_key ?? env('GOOGLE_MAPS_API_KEY');
        @endphp
        @if ($googleMapsApiKey)
            <script src="https://maps.googleapis.com/maps/api/js?key={{ $googleMapsApiKey }}&callback=initMap" async defer>
            </script>
            <script>
                // Initialize Google Map
                function initMap() {
                    // Map center from database
                    const center = {
                        lat: {{ $contactInfo['map_latitude'] ?? 30.0444 }},
                        lng: {{ $contactInfo['map_longitude'] ?? 31.2357 }}
                    };

                    // Create map
                    const map = new google.maps.Map(document.getElementById("map"), {
                        zoom: {{ $contactInfo['map_zoom'] ?? 15 }},
                        center: center,
                    });

                    // Add marker
                    const marker = new google.maps.Marker({
                        position: center,
                        map: map,
                        title: "{{ $contactInfo['map_marker_title'] ?? $contactInfo['site_name'] . ' - المقر الرئيسي' }}",
                        // استخدام الأيقونة الافتراضية لـ Google Maps
                    });

                    // Info window content
                    const contentString = `
            <div class="p-3" style="min-width: 200px;">
                <h3 class="font-bold mb-1">{{ $contactInfo['map_marker_title'] ?? $contactInfo['site_name'] . ' - المقر الرئيسي' }}</h3>
                <p class="text-gray-600 mb-2">{{ $contactInfo['address'] ?? 'العنوان غير محدد' }}</p>
                @if ($contactInfo['phone'])
                <p class="mb-2"><i class="fas fa-phone-alt text-primary-500 mr-1"></i> {{ $contactInfo['phone'] }}</p>
                @endif
                @if ($contactInfo['email'])
                <p class="mb-2"><i class="fas fa-envelope text-primary-500 mr-1"></i> {{ $contactInfo['email'] }}</p>
                @endif
            </div>
        `;

                    // Create info window
                    const infowindow = new google.maps.InfoWindow({
                        content: contentString,
                    });

                    // Add click event to marker
                    marker.addListener("click", () => {
                        infowindow.open(map, marker);
                    });
                }
            </script>
        @endif
    @endif
@endsection
