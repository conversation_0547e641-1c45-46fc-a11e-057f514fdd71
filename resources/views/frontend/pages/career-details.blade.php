@extends('layouts.frontend')

@section('title', app()->getLocale() == 'ar' ? $job->title : ($job->translations['en']['title'] ?? $job->title))

@section('meta_description', app()->getLocale() == 'ar' ? $job->description : ($job->translations['en']['description'] ?? $job->description))

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('careers') }}" class="text-gray-600 hover:text-primary-500">{{ __('Careers') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ app()->getLocale() == 'ar' ? $job->title : ($job->translations['en']['title'] ?? $job->title) }}</span>
            </div>
        </div>
    </div>

    <!-- Job Details Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <div class="flex flex-col md:flex-row md:justify-between md:items-start mb-6">
                        <div>
                            <h1 class="text-3xl font-bold mb-4">{{ app()->getLocale() == 'ar' ? $job->title : ($job->translations['en']['title'] ?? $job->title) }}</h1>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">{{ app()->getLocale() == 'ar' ? $job->type : ($job->translations['en']['type'] ?? $job->type) }}</span>
                                <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">{{ app()->getLocale() == 'ar' ? $job->location : ($job->translations['en']['location'] ?? $job->location) }}</span>
                                @if($job->department)
                                <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm">{{ app()->getLocale() == 'ar' ? $job->department : ($job->translations['en']['department'] ?? $job->department) }}</span>
                                @endif
                            </div>
                            @if($job->expires_at)
                            <p class="text-gray-500 text-sm">
                                {{ __('Application Deadline') }}: {{ $job->expires_at->locale(app()->getLocale())->format('d F Y') }}
                            </p>
                            @endif
                        </div>
                        <div class="mt-4 md:mt-0">
                            <a href="#apply-form" class="inline-block bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-md font-medium transition duration-300">
                                {{ __('Apply Now') }}
                            </a>
                        </div>
                    </div>

                    @if($job->salary_min || $job->salary_max)
                    <div class="border-t border-gray-200 pt-6 mt-6">
                        <h2 class="text-xl font-bold mb-4">{{ __('Salary Range') }}</h2>
                        <p class="text-gray-700">
                            @if($job->salary_min && $job->salary_max)
                                {{ number_format($job->salary_min) }} - {{ number_format($job->salary_max) }} {{ __('SAR') }}
                            @elseif($job->salary_min)
                                {{ __('From') }} {{ number_format($job->salary_min) }} {{ __('SAR') }}
                            @elseif($job->salary_max)
                                {{ __('Up to') }} {{ number_format($job->salary_max) }} {{ __('SAR') }}
                            @endif
                        </p>
                    </div>
                    @endif
                </div>

                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 class="text-xl font-bold mb-4">{{ __('Job Description') }}</h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! app()->getLocale() == 'ar' ? $job->description : ($job->translations['en']['description'] ?? $job->description) !!}
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 class="text-xl font-bold mb-4">{{ __('Requirements') }}</h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! app()->getLocale() == 'ar' ? $job->requirements : ($job->translations['en']['requirements'] ?? $job->requirements) !!}
                    </div>
                </div>

                @if($job->responsibilities)
                <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
                    <h2 class="text-xl font-bold mb-4">{{ __('Responsibilities') }}</h2>
                    <div class="prose prose-lg max-w-none text-gray-700">
                        {!! app()->getLocale() == 'ar' ? $job->responsibilities : ($job->translations['en']['responsibilities'] ?? $job->responsibilities) !!}
                    </div>
                </div>
                @endif

                <div id="apply-form" class="bg-white rounded-lg shadow-sm p-8">
                    <h2 class="text-xl font-bold mb-6">{{ __('Apply for this Position') }}</h2>

                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
                            {{ session('error') }}
                        </div>
                    @endif

                    <form action="{{ route('careers.apply') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        <input type="hidden" name="job_id" value="{{ $job->id }}">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Full Name') }} <span class="text-red-500">*</span></label>
                                <input type="text" id="name" name="name" value="{{ old('name') }}" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 @error('name') border-red-500 @enderror" required>
                                @error('name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Email Address') }} <span class="text-red-500">*</span></label>
                                <input type="email" id="email" name="email" value="{{ old('email') }}" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 @error('email') border-red-500 @enderror" required>
                                @error('email')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-6">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Phone Number') }} <span class="text-red-500">*</span></label>
                            <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 @error('phone') border-red-500 @enderror" required>
                            @error('phone')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="cover_letter" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Cover Letter') }}</label>
                            <textarea id="cover_letter" name="cover_letter" rows="5" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 @error('cover_letter') border-red-500 @enderror">{{ old('cover_letter') }}</textarea>
                            @error('cover_letter')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="resume" class="block text-sm font-medium text-gray-700 mb-1">{{ __('Resume/CV') }} <span class="text-red-500">*</span></label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="resume" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                            <span>{{ __('Upload a file') }}</span>
                                            <input id="resume" name="resume" type="file" class="sr-only" accept=".pdf,.doc,.docx" required>
                                        </label>
                                        <p class="pr-1">{{ __('or drag and drop') }}</p>
                                    </div>
                                    <p class="text-xs text-gray-500">
                                        {{ __('PDF, DOC, DOCX up to 2MB') }}
                                    </p>
                                </div>
                            </div>
                            @error('resume')
                                <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white px-6 py-3 rounded-md font-medium transition duration-300">
                                {{ __('Submit Application') }}
                            </button>
                        </div>
                    </form>
                </div>

                @if($similarJobs->count() > 0)
                <div class="mt-12">
                    <h2 class="text-2xl font-bold mb-6">{{ __('Similar Jobs') }}</h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @foreach($similarJobs as $similarJob)
                            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                <div class="p-6">
                                    <h3 class="font-bold text-lg mb-2">{{ app()->getLocale() == 'ar' ? $similarJob->title : ($similarJob->translations['en']['title'] ?? $similarJob->title) }}</h3>
                                    <div class="flex flex-wrap gap-2 mb-3">
                                        <span class="bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded">{{ app()->getLocale() == 'ar' ? $similarJob->type : ($similarJob->translations['en']['type'] ?? $similarJob->type) }}</span>
                                        <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">{{ app()->getLocale() == 'ar' ? $similarJob->location : ($similarJob->translations['en']['location'] ?? $similarJob->location) }}</span>
                                    </div>
                                    <a href="{{ route('careers.show', $similarJob->slug) }}" class="text-primary-500 hover:text-primary-600 font-medium">
                                        {{ __('View Details') }} <i class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} ml-1"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </section>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // File upload preview
        const resumeInput = document.getElementById('resume');
        const fileNameDisplay = document.createElement('p');
        fileNameDisplay.className = 'text-sm text-gray-700 mt-2';

        resumeInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                fileNameDisplay.textContent = this.files[0].name;
                this.parentElement.appendChild(fileNameDisplay);
            }
        });
    });
</script>
@endsection
