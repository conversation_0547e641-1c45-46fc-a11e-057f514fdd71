@extends('layouts.frontend')

@section('title', app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title)

@section('meta_description', app()->getLocale() == 'ar' ? $post->meta_description :
    $post->translations['en']['meta_description'] ?? $post->meta_description)
@section('meta_keywords', $post->meta_keywords)

@push('styles')
    <style>
        .share-button {
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .share-button:hover {
            transform: translateY(-2px);
        }

        .post-content img {
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .post-content h1,
        .post-content h2,
        .post-content h3,
        .post-content h4,
        .post-content h5,
        .post-content h6 {
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 700;
            line-height: 1.25;
        }

        .post-content h1 {
            font-size: 2.25rem;
        }

        .post-content h2 {
            font-size: 1.875rem;
        }

        .post-content h3 {
            font-size: 1.5rem;
        }

        .post-content h4 {
            font-size: 1.25rem;
        }

        .post-content p {
            margin-bottom: 1.5rem;
            line-height: 1.75;
        }

        .post-content ul,
        .post-content ol {
            margin: 1.5rem 0;
            padding-left: 2rem;
        }

        .post-content li {
            margin-bottom: 0.5rem;
        }

        .post-content blockquote {
            border-left: 4px solid #3B82F6;
            padding-left: 1rem;
            margin: 2rem 0;
            font-style: italic;
            background-color: #F8FAFC;
            padding: 1rem;
            border-radius: 0.5rem;
        }

        .sticky-share {
            position: sticky;
            top: 100px;
        }

        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: linear-gradient(to right, #3B82F6, #8B5CF6);
            z-index: 9999;
            transition: width 0.3s ease;
        }

        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
@endpush

@section('content')
    <!-- Reading Progress Bar -->
    <div class="reading-progress"></div>

    <!-- Enhanced Breadcrumb -->
    <div class="bg-gray-50 py-4">
        <div class="container mx-auto px-4">
            <nav class="flex items-center text-sm" aria-label="Breadcrumb">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500 transition-colors">
                    <i class="fas fa-home {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                    {{ __('Home') }}
                </a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('blog') }}"
                    class="text-gray-600 hover:text-primary-500 transition-colors">{{ __('Blog') }}</a>
                @if ($post->category)
                    <i
                        class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                    <a href="{{ route('blog', ['category' => $post->category->id]) }}"
                        class="text-gray-600 hover:text-primary-500 transition-colors">
                        {{ $post->category->{'name_' . app()->getLocale()} }}
                    </a>
                @endif
                <i
                    class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800 font-medium">
                    {{ Str::limit(app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title, 50) }}
                </span>
            </nav>
        </div>
    </div>

    <!-- Blog Post Content -->
    <article class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
                    <!-- Main Content -->
                    <div class="lg:col-span-3">
                        <!-- Post Header -->
                        <header class="mb-8">
                            <div class="mb-6">
                                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                                    <div class="flex items-center">
                                        <i
                                            class="fas fa-calendar-alt {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                        <span>{{ $post->published_at->locale(app()->getLocale())->format('d F Y') }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i
                                            class="fas fa-folder {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                        <a href="{{ route('blog', ['category' => $post->category->id]) }}"
                                            class="hover:text-primary-600 transition-colors">
                                            {{ $post->category->{'name_' . app()->getLocale()} ?? '' }}
                                        </a>
                                    </div>
                                    <div class="flex items-center">
                                        <i
                                            class="fas fa-user {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                        <span>{{ $post->author->name ?? __('Admin') }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i
                                            class="fas fa-eye {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                        <span>{{ number_format($post->views) }} {{ __('views') }}</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i
                                            class="fas fa-clock {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                        <span>{{ __('5 min read') }}</span>
                                    </div>
                                </div>
                            </div>

                            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                                {{ app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title }}
                            </h1>

                            @if ($post->excerpt)
                                <div
                                    class="text-xl text-gray-600 leading-relaxed mb-8 p-6 bg-gray-50 rounded-xl border-l-4 border-primary-500">
                                    {{ app()->getLocale() == 'ar' ? $post->excerpt : $post->translations['en']['excerpt'] ?? $post->excerpt }}
                                </div>
                            @endif
                        </header>

                        <!-- Featured Image -->
                        <div class="mb-8">
                            <img src="{{ asset('storage/' . $post->featured_image) }}"
                                alt="{{ app()->getLocale() == 'ar' ? $post->title : $post->translations['en']['title'] ?? $post->title }}"
                                class="w-full h-auto rounded-2xl shadow-2xl object-cover">
                        </div>

                        <!-- Post Content -->
                        <div class="post-content prose prose-lg max-w-none mb-12" id="post-content">
                            {!! app()->getLocale() == 'ar' ? $post->content : $post->translations['en']['content'] ?? $post->content !!}
                        </div>

                        <!-- Tags -->
                        @if ($post->meta_keywords)
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold mb-4 flex items-center">
                                    <i
                                        class="fas fa-tags {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                    {{ __('Tags') }}
                                </h3>
                                <div class="flex flex-wrap gap-2">
                                    @foreach (explode(',', $post->meta_keywords) as $tag)
                                        @if (trim($tag))
                                            <a href="{{ route('blog', ['tag' => trim($tag)]) }}"
                                                class="bg-gray-100 hover:bg-primary-50 hover:text-primary-600 text-gray-700 px-4 py-2 rounded-full text-sm transition-colors font-medium">
                                                <i
                                                    class="fas fa-hashtag {{ app()->getLocale() == 'ar' ? 'ml-1' : 'mr-1' }} text-xs"></i>{{ trim($tag) }}
                                            </a>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Social Share -->
                        <div class="mb-12 p-6 bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl">
                            <h3 class="text-lg font-semibold mb-4 text-center">{{ __('Share this article') }}</h3>
                            <div
                                class="flex justify-center space-x-4 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode($shareUrl) }}"
                                    class="share-button bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
                                    target="_blank" rel="noopener noreferrer" title="{{ __('Share on Facebook') }}">
                                    <i class="fab fa-facebook-f text-lg"></i>
                                </a>
                                <a href="https://twitter.com/intent/tweet?url={{ urlencode($shareUrl) }}&text={{ urlencode($shareTitle) }}"
                                    class="share-button bg-sky-500 hover:bg-sky-600 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
                                    target="_blank" rel="noopener noreferrer" title="{{ __('Share on Twitter') }}">
                                    <i class="fab fa-twitter text-lg"></i>
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ urlencode($shareUrl) }}"
                                    class="share-button bg-blue-700 hover:bg-blue-800 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
                                    target="_blank" rel="noopener noreferrer" title="{{ __('Share on LinkedIn') }}">
                                    <i class="fab fa-linkedin-in text-lg"></i>
                                </a>
                                <a href="https://wa.me/?text={{ urlencode($shareTitle . ' - ' . $shareUrl) }}"
                                    class="share-button bg-green-500 hover:bg-green-600 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
                                    target="_blank" rel="noopener noreferrer" title="{{ __('Share on WhatsApp') }}">
                                    <i class="fab fa-whatsapp text-lg"></i>
                                </a>
                                <button onclick="copyToClipboard('{{ $shareUrl }}')"
                                    class="share-button bg-gray-600 hover:bg-gray-700 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
                                    title="{{ __('Copy Link') }}">
                                    <i class="fas fa-link text-lg"></i>
                                </button>
                            </div>
                            <div class="text-center mt-4">
                                <span class="text-sm text-gray-600" id="share-count">{{ __('0 shares') }}</span>
                            </div>
                        </div>

                        <!-- Author Bio -->
                        <div class="bg-white rounded-xl shadow-lg p-6 mb-12 border border-gray-100">
                            <h3 class="text-lg font-semibold mb-4 flex items-center">
                                <i
                                    class="fas fa-user-edit {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                {{ __('About the Author') }}
                            </h3>
                            <div
                                class="flex items-start space-x-4 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                                <div class="flex-shrink-0">
                                    <div
                                        class="w-20 h-20 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full overflow-hidden flex items-center justify-center">
                                        @if ($post->author && $post->author->avatar)
                                            <img src="{{ asset('storage/' . $post->author->avatar) }}"
                                                alt="{{ $post->author->name }}" class="w-full h-full object-cover">
                                        @else
                                            <i class="fas fa-user text-white text-2xl"></i>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <h4 class="font-bold text-lg mb-2">{{ $post->author->name ?? __('Admin') }}</h4>
                                    <p class="text-gray-600 mb-3">
                                        {{ $post->author->bio ?? __('Expert in jewelry and precious metals with years of experience in the industry.') }}
                                    </p>
                                    <div class="flex space-x-3 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                                        <a href="#" class="text-gray-400 hover:text-blue-600 transition-colors">
                                            <i class="fab fa-twitter"></i>
                                        </a>
                                        <a href="#" class="text-gray-400 hover:text-blue-700 transition-colors">
                                            <i class="fab fa-linkedin"></i>
                                        </a>
                                        <a href="#" class="text-gray-400 hover:text-red-600 transition-colors">
                                            <i class="fab fa-instagram"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Navigation between posts -->
                        @if ($previousPost || $nextPost)
                            <div class="mb-12">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    @if ($previousPost)
                                        <div
                                            class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow">
                                            <div class="flex items-center mb-3">
                                                <i
                                                    class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'right' : 'left' }} {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }} text-primary-500"></i>
                                                <span
                                                    class="text-sm text-gray-600 font-medium">{{ __('Previous Post') }}</span>
                                            </div>
                                            <a href="{{ route('blog.show', $previousPost->slug) }}" class="group">
                                                <h4
                                                    class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors line-clamp-2">
                                                    {{ app()->getLocale() == 'ar' ? $previousPost->title : $previousPost->translations['en']['title'] ?? $previousPost->title }}
                                                </h4>
                                            </a>
                                        </div>
                                    @endif

                                    @if ($nextPost)
                                        <div
                                            class="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-shadow {{ !$previousPost ? 'md:col-start-2' : '' }}">
                                            <div class="flex items-center justify-end mb-3">
                                                <span
                                                    class="text-sm text-gray-600 font-medium">{{ __('Next Post') }}</span>
                                                <i
                                                    class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} {{ app()->getLocale() == 'ar' ? 'mr-2' : 'ml-2' }} text-primary-500"></i>
                                            </div>
                                            <a href="{{ route('blog.show', $nextPost->slug) }}" class="group">
                                                <h4
                                                    class="font-semibold text-gray-900 group-hover:text-primary-600 transition-colors line-clamp-2 text-right">
                                                    {{ app()->getLocale() == 'ar' ? $nextPost->title : $nextPost->translations['en']['title'] ?? $nextPost->title }}
                                                </h4>
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Related Posts -->
                        @if ($relatedPosts->count() > 0)
                            <div class="mb-12">
                                <h3 class="text-2xl font-bold mb-8 flex items-center">
                                    <i
                                        class="fas fa-newspaper {{ app()->getLocale() == 'ar' ? 'ml-3' : 'mr-3' }} text-primary-500"></i>
                                    {{ __('Related Posts') }}
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    @foreach ($relatedPosts as $relatedPost)
                                        <article
                                            class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-1">
                                            <div class="relative">
                                                <img src="{{ asset('storage/' . $relatedPost->featured_image) }}"
                                                    alt="{{ $relatedPost->title }}" class="w-full h-48 object-cover">
                                                <div
                                                    class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent">
                                                </div>
                                            </div>
                                            <div class="p-4">
                                                <div class="flex items-center text-xs text-gray-500 mb-2">
                                                    <span>{{ $relatedPost->published_at->locale(app()->getLocale())->format('d M Y') }}</span>
                                                    <span class="mx-2">•</span>
                                                    <span>{{ number_format($relatedPost->views) }}
                                                        {{ __('views') }}</span>
                                                </div>
                                                <h4
                                                    class="font-bold mb-2 line-clamp-2 hover:text-primary-600 transition-colors">
                                                    <a href="{{ route('blog.show', $relatedPost->slug) }}">
                                                        {{ app()->getLocale() == 'ar' ? $relatedPost->title : $relatedPost->translations['en']['title'] ?? $relatedPost->title }}
                                                    </a>
                                                </h4>
                                                <p class="text-gray-600 text-sm mb-3 line-clamp-2">
                                                    {{ Str::limit(app()->getLocale() == 'ar' ? $relatedPost->excerpt : $relatedPost->translations['en']['excerpt'] ?? $relatedPost->excerpt, 80) }}
                                                </p>
                                                <a href="{{ route('blog.show', $relatedPost->slug) }}"
                                                    class="inline-flex items-center text-primary-500 hover:text-primary-600 text-sm font-semibold transition-colors">
                                                    {{ __('Read More') }}
                                                    <i
                                                        class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} {{ app()->getLocale() == 'ar' ? 'mr-2' : 'ml-2' }} text-xs"></i>
                                                </a>
                                            </div>
                                        </article>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Sidebar -->
                    <aside class="lg:col-span-1">
                        <div class="sticky-share space-y-6">
                            <!-- Quick Share -->
                            <div class="bg-white rounded-xl shadow-lg p-6">
                                <h3 class="font-semibold mb-4 text-center">{{ __('Share') }}</h3>
                                <div class="flex flex-col space-y-3">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode($shareUrl) }}"
                                        class="flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors"
                                        target="_blank">
                                        <i
                                            class="fab fa-facebook-f {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                        <span class="text-sm">Facebook</span>
                                    </a>
                                    <a href="https://twitter.com/intent/tweet?url={{ urlencode($shareUrl) }}&text={{ urlencode($shareTitle) }}"
                                        class="flex items-center justify-center bg-sky-500 hover:bg-sky-600 text-white p-2 rounded-lg transition-colors"
                                        target="_blank">
                                        <i class="fab fa-twitter {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                        <span class="text-sm">Twitter</span>
                                    </a>
                                    <a href="https://wa.me/?text={{ urlencode($shareTitle . ' - ' . $shareUrl) }}"
                                        class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white p-2 rounded-lg transition-colors"
                                        target="_blank">
                                        <i class="fab fa-whatsapp {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                        <span class="text-sm">WhatsApp</span>
                                    </a>
                                </div>
                            </div>

                            <!-- Back to Blog -->
                            <div class="bg-white rounded-xl shadow-lg p-6">
                                <a href="{{ route('blog') }}"
                                    class="flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-700 p-3 rounded-lg transition-colors">
                                    <i
                                        class="fas fa-arrow-{{ app()->getLocale() == 'ar' ? 'right' : 'left' }} {{ app()->getLocale() == 'ar' ? 'ml-2' : 'mr-2' }}"></i>
                                    {{ __('Back to Blog') }}
                                </a>
                            </div>

                            <!-- Reading Time -->
                            <div class="bg-white rounded-xl shadow-lg p-6">
                                <div class="text-center">
                                    <i class="fas fa-clock text-2xl text-primary-500 mb-2"></i>
                                    <p class="text-sm text-gray-600">{{ __('Estimated reading time') }}</p>
                                    <p class="font-semibold">{{ __('5 minutes') }}</p>
                                </div>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </div>
    </article>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Reading progress bar
            function updateReadingProgress() {
                const article = document.getElementById('post-content');
                const progressBar = document.querySelector('.reading-progress');

                if (!article || !progressBar) return;

                const articleTop = article.offsetTop;
                const articleHeight = article.offsetHeight;
                const windowHeight = window.innerHeight;
                const scrollTop = window.pageYOffset;

                const articleBottom = articleTop + articleHeight;
                const windowBottom = scrollTop + windowHeight;

                if (scrollTop >= articleTop) {
                    const progress = Math.min(100, ((windowBottom - articleTop) / articleHeight) * 100);
                    progressBar.style.width = progress + '%';
                } else {
                    progressBar.style.width = '0%';
                }
            }

            window.addEventListener('scroll', updateReadingProgress);
            updateReadingProgress();

            // Smooth scroll for internal links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const button = event.target.closest('button');
                const originalIcon = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check text-lg"></i>';
                button.classList.add('bg-green-500', 'hover:bg-green-600');
                button.classList.remove('bg-gray-600', 'hover:bg-gray-700');

                setTimeout(() => {
                    button.innerHTML = originalIcon;
                    button.classList.remove('bg-green-500', 'hover:bg-green-600');
                    button.classList.add('bg-gray-600', 'hover:bg-gray-700');
                }, 2000);

                // Show notification
                if (typeof window.showNotification === 'function') {
                    window.showNotification('{{ __('Link copied to clipboard!') }}', 'success');
                } else {
                    alert('{{ __('Link copied to clipboard!') }}');
                }
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                alert('{{ __('Failed to copy link') }}');
            });
        }

        // Track social shares (optional)
        function trackShare(platform) {
            // You can implement analytics tracking here
            console.log('Shared on:', platform);

            // Update share count (this would typically come from a backend API)
            const shareCount = document.getElementById('share-count');
            if (shareCount) {
                const currentCount = parseInt(shareCount.textContent.match(/\d+/)[0] || 0);
                shareCount.textContent = `${currentCount + 1} shares`;
            }
        }

        // Add click tracking to share buttons
        document.querySelectorAll('.share-button[href]').forEach(button => {
            button.addEventListener('click', function(e) {
                const platform = this.href.includes('facebook') ? 'facebook' :
                    this.href.includes('twitter') ? 'twitter' :
                    this.href.includes('linkedin') ? 'linkedin' :
                    this.href.includes('whatsapp') ? 'whatsapp' : 'unknown';
                trackShare(platform);
            });
        });
    </script>
@endpush
