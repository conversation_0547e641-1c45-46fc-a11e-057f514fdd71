@extends('layouts.frontend')

@section('title', $category->name_ar)

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('الرئيسية') }}</a>
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <a href="{{ route('products') }}" class="text-gray-600 hover:text-primary-500">{{ __('المنتجات') }}</a>
                @if ($category->parent)
                    <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                    <a href="{{ route('category', $category->parent->slug) }}"
                        class="text-gray-600 hover:text-primary-500">{{ $category->parent->name_ar }}</a>
                @endif
                <i class="fas fa-chevron-left mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ $category->name_ar }}</span>
            </div>
        </div>
    </div>

    <!-- Category Banner -->
    <section class="relative py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="bg-white rounded-lg overflow-hidden shadow-md">
                <div class="relative h-64 md:h-80">
                    <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name_ar }}"
                        class="w-full h-full object-cover">
                    <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center">
                        <div class="container mx-auto px-4">
                            <div class="max-w-xl">
                                <h1 class="text-3xl md:text-4xl font-bold text-white mb-2">{{ $category->name_ar }}</h1>
                                <p class="text-white/90 mb-6">{{ $category->description_ar }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Subcategories (if any) -->
    @if ($category->children->count() > 0)
        <section class="py-8">
            <div class="container mx-auto px-4">
                <h2 class="text-2xl font-bold mb-6">{{ __('الفئات الفرعية') }}</h2>

                <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    @foreach ($category->children as $child)
                        <a href="{{ route('category', $child->slug) }}" class="group">
                            <div
                                class="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition duration-300 text-center p-4">
                                <div
                                    class="w-16 h-16 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-gem text-primary-500 text-xl"></i>
                                </div>
                                <h3 class="font-medium text-gray-800 group-hover:text-primary-500 transition duration-300">
                                    {{ $child->name_ar }}</h3>
                            </div>
                        </a>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    <!-- Products Section -->
    <section class="py-8">
        <div class="container mx-auto px-4">
            @livewire('category-products', ['categorySlug' => $category->slug])
        </div>
    </section>
@endsection

@section('scripts')
@endsection
