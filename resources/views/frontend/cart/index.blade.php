@extends('layouts.frontend')

@section('title', 'سلة التسوق')

@section('content')
    @livewire('cart.session-cart-page')

    <!-- You May Also Like Section -->
    <section class="py-12 bg-gray-50">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl font-bold mb-8">قد يعجبك أيضًا</h2>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                @foreach (\App\Models\Product::where('is_featured', true)->where('is_active', true)->inRandomOrder()->take(4)->get() as $product)
                    <div
                        class="product-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition duration-300">
                        <a href="{{ route('product.show', $product->slug) }}" class="block relative">
                            <img src="{{ $product->image ? asset('storage/' . $product->image) : asset('images/products/default.jpg') }}"
                                alt="{{ $product->name_ar }}" class="w-full h-64 object-cover">
                            @if ($product->discount_percentage > 0)
                                <span class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded">
                                    خصم {{ $product->discount_percentage }}%
                                </span>
                            @endif
                        </a>
                        <div class="p-4">
                            <a href="{{ route('category', $product->category->slug) }}"
                                class="text-xs text-primary-500 hover:text-primary-600 mb-2 inline-block">
                                {{ $product->category->name_ar }}
                            </a>
                            <h3 class="text-lg font-bold mb-2">
                                <a href="{{ route('product.show', $product->slug) }}"
                                    class="text-gray-800 hover:text-primary-500">
                                    {{ $product->name_ar }}
                                </a>
                            </h3>
                            <div class="flex items-center mb-3">
                                <div class="flex text-yellow-400">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= $product->rating)
                                            <i class="fas fa-star"></i>
                                        @elseif($i - 0.5 <= $product->rating)
                                            <i class="fas fa-star-half-alt"></i>
                                        @else
                                            <i class="far fa-star"></i>
                                        @endif
                                    @endfor
                                </div>
                                <span class="text-gray-500 text-xs mr-1">({{ $product->reviews_count ?? 0 }})</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <div>
                                    @if ($product->old_price > 0)
                                        <span
                                            class="text-gray-400 line-through text-sm">{{ number_format($product->old_price, 2) }}
                                            ج.م</span>
                                    @endif
                                    <span class="text-lg font-bold text-gray-800">{{ number_format($product->price, 2) }}
                                        ج.م</span>
                                </div>
                                <div class="flex space-x-2 space-x-reverse">
                                    <a href="{{ route('account.wishlist.add', $product->id) }}"
                                        class="text-gray-400 hover:text-red-500 transition duration-300">
                                        <i class="far fa-heart"></i>
                                    </a>
                                    <a href="{{ route('product.show', $product->slug) }}"
                                        class="text-gray-400 hover:text-primary-500 transition duration-300">
                                        <i class="fas fa-shopping-cart"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>
@endsection
