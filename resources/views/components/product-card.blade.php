@props([
    'product',
    'showQuickAdd' => true,
    'showWishlist' => true,
    'showRatings' => true,
    'showMetalInfo' => true,
    'showCategory' => true,
    'showWhatsapp' => true,
    'layout' => 'grid', // grid or list
    'size' => 'normal', // small, normal, large
    'badge' => null, // discount, new, featured, etc.
    'whatsappPhone' => null,
])

@php
    // تحديد أبعاد الصورة حسب الحجم
    $imageClasses = match ($size) {
        'small' => 'h-28 sm:h-32 md:h-40',
        'large' => 'h-48 sm:h-64 md:h-80',
        default => 'h-32 sm:h-40 md:h-64',
    };

    // تحديد padding حسب الحجم
    $paddingClasses = match ($size) {
        'small' => 'p-1.5 sm:p-2 md:p-3',
        'large' => 'p-3 sm:p-4 md:p-6',
        default => 'p-2 sm:p-3 md:p-4',
    };

    // تحديد تخطيط العرض
    $layoutClasses = $layout === 'list' ? 'flex flex-row' : 'flex flex-col';
    $imageContainerClasses = $layout === 'list' ? 'w-1/3 flex-shrink-0' : 'w-full';
    $contentClasses = $layout === 'list' ? 'flex-1 flex flex-col justify-between' : 'w-full';
@endphp

<div
    class="product-card bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition duration-300 {{ $layoutClasses }}">
    <!-- صورة المنتج -->
    <div class="relative bg-gray-100 {{ $imageContainerClasses }}">
        <a href="{{ route('product.show', $product->slug) }}">
            @if ($product->primaryImage)
                <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name_ar }}"
                    class="w-full {{ $imageClasses }} object-cover hover:scale-105 transition duration-300">
            @elseif($product->image)
                <img src="{{ asset('storage/' . $product->image) }}" alt="{{ $product->name_ar }}"
                    class="w-full {{ $imageClasses }} object-cover hover:scale-105 transition duration-300">
            @else
                <div class="w-full {{ $imageClasses }} bg-gray-200 flex items-center justify-center">
                    <i class="fas fa-image text-gray-400 text-2xl"></i>
                </div>
            @endif
        </a>

        <!-- الشارات -->
        @if ($badge === 'discount' && $product->discount_percentage > 0)
            <div
                class="absolute top-1 sm:top-2 right-1 sm:right-2 bg-red-500 text-white text-xs font-bold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs">
                خصم {{ $product->discount_percentage }}%
            </div>
        @elseif($badge === 'new')
            <div
                class="absolute top-1 sm:top-2 right-1 sm:right-2 bg-green-500 text-white text-xs font-bold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs">
                جديد
            </div>
        @elseif($badge === 'featured')
            <div
                class="absolute top-1 sm:top-2 right-1 sm:right-2 bg-primary-500 text-white text-xs font-bold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs">
                مميز
            </div>
        @elseif($product->old_price && $product->old_price > $product->price)
            <div
                class="absolute top-1 sm:top-2 right-1 sm:right-2 bg-red-500 text-white text-xs font-bold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs">
                خصم {{ round((($product->old_price - $product->price) / $product->old_price) * 100) }}%
            </div>
        @endif

        <!-- زر المفضلة السريع باستخدام Livewire -->
        @if ($showWishlist && $layout !== 'list')
            <div class="absolute top-1 sm:top-2 left-1 sm:left-2">
                <livewire:wishlist-manager :product-id="$product->id" />
            </div>
        @endif
    </div>

    <!-- معلومات المنتج -->
    <div class="{{ $paddingClasses }} {{ $contentClasses }}">
        <!-- فئة المنتج -->
        @if ($showCategory && $product->category)
            <a href="{{ route('category', $product->category->slug) }}"
                class="text-[10px] sm:text-xs text-primary-500 hover:text-primary-600 mb-1 sm:mb-2 inline-block">
                {{ $product->category->name_ar }}
            </a>
        @endif

        <!-- اسم المنتج -->
        <h3
            class="{{ $size === 'small' ? 'text-xs sm:text-sm' : 'text-xs sm:text-sm md:text-lg' }} font-bold mb-1 sm:mb-2 line-clamp-2 leading-tight">
            <a href="{{ route('product.show', $product->slug) }}"
                class="text-gray-800 hover:text-primary-500 transition duration-200">
                {{ $product->name_ar }}
            </a>
        </h3>

        <!-- معلومات المعدن والعيار -->
        @if ($showMetalInfo && ($product->metal || $product->metalPurity))
            <div class="flex items-center gap-1 sm:gap-2 mb-1 sm:mb-2 text-[10px] sm:text-xs">
                @if ($product->metal)
                    <span class="bg-gray-100 text-gray-700 px-1 sm:px-2 py-0.5 sm:py-1 rounded-full">
                        {{ $product->metal->name_ar }}
                    </span>
                @endif
                @if ($product->metalPurity)
                    <span class="bg-primary-100 text-primary-700 px-1 sm:px-2 py-0.5 sm:py-1 rounded-full">
                        {{ $product->metalPurity->name_ar }}
                    </span>
                @endif
            </div>
        @endif

        <!-- التقييم -->
        @if ($showRatings)
            <div class="flex items-center mb-2 sm:mb-3">
                <div class="flex text-yellow-400">
                    @for ($i = 1; $i <= 5; $i++)
                        @if ($i <= ($product->rating ?? 0))
                            <i class="fas fa-star text-[10px] sm:text-xs"></i>
                        @elseif($i - 0.5 <= ($product->rating ?? 0))
                            <i class="fas fa-star-half-alt text-[10px] sm:text-xs"></i>
                        @else
                            <i class="far fa-star text-[10px] sm:text-xs"></i>
                        @endif
                    @endfor
                </div>
                <span class="text-gray-500 text-[10px] sm:text-xs mr-1">({{ $product->reviews_count ?? 0 }})</span>
            </div>
        @endif

        <!-- السعر أو زر WhatsApp -->
        <div class="mb-2 sm:mb-3">
            @if ($product->show_price)
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-1 sm:gap-2">
                        @if ($product->old_price && $product->old_price > $product->price)
                            <span class="text-gray-400 line-through text-[10px] sm:text-sm">
                                {{ number_format($product->old_price, 2) }} ج.م
                            </span>
                        @endif
                        <span
                            class="{{ $size === 'small' ? 'text-sm sm:text-base' : 'text-sm sm:text-base md:text-lg' }} font-bold text-gray-800">
                            {{ number_format($product->price, 2) }} ج.م
                        </span>
                    </div>
                </div>
            @else
                <!-- عرض زر WhatsApp عندما السعر مخفي -->
                @if ($whatsappPhone)
                    <div class="text-center">
                        <a href="https://wa.me/{{ $whatsappPhone }}?text=أريد الاستفسار عن {{ $product->name_ar }}"
                            target="_blank"
                            class="inline-flex items-center bg-green-500 hover:bg-green-600 text-white text-[10px] sm:text-sm font-medium py-1.5 sm:py-2 px-2 sm:px-4 rounded-lg transition duration-200">
                            <i class="fab fa-whatsapp ml-1 text-[10px] sm:text-sm"></i>
                            تواصل للسعر
                        </a>
                    </div>
                @endif
            @endif
        </div>

        <!-- أزرار الإجراءات -->
        @if ($layout === 'list')
            <!-- عرض قائمة - أزرار أفقية -->
            <div class="flex items-center gap-1 sm:gap-2 mt-auto">
                @if ($showWishlist)
                    <div class="p-0.5 sm:p-1">
                        <livewire:wishlist-manager :product-id="$product->id" />
                    </div>
                @endif

                <a href="{{ route('product.show', $product->slug) }}"
                    class="flex-1 bg-primary-500 hover:bg-primary-600 text-white text-[10px] sm:text-sm font-medium py-1.5 sm:py-2 px-2 sm:px-4 rounded-lg text-center transition duration-200">
                    عرض التفاصيل
                </a>

                @if ($showWhatsapp && $whatsappPhone && $product->show_price)
                    <a href="https://wa.me/{{ $whatsappPhone }}?text=أريد الاستفسار عن {{ $product->name_ar }}"
                        target="_blank"
                        class="p-1.5 sm:p-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition duration-200 min-w-[32px] sm:min-w-[36px] flex items-center justify-center">
                        <i class="fab fa-whatsapp text-[10px] sm:text-sm"></i>
                    </a>
                @endif
            </div>
        @else
            <!-- عرض شبكة - أزرار عمودية -->
            <div class="flex gap-1 sm:gap-2">
                <a href="{{ route('product.show', $product->slug) }}"
                    class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 text-[10px] sm:text-sm font-medium py-1.5 sm:py-2 px-2 sm:px-4 rounded-lg text-center transition duration-200 min-h-[32px] sm:min-h-[36px] flex items-center justify-center">
                    عرض التفاصيل
                </a>

                @if ($showWhatsapp && $whatsappPhone && $product->show_price)
                    <a href="https://wa.me/{{ $whatsappPhone }}?text=أريد الاستفسار عن {{ $product->name_ar }}"
                        target="_blank"
                        class="bg-green-500 hover:bg-green-600 text-white text-[10px] sm:text-sm font-medium py-1.5 sm:py-2 px-2 sm:px-3 rounded-lg transition duration-200 min-w-[32px] sm:min-w-[36px] flex items-center justify-center">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                @endif
            </div>


        @endif
    </div>
</div>
