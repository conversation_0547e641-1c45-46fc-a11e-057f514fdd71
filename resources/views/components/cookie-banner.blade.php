@if(isset($superAdminSettings) && $superAdminSettings->isCookieBannerEnabled())
<div id="cookie-banner" class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 shadow-lg z-50 transform translate-y-full transition-transform duration-300" style="display: none;">
    <div class="container mx-auto max-w-6xl">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
            <!-- نص ملفات تعريف الارتباط -->
            <div class="flex-1">
                <div class="flex items-start gap-3">
                    <!-- أيقونة ملفات تعريف الارتباط -->
                    <div class="flex-shrink-0 mt-1">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    
                    <!-- النص -->
                    <div>
                        <p class="text-sm leading-relaxed">
                            {{ $superAdminSettings->getCookieBannerText() }}
                        </p>
                        
                        @if($superAdminSettings->isGdprComplianceEnabled())
                        <p class="text-xs text-gray-300 mt-2">
                            {{ $superAdminSettings->getGdprComplianceText() }}
                        </p>
                        @endif
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="flex flex-col sm:flex-row gap-2 flex-shrink-0">
                <!-- زر رفض (اختياري) -->
                <button id="cookie-decline" class="px-4 py-2 text-sm border border-gray-600 text-gray-300 hover:bg-gray-800 rounded-lg transition duration-200">
                    {{ __('رفض') }}
                </button>
                
                <!-- زر الموافقة -->
                <button id="cookie-accept" class="px-6 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition duration-200 font-medium">
                    {{ $superAdminSettings->getCookieBannerButtonText() }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const banner = document.getElementById('cookie-banner');
    const acceptBtn = document.getElementById('cookie-accept');
    const declineBtn = document.getElementById('cookie-decline');
    
    // فحص إذا كان المستخدم قد وافق مسبقاً
    if (!localStorage.getItem('cookies_accepted') && !localStorage.getItem('cookies_declined')) {
        // إظهار الشريط بعد ثانية واحدة
        setTimeout(() => {
            banner.style.display = 'block';
            banner.classList.remove('translate-y-full');
        }, 1000);
    }
    
    // عند الموافقة على ملفات تعريف الارتباط
    acceptBtn.addEventListener('click', function() {
        localStorage.setItem('cookies_accepted', 'true');
        localStorage.removeItem('cookies_declined');
        hideBanner();
        
        // تفعيل ملفات تعريف الارتباط التحليلية والتسويقية
        enableAnalytics();
    });
    
    // عند رفض ملفات تعريف الارتباط
    declineBtn.addEventListener('click', function() {
        localStorage.setItem('cookies_declined', 'true');
        localStorage.removeItem('cookies_accepted');
        hideBanner();
        
        // تعطيل ملفات تعريف الارتباط غير الضرورية
        disableAnalytics();
    });
    
    function hideBanner() {
        banner.classList.add('translate-y-full');
        setTimeout(() => {
            banner.style.display = 'none';
        }, 300);
    }
    
    function enableAnalytics() {
        // تفعيل Google Analytics إذا كان متاحاً
        if (typeof gtag !== 'undefined') {
            gtag('consent', 'update', {
                'analytics_storage': 'granted',
                'ad_storage': 'granted'
            });
        }
        
        // تفعيل Facebook Pixel إذا كان متاحاً
        if (typeof fbq !== 'undefined') {
            fbq('consent', 'grant');
        }
    }
    
    function disableAnalytics() {
        // تعطيل Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('consent', 'update', {
                'analytics_storage': 'denied',
                'ad_storage': 'denied'
            });
        }
        
        // تعطيل Facebook Pixel
        if (typeof fbq !== 'undefined') {
            fbq('consent', 'revoke');
        }
    }
});
</script>
@endif
