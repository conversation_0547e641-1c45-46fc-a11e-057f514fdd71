@props(['selectedMethod' => null])

@php
    $paymentHelper = app(\App\Helpers\PaymentHelper::class);
    $availableMethods = $paymentHelper->getAvailablePaymentMethods();
@endphp

@if($paymentHelper->hasAvailablePaymentMethods())
<div class="payment-methods">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">{{ __('اختر طريقة الدفع') }}</h3>
    
    <div class="space-y-3">
        @foreach($availableMethods as $methodKey => $method)
        <label class="payment-method-option relative flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition duration-200 {{ $selectedMethod === $methodKey ? 'border-blue-500 bg-blue-50' : '' }}">
            <input type="radio" 
                   name="payment_method" 
                   value="{{ $methodKey }}" 
                   class="sr-only"
                   {{ $selectedMethod === $methodKey ? 'checked' : '' }}
                   onchange="selectPaymentMethod('{{ $methodKey }}')">
            
            <!-- أيقونة طريقة الدفع -->
            <div class="flex-shrink-0 mr-4">
                @switch($method['icon'])
                    @case('credit-card')
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                        @break
                    @case('paypal')
                        <svg class="w-8 h-8 text-blue-700" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944 2.28A.859.859 0 0 1 5.792 1.5h6.38c1.917 0 3.553.702 4.7 2.031 1.146 1.328 1.565 3.21 1.565 5.563 0 6.702-4.303 10.418-9.435 10.418H7.076z"/>
                        </svg>
                        @break
                    @case('bank')
                        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        @break
                    @case('cash')
                        <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        @break
                    @case('fawry')
                        <div class="w-8 h-8 bg-orange-500 rounded flex items-center justify-center">
                            <span class="text-white text-xs font-bold">F</span>
                        </div>
                        @break
                    @default
                        <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                @endswitch
            </div>
            
            <!-- معلومات طريقة الدفع -->
            <div class="flex-1">
                <div class="flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900">{{ $method['name'] }}</h4>
                    <div class="payment-method-indicator w-4 h-4 border-2 border-gray-300 rounded-full {{ $selectedMethod === $methodKey ? 'border-blue-500 bg-blue-500' : '' }}">
                        @if($selectedMethod === $methodKey)
                        <div class="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                        @endif
                    </div>
                </div>
                <p class="text-sm text-gray-500 mt-1">{{ $method['description'] }}</p>
            </div>
        </label>
        @endforeach
    </div>
    
    <!-- معلومات إضافية حسب طريقة الدفع المختارة -->
    <div id="payment-method-details" class="mt-6">
        @if($selectedMethod === 'bank_transfer')
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h5 class="font-medium text-blue-900 mb-2">{{ __('تفاصيل التحويل البنكي') }}</h5>
            <div class="text-sm text-blue-800 space-y-1">
                <p><strong>{{ __('اسم البنك:') }}</strong> البنك الأهلي المصري</p>
                <p><strong>{{ __('رقم الحساب:') }}</strong> ****************</p>
                <p><strong>{{ __('اسم المستفيد:') }}</strong> مكة جولد للمجوهرات</p>
                <p class="text-xs mt-2">{{ __('يرجى إرسال إيصال التحويل عبر واتساب أو البريد الإلكتروني') }}</p>
            </div>
        </div>
        @endif
        
        @if($selectedMethod === 'cash_on_delivery')
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <h5 class="font-medium text-green-900 mb-2">{{ __('الدفع عند الاستلام') }}</h5>
            <p class="text-sm text-green-800">
                {{ __('سيتم تحصيل قيمة الطلب نقداً عند التسليم. يرجى التأكد من توفر المبلغ المطلوب.') }}
            </p>
        </div>
        @endif
    </div>
</div>

<script>
function selectPaymentMethod(method) {
    // إزالة التحديد من جميع الخيارات
    document.querySelectorAll('.payment-method-option').forEach(option => {
        option.classList.remove('border-blue-500', 'bg-blue-50');
        const indicator = option.querySelector('.payment-method-indicator');
        indicator.classList.remove('border-blue-500', 'bg-blue-500');
        indicator.innerHTML = '';
    });
    
    // تحديد الخيار المختار
    const selectedOption = document.querySelector(`input[value="${method}"]`).closest('.payment-method-option');
    selectedOption.classList.add('border-blue-500', 'bg-blue-50');
    const indicator = selectedOption.querySelector('.payment-method-indicator');
    indicator.classList.add('border-blue-500', 'bg-blue-500');
    indicator.innerHTML = '<div class="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>';
    
    // إظهار تفاصيل طريقة الدفع
    showPaymentMethodDetails(method);
    
    // تحديث النموذج
    document.querySelector(`input[value="${method}"]`).checked = true;
}

function showPaymentMethodDetails(method) {
    const detailsContainer = document.getElementById('payment-method-details');
    
    // مسح التفاصيل السابقة
    detailsContainer.innerHTML = '';
    
    if (method === 'bank_transfer') {
        detailsContainer.innerHTML = `
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h5 class="font-medium text-blue-900 mb-2">{{ __('تفاصيل التحويل البنكي') }}</h5>
                <div class="text-sm text-blue-800 space-y-1">
                    <p><strong>{{ __('اسم البنك:') }}</strong> البنك الأهلي المصري</p>
                    <p><strong>{{ __('رقم الحساب:') }}</strong> ****************</p>
                    <p><strong>{{ __('اسم المستفيد:') }}</strong> مكة جولد للمجوهرات</p>
                    <p class="text-xs mt-2">{{ __('يرجى إرسال إيصال التحويل عبر واتساب أو البريد الإلكتروني') }}</p>
                </div>
            </div>
        `;
    } else if (method === 'cash_on_delivery') {
        detailsContainer.innerHTML = `
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 class="font-medium text-green-900 mb-2">{{ __('الدفع عند الاستلام') }}</h5>
                <p class="text-sm text-green-800">
                    {{ __('سيتم تحصيل قيمة الطلب نقداً عند التسليم. يرجى التأكد من توفر المبلغ المطلوب.') }}
                </p>
            </div>
        `;
    }
}
</script>

@else
<div class="no-payment-methods bg-red-50 border border-red-200 rounded-lg p-6 text-center">
    <svg class="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
    </svg>
    <h3 class="text-lg font-medium text-red-900 mb-2">{{ __('لا توجد طرق دفع متاحة') }}</h3>
    <p class="text-red-700">{{ $paymentHelper->getNoPaymentMethodsMessage() }}</p>
</div>
@endif
