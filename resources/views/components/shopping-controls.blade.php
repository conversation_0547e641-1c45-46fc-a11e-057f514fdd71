@props([
    'product' => null,
    'productId' => null,
    'showPrice' => true,
    'showWishlist' => true,
    'whatsappPhone' => '',
    'quantity' => 1,
    'showQuantityControls' => false,
    'compact' => false,
])

@php
    // إذا لم يتم تمرير المنتج، جلبه من productId
    if (!$product && $productId) {
        $product = \App\Models\Product::find($productId);
    }
@endphp

<div class="shopping-controls">
    @if ($showPrice && $product)
        <!-- عرض السعر دائماً -->
        <div class="price-section mb-4">
            @if ($compact)
                <div class="flex justify-between items-center">
                    <div>
                        @if ($product->show_price)
                            @if ($product->old_price > 0)
                                <span
                                    class="text-gray-400 line-through text-sm">{{ number_format($product->old_price, 2) }}
                                    ج.م</span>
                            @endif
                            <span class="text-lg font-bold text-gray-800">{{ number_format($product->price, 2) }}
                                ج.م</span>
                        @else
                            <a href="https://wa.me/{{ $whatsappPhone }}" target="_blank"
                                class="inline-flex items-center text-green-500 hover:text-green-600 transition duration-300">
                                <i class="fab fa-whatsapp text-lg ml-1"></i>
                                <span class="text-sm">تواصل للسعر</span>
                            </a>
                        @endif
                    </div>
                </div>
            @else
                <div class="flex items-center gap-4">
                    @if ($product->show_price)
                        @if ($product->old_price > 0)
                            <span class="text-gray-400 line-through text-lg">{{ number_format($product->old_price, 2) }}
                                ج.م</span>
                        @endif
                        <span class="text-3xl font-bold text-gray-800">{{ number_format($product->price, 2) }}
                            ج.م</span>
                        @if ($product->discount_percentage > 0)
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded">خصم
                                {{ $product->discount_percentage }}%</span>
                        @endif
                    @else
                        <a href="https://wa.me/{{ $whatsappPhone }}" target="_blank"
                            class="inline-flex items-center bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md transition duration-300">
                            <i class="fab fa-whatsapp text-xl ml-2"></i>
                            <span>تواصل عبر واتساب</span>
                        </a>
                    @endif
                </div>
            @endif
        </div>
    @endif

    <!-- أزرار التحكم -->
    <x-feature-check feature="shopping" :show-message="false">
        @if ($compact)
            <!-- عرض مضغوط للبطاقات -->
            <div class="flex space-x-2 space-x-reverse">
                @if ($showWishlist)
                    <x-feature-check feature="wishlist" :show-message="false">
                        <button type="button" onclick="toggleWishlist({{ $product->id ?? $productId }})"
                            class="text-gray-400 hover:text-red-500 transition duration-300 wishlist-btn"
                            data-product-id="{{ $product->id ?? $productId }}">
                            <i class="far fa-heart"></i>
                        </button>
                    </x-feature-check>
                @endif
                @if ($product)
                    <button onclick="addToCart({{ $product->id ?? $productId }}, 1)"
                        class="text-gray-400 hover:text-primary-500 transition duration-300"
                        data-product-id="{{ $product->id ?? $productId }}" data-quantity="1">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                @endif
            </div>
        @else
            <!-- عرض كامل لصفحات التفاصيل -->
            <div class="action-buttons space-y-4">
                @if ($product && $product->stock_quantity > 0)
                    @if ($showQuantityControls)
                        <div class="flex items-center mb-4">
                            <label for="quantity" class="text-gray-700 font-medium ml-4">الكمية:</label>
                            <div class="flex items-center border border-gray-300 rounded-md">
                                <button type="button" onclick="callMethod('product-detail', 'decrementQuantity')"
                                    class="px-3 py-1 text-gray-500 hover:text-primary-500 focus:outline-none transition-colors duration-200 hover:bg-gray-100">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" id="quantity-input" value="{{ $quantity }}" min="1"
                                    max="{{ $product->stock_quantity }}"
                                    onchange="callMethod('product-detail', 'set', 'quantity', parseInt(this.value))"
                                    class="w-16 text-center border-0 focus:outline-none focus:ring-0 font-medium">
                                <button type="button" onclick="callMethod('product-detail', 'incrementQuantity')"
                                    class="px-3 py-1 text-gray-500 hover:text-primary-500 focus:outline-none transition-colors duration-200 hover:bg-gray-100">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    @endif

                    <div class="flex flex-wrap gap-4">
                        <!-- مكون إدارة السلة باستخدام Livewire -->
                        <div class="flex-1">
                            <div
                                class="bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                                <livewire:cart-manager :product-id="$product->id ?? $productId" :quantity="$quantity" />
                            </div>
                        </div>

                        <!-- مكون إدارة المفضلة باستخدام Livewire -->
                        <div>
                            <div
                                class="bg-white border-2 border-gray-200 rounded-lg shadow-md hover:shadow-lg hover:border-red-300 transition-all duration-300">
                                <livewire:wishlist-manager :product-id="$product->id ?? $productId" />
                            </div>
                        </div>
                    </div>
                @elseif ($product)
                    <div class="mb-8">
                        <button disabled
                            class="bg-gray-300 text-gray-500 px-6 py-3 rounded-full font-medium cursor-not-allowed">
                            <i class="fas fa-shopping-cart ml-2"></i>
                            غير متوفر حاليًا
                        </button>
                    </div>
                @endif
            </div>
        @endif

        @if (!$compact)
            <!-- معلومات إضافية -->
            <div class="additional-info space-y-3 mt-4">
                @if ($product && $product->stock_quantity > 0)
                    <div class="flex items-center text-green-600">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                        <span class="text-sm">{{ __('متوفر في المخزن') }} ({{ $product->stock_quantity }}
                            {{ __('قطعة') }})</span>
                    </div>
                @elseif($product)
                    <div class="flex items-center text-red-600">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12">
                            </path>
                        </svg>
                        <span class="text-sm">{{ __('غير متوفر حالياً') }}</span>
                    </div>
                @endif

                <!-- خيار الاستلام من المتجر -->
                <x-feature-check feature="local_pickup" :show-message="false">
                    <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            <span class="text-sm text-green-800">{{ __('متاح للاستلام من المتجر') }}</span>
                        </div>
                    </div>
                </x-feature-check>
            </div>
        @endif
    </x-feature-check>

    <!-- رسالة وضع العرض فقط -->
    <x-feature-check feature="shopping" :show-message="true">
        <!-- سيتم عرض رسالة تلقائية من feature-check -->
    </x-feature-check>
</div>
