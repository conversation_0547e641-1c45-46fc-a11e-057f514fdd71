@props(['productId' => null, 'quantity' => 1, 'text' => null, 'class' => '', 'icon' => true])

@php
    $buttonText = $text ?? __('إضافة إلى السلة');
    $buttonClass =
        $class ?: 'bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition duration-200';
@endphp

<x-feature-check feature="shopping" :show-message="false">
    <button type="button" onclick="addToCart({{ $productId }}, {{ $quantity }})" class="{{ $buttonClass }}"
        data-product-id="{{ $productId }}" data-quantity="{{ $quantity }}">
        @if ($icon)
            <div class="flex items-center justify-center gap-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01">
                    </path>
                </svg>
                <span>{{ $buttonText }}</span>
            </div>
        @else
            {{ $buttonText }}
        @endif
    </button>
</x-feature-check>

<x-feature-check feature="shopping" :show-message="true">
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd"></path>
                </svg>
            </div>
            {{-- <div class="mr-3">
                <p class="text-sm text-yellow-800">
                    {{ __('عذراً، الموقع في وضع العرض فقط حالياً. لا يمكن إضافة المنتجات إلى السلة.') }}
                </p>
            </div> --}}
        </div>
    </div>
</x-feature-check>

@push('scripts')
    <script>
        function addToCart(productId, quantity = 1) {
            // التحقق من وضع العرض فقط قبل الإضافة
            fetch('/api/check-display-mode')
                .then(response => response.json())
                .then(data => {
                    if (data.display_only_mode) {
                        alert('{{ __('عذراً، الموقع في وضع العرض فقط حالياً. لا يمكن إتمام عمليات الشراء.') }}');
                        return;
                    }

                    // إضافة المنتج إلى السلة
                    fetch(`/cart/add/${productId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                    'content')
                            },
                            body: JSON.stringify({
                                quantity: quantity
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // تحديث عداد السلة
                                updateCartCount();

                                // إظهار رسالة نجاح
                                showNotification('{{ __('تم إضافة المنتج إلى السلة بنجاح') }}', 'success');
                            } else {
                                showNotification(data.message || '{{ __('حدث خطأ أثناء إضافة المنتج') }}', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('{{ __('حدث خطأ أثناء إضافة المنتج') }}', 'error');
                        });
                })
                .catch(error => {
                    console.error('Error checking display mode:', error);
                    showNotification('{{ __('حدث خطأ أثناء التحقق من حالة الموقع') }}', 'error');
                });
        }

        function updateCartCount() {
            fetch('/cart/count')
                .then(response => response.json())
                .then(data => {
                    const cartCountElements = document.querySelectorAll('.cart-count');
                    cartCountElements.forEach(element => {
                        element.textContent = data.count;
                        if (data.count > 0) {
                            element.classList.remove('hidden');
                        } else {
                            element.classList.add('hidden');
                        }
                    });
                })
                .catch(error => {
                    console.error('Error updating cart count:', error);
                });
        }

        function showNotification(message, type = 'info') {
            // إنشاء إشعار
            const notification = document.createElement('div');
            notification.className =
                `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

            if (type === 'success') {
                notification.classList.add('bg-green-500', 'text-white');
            } else if (type === 'error') {
                notification.classList.add('bg-red-500', 'text-white');
            } else {
                notification.classList.add('bg-blue-500', 'text-white');
            }

            notification.innerHTML = `
        <div class="flex items-center">
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `;

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // إخفاء الإشعار بعد 5 ثوان
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 5000);
        }
    </script>
@endpush
