@props(['class' => ''])

<nav class="{{ $class }}">
    <div class="flex items-center space-x-8">
        <!-- الروابط الأساسية -->
        <a href="{{ route('home') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
            {{ __('الرئيسية') }}
        </a>
        
        <a href="{{ route('products.index') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
            {{ __('المنتجات') }}
        </a>
        
        <a href="{{ route('categories.index') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
            {{ __('الفئات') }}
        </a>
        
        <!-- روابط متعلقة بالشراء (تظهر فقط إذا لم يكن وضع العرض فقط مفعل) -->
        <x-feature-check feature="shopping" :show-message="false">
            <a href="{{ route('cart.index') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
                {{ __('السلة') }}
            </a>
            
            <a href="{{ route('checkout.index') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
                {{ __('إتمام الطلب') }}
            </a>
        </x-feature-check>
        
        <!-- رابط المفضلة (يظهر حسب الإعدادات) -->
        <x-feature-check feature="wishlist" :show-message="false">
            <a href="{{ route('wishlist.index') }}" class="text-gray-700 hover:text-red-600 transition duration-200">
                {{ __('المفضلة') }}
            </a>
        </x-feature-check>
        
        <!-- روابط أخرى -->
        <a href="{{ route('about') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
            {{ __('من نحن') }}
        </a>
        
        <a href="{{ route('contact') }}" class="text-gray-700 hover:text-blue-600 transition duration-200">
            {{ __('اتصل بنا') }}
        </a>
    </div>
</nav>
