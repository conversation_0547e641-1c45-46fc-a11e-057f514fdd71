@props(['class' => '', 'showCount' => true])

@php
    $linkClass = $class ?: 'relative text-gray-700 hover:text-blue-600 transition duration-200';
    $cartCount = session('cart_count', 0);
@endphp

<x-feature-check feature="shopping" :show-message="false">
    <a href="{{ route('cart.index') }}" class="{{ $linkClass }}">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
        </svg>
        
        @if($showCount && $cartCount > 0)
        <span class="cart-count absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {{ $cartCount }}
        </span>
        @else
        <span class="cart-count absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">
            0
        </span>
        @endif
    </a>
</x-feature-check>

<x-feature-check feature="shopping" :show-message="false">
    <!-- إذا كان وضع العرض فقط مفعل، لا نعرض رابط السلة -->
</x-feature-check>
