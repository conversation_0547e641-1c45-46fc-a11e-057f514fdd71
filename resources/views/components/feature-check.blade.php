@props(['feature', 'message' => null, 'showMessage' => true])

@php
    $isEnabled = false;
    $defaultMessage = '';

    // التأكد من وجود إعدادات السوبر أدمن
    if (isset($superAdminSettings)) {
        switch ($feature) {
            case 'ratings':
                $isEnabled = $superAdminSettings->showRatings();
                $defaultMessage = 'عذراً، ميزة التقييمات غير متاحة حالياً.';
                break;
            case 'wishlist':
                $isEnabled = $superAdminSettings->showWishlist();
                $defaultMessage = 'عذراً، ميزة المفضلة غير متاحة حالياً.';
                break;
            case 'guest_checkout':
                $isEnabled = $superAdminSettings->isGuestCheckoutEnabled();
                $defaultMessage = 'عذراً، الشراء كزائر غير متاح حالياً. يرجى تسجيل الدخول أولاً.';
                break;
            case 'local_pickup':
                $isEnabled = $superAdminSettings->isLocalPickupEnabled();
                $defaultMessage = 'عذراً، خدمة الاستلام من المتجر غير متاحة حالياً.';
                break;
            case 'social_login':
                $isEnabled = $superAdminSettings->isSocialLoginEnabled();
                $defaultMessage = 'عذراً، تسجيل الدخول بوسائل التواصل الاجتماعي غير متاح حالياً.';
                break;
            case 'social_sharing':
                $isEnabled = $superAdminSettings->isSocialSharingEnabled();
                $defaultMessage = 'عذراً، مشاركة المنتجات غير متاحة حالياً.';
                break;
            case 'display_only':
                $isEnabled = !$superAdminSettings->isDisplayOnlyModeEnabled();
                $defaultMessage = '';
                break;
            case 'shopping':
            case 'cart':
            case 'add_to_cart':
            case 'checkout':
            case 'purchase':
                $isEnabled = !$superAdminSettings->isDisplayOnlyModeEnabled();
                $defaultMessage = 'عذراً، الموقع في وضع العرض فقط حالياً. لا يمكن إتمام عمليات الشراء.';
                break;
            default:
                $isEnabled = true;
        }
    } else {
        // إذا لم تكن الإعدادات متاحة، استخدم القيم الافتراضية
        $isEnabled = !in_array($feature, ['shopping', 'cart', 'add_to_cart', 'checkout', 'purchase']);
    }

    $displayMessage = $message ?? $defaultMessage;
@endphp

@if ($isEnabled)
    {{ $slot }}
@else
    {{-- @if ($showMessage)
        <div class="feature-disabled-message bg-yellow-50 border border-yellow-200 rounded-lg p-4 my-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="mr-3">
                    <p class="text-sm text-yellow-800">
                        {{ $displayMessage }}
                    </p>
                </div>
            </div>
        </div>
    @endif --}}
@endif
