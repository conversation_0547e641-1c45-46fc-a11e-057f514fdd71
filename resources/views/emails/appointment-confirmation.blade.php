<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app()->getLocale() == 'ar' ? 'تأكيد موعدك' : 'Your Appointment Confirmation' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 20px 0;
        }
        .appointment-details {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: bold;
            color: #666;
        }
        .confirmation-code {
            background-color: #FFC107;
            color: #333;
            font-weight: bold;
            padding: 10px;
            text-align: center;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #999;
        }
        .button {
            display: inline-block;
            background-color: #FFC107;
            color: #333;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
        }
        .note {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 14px;
        }
        .rtl {
            direction: rtl;
            text-align: right;
        }
        .ltr {
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container {{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
        <div class="header">
            <img src="{{ asset('images/logo.png') }}" alt="Makkah Gold" class="logo">
            <h1>{{ app()->getLocale() == 'ar' ? 'تأكيد موعدك' : 'Your Appointment Confirmation' }}</h1>
        </div>
        
        <div class="content">
            <p>{{ app()->getLocale() == 'ar' ? 'عزيزي' : 'Dear' }} {{ $appointment->name }},</p>
            
            <p>
                {{ app()->getLocale() == 'ar' ? 'شكراً لحجز موعد مع مكة جولد. نحن نتطلع إلى رؤيتك قريباً!' : 'Thank you for scheduling an appointment with Makkah Gold. We look forward to seeing you soon!' }}
            </p>
            
            <div class="confirmation-code">
                {{ app()->getLocale() == 'ar' ? 'رمز التأكيد الخاص بك' : 'Your Confirmation Code' }}: {{ $appointment->confirmation_code }}
            </div>
            
            <div class="appointment-details">
                <h2>{{ app()->getLocale() == 'ar' ? 'تفاصيل الموعد' : 'Appointment Details' }}</h2>
                
                <div class="detail-row">
                    <span class="detail-label">{{ app()->getLocale() == 'ar' ? 'الفرع' : 'Store' }}:</span>
                    <span>{{ $appointment->store_name }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">{{ app()->getLocale() == 'ar' ? 'التاريخ' : 'Date' }}:</span>
                    <span>{{ $appointment->appointment_date->format('l, F j, Y') }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">{{ app()->getLocale() == 'ar' ? 'الوقت' : 'Time' }}:</span>
                    <span>{{ date('h:i A', strtotime($appointment->appointment_time)) }}</span>
                </div>
                
                <div class="detail-row">
                    <span class="detail-label">{{ app()->getLocale() == 'ar' ? 'الغرض' : 'Purpose' }}:</span>
                    <span>{{ $appointment->purpose_name }}</span>
                </div>
            </div>
            
            <div class="note">
                <p><strong>{{ app()->getLocale() == 'ar' ? 'ملاحظة مهمة' : 'Important Note' }}:</strong></p>
                <p>
                    {{ app()->getLocale() == 'ar' ? 'يرجى الاحتفاظ برمز التأكيد هذا في حالة الحاجة إلى تعديل أو إلغاء موعدك. يرجى الوصول قبل 10 دقائق من موعدك المحدد.' : 'Please keep this confirmation code in case you need to modify or cancel your appointment. Please arrive 10 minutes before your scheduled time.' }}
                </p>
            </div>
            
            <p>
                {{ app()->getLocale() == 'ar' ? 'إذا كنت بحاجة إلى إلغاء أو إعادة جدولة موعدك، يرجى الاتصال بنا على الرقم +20 2 2391 0000 أو زيارة موقعنا الإلكتروني.' : 'If you need to cancel or reschedule your appointment, please contact us at +20 2 2391 0000 or visit our website.' }}
            </p>
            
            <p>
                {{ app()->getLocale() == 'ar' ? 'نتطلع إلى خدمتك قريباً!' : 'We look forward to serving you soon!' }}
            </p>
            
            <p>
                {{ app()->getLocale() == 'ar' ? 'مع أطيب التحيات،' : 'Best regards,' }}<br>
                {{ app()->getLocale() == 'ar' ? 'فريق مكة جولد' : 'The Makkah Gold Team' }}
            </p>
        </div>
        
        <div class="footer">
            <p>&copy; {{ date('Y') }} {{ app()->getLocale() == 'ar' ? 'مكة جولد للمجوهرات. جميع الحقوق محفوظة.' : 'Makkah Gold Jewelry. All rights reserved.' }}</p>
            <p>{{ app()->getLocale() == 'ar' ? 'هذا البريد الإلكتروني تم إرساله تلقائياً، يرجى عدم الرد عليه.' : 'This is an automated email, please do not reply.' }}</p>
        </div>
    </div>
</body>
</html>
