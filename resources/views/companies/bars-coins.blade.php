@extends('layouts.frontend')

@section('title', 'السبائك والعملات')

@section('content')
    <div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
        <!-- Header -->
        <div class="bg-gradient-to-r from-yellow-600 via-yellow-500 to-yellow-400 px-4 py-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 rtl:space-x-reverse">
                    <button onclick="history.back()" class="text-gray-800 hover:text-gray-900">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7">
                            </path>
                        </svg>
                    </button>
                    <h1 class="text-xl font-bold text-gray-800">السبائك والعملات</h1>
                </div>
                <div class="text-gray-800">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                        <path
                            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="px-4 py-4">
            <div class="flex bg-gray-800 rounded-full p-1 mb-6">
                <button id="bars-tab"
                    class="flex-1 py-3 px-6 text-center rounded-full transition-all duration-300 font-semibold tab-button active"
                    onclick="switchTab('bars')">
                    سبائك عيار 24
                </button>
                <button id="coins-tab"
                    class="flex-1 py-3 px-6 text-center rounded-full transition-all duration-300 font-semibold tab-button"
                    onclick="switchTab('coins')">
                    عملات عيار 21
                </button>
            </div>

            <!-- Content Container -->
            <div id="content-container">
                <!-- Bars Content -->
                <div id="bars-content" class="tab-content active">
                    @livewire('company-product-selector', ['type' => 'سبيكة'])
                </div>

                <!-- Coins Content -->
                <div id="coins-content" class="tab-content hidden">
                    @livewire('company-product-selector', ['type' => 'عملة'])
                </div>
            </div>
        </div>
    </div>

    <style>
        .tab-button {
            color: #9CA3AF;
            background: transparent;
        }

        .tab-button.active {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            color: #1F2937;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
        }

        .tab-content {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease-in-out;
        }

        .tab-content.hidden {
            display: none;
            opacity: 0;
            transform: translateY(10px);
        }
    </style>

    <script>
        function switchTab(type) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(type + '-tab').classList.add('active');

            // Update content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
                content.classList.remove('active');
            });

            const targetContent = document.getElementById(type + '-content');
            targetContent.classList.remove('hidden');
            targetContent.classList.add('active');
        }
    </script>
@endsection
