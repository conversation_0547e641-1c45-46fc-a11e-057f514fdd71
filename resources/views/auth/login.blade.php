@extends('layouts.frontend')

@section('title', __('Login'))

@section('content')
    <!-- Breadcrumb -->
    <div class="bg-gray-100 py-4">
        <div class="container mx-auto px-4">
            <div class="flex items-center text-sm">
                <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary-500">{{ __('Home') }}</a>
                <i class="fas fa-chevron-{{ app()->getLocale() == 'ar' ? 'left' : 'right' }} mx-2 text-gray-400 text-xs"></i>
                <span class="text-gray-800">{{ __('Login') }}</span>
            </div>
        </div>
    </div>

    <!-- Login Section -->
    <section class="py-16">
        <div class="container mx-auto px-4">
            <div class="max-w-md mx-auto">
                <h1 class="text-3xl font-bold text-center mb-8">{{ __('Login') }}</h1>
                
                <div class="bg-white rounded-lg shadow-sm p-8">
                    @if ($errors->any())
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
                            <ul class="list-disc list-inside">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                    
                    <form action="{{ route('login') }}" method="POST">
                        @csrf
                        
                        <div class="mb-6">
                            <label for="email" class="block text-gray-700 text-sm font-medium mb-2">{{ __('Email') }}</label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}" required autofocus
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        
                        <div class="mb-6">
                            <label for="password" class="block text-gray-700 text-sm font-medium mb-2">{{ __('Password') }}</label>
                            <input type="password" id="password" name="password" required
                                class="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>
                        
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="remember" name="remember" class="text-primary-500 focus:ring-primary-500 rounded">
                                <label for="remember" class="text-gray-700 text-sm mr-2">{{ __('Remember Me') }}</label>
                            </div>
                            
                            <a href="#" class="text-sm text-primary-500 hover:text-primary-600">{{ __('Forgot Password?') }}</a>
                        </div>
                        
                        <button type="submit" class="w-full bg-primary-500 hover:bg-primary-600 text-white py-3 rounded-md font-medium transition duration-300">
                            {{ __('Login') }}
                        </button>
                    </form>
                    
                    <div class="mt-6 text-center">
                        <p class="text-gray-600">
                            {{ __('Don\'t have an account?') }} 
                            <a href="{{ route('register') }}" class="text-primary-500 hover:text-primary-600 font-medium">{{ __('Register') }}</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection
