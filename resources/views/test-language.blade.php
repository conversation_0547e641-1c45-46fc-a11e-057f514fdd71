<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اللغة | Language Test</title>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen flex flex-col items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 class="text-2xl font-bold mb-6 text-center">{{ __('Language Test') }}</h1>

        <div class="mb-6">
            <p class="text-gray-700 mb-2"><strong>{{ __('Current Language') }}:</strong> {{ app()->getLocale() }}</p>
            <p class="text-gray-700 mb-2"><strong>{{ __('Direction') }}:</strong> {{ app()->getLocale() == 'ar' ? 'RTL' : 'LTR' }}</p>
            <p class="text-gray-700 mb-2"><strong>{{ __('Session Locale') }}:</strong> {{ session('locale', 'Not set') }}</p>
            <p class="text-gray-700 mb-2"><strong>{{ __('Cookie Locale') }}:</strong> <span id="cookie-locale">Checking...</span></p>
            <p class="text-gray-700 mb-2"><strong>App Locale:</strong> {{ app()->getLocale() }}</p>
            <p class="text-gray-700 mb-2"><strong>Config Locale:</strong> {{ config('app.locale') }}</p>
        </div>

        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-3">{{ __('Sample Translations') }}:</h2>
            <ul class="list-disc list-inside space-y-2">
                <li>{{ __('Home') }}</li>
                <li>{{ __('Products') }}</li>
                <li>{{ __('About Us') }}</li>
                <li>{{ __('Contact Us') }}</li>
                <li>{{ __('Language') }}</li>
            </ul>
        </div>

        <div class="flex justify-center space-x-4">
            <a href="{{ route('language.switch', 'ar') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block">
                العربية
            </a>
            <a href="{{ route('language.switch', 'en') }}" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded inline-block">
                English
            </a>
        </div>

        <div class="mt-8 text-center">
            <a href="{{ route('home') }}" class="text-blue-500 hover:underline">{{ __('Back to Home') }}</a>
        </div>
    </div>

    <script>
        // Display cookie locale
        document.addEventListener('DOMContentLoaded', function() {
            const getCookie = (name) => {
                const value = `; ${document.cookie}`;
                const parts = value.split(`; ${name}=`);
                if (parts.length === 2) return parts.pop().split(';').shift();
                return 'Not set';
            };

            document.getElementById('cookie-locale').textContent = getCookie('locale');
        });
    </script>
</body>
</html>
