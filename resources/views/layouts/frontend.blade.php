<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $settings->site_name ?? config('app.name') }} - @yield('title', 'الصفحة الرئيسية')</title>

    <!-- Meta Tags -->
    <meta name="description" content="@yield('meta_description', $settings->meta_description ?? ($settings->site_description ?? ''))">
    <meta name="keywords" content="@yield('meta_keywords', $settings->meta_keywords ?? '')">

    <!-- Favicon -->
    <link rel="icon" href="{{ \App\Helpers\ImageHelper::getSiteFavicon($settings->favicon) }}" type="image/png">

    <!-- Google Analytics -->
    @if ($settings->google_analytics_id)
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ $settings->google_analytics_id }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', '{{ $settings->google_analytics_id }}');
        </script>
    @endif

    <!-- Facebook Pixel -->
    @if ($settings->facebook_pixel_id)
        <script>
            ! function(f, b, e, v, n, t, s) {
                if (f.fbq) return;
                n = f.fbq = function() {
                    n.callMethod ?
                        n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                };
                if (!f._fbq) f._fbq = n;
                n.push = n;
                n.loaded = !0;
                n.version = '2.0';
                n.queue = [];
                t = b.createElement(e);
                t.async = !0;
                t.src = v;
                s = b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
                'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '{{ $settings->facebook_pixel_id }}');
            fbq('track', 'PageView');
        </script>
        <noscript>
            <img height="1" width="1" style="display:none"
                src="https://www.facebook.com/tr?id={{ $settings->facebook_pixel_id }}&ev=PageView&noscript=1" />
        </noscript>
    @endif

    <!-- Custom Header Scripts -->
    @if ($settings->custom_header_scripts)
        {!! $settings->custom_header_scripts !!}
    @endif

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap"
        rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Swiper.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <!-- Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer></script>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('css/logo-enhancements.css') }}">
    <link rel="stylesheet" href="{{ asset('css/mobile-enhancements.css') }}">

    <!-- Top Bar Custom Styles -->
    <style>
        /* إصلاح تداخل الهيدر مع المحتوى */
        body {
            padding-top: 48px !important;
        }

        header {
            margin-top: 0;
            /* إزالة المسافة تماماً */
            position: relative;
            z-index: 1000;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo-container {
            position: relative;
            z-index: 1001;
        }

        .logo-container h1 {
            margin: 0;
            line-height: 1.2;
        }

        .logo-container p {
            margin: 0;
            line-height: 1;
        }

        #top-bar {
            height: 48px;
            z-index: 9999;
        }

        /* تحسين المسافات للموبايل */
        @media (max-width: 640px) {
            body {
                padding-top: 50px !important;
            }

            header {
                margin-top: 0;
                /* إزالة المسافة للموبايل أيضاً */
            }

            #top-bar {
                height: 50px;
            }

            .logo-container h1 {
                font-size: 1rem !important;
            }

            .logo-container img {
                height: 2rem !important;
            }
        }

        /* تحسينات إضافية للهيدر والبحث */
        .main-navigation {
            min-height: 80px;
            display: flex;
            align-items: center;
        }

        .search-container {
            flex: 1;
            display: flex;
            justify-content: center;
            padding: 0 1rem;
        }

        @media (min-width: 768px) {
            .logo-container {
                min-width: 250px;
            }

            .search-container {
                max-width: 500px;
                padding: 0 2rem;
            }
        }

        /* تحسين مظهر شريط البحث */
        .search-input {
            transition: all 0.3s ease;
        }

        .search-input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3b82f6;
        }

        /* محاذاة البحث بدقة */
        .search-container .w-full {
            margin: 0 auto;
        }

        /* تحسين شكل البحث */
        .product-search-wrapper input {
            border-radius: 0.75rem;
            border: 2px solid #e5e7eb;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .product-search-wrapper input:focus {
            outline: none;
            border-color: #d4af37;
            box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
        }

        /* تحسين التباعد العام */
        .container {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        @media (min-width: 640px) {
            .container {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
        }

        /* تحسينات الشريط العلوي */
        .top-bar-sticky {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* تحسين الانتقالات */
        .top-bar-link {
            position: relative;
            overflow: hidden;
        }

        .top-bar-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.8);
            transition: width 0.3s ease;
        }

        .top-bar-link:hover::before {
            width: 100%;
        }

        /* تحسين أيقونات وسائل التواصل */
        .social-icon {
            transition: all 0.3s ease;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .social-icon:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        /* تحسين القائمة المنسدلة */
        .dropdown-container:hover .dropdown-menu {
            display: block !important;
            animation: fadeInDown 0.3s ease;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .top-bar-mobile {
                padding: 8px 0;
            }

            .social-icon {
                width: 28px;
                height: 28px;
            }

            /* تحسين عرض معلومات الاتصال على الأجهزة المحمولة */
            .contact-info-mobile {
                font-size: 0.875rem;
            }

            /* تحسين المسافات */
            .top-bar-container {
                padding: 0.5rem 1rem;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً */
        @media (max-width: 480px) {
            .top-bar-container {
                padding: 0.5rem 0.75rem;
            }

            .contact-info-mobile {
                font-size: 0.8rem;
            }

            .social-icon {
                width: 24px;
                height: 24px;
            }

            /* تقليل المسافات بين العناصر */
            .mobile-social-icons {
                gap: 0.5rem;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً (320px) */
        @media (max-width: 320px) {
            .top-bar-container {
                padding: 0.5rem;
            }

            .contact-info-mobile {
                font-size: 0.75rem;
            }

            .mobile-social-icons {
                gap: 0.25rem;
            }

            .social-icon {
                width: 20px;
                height: 20px;
            }
        }

        /* تأثير التمرير للشريط الثابت */
        .top-bar-scrolled {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        /* تحسينات زر واتساب العائم */
        .whatsapp-btn {
            position: relative;
            overflow: hidden;
        }

        .whatsapp-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .whatsapp-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        /* تأثير النبضة المحسن */
        @keyframes whatsapp-pulse {
            0% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
            }

            70% {
                transform: scale(1);
                box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
            }

            100% {
                transform: scale(0.95);
                box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
            }
        }

        .whatsapp-btn {
            animation: whatsapp-pulse 2s infinite;
        }

        /* تحسين الاستجابة للهواتف */
        @media (max-width: 768px) {
            #whatsapp-float {
                bottom: 20px;
                right: 20px;
            }

            .whatsapp-btn {
                width: 50px;
                height: 50px;
            }

            .whatsapp-btn i {
                font-size: 1.5rem;
            }

            /* إخفاء النص التوضيحي على الهواتف */
            #whatsapp-float .group-hover\:opacity-100 {
                display: none;
            }
        }

        /* تحسين الشريط العلوي للهواتف */
        @media (max-width: 640px) {
            body {
                padding-top: 3rem;
                /* 48px */
            }

            #top-bar {
                padding: 0.5rem 0;
                /* py-2 */
            }
        }

        /* تحسينات إضافية للأجهزة المحمولة */
        @media (max-width: 480px) {

            /* تحسين الشريط العلوي */
            .hero-slider .swiper-pagination {
                bottom: 1rem !important;
            }

            .hero-slider .swiper-pagination-bullet {
                width: 8px !important;
                height: 8px !important;
                margin: 0 3px !important;
            }

            /* تحسين النصوص في السلايدر للموبايل */
            .hero-slider .max-w-xl {
                max-width: 100% !important;
                padding: 0 1.5rem;
                text-align: center;
                margin: 0 auto;
            }

            .hero-slider h1 {
                font-size: 1.5rem !important;
                /* تصغير الخط للموبايل */
                line-height: 1.3 !important;
                margin-bottom: 0.75rem !important;
                font-weight: 700 !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
                letter-spacing: -0.01em;
            }

            .hero-slider p {
                font-size: 0.875rem !important;
                /* تصغير النص الوصفي */
                line-height: 1.4 !important;
                margin-bottom: 1.25rem !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
                opacity: 0.95;
                max-width: 90%;
                margin-left: auto;
                margin-right: auto;
            }

            .hero-slider .flex {
                flex-direction: column !important;
                gap: 0.5rem !important;
                align-items: center;
                justify-content: center;
            }

            .hero-slider a {
                width: auto !important;
                min-width: 150px;
                max-width: 200px;
                padding: 0.75rem 1.25rem !important;
                font-size: 0.875rem !important;
                font-weight: 600 !important;
                border-radius: 1.5rem !important;
                text-align: center;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                transition: all 0.3s ease;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .hero-slider a:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25);
            }

            /* تحسين التباعد العام للسلايدر */
            .hero-slider .container {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }

            /* تحسين موضع المحتوى */
            .hero-slider .absolute.inset-0.flex {
                align-items: center !important;
                justify-content: center !important;
                padding: 1rem 0;
            }

            /* تحسين المسافات العامة */
            .container {
                padding-left: 0.75rem;
                padding-right: 0.75rem;
            }

            /* تحسين النصوص للشاشات الصغيرة */
            .text-responsive {
                font-size: 0.875rem;
                line-height: 1.4;
            }

            /* تحسين الأزرار للمس */
            .btn-mobile {
                min-height: 44px;
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }

            /* تحسين البطاقات */
            .card-mobile {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }

            /* إخفاء عناصر غير ضرورية على الموبايل */
            .hidden-mobile {
                display: none !important;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً (أقل من 375px) */
        @media (max-width: 374px) {
            .hero-slider .swiper-slide .max-w-xl {
                max-width: 100% !important;
                padding: 0 0.75rem;
            }

            .hero-slider h1 {
                font-size: 1.25rem !important;
                /* أصغر للشاشات الضيقة */
                line-height: 1.3 !important;
                margin-bottom: 0.625rem !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9);
            }

            .hero-slider p {
                font-size: 0.8rem !important;
                /* أصغر للشاشات الضيقة */
                line-height: 1.4 !important;
                margin-bottom: 1rem !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.8);
                max-width: 85%;
                margin-left: auto;
                margin-right: auto;
            }

            .hero-slider .flex {
                flex-direction: column !important;
                gap: 0.625rem !important;
                align-items: center;
            }

            .hero-slider a {
                width: auto !important;
                min-width: 130px;
                max-width: 180px;
                padding: 0.625rem 1rem !important;
                font-size: 0.8rem !important;
                font-weight: 600 !important;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            /* تحسين المحاذاة للشاشات الصغيرة جداً */
            .hero-slider .container {
                padding-left: 0.5rem !important;
                padding-right: 0.5rem !important;
            }

            /* تحسين ارتفاع السلايدر للشاشات الصغيرة */
            .hero-slider .swiper-slide>div {
                min-height: 350px !important;
            }
        }

        /* تحسينات للأجهزة اللوحية والشاشات المتوسطة */
        @media (min-width: 481px) and (max-width: 768px) {
            .hero-slider h1 {
                font-size: 2.25rem !important;
                /* text-4xl */
                line-height: 1.1 !important;
                margin-bottom: 1.25rem !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }

            .hero-slider p {
                font-size: 1.125rem !important;
                /* text-lg */
                line-height: 1.4 !important;
                margin-bottom: 2rem !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
            }

            .hero-slider .max-w-xl {
                max-width: 600px !important;
                text-align: center;
                padding: 0 2rem;
            }

            .hero-slider a {
                padding: 1rem 2rem !important;
                font-size: 1rem !important;
                min-width: 200px;
            }
        }

        /* تحسين التمرير السلس */
        html {
            scroll-behavior: smooth;
        }

        /* تحسين عناصر التحكم في السلايدر */
        .hero-slider .swiper-button-next,
        .hero-slider .swiper-button-prev {
            color: white !important;
            background: rgba(0, 0, 0, 0.5) !important;
            border-radius: 50% !important;
            width: 44px !important;
            height: 44px !important;
            margin-top: -22px !important;
            transition: all 0.3s ease !important;
            z-index: 10 !important;
        }

        .hero-slider .swiper-button-next:hover,
        .hero-slider .swiper-button-prev:hover {
            background: rgba(0, 0, 0, 0.8) !important;
            transform: scale(1.1) !important;
        }

        .hero-slider .swiper-button-next::after,
        .hero-slider .swiper-button-prev::after {
            font-size: 16px !important;
            font-weight: bold !important;
        }

        .hero-slider .swiper-pagination {
            position: absolute !important;
            bottom: 20px !important;
            left: 50% !important;
            transform: translateX(-50%) !important;
            width: auto !important;
            z-index: 10 !important;
        }

        .hero-slider .swiper-pagination-bullet {
            background: rgba(255, 255, 255, 0.6) !important;
            opacity: 1 !important;
            width: 10px !important;
            height: 10px !important;
            margin: 0 4px !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
        }

        .hero-slider .swiper-pagination-bullet-active {
            background: white !important;
            transform: scale(1.2) !important;
        }

        /* التأكد من أن السلايدر يعمل */
        .hero-slider .swiper-wrapper {
            transition-timing-function: ease !important;
        }

        .hero-slider .swiper-slide {
            opacity: 1 !important;
            transform: none !important;
        }

        /* إخفاء الأسهم على الأجهزة المحمولة */
        @media (max-width: 640px) {

            .hero-slider .swiper-button-next,
            .hero-slider .swiper-button-prev {
                display: none !important;
            }
        }

        /* تحسين التفاعل مع اللمس */
        .touch-target {
            min-height: 44px;
            min-width: 44px;
        }

        /* تحسين الصور للأجهزة المحمولة */
        .responsive-image {
            max-width: 100%;
            height: auto;
        }

        /* تحسين الجداول للأجهزة المحمولة */
        @media (max-width: 640px) {
            .table-responsive {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }
        }

        /* تحسينات القائمة الجانبية المحمولة */
        #mobile-side-menu {
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            z-index: 10000 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
        }

        #mobile-side-menu>div {
            z-index: 10001 !important;
            position: relative;
        }

        /* التأكد من أن القائمة الجانبية تظهر فوق كل العناصر */
        @media (max-width: 768px) {
            #mobile-side-menu {
                z-index: 99999 !important;
            }
        }

        .side-menu-category {
            border-bottom: 1px solid #f3f4f6;
        }

        .side-menu-category:last-child {
            border-bottom: none;
        }

        .side-menu-subcategories {
            background-color: #f9fafb;
        }

        /* تحسين زر إغلاق القائمة */
        #close-side-menu {
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: background-color 0.2s;
        }

        #close-side-menu:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* تحسين أزرار التبديل في القائمة */
        .side-menu-toggle {
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }

        .side-menu-toggle:hover {
            background-color: #f3f4f6;
        }
    </style>

    <!-- Livewire Styles -->
    @livewireStyles()
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#FFF8E1',
                            100: '#FFECB3',
                            200: '#FFE082',
                            300: '#FFD54F',
                            400: '#FFCA28',
                            500: '#FFC107', // Amber
                            600: '#FFB300',
                            700: '#FFA000',
                            800: '#FF8F00',
                            900: '#FF6F00',
                        },
                        secondary: {
                            50: '#E3F2FD',
                            100: '#BBDEFB',
                            200: '#90CAF9',
                            300: '#64B5F6',
                            400: '#42A5F5',
                            500: '#2196F3', // Blue
                            600: '#1E88E5',
                            700: '#1976D2',
                            800: '#1565C0',
                            900: '#0D47A1',
                        },
                        gold: '#D4AF37',
                        silver: '#C0C0C0',
                        platinum: '#E5E4E2',
                    },
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif'],
                    },
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
        }

        .gold-gradient {
            background: linear-gradient(135deg, #D4AF37 0%, #F9F295 50%, #D4AF37 100%);
        }

        .silver-gradient {
            background: linear-gradient(135deg, #C0C0C0 0%, #E8E8E8 50%, #C0C0C0 100%);
        }

        .platinum-gradient {
            background: linear-gradient(135deg, #E5E4E2 0%, #F7F7F7 50%, #E5E4E2 100%);
        }

        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        /* تحسينات لتجربة المستخدم على الأجهزة المحمولة */
        @media (hover: none) {
            .product-card:hover {
                transform: none;
            }

            .product-card:active {
                transform: scale(0.98);
            }
        }

        .nav-link {
            position: relative;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #D4AF37;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .active-nav::after {
            width: 100%;
        }

        /* Hero Slider Styles */
        .hero-slider {
            position: relative;
            width: 100%;
            height: auto;
            overflow: hidden;
        }

        .hero-slider .swiper-wrapper {
            height: auto;
        }

        .hero-slider .swiper-slide {
            height: auto;
            display: block;
        }

        .hero-slider .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: rgba(255, 255, 255, 0.7);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .hero-slider .swiper-pagination-bullet-active {
            background: #FFC107;
            opacity: 1;
        }

        .hero-slider .swiper-button-next,
        .hero-slider .swiper-button-prev {
            color: #FFC107;
            background: rgba(0, 0, 0, 0.3);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-slider .swiper-button-next:after,
        .hero-slider .swiper-button-prev:after {
            font-size: 18px;
        }

        /* CSS أساسي للسلايدر للتأكد من الظهور */
        .swiper {
            width: 100%;
            height: 100%;
        }

        .swiper-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            z-index: 1;
            display: flex;
            transition-property: transform;
            box-sizing: content-box;
        }

        .swiper-slide {
            flex-shrink: 0;
            width: 100%;
            height: 100%;
            position: relative;
            transition-property: transform;
        }

        /* تأكد من ظهور الصور */
        .hero-slider img {
            display: block !important;
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
        }

        /* تأكد من ظهور المحتوى */
        .hero-slider .container {
            position: relative !important;
            z-index: 2 !important;
        }

        /* تحسينات عامة لنصوص السلايدر */
        .hero-slider h1 {
            font-family: inherit;
            font-weight: 700;
            letter-spacing: -0.025em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        }

        .hero-slider p {
            font-weight: 400;
            opacity: 0.95;
            max-width: 95%;
            margin-left: auto;
            margin-right: auto;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        }

        .hero-slider .swiper-slide {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* تحسين الخلفية المتدرجة */
        .hero-slider .bg-gradient-to-r {
            background: linear-gradient(to right,
                    rgba(0, 0, 0, 0.75) 0%,
                    rgba(0, 0, 0, 0.55) 40%,
                    rgba(0, 0, 0, 0.35) 70%,
                    rgba(0, 0, 0, 0.15) 100%) !important;
        }

        /* تحسين أزرار التنقل للموبايل */
        @media (max-width: 640px) {

            .hero-slider .swiper-button-next,
            .hero-slider .swiper-button-prev {
                display: none !important;
            }

            .hero-slider .swiper-pagination {
                position: absolute;
                bottom: 1.5rem !important;
                left: 50%;
                transform: translateX(-50%);
                z-index: 10;
            }
        }

        /* إصلاح مشكلة عدم ظهور السلايدر */
        section.relative {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        .hero-slider {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            min-height: 400px;
        }

        /* تأكد من ظهور المحتوى بشكل صحيح */
        .hero-slider .relative {
            display: block !important;
            min-height: 400px;
        }

        @media (min-width: 640px) {
            .hero-slider .relative {
                min-height: 500px;
            }
        }

        @media (min-width: 768px) {
            .hero-slider .relative {
                min-height: 600px;
            }
        }

        /* تأكد من عدم إخفاء السلايدر بواسطة أي CSS آخر */
        .hero-slider {
            display: block !important;
            position: relative !important;
            z-index: 1 !important;
        }

        .hero-slider .swiper-wrapper {
            display: flex !important;
        }

        .hero-slider .swiper-slide {
            display: block !important;
            opacity: 1 !important;
        }

        /* إصلاح مشاكل التداخل مع الهيدر */
        main {
            position: relative;
            z-index: 1;
        }

        .hero-slider {
            margin-top: 0 !important;
        }

        /* تحسينات إضافية للسلايدر على الموبايل */
        @media (max-width: 640px) {
            .hero-slider {
                min-height: 350px !important;
            }

            .hero-slider .swiper-slide {
                min-height: 350px !important;
            }

            .hero-slider .relative {
                min-height: 350px !important;
                display: flex !important;
                align-items: center !important;
            }

            /* تحسين محاذاة المحتوى */
            .hero-slider .absolute.inset-0.flex.items-center {
                align-items: center !important;
                justify-content: center !important;
                text-align: center !important;
            }

            /* تحسين الخلفية المتدرجة على الموبايل */
            .hero-slider .bg-gradient-to-r {
                background: linear-gradient(to bottom,
                        rgba(0, 0, 0, 0.6) 0%,
                        rgba(0, 0, 0, 0.4) 50%,
                        rgba(0, 0, 0, 0.6) 100%) !important;
            }
        }

        /* تحسينات نهائية للنص في السلايدر */
        .hero-slider h1,
        .hero-slider p,
        .hero-slider a {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* تحسين قابلية القراءة */
        .hero-slider h1 {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .hero-slider p {
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        /* تحسين تجربة اللمس على الموبايل */
        @media (max-width: 640px) {
            .hero-slider a {
                -webkit-tap-highlight-color: transparent;
                tap-highlight-color: transparent;
                touch-action: manipulation;
            }

            /* تحسين المسافات بين العناصر */
            .hero-slider .max-w-xl>*+* {
                margin-top: 0.75rem;
            }
        }

        /* تحسينات القوائم المنسدلة */
        .dropdown-container {
            position: relative;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            z-index: 1000;
        }

        .dropdown-container:hover .dropdown-menu {
            display: block;
        }

        /* إضافة مساحة للقائمة المنسدلة لتجنب اختفائها عند تحريك المؤشر */
        .dropdown-menu::before {
            content: '';
            position: absolute;
            top: -10px;
            right: 0;
            left: 0;
            height: 10px;
        }

        /* تأكد من أن القائمة المنسدلة تبقى مرئية عند التحويم عليها */
        .dropdown-menu:hover {
            display: block;
        }

        /* تحسينات للأجهزة المحمولة */
        @media (max-width: 768px) {
            .dropdown-menu {
                position: static;
                box-shadow: none;
                width: 100%;
                margin-top: 0;
                padding-right: 1rem;
                background-color: #f9f9f9;
            }

            /* تحسين عرض المنتجات على الأجهزة المحمولة */
            .product-grid {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 0.5rem !important;
            }

            .product-card {
                padding: 0.5rem !important;
            }

            .product-card h3 {
                font-size: 0.875rem !important;
            }

            .product-card .price {
                font-size: 0.875rem !important;
            }

            /* تحسين عرض الصور على الأجهزة المحمولة */
            .product-image-container {
                height: 120px !important;
            }

            /* تحسين عرض الأزرار على الأجهزة المحمولة */
            .btn-sm-block {
                width: 100% !important;
                margin-bottom: 0.5rem !important;
                text-align: center !important;
            }

            /* تحسين عرض النص على الأجهزة المحمولة */
            .text-sm-center {
                text-align: center !important;
            }

            /* تحسين عرض الهوامش على الأجهزة المحمولة */
            .sm-my-2 {
                margin-top: 0.5rem !important;
                margin-bottom: 0.5rem !important;
            }

            /* تحسين عرض الشبكة على الأجهزة المحمولة */
            .sm-grid-cols-1 {
                grid-template-columns: 1fr !important;
            }

            /* تحسين عرض الشبكة على الأجهزة المحمولة */
            .sm-grid-cols-2 {
                grid-template-columns: repeat(2, 1fr) !important;
            }

            /* تحسين عرض الفواصل على الأجهزة المحمولة */
            .sm-gap-2 {
                gap: 0.5rem !important;
            }

            /* تحسين عرض الحاويات على الأجهزة المحمولة */
            .container {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }

            /* تحسين عرض الأقسام على الأجهزة المحمولة */
            .section-title {
                font-size: 1.25rem !important;
                margin-bottom: 1rem !important;
            }

            /* تحسين عرض التذييل على الأجهزة المحمولة */
            footer .grid {
                grid-template-columns: 1fr !important;
                gap: 1.5rem !important;
            }
        }
    </style>

    <!-- Blog Enhancements CSS -->
    <link rel="stylesheet" href="{{ asset('css/blog-enhancements.css') }}">

    @yield('styles')
    @stack('styles')
</head>

<body class="bg-gray-50 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white shadow-md">
        <!-- Top Bar - Sticky -->
        <div id="top-bar"
            class="bg-primary-500 text-white py-2 fixed top-0 left-0 right-0 z-[9999] shadow-md top-bar-sticky transition-all duration-300 min-h-[48px] sm:min-h-[48px] flex items-center">
            <div class="container mx-auto px-4 flex justify-between items-center top-bar-container">
                <!-- Contact Info - Left Side -->
                <div class="flex items-center space-x-4 space-x-reverse flex-shrink-0">
                    @php
                        $phone = $settings->contact_phone ?? '+201000404777';
                        // تصحيح موضع علامة + في رقم الهاتف
                        if (!str_starts_with($phone, '+')) {
                            // إذا كان الرقم يبدأ بـ 0، نحوله إلى +2
                            if (str_starts_with($phone, '0')) {
                                $phone = '+2' . substr($phone, 1);
                            } else {
                                $phone = '+' . ltrim($phone, '+');
                            }
                        }
                        // التأكد من أن الرقم يبدأ بـ +2
                        if (str_starts_with($phone, '+20') && strlen($phone) < 13) {
                            // الرقم صحيح
                        } elseif (str_starts_with($phone, '+2') && !str_starts_with($phone, '+20')) {
                            $phone = '+20' . substr($phone, 2);
                        }

                        // إنشاء رقم للعرض مع + في النهاية
                        $displayPhone = ltrim($phone, '+') . '+';
                    @endphp
                    <a href="tel:{{ $phone }}"
                        class="text-sm hover:text-white/80 transition-colors duration-200 top-bar-link flex items-center contact-info-mobile">
                        <i class="fas fa-phone-alt ml-1 text-white"></i>
                        <span class="hidden sm:inline font-medium">{{ $displayPhone }}</span>
                        <span class="sm:hidden font-medium">{{ $displayPhone }}</span>
                    </a>
                    <a href="mailto:{{ $settings->contact_email ?? '<EMAIL>' }}"
                        class="hidden sm:flex text-sm hover:text-white/80 transition-colors duration-200 top-bar-link">
                        <i class="fas fa-envelope ml-1"></i> {{ $settings->contact_email ?? '<EMAIL>' }}
                    </a>

                </div>

                <!-- Center - Social Media Icons -->
                <div class="hidden lg:flex items-center space-x-6 space-x-reverse flex-1 justify-center">
                    @if ($settings->facebook_url)
                        <a href="{{ $settings->facebook_url }}" target="_blank"
                            class="text-white hover:text-blue-200 transition-colors duration-200 social-icon"
                            title="فيسبوك">
                            <i class="fab fa-facebook-f text-lg"></i>
                        </a>
                    @endif
                    @if ($settings->instagram_url)
                        <a href="{{ $settings->instagram_url }}" target="_blank"
                            class="text-white hover:text-pink-200 transition-colors duration-200 social-icon"
                            title="إنستغرام">
                            <i class="fab fa-instagram text-lg"></i>
                        </a>
                    @endif
                    @if ($settings->twitter_url)
                        <a href="{{ $settings->twitter_url }}" target="_blank"
                            class="text-white hover:text-blue-300 transition-colors duration-200 social-icon"
                            title="تويتر">
                            <i class="fab fa-twitter text-lg"></i>
                        </a>
                    @endif
                    @if ($settings->youtube_url)
                        <a href="{{ $settings->youtube_url }}" target="_blank"
                            class="text-white hover:text-red-200 transition-colors duration-200 social-icon"
                            title="يوتيوب">
                            <i class="fab fa-youtube text-lg"></i>
                        </a>
                    @endif
                    @if ($settings->tiktok_url)
                        <a href="{{ $settings->tiktok_url }}" target="_blank"
                            class="text-white hover:text-gray-200 transition-colors duration-200 social-icon"
                            title="تيك توك">
                            <i class="fab fa-tiktok text-lg"></i>
                        </a>
                    @endif
                    @if ($settings->linkedin_url)
                        <a href="{{ $settings->linkedin_url }}" target="_blank"
                            class="text-white hover:text-blue-300 transition-colors duration-200 social-icon"
                            title="لينكد إن">
                            <i class="fab fa-linkedin-in text-lg"></i>
                        </a>
                    @endif
                </div>

                <!-- Right Side - Quick Links & Language -->
                <div class="flex items-center space-x-4 space-x-reverse flex-shrink-0">
                    <!-- Quick Links - Hidden on mobile -->
                    <div class="hidden xl:flex items-center space-x-3 space-x-reverse">
                        <a href="{{ route('stores') }}"
                            class="text-sm hover:text-white/80 transition-colors duration-200">
                            <i class="fas fa-map-marker-alt ml-1"></i> {{ __('Stores') }}
                        </a>
                        <a href="{{ route('about') }}"
                            class="text-sm hover:text-white/80 transition-colors duration-200">
                            <i class="fas fa-info-circle ml-1"></i> {{ __('About Us') }}
                        </a>
                        <a href="{{ route('contact') }}"
                            class="text-sm hover:text-white/80 transition-colors duration-200">
                            <i class="fas fa-phone-alt ml-1"></i> {{ __('Contact') }}
                        </a>
                    </div>

                    <!-- Mobile Social Media Icons - Only visible on mobile -->
                    <div class="flex lg:hidden items-center space-x-3 space-x-reverse mobile-social-icons">
                        @if ($settings->facebook_url)
                            <a href="{{ $settings->facebook_url }}" target="_blank"
                                class="text-white hover:text-blue-200 transition-colors duration-200 social-icon">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        @endif
                        @if ($settings->instagram_url)
                            <a href="{{ $settings->instagram_url }}" target="_blank"
                                class="text-white hover:text-pink-200 transition-colors duration-200 social-icon">
                                <i class="fab fa-instagram"></i>
                            </a>
                        @endif
                        @if ($settings->twitter_url)
                            <a href="{{ $settings->twitter_url }}" target="_blank"
                                class="text-white hover:text-blue-300 transition-colors duration-200 social-icon">
                                <i class="fab fa-twitter"></i>
                            </a>
                        @endif
                    </div>

                    <!-- Mobile Menu Toggle Button -->
                    <button id="mobile-menu-toggle"
                        class="md:hidden text-white focus:outline-none ml-2 p-1 rounded hover:bg-white/10 transition-colors duration-200"
                        style="z-index: 9999; position: relative;">
                        <i class="fas fa-bars text-lg"></i>
                    </button>
                    @php
                        // استخدام متغير $settings الذي تم مشاركته مع العرض
                        $enableMultilingual = isset($settings) && $settings ? $settings->enable_multilingual : true;
                        $languages = \App\Models\Language::where('is_active', true)->get();
                    @endphp

                    <!-- Language Switcher - Hidden on mobile -->
                    @if ($enableMultilingual && $languages->count() > 1)
                        <div class="hidden md:block relative group dropdown-container">
                            <button
                                class="text-sm hover:text-white/80 flex items-center dropdown-toggle transition-colors duration-200">
                                <i class="fas fa-globe ml-1"></i> {{ __('Language') }}
                                <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </button>
                            <div
                                class="absolute z-10 {{ app()->getLocale() == 'ar' ? 'right-0' : 'left-0' }} mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden dropdown-menu">
                                @foreach ($languages as $language)
                                    <a href="{{ route('language.switch', $language->code) }}"
                                        class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 {{ app()->getLocale() == $language->code ? 'bg-gray-100 font-semibold' : '' }}">
                                        <i class="fas fa-flag ml-2"></i>{{ $language->native_name }}
                                        @if (app()->getLocale() == $language->code)
                                            <i class="fas fa-check mr-2 text-green-600"></i>
                                        @endif
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="container mx-auto px-3 sm:px-4 py-4 sm:py-5">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <a href="{{ route('home') }}" class="logo-container flex items-center flex-shrink-0">
                    <img src="{{ \App\Helpers\ImageHelper::getSiteLogo($settings->logo) }}"
                        alt="{{ $settings->site_name ?? 'مكة جولد' }}"
                        class="frontend-logo site-logo h-8 sm:h-10 w-auto" loading="eager" decoding="async">
                    <div class="mr-2 sm:mr-3">
                        <h1 class="text-sm sm:text-lg md:text-xl lg:text-2xl font-bold text-gold">
                            {{ $settings->site_name ?? 'مكة جولد' }}</h1>
                        <p class="text-xs text-gray-500 hidden sm:block">
                            {{ $settings->site_description ?? 'للمجوهرات الراقية' }}</p>
                    </div>
                </a>

                <!-- Search Bar -->
                <div class="hidden md:flex w-1/2 justify-center px-4 search-container">
                    <div class="w-full max-w-md">
                        @livewire('product-search')
                    </div>
                </div>

                <!-- Right Navigation -->
                <div class="flex items-center space-x-3 sm:space-x-6 space-x-reverse flex-shrink-0">
                    <!-- User Actions Component -->
                    <livewire:header-component />
                </div>
            </div>

            <!-- Mobile Search (Always visible on mobile) -->
            <div class="md:hidden mt-2" id="mobile-search">
                <div class="relative">
                    <form action="{{ route('search') }}" method="GET" class="relative">
                        <div class="flex">
                            <input type="text" name="query" id="mobile-search-input"
                                placeholder="ابحث عن منتجات..."
                                class="w-full py-2.5 sm:py-3 px-4 border border-gray-300 rounded-r-full focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm sm:text-base"
                                autocomplete="off">
                            <button type="submit"
                                class="bg-primary-500 text-white px-3 sm:px-4 rounded-l-full hover:bg-primary-600 transition duration-300 flex items-center justify-center min-w-[44px]">
                                <i class="fas fa-search text-sm sm:text-base"></i>
                            </button>
                        </div>
                    </form>
                    <div id="mobile-search-results"
                        class="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-60 overflow-y-auto hidden">
                        <ul class="py-1" id="mobile-results-list">
                            <!-- نتائج البحث ستظهر هنا -->
                        </ul>
                    </div>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // وظيفة البحث مع الإكمال التلقائي
                    function setupSearch(inputId, resultsId, resultsListId) {
                        const searchInput = document.getElementById(inputId);
                        const searchResults = document.getElementById(resultsId);
                        const resultsList = document.getElementById(resultsListId);

                        if (!searchInput || !searchResults || !resultsList) return;

                        let debounceTimer;

                        searchInput.addEventListener('focus', function() {
                            if (resultsList.children.length > 0) {
                                searchResults.classList.remove('hidden');
                            }
                        });

                        searchInput.addEventListener('input', function() {
                            clearTimeout(debounceTimer);

                            const query = this.value.trim();

                            if (query.length < 2) {
                                searchResults.classList.add('hidden');
                                return;
                            }

                            debounceTimer = setTimeout(function() {
                                fetch(`/api/search?query=${encodeURIComponent(query)}`)
                                    .then(response => response.json())
                                    .then(data => {
                                        resultsList.innerHTML = '';

                                        if (data.length === 0) {
                                            const li = document.createElement('li');
                                            li.className = 'px-4 py-2 text-sm text-gray-500';
                                            li.textContent = 'لا توجد نتائج';
                                            resultsList.appendChild(li);
                                        } else {
                                            data.forEach(product => {
                                                const li = document.createElement('li');
                                                li.className = 'search-result-item';

                                                const button = document.createElement('button');
                                                button.className =
                                                    'w-full text-right block px-4 py-2 text-sm hover:bg-gray-100';
                                                button.onclick = function() {
                                                    window.location.href =
                                                        `/product/${product.slug}`;
                                                };

                                                const flexDiv = document.createElement('div');
                                                flexDiv.className = 'flex items-center';

                                                let imgContainer;
                                                if (product.image) {
                                                    imgContainer = document.createElement(
                                                        'img');
                                                    imgContainer.src =
                                                        `/storage/${product.image}`;
                                                    imgContainer.alt = product.name_ar;
                                                    imgContainer.className =
                                                        'w-10 h-10 object-cover rounded-md ml-3';
                                                } else {
                                                    imgContainer = document.createElement(
                                                        'div');
                                                    imgContainer.className =
                                                        'w-10 h-10 bg-gray-200 rounded-md ml-3 flex items-center justify-center';

                                                    const icon = document.createElement('i');
                                                    icon.className =
                                                        'fas fa-image text-gray-400';
                                                    imgContainer.appendChild(icon);
                                                }

                                                const textDiv = document.createElement('div');

                                                const nameDiv = document.createElement('div');
                                                nameDiv.className = 'font-medium';
                                                nameDiv.textContent = product.name_ar;

                                                const priceDiv = document.createElement('div');
                                                priceDiv.className = 'text-gray-500 text-xs';
                                                priceDiv.textContent =
                                                    `${product.price.toLocaleString()} ج.م`;

                                                textDiv.appendChild(nameDiv);
                                                textDiv.appendChild(priceDiv);

                                                flexDiv.appendChild(imgContainer);
                                                flexDiv.appendChild(textDiv);

                                                button.appendChild(flexDiv);
                                                li.appendChild(button);
                                                resultsList.appendChild(li);
                                            });
                                        }

                                        searchResults.classList.remove('hidden');
                                    })
                                    .catch(error => {
                                        console.error('Error fetching search results:', error);
                                    });
                            }, 300);
                        });

                        document.addEventListener('click', function(event) {
                            if (!searchInput.contains(event.target) && !searchResults.contains(event.target)) {
                                searchResults.classList.add('hidden');
                            }
                        });
                    }

                    // إعداد البحث لسطح المكتب والهاتف المحمول
                    setupSearch('search-input', 'search-results', 'results-list');
                    setupSearch('mobile-search-input', 'mobile-search-results', 'mobile-results-list');
                });
            </script>
        </div>

        <!-- Category Navigation -->
        <nav class="bg-gray-100 py-2 shadow-inner">
            <div class="container mx-auto px-4">
                <!-- Desktop Navigation Menu -->
                <ul class="hidden md:flex flex-wrap items-center justify-center space-x-6 space-x-reverse">
                    <li class="px-1">
                        <a href="{{ route('home') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('home') ? 'active-nav' : '' }}">{{ __('Home') }}</a>
                    </li>
                    @foreach (\App\Models\Category::where('parent_id', null)->take(6)->get() as $category)
                        <li class="relative group dropdown-container px-1">
                            <a href="{{ route('category', $category->slug) }}"
                                class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->segment(2) == $category->slug ? 'active-nav' : '' }} flex items-center">
                                {{ $category->name }}
                                @if ($category->children->count() > 0)
                                    <i class="fas fa-chevron-down text-xs mr-1"></i>
                                @endif
                            </a>
                            @if ($category->children->count() > 0)
                                <div
                                    class="absolute z-10 right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden dropdown-menu">
                                    @foreach ($category->children as $child)
                                        <a href="{{ route('category', $child->slug) }}"
                                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                            {{ $child->name }}
                                        </a>
                                    @endforeach
                                </div>
                            @endif
                        </li>
                    @endforeach
                    {{-- <li>
                        <a href="{{ route('companies') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('companies*') ? 'active-nav' : '' }}">الشركات</a>
                    </li> --}}
                    <li>
                        <a href="{{ route('bars-coins') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('bars-coins') ? 'active-nav' : '' }}">السبائك
                            والعملات</a>
                    </li>
                    <li>
                        <a href="{{ route('metal-prices') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('metal-prices') ? 'active-nav' : '' }}">{{ __('Metal Prices') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('zakat-calculator') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('zakat-calculator') ? 'active-nav' : '' }}">{{ __('Zakat Calculator') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('jewelry-value-calculator') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('jewelry-value-calculator') ? 'active-nav' : '' }}">{{ __('Jewelry Value Calculator') }}</a>
                    </li>
                    <li>
                        <a href="{{ route('contact') }}"
                            class="nav-link text-gray-700 hover:text-primary-500 font-medium {{ request()->routeIs('contact') ? 'active-nav' : '' }}">{{ __('Contact Us') }}</a>
                    </li>
                </ul>

                <!-- Mobile Navigation Menu (Hamburger) -->
                <div class="md:hidden flex justify-center">
                    <button id="mobile-category-toggle"
                        class="text-gray-700 hover:text-primary-500 focus:outline-none">
                        <span class="block text-sm font-medium">{{ __('Categories') }}</span>
                    </button>
                </div>

                <!-- Mobile Navigation Dropdown (Hidden by default) -->
                <div id="mobile-category-menu" class="md:hidden mt-4 hidden">
                    <ul class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <li>
                            <a href="{{ route('home') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('home') ? 'bg-gray-100' : '' }}">
                                {{ __('Home') }}
                            </a>
                        </li>
                        @foreach (\App\Models\Category::where('parent_id', null)->get() as $category)
                            <li class="border-t border-gray-200">
                                <div class="mobile-category-item">
                                    <a href="{{ route('category', $category->slug) }}"
                                        class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->segment(2) == $category->slug ? 'bg-gray-100' : '' }}">
                                        {{ $category->name }}
                                    </a>
                                    @if ($category->children->count() > 0)
                                        <button class="mobile-dropdown-toggle px-4 py-2 text-gray-500">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    @endif
                                </div>
                                @if ($category->children->count() > 0)
                                    <div class="mobile-subcategories hidden bg-gray-50 py-1">
                                        @foreach ($category->children as $child)
                                            <a href="{{ route('category', $child->slug) }}"
                                                class="block px-8 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                {{ $child->name }}
                                            </a>
                                        @endforeach
                                    </div>
                                @endif
                            </li>
                        @endforeach
                        {{-- <li class="border-t border-gray-200">
                            <a href="{{ route('companies') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('companies*') ? 'bg-gray-100' : '' }}">
                                الشركات
                            </a>
                        </li> --}}
                        <li class="border-t border-gray-200">
                            <a href="{{ route('bars-coins') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('bars-coins') ? 'bg-gray-100' : '' }}">
                                السبائك والعملات
                            </a>
                        </li>
                        <li class="border-t border-gray-200">
                            <a href="{{ route('metal-prices') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('metal-prices') ? 'bg-gray-100' : '' }}">
                                {{ __('Metal Prices') }}
                            </a>
                        </li>
                        <li class="border-t border-gray-200">
                            <a href="{{ route('zakat-calculator') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('zakat-calculator') ? 'bg-gray-100' : '' }}">
                                {{ __('Zakat Calculator') }}
                            </a>
                        </li>
                        <li class="border-t border-gray-200">
                            <a href="{{ route('jewelry-value-calculator') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('jewelry-value-calculator') ? 'bg-gray-100' : '' }}">
                                {{ __('Jewelry Value Calculator') }}
                            </a>
                        </li>
                        <li class="border-t border-gray-200">
                            <a href="{{ route('contact') }}"
                                class="block px-4 py-2 text-gray-700 hover:bg-gray-100 {{ request()->routeIs('contact') ? 'bg-gray-100' : '' }}">
                                {{ __('Contact Us') }}
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Notifications -->
    <livewire:notification />

    <!-- Main Content -->
    <main class="flex-grow">
        @yield('content')
    </main>

    <!-- Newsletter -->
    @php
        $showNewsletterFooter = false;
        if (isset($showNewsletter)) {
            // إذا كان المتغير موجود في الصفحة الرئيسية، لا تعرض النشرة في التذييل
            $showNewsletterFooter = false;
        } else {
            // في الصفحات الأخرى، تحقق من إعدادات الموقع
            // استخدام متغير $settings الذي تم مشاركته مع العرض
            $showNewsletterFooter = isset($settings) && $settings ? $settings->show_newsletter : true;
        }
    @endphp

    @if ($showNewsletterFooter)
        @livewire('newsletter-form')
    @endif

    <!-- Footer -->
    <footer class="bg-gray-900 text-white pt-12 pb-6">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- About -->
                <div>
                    <h3 class="text-xl font-bold mb-4">{{ __('About') }}
                        {{ app()->getLocale() == 'ar' ? 'مكة جولد' : 'Makkah Gold' }}</h3>
                    <p class="text-gray-400 mb-4">
                        مكة جولد هي علامة تجارية رائدة في مجال المجوهرات الراقية، نقدم تشكيلة واسعة من المجوهرات الذهبية
                        والفضية والبلاتينية بتصاميم عصرية وفريدة.
                    </p>
                    <div class="flex space-x-4 space-x-reverse">
                        @if ($settings->facebook_url)
                            <a href="{{ $settings->facebook_url }}" target="_blank"
                                class="text-gray-400 hover:text-white transition duration-300">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                        @endif
                        @if ($settings->twitter_url)
                            <a href="{{ $settings->twitter_url }}" target="_blank"
                                class="text-gray-400 hover:text-white transition duration-300">
                                <i class="fab fa-twitter"></i>
                            </a>
                        @endif
                        @if ($settings->instagram_url)
                            <a href="{{ $settings->instagram_url }}" target="_blank"
                                class="text-gray-400 hover:text-white transition duration-300">
                                <i class="fab fa-instagram"></i>
                            </a>
                        @endif

                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4">{{ __('Quick Links') }}</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('about') }}"
                                class="text-gray-400 hover:text-white transition duration-300">من نحن</a></li>
                        <li><a href="{{ route('products') }}"
                                class="text-gray-400 hover:text-white transition duration-300">منتجاتنا</a></li>
                        <li><a href="{{ route('stores') }}"
                                class="text-gray-400 hover:text-white transition duration-300">فروعنا</a></li>
                        {{-- <li><a href="{{ route('appointment') }}"
                                class="text-gray-400 hover:text-white transition duration-300">حجز موعد</a></li> --}}
                        <li><a href="{{ route('blog') }}"
                                class="text-gray-400 hover:text-white transition duration-300">المدونة</a></li>
                        {{-- <li><a href="{{ route('careers') }}"
                                class="text-gray-400 hover:text-white transition duration-300">وظائف</a></li> --}}
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h3 class="text-xl font-bold mb-4">{{ __('Customer Service') }}</h3>
                    <ul class="space-y-2">
                        <li>
                            <a href="{{ route('faq') }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ __('الأسئلة الشائعة') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('contact') }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ __('اتصل بنا') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('stores') }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ __('فروعنا') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('privacy') }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ app()->getLocale() == 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy' }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('terms') }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ app()->getLocale() == 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions' }}
                            </a>
                        </li>
                        {{-- <li>
                            <a href="{{ route('appointment') }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ __('حجز موعد') }}
                            </a>
                        </li> --}}
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h3 class="text-xl font-bold mb-4">{{ __('Contact Us') }}</h3>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt mt-1 ml-2 text-primary-500"></i>
                            <span
                                class="text-gray-400">{{ $settings->address ?? 'المقر الرئيسي: القاهرة، مصر' }}</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone-alt ml-2 text-primary-500"></i>
                            @php
                                $footerPhone = $settings->contact_phone ?? '+201000404777';
                                // تصحيح موضع علامة + في رقم الهاتف
                                if (!str_starts_with($footerPhone, '+')) {
                                    // إذا كان الرقم يبدأ بـ 0، نحوله إلى +2
                                    if (str_starts_with($footerPhone, '0')) {
                                        $footerPhone = '+2' . substr($footerPhone, 1);
                                    } else {
                                        $footerPhone = '+' . ltrim($footerPhone, '+');
                                    }
                                }
                                // التأكد من أن الرقم يبدأ بـ +2
                                if (str_starts_with($footerPhone, '+20') && strlen($footerPhone) < 13) {
                                    // الرقم صحيح
                                } elseif (
                                    str_starts_with($footerPhone, '+2') &&
                                    !str_starts_with($footerPhone, '+20')
                                ) {
                                    $footerPhone = '+20' . substr($footerPhone, 2);
                                }

                                // إنشاء رقم للعرض مع + في النهاية
                                $displayFooterPhone = ltrim($footerPhone, '+') . '+';
                            @endphp
                            <a href="tel:{{ $footerPhone }}"
                                class="text-gray-400 hover:text-white transition duration-300">
                                {{ $displayFooterPhone }}
                            </a>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope ml-2 text-primary-500"></i>
                            <span
                                class="text-gray-400">{{ $settings->contact_email ?? '<EMAIL>' }}</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-clock ml-2 text-primary-500"></i>
                            <span class="text-gray-400">السبت - الخميس: 10 ص - 10 م</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-clock ml-2 text-primary-500"></i>
                            <span class="text-gray-400">الجمعة: 2 م - 10 م</span>
                        </li>
                    </ul>
                </div>
            </div>

            <hr class="border-gray-800 my-8">

            <!-- Bottom Footer -->
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0">
                    {!! $settings->footer_text ??
                        '&copy; ' .
                            date('Y') .
                            ' ' .
                            ($settings->site_name ?? (app()->getLocale() == 'ar' ? 'مكة جولد للمجوهرات' : 'Makkah Gold Jewelry')) .
                            '. ' .
                            __('All Rights Reserved') .
                            '.' !!}
                </p>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="{{ route('privacy') }}"
                        class="text-gray-400 hover:text-white transition duration-300 text-sm">
                        {{ app()->getLocale() == 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy' }}
                    </a>
                    <span class="text-gray-600">|</span>
                    <a href="{{ route('terms') }}"
                        class="text-gray-400 hover:text-white transition duration-300 text-sm">
                        {{ app()->getLocale() == 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions' }}
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top"
        class="fixed bottom-6 left-6 bg-primary-500 text-white p-3 rounded-full shadow-lg opacity-0 invisible transition-all duration-300">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Mobile Side Menu (Hidden by default) -->
    <div id="mobile-side-menu" class="fixed inset-0 bg-black bg-opacity-50 hidden" style="z-index: 10000;">
        <div
            class="absolute top-0 {{ app()->getLocale() == 'ar' ? 'right-0' : 'left-0' }} h-full w-4/5 max-w-xs sm:max-w-sm bg-white shadow-xl transform transition-transform duration-300 ease-in-out">
            <div class="p-4 bg-primary-500 text-white flex justify-between items-center">
                <h3 class="text-lg font-bold">{{ $settings->site_name ?? 'مكة جولد' }}</h3>
                <button id="close-side-menu" class="text-white focus:outline-none">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="overflow-y-auto h-full pb-20">
                <div class="p-4 border-b border-gray-200">
                    <a href="{{ route('home') }}" class="block py-2 text-gray-800 hover:text-primary-500">
                        <i class="fas fa-home ml-2"></i> {{ __('Home') }}
                    </a>
                </div>

                <div class="p-4 border-b border-gray-200">
                    <p class="font-bold mb-2">{{ __('Categories') }}</p>
                    <ul class="space-y-2">
                        @foreach (\App\Models\Category::where('parent_id', null)->get() as $category)
                            <li class="side-menu-category">
                                <div class="flex justify-between items-center py-1">
                                    <a href="{{ route('category', $category->slug) }}"
                                        class="text-gray-700 hover:text-primary-500">
                                        {{ $category->name }}
                                    </a>
                                    @if ($category->children->count() > 0)
                                        <button class="side-menu-toggle text-gray-500 focus:outline-none">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    @endif
                                </div>
                                @if ($category->children->count() > 0)
                                    <ul
                                        class="side-menu-subcategories hidden mr-4 mt-1 space-y-1 border-r-2 border-gray-200 pr-2">
                                        @foreach ($category->children as $child)
                                            <li>
                                                <a href="{{ route('category', $child->slug) }}"
                                                    class="text-gray-600 hover:text-primary-500 text-sm">
                                                    {{ $child->name }}
                                                </a>
                                            </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </li>
                        @endforeach
                    </ul>
                </div>

                <div class="p-4 border-b border-gray-200">
                    <p class="font-bold mb-2">{{ __('Quick Links') }}</p>
                    <ul class="space-y-2">
                        {{-- <li>
                            <a href="{{ route('companies') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-building ml-2"></i> الشركات
                            </a>
                        </li> --}}
                        <li>
                            <a href="{{ route('bars-coins') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-coins ml-2"></i> السبائك والعملات
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('metal-prices') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-coins ml-2"></i> {{ __('Metal Prices') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('zakat-calculator') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-calculator ml-2"></i> {{ __('Zakat Calculator') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('jewelry-value-calculator') }}"
                                class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-gem ml-2"></i> {{ __('Jewelry Value Calculator') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('about') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-info-circle ml-2"></i> {{ __('About Us') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('contact') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-envelope ml-2"></i> {{ __('Contact Us') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('stores') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-map-marker-alt ml-2"></i> {{ __('Our Stores') }}
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('faq') }}" class="text-gray-700 hover:text-primary-500">
                                <i class="fas fa-question-circle ml-2"></i> {{ __('FAQ') }}
                            </a>
                        </li>
                    </ul>
                </div>

                @if ($enableMultilingual && $languages->count() > 1)
                    <div class="p-4 border-b border-gray-200">
                        <p class="font-bold mb-2">{{ __('Language') }}</p>
                        <ul class="space-y-2">
                            @foreach ($languages as $language)
                                <li>
                                    <a href="{{ route('language.switch', $language->code) }}"
                                        class="text-gray-700 hover:text-primary-500 {{ app()->getLocale() == $language->code ? 'font-bold text-primary-500' : '' }}">
                                        {{ $language->native_name }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                {{-- <div class="p-4 border-b border-gray-200">
                    <p class="font-bold mb-2">{{ __('Currency') }}</p>
                    <ul class="space-y-2">
                        <li>
                            <a href="{{ route('currency.switch', 'EGP') }}"
                                class="text-gray-700 hover:text-primary-500 {{ Session::get('currency', 'EGP') == 'EGP' ? 'font-bold text-primary-500' : '' }}">
                                {{ __('Egyptian Pound') }} (ج.م)
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('currency.switch', 'USD') }}"
                                class="text-gray-700 hover:text-primary-500 {{ Session::get('currency') == 'USD' ? 'font-bold text-primary-500' : '' }}">
                                {{ __('US Dollar') }} ($)
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('currency.switch', 'EUR') }}"
                                class="text-gray-700 hover:text-primary-500 {{ Session::get('currency') == 'EUR' ? 'font-bold text-primary-500' : '' }}">
                                {{ __('Euro') }} (€)
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('currency.switch', 'GBP') }}"
                                class="text-gray-700 hover:text-primary-500 {{ Session::get('currency') == 'GBP' ? 'font-bold text-primary-500' : '' }}">
                                {{ __('British Pound') }} (£)
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('currency.switch', 'AED') }}"
                                class="text-gray-700 hover:text-primary-500 {{ Session::get('currency') == 'AED' ? 'font-bold text-primary-500' : '' }}">
                                {{ __('UAE Dirham') }} (د.إ)
                            </a>
                        </li>
                    </ul>
                </div> --}}

                <div class="p-4">
                    <p class="font-bold mb-2">{{ __('Contact Information') }}</p>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-phone-alt mt-1 ml-2 text-primary-500"></i>
                            <span>{{ $settings->contact_phone ?? '+20 ************' }}</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope mt-1 ml-2 text-primary-500"></i>
                            <span>{{ $settings->contact_email ?? '<EMAIL>' }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    @livewireScripts()

    <!-- Livewire Notifications -->
    <script src="{{ asset('js/livewire-notifications.js') }}"></script>

    <!-- Custom JS -->
    <script>
        // Back to Top Button
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $('#back-to-top').removeClass('opacity-0 invisible').addClass('opacity-100 visible');
            } else {
                $('#back-to-top').removeClass('opacity-100 visible').addClass('opacity-0 invisible');
            }
        });

        $('#back-to-top').click(function() {
            $('html, body').animate({
                scrollTop: 0
            }, 500);
            return false;
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Mobile Menu Toggle
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const mobileSideMenu = document.getElementById('mobile-side-menu');
            const closeSideMenu = document.getElementById('close-side-menu');

            if (mobileMenuToggle && mobileSideMenu) {
                mobileMenuToggle.addEventListener('click', function() {
                    console.log('🖱️ Mobile menu toggle clicked');

                    // إظهار القائمة مع تأثير الانزلاق
                    mobileSideMenu.classList.remove('hidden');
                    mobileSideMenu.style.display = 'flex';
                    document.body.style.overflow = 'hidden'; // منع التمرير

                    // إضافة تأثير الانزلاق للخلفية
                    setTimeout(() => {
                        mobileSideMenu.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                    }, 10);

                    // إضافة تأثير انزلاق للقائمة نفسها
                    const sideMenuPanel = mobileSideMenu.querySelector('div');
                    if (sideMenuPanel) {
                        sideMenuPanel.style.transform = 'translateX(' + (document.dir === 'rtl' || document
                            .documentElement.lang === 'ar' ? '100%' : '-100%') + ')';
                        setTimeout(() => {
                            sideMenuPanel.style.transform = 'translateX(0)';
                        }, 10);
                    }

                    console.log('✅ Mobile menu opened');
                });
            }

            if (closeSideMenu && mobileSideMenu) {
                // وظيفة إغلاق القائمة مع التأثيرات
                function closeMobileMenu() {
                    console.log('🖱️ Closing mobile menu...');

                    const sideMenuPanel = mobileSideMenu.querySelector('div');

                    // إضافة تأثير انزلاق للخروج
                    if (sideMenuPanel) {
                        sideMenuPanel.style.transform = 'translateX(' + (document.dir === 'rtl' || document
                            .documentElement.lang === 'ar' ? '100%' : '-100%') + ')';
                    }

                    // إخفاء الخلفية
                    mobileSideMenu.style.backgroundColor = 'rgba(0, 0, 0, 0)';

                    // إخفاء القائمة بعد انتهاء التأثير
                    setTimeout(() => {
                        mobileSideMenu.classList.add('hidden');
                        mobileSideMenu.style.display = 'none';
                        document.body.style.overflow = ''; // إعادة تفعيل التمرير
                        console.log('✅ Mobile menu closed');
                    }, 300);
                }

                // إغلاق عند النقر على زر الإغلاق
                closeSideMenu.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    closeMobileMenu();
                });

                // إغلاق عند النقر خارج القائمة
                mobileSideMenu.addEventListener('click', function(e) {
                    if (e.target === mobileSideMenu) {
                        closeMobileMenu();
                    }
                });

                // إغلاق عند الضغط على مفتاح Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !mobileSideMenu.classList.contains('hidden')) {
                        closeMobileMenu();
                    }
                });
            }

            // Mobile Category Toggle
            const mobileCategoryToggle = document.getElementById('mobile-category-toggle');
            const mobileCategoryMenu = document.getElementById('mobile-category-menu');

            if (mobileCategoryToggle && mobileCategoryMenu) {
                mobileCategoryToggle.addEventListener('click', function() {
                    mobileCategoryMenu.classList.toggle('hidden');
                });
            }

            // Mobile Category Dropdowns
            const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');

            mobileDropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const parent = this.closest('li');
                    const subcategories = parent.querySelector('.mobile-subcategories');

                    if (subcategories) {
                        subcategories.classList.toggle('hidden');
                        this.querySelector('i').classList.toggle('fa-chevron-down');
                        this.querySelector('i').classList.toggle('fa-chevron-up');
                    }
                });
            });

            // Side Menu Category Toggles
            const sideMenuToggles = document.querySelectorAll('.side-menu-toggle');

            sideMenuToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const parent = this.closest('.side-menu-category');
                    const subcategories = parent.querySelector('.side-menu-subcategories');

                    if (subcategories) {
                        subcategories.classList.toggle('hidden');
                        this.querySelector('i').classList.toggle('fa-chevron-down');
                        this.querySelector('i').classList.toggle('fa-chevron-up');
                    }
                });
            });

            // قوائم اللغة والعملة
            const headerDropdowns = document.querySelectorAll('header .dropdown-container');

            headerDropdowns.forEach(container => {
                const button = container.querySelector('.dropdown-toggle');
                const dropdown = container.querySelector('.dropdown-menu');

                if (button && dropdown) {
                    // إظهار القائمة عند النقر
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // إغلاق جميع القوائم المنسدلة الأخرى
                        headerDropdowns.forEach(otherContainer => {
                            if (otherContainer !== container) {
                                const otherDropdown = otherContainer.querySelector(
                                    '.dropdown-menu');
                                if (otherDropdown) {
                                    otherDropdown.style.display = 'none';
                                }
                            }
                        });

                        // تبديل حالة القائمة الحالية
                        if (dropdown.style.display === 'block') {
                            dropdown.style.display = 'none';
                        } else {
                            dropdown.style.display = 'block';
                        }
                    });
                }
            });

            // إخفاء القوائم المنسدلة عند النقر خارجها
            document.addEventListener('click', function(e) {
                const isDropdownClick = e.target.closest('.dropdown-container');
                if (!isDropdownClick) {
                    const allDropdowns = document.querySelectorAll('.dropdown-menu');
                    allDropdowns.forEach(dropdown => {
                        dropdown.style.display = 'none';
                    });
                }
            });

            // قوائم الأقسام على الأجهزة المحمولة
            const isMobile = window.matchMedia('(max-width: 768px)').matches;

            if (isMobile) {
                const categoryItems = document.querySelectorAll('nav li.dropdown-container');

                categoryItems.forEach(item => {
                    const link = item.querySelector('a');
                    const dropdown = item.querySelector('.dropdown-menu');

                    if (link && dropdown) {
                        // سلوك الجوال: إظهار عند النقر
                        link.addEventListener('click', function(e) {
                            // منع التنقل عند النقر على الرابط الرئيسي
                            if (dropdown) {
                                e.preventDefault();

                                // تبديل حالة العرض
                                if (dropdown.style.display === 'block') {
                                    dropdown.style.display = 'none';
                                } else {
                                    // إغلاق جميع القوائم المنسدلة الأخرى
                                    categoryItems.forEach(otherItem => {
                                        const otherDropdown = otherItem.querySelector(
                                            '.dropdown-menu');
                                        if (otherDropdown) {
                                            otherDropdown.style.display = 'none';
                                        }
                                    });

                                    // إظهار القائمة الحالية
                                    dropdown.style.display = 'block';
                                }
                            }
                        });
                    }
                });
            }

            // إضافة تأخير للقوائم المنسدلة على الحاسوب
            let dropdownTimeout;
            const categoryContainers = document.querySelectorAll('nav li.dropdown-container');

            categoryContainers.forEach(container => {
                const dropdown = container.querySelector('.dropdown-menu');

                if (!isMobile && dropdown) {
                    // عند التحويم على القائمة
                    container.addEventListener('mouseenter', function() {
                        clearTimeout(dropdownTimeout);

                        // إخفاء جميع القوائم المنسدلة الأخرى
                        categoryContainers.forEach(otherContainer => {
                            if (otherContainer !== container) {
                                const otherDropdown = otherContainer.querySelector(
                                    '.dropdown-menu');
                                if (otherDropdown) {
                                    otherDropdown.style.display = 'none';
                                }
                            }
                        });

                        // إظهار القائمة الحالية
                        dropdown.style.display = 'block';
                    });

                    // عند مغادرة القائمة
                    container.addEventListener('mouseleave', function() {
                        dropdownTimeout = setTimeout(function() {
                            dropdown.style.display = 'none';
                        }, 300); // تأخير 300 مللي ثانية
                    });

                    // إلغاء التأخير عند التحويم على القائمة المنسدلة
                    dropdown.addEventListener('mouseenter', function() {
                        clearTimeout(dropdownTimeout);
                    });

                    // إعادة تفعيل التأخير عند مغادرة القائمة المنسدلة
                    dropdown.addEventListener('mouseleave', function() {
                        dropdownTimeout = setTimeout(function() {
                            dropdown.style.display = 'none';
                        }, 300); // تأخير 300 مللي ثانية
                    });
                }
            });
        });
    </script>

    <!-- Blog Enhancements JavaScript -->
    <script src="{{ asset('js/blog-enhancements.js') }}"></script>

    @yield('scripts')

    <!-- تهيئة السلايدر الأساسية -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM loaded, starting slider initialization...');

            // انتظار قليل ثم تهيئة السلايدر
            setTimeout(function() {
                initializeHeroSlider();
            }, 500);
        });

        function initializeHeroSlider() {
            console.log('🔧 Attempting to initialize slider...');

            // التحقق من وجود مكتبة Swiper
            if (typeof Swiper === 'undefined') {
                console.error('❌ Swiper library not found!');
                return;
            }
            console.log('✅ Swiper library found');

            // التحقق من وجود عنصر السلايدر
            const sliderElement = document.querySelector('.hero-slider');
            if (!sliderElement) {
                console.error('❌ Hero slider element not found!');
                return;
            }
            console.log('✅ Hero slider element found');

            // التحقق من وجود شرائح
            const slides = sliderElement.querySelectorAll('.swiper-slide');
            if (slides.length === 0) {
                console.error('❌ No slides found!');
                return;
            }
            console.log('✅ Found', slides.length, 'slides');

            // تشخيص محتوى الشرائح
            slides.forEach((slide, index) => {
                const img = slide.querySelector('img');
                const title = slide.querySelector('h1');
                const description = slide.querySelector('p');

                console.log(`📊 Slide ${index + 1}:`);
                console.log('   - Image src:', img ? img.src : 'No image');
                console.log('   - Title:', title ? title.textContent.substring(0, 50) + '...' : 'No title');
                console.log('   - Description:', description ? description.textContent.substring(0, 50) + '...' :
                    'No description');

                // تحقق من تحميل الصورة
                if (img) {
                    img.addEventListener('load', function() {
                        console.log(`✅ Image loaded for slide ${index + 1}:`, this.src);
                    });
                    img.addEventListener('error', function() {
                        console.error(`❌ Image failed to load for slide ${index + 1}:`, this.src);
                    });
                }
            });

            // التحقق من وجود الأسهم والنقاط
            const nextBtn = document.querySelector('.swiper-button-next');
            const prevBtn = document.querySelector('.swiper-button-prev');
            const pagination = document.querySelector('.swiper-pagination');

            console.log('🎯 Next button:', nextBtn ? 'Found' : 'Not found');
            console.log('🎯 Prev button:', prevBtn ? 'Found' : 'Not found');
            console.log('🎯 Pagination:', pagination ? 'Found' : 'Not found');

            // تدمير أي سلايدر موجود مسبقاً
            if (sliderElement.swiper) {
                console.log('🗑️ Destroying existing slider...');
                sliderElement.swiper.destroy(true, true);
            }

            try {
                console.log('🔨 Creating new Swiper instance...');

                // تهيئة السلايدر بإعدادات بسيطة
                const heroSlider = new Swiper('.hero-slider', {
                    // الإعدادات الأساسية
                    loop: slides.length > 1,
                    autoplay: slides.length > 1 ? {
                        delay: 5000,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: true,
                    } : false,
                    speed: 800,
                    grabCursor: true,
                    touchRatio: 1,
                    allowTouchMove: true,

                    // تأثير الانتقال البسيط
                    effect: 'slide',
                    slidesPerView: 1,
                    spaceBetween: 0,

                    // النقاط
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                        bulletClass: 'swiper-pagination-bullet',
                        bulletActiveClass: 'swiper-pagination-bullet-active',
                    },

                    // الأسهم
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },

                    // الأحداث
                    on: {
                        init: function() {
                            console.log('✅ Hero slider initialized successfully!');
                            console.log('📊 Active slide index:', this.activeIndex);
                            console.log('📊 Total slides:', this.slides.length);
                            console.log('📊 Loop enabled:', this.params.loop);

                            // بدء التشغيل التلقائي
                            if (this.autoplay) {
                                this.autoplay.start();
                                console.log('▶️ Autoplay started');
                            }
                        },

                        slideChange: function() {
                            console.log('🔄 Slide changed to:', this.activeIndex);
                        },

                        slideNextTransitionStart: function() {
                            console.log('➡️ Next slide transition started');
                        },

                        slidePrevTransitionStart: function() {
                            console.log('⬅️ Previous slide transition started');
                        },

                        touchStart: function() {
                            console.log('👆 Touch started - pausing autoplay');
                        },

                        touchEnd: function() {
                            console.log('👆 Touch ended');
                            const self = this;
                            setTimeout(() => {
                                if (self && self.autoplay) {
                                    self.autoplay.start();
                                    console.log('🔄 Autoplay resumed after touch');
                                }
                            }, 3000);
                        }
                    }
                });

                // حفظ مرجع السلايدر
                window.heroSliderInstance = heroSlider;
                console.log('💾 Slider instance saved to window.heroSliderInstance');

                // اختبار التنقل يدوياً بعد 3 ثوان
                if (slides.length > 1) {
                    setTimeout(() => {
                        console.log('🧪 Testing manual navigation...');
                        if (heroSlider && heroSlider.slideNext) {
                            heroSlider.slideNext();
                            console.log('✅ Manual slideNext() called');
                        }
                    }, 3000);
                }

            } catch (error) {
                console.error('❌ Error initializing slider:', error);
                console.error('Stack trace:', error.stack);
            }
        }

        // إضافة اختبار الأسهم عند النقر
        document.addEventListener('click', function(e) {
            if (e.target.closest('.swiper-button-next')) {
                console.log('🖱️ Next button clicked');
            }
            if (e.target.closest('.swiper-button-prev')) {
                console.log('🖱️ Prev button clicked');
            }
            if (e.target.closest('.swiper-pagination-bullet')) {
                console.log('🖱️ Pagination bullet clicked');
            }
        });
    </script>
    <!-- Floating WhatsApp Button -->
    @if ($settings->whatsapp_number)
        @php
            $whatsappNumber = $settings->whatsapp_number;
            // تحويل الرقم إذا كان يبدأ بـ 0 إلى +2
            if (str_starts_with($whatsappNumber, '0')) {
                $whatsappNumber = '+2' . substr($whatsappNumber, 1);
            } elseif (!str_starts_with($whatsappNumber, '+')) {
                $whatsappNumber = '+' . ltrim($whatsappNumber, '+');
            }
            // التأكد من أن الرقم يبدأ بـ +2
            if (str_starts_with($whatsappNumber, '+20') && strlen($whatsappNumber) < 13) {
                // الرقم صحيح
            } elseif (str_starts_with($whatsappNumber, '+2') && !str_starts_with($whatsappNumber, '+20')) {
                $whatsappNumber = '+20' . substr($whatsappNumber, 2);
            }
            // الاحتفاظ بـ + في الرابط
            $whatsappMessage = urlencode('مرحباً، أحتاج مساعدة بخصوص منتجاتكم');
        @endphp

        <div id="whatsapp-float" class="fixed bottom-6 right-6 z-[9998] group">
            <!-- الزر الرئيسي -->
            <a href="https://wa.me/{{ $whatsappNumber }}?text={{ $whatsappMessage }}" target="_blank"
                class="whatsapp-btn flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
                <i class="fab fa-whatsapp text-2xl"></i>

                <!-- تأثير النبضة -->
                <span
                    class="absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75 animate-ping"></span>
            </a>

            <!-- النص التوضيحي -->
            <div
                class="absolute right-16 top-1/2 transform -translate-y-1/2 bg-white text-gray-800 px-3 py-2 rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
                <span class="text-sm font-medium">تحدث معنا على واتساب</span>
                <!-- السهم -->
                <div class="absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-white rotate-45"></div>
            </div>
        </div>
    @endif

    <!-- Top Bar Enhancement Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const topBar = document.getElementById('top-bar');
            const whatsappFloat = document.getElementById('whatsapp-float');
            let lastScrollTop = 0;

            // تأثير التمرير للشريط العلوي
            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 50) {
                    topBar.classList.add('top-bar-scrolled');
                } else {
                    topBar.classList.remove('top-bar-scrolled');
                }

                // إظهار/إخفاء زر واتساب حسب التمرير
                if (whatsappFloat) {
                    if (scrollTop > 200) {
                        whatsappFloat.style.opacity = '1';
                        whatsappFloat.style.transform = 'translateY(0)';
                    } else {
                        whatsappFloat.style.opacity = '0.7';
                        whatsappFloat.style.transform = 'translateY(10px)';
                    }
                }

                lastScrollTop = scrollTop;
            });

            // تحسين زر واتساب العائم
            if (whatsappFloat) {
                const whatsappBtn = whatsappFloat.querySelector('.whatsapp-btn');

                // تأثير النقر
                whatsappBtn.addEventListener('click', function() {
                    // تأثير الاهتزاز
                    this.style.animation = 'none';
                    setTimeout(() => {
                        this.style.animation = 'whatsapp-pulse 2s infinite';
                    }, 100);

                    // تتبع النقرات (اختياري)
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'click', {
                            'event_category': 'WhatsApp',
                            'event_label': 'Floating Button'
                        });
                    }
                });

                // تأثير التحويم المحسن
                whatsappBtn.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1) rotate(5deg)';
                });

                whatsappBtn.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            }

            // تحسين أيقونات وسائل التواصل الاجتماعي
            const socialIcons = document.querySelectorAll('.social-icon');
            socialIcons.forEach(icon => {
                icon.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.1)';
                });

                icon.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // تحسين الروابط في الشريط العلوي
            const topBarLinks = document.querySelectorAll('.top-bar-link');
            topBarLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.textShadow = '0 0 8px rgba(255, 255, 255, 0.5)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.textShadow = 'none';
                });
            });

            // تحسين القائمة المنسدلة للغات
            const dropdownContainers = document.querySelectorAll('.dropdown-container');
            dropdownContainers.forEach(container => {
                const menu = container.querySelector('.dropdown-menu');
                let timeout;

                container.addEventListener('mouseenter', function() {
                    clearTimeout(timeout);
                    if (menu) {
                        menu.style.display = 'block';
                        menu.style.opacity = '0';
                        menu.style.transform = 'translateY(-10px)';

                        setTimeout(() => {
                            menu.style.opacity = '1';
                            menu.style.transform = 'translateY(0)';
                        }, 10);
                    }
                });

                container.addEventListener('mouseleave', function() {
                    timeout = setTimeout(() => {
                        if (menu) {
                            menu.style.opacity = '0';
                            menu.style.transform = 'translateY(-10px)';
                            setTimeout(() => {
                                menu.style.display = 'none';
                            }, 200);
                        }
                    }, 100);
                });
            });
        });
    </script>

    <!-- Custom Footer Scripts -->
    @if (isset($settings->custom_footer_scripts) && $settings->custom_footer_scripts)
        {!! $settings->custom_footer_scripts !!}
    @endif
</body>

</html>
