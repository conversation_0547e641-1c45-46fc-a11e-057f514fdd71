<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', config('app.name'))</title>

    <!-- Meta Tags -->
    <meta name="description" content="@yield('description', 'مكة جولد للمجوهرات')">
    <meta name="keywords" content="@yield('keywords', 'مجوهرات, ذهب, فضة')">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">

    <!-- Styles -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Analytics (إذا كان مفعلاً) -->
    @if (isset($superAdminSettings))
        @php
            $siteSettings = app(\App\Services\SiteSettingsService::class);
            $googleAnalyticsId = $siteSettings->get('google_analytics_id');
        @endphp

        @if ($googleAnalyticsId)
            <!-- Google Analytics -->
            <script async src="https://www.googletagmanager.com/gtag/js?id={{ $googleAnalyticsId }}"></script>
            <script>
                window.dataLayer = window.dataLayer || [];

                function gtag() {
                    dataLayer.push(arguments);
                }
                gtag('js', new Date());

                // تحديد حالة الموافقة الافتراضية
                gtag('consent', 'default', {
                    'analytics_storage': 'denied',
                    'ad_storage': 'denied'
                });

                gtag('config', '{{ $googleAnalyticsId }}');
            </script>
        @endif

        @php
            $facebookPixelId = $siteSettings->get('facebook_pixel_id');
        @endphp

        @if ($facebookPixelId)
            <!-- Facebook Pixel -->
            <script>
                ! function(f, b, e, v, n, t, s) {
                    if (f.fbq) return;
                    n = f.fbq = function() {
                        n.callMethod ?
                            n.callMethod.apply(n, arguments) : n.queue.push(arguments)
                    };
                    if (!f._fbq) f._fbq = n;
                    n.push = n;
                    n.loaded = !0;
                    n.version = '2.0';
                    n.queue = [];
                    t = b.createElement(e);
                    t.async = !0;
                    t.src = v;
                    s = b.getElementsByTagName(e)[0];
                    s.parentNode.insertBefore(t, s)
                }(window, document, 'script',
                    'https://connect.facebook.net/en_US/fbevents.js');

                fbq('consent', 'revoke');
                fbq('init', '{{ $facebookPixelId }}');
                fbq('track', 'PageView');
            </script>
            <noscript>
                <img height="1" width="1" style="display:none"
                    src="https://www.facebook.com/tr?id={{ $facebookPixelId }}&ev=PageView&noscript=1" />
            </noscript>
        @endif

        @php
            $customHeaderScripts = $siteSettings->get('custom_header_scripts');
        @endphp

        @if ($customHeaderScripts)
            <!-- Custom Header Scripts -->
            {!! $customHeaderScripts !!}
        @endif
    @endif

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ asset('css/logo-enhancements.css') }}">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
    </style>

    @stack('styles')
</head>

<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{{ route('home') }}" class="logo-container">
                        <img src="{{ asset('images/logo.png') }}" alt="{{ config('app.name') }}"
                            class="frontend-logo site-logo" loading="eager" decoding="async">
                    </a>
                </div>

                <!-- Navigation -->
                <x-navigation-menu class="hidden md:flex space-x-8" />

                <!-- User Actions -->
                <div class="flex items-center space-x-4">
                    <!-- Wishlist (إذا كان مفعلاً) -->
                    <x-feature-check feature="wishlist" :show-message="false">
                        <a href="{{ route('wishlist.index') }}" class="text-gray-700 hover:text-red-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z">
                                </path>
                            </svg>
                        </a>
                    </x-feature-check>

                    <!-- Cart -->
                    <x-cart-link :show-count="true" />

                    <!-- User Menu -->
                    @auth
                        <div class="relative">
                            <button class="text-gray-700 hover:text-blue-600">
                                {{ auth()->user()->name }}
                            </button>
                        </div>
                    @else
                        @if (isset($superAdminSettings) && $superAdminSettings->isRegistrationEnabled())
                            <a href="{{ route('login') }}"
                                class="text-gray-700 hover:text-blue-600">{{ __('تسجيل الدخول') }}</a>
                        @endif
                    @endauth
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Display Only Mode Notice -->
        <x-feature-check feature="display_only" :show-message="true">
            <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm">
                            {{ __('الموقع في وضع العرض فقط حالياً. لا يمكن إتمام عمليات الشراء.') }}
                        </p>
                    </div>
                </div>
            </div>
        </x-feature-check>

        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white mt-16">
        <div class="container mx-auto px-4 py-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ config('app.name') }}</h3>
                    <p class="text-gray-300 text-sm">
                        {{ __('متجر متخصص في بيع المجوهرات والذهب عالي الجودة') }}
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ __('روابط سريعة') }}</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="{{ route('home') }}"
                                class="text-gray-300 hover:text-white">{{ __('الرئيسية') }}</a></li>
                        <li><a href="{{ route('products.index') }}"
                                class="text-gray-300 hover:text-white">{{ __('المنتجات') }}</a></li>
                        <li><a href="{{ route('about') }}"
                                class="text-gray-300 hover:text-white">{{ __('من نحن') }}</a></li>
                        <li><a href="{{ route('contact') }}"
                                class="text-gray-300 hover:text-white">{{ __('اتصل بنا') }}</a></li>
                    </ul>
                </div>

                <!-- Customer Service -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ __('خدمة العملاء') }}</h3>
                    <ul class="space-y-2 text-sm">
                        <li><a href="{{ route('privacy') }}"
                                class="text-gray-300 hover:text-white">{{ __('سياسة الخصوصية') }}</a></li>
                        <li><a href="{{ route('terms') }}"
                                class="text-gray-300 hover:text-white">{{ __('الشروط والأحكام') }}</a></li>
                        <li><a href="{{ route('returns') }}"
                                class="text-gray-300 hover:text-white">{{ __('سياسة الإرجاع') }}</a></li>
                        <li><a href="{{ route('shipping') }}"
                                class="text-gray-300 hover:text-white">{{ __('سياسة الشحن') }}</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">{{ __('تواصل معنا') }}</h3>
                    <div class="space-y-2 text-sm text-gray-300">
                        <p>{{ __('الهاتف: +20 123 456 7890') }}</p>
                        <p>{{ __('البريد الإلكتروني: <EMAIL>') }}</p>
                        <p>{{ __('العنوان: القاهرة، مصر') }}</p>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-300">
                <p>&copy; {{ date('Y') }} {{ config('app.name') }}. {{ __('جميع الحقوق محفوظة') }}.</p>
            </div>
        </div>
    </footer>

    <!-- Cookie Banner -->
    <x-cookie-banner />

    <!-- Scripts -->
    @if (isset($superAdminSettings))
        @php
            $customFooterScripts = app(\App\Services\SiteSettingsService::class)->get('custom_footer_scripts');
        @endphp

        @if ($customFooterScripts)
            <!-- Custom Footer Scripts -->
            {!! $customFooterScripts !!}
        @endif
    @endif

    @stack('scripts')
</body>

</html>
