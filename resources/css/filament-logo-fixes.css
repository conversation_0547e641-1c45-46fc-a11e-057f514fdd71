/* تحسينات شعار Filament - Filament Logo Enhancements */

/* ===== إصلاح عرض الشعار في Filament ===== */

/* Brand logo في sidebar */
.fi-sidebar-header .fi-logo,
.fi-sidebar-header img[class*="logo"] {
    max-height: 40px !important;
    max-width: 180px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center !important;
    display: block !important;
}

/* Brand logo في topbar */
.fi-topbar .fi-logo,
.fi-topbar img[class*="logo"] {
    max-height: 32px !important;
    max-width: 150px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center !important;
}

/* إصلاح container الشعار */
.fi-sidebar-header,
.fi-topbar-item {
    overflow: visible !important;
    white-space: nowrap !important;
}

/* إصلاح مشكلة الـ aspect ratio */
.fi-logo,
.fi-sidebar-header img,
.fi-topbar img {
    aspect-ratio: auto !important;
    min-width: 0 !important;
    min-height: 0 !important;
}

/* منع اقتصاص الشعار */
.fi-sidebar-header .fi-logo,
.fi-topbar .fi-logo {
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
}

/* تحسين spacing */
.fi-sidebar-header .fi-logo {
    margin: 12px 0 !important;
    padding: 0 !important;
}

.fi-topbar .fi-logo {
    margin: 6px 12px !important;
    padding: 0 !important;
}

/* ===== تحسينات responsive لـ Filament ===== */

@media (max-width: 1024px) {
    .fi-sidebar-header .fi-logo {
        max-height: 35px !important;
        max-width: 140px !important;
    }
    
    .fi-topbar .fi-logo {
        max-height: 28px !important;
        max-width: 120px !important;
    }
}

@media (max-width: 768px) {
    .fi-sidebar-header .fi-logo {
        max-height: 30px !important;
        max-width: 120px !important;
    }
    
    .fi-topbar .fi-logo {
        max-height: 24px !important;
        max-width: 100px !important;
    }
}

/* ===== إصلاح مشاكل خاصة بـ Filament ===== */

/* إصلاح عرض الشعار في collapsed sidebar */
.fi-sidebar-collapsed .fi-sidebar-header .fi-logo {
    max-width: 40px !important;
    max-height: 40px !important;
}

/* إصلاح عرض الشعار في mobile sidebar */
.fi-sidebar-mobile .fi-sidebar-header .fi-logo {
    max-height: 35px !important;
    max-width: 150px !important;
}

/* إصلاح مشكلة overflow في brand area */
.fi-sidebar-header,
.fi-topbar-item,
.fi-brand {
    overflow: visible !important;
    contain: none !important;
}

/* إصلاح مشكلة z-index */
.fi-sidebar-header .fi-logo,
.fi-topbar .fi-logo {
    z-index: 1 !important;
    position: relative !important;
}

/* ===== تحسينات الجودة في Filament ===== */

.fi-logo,
.fi-sidebar-header img,
.fi-topbar img {
    image-rendering: high-quality !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
}

/* ===== Dark mode support في Filament ===== */

.dark .fi-logo,
.dark .fi-sidebar-header img,
.dark .fi-topbar img {
    filter: brightness(0.9) !important;
}

/* ===== إصلاحات خاصة للمتصفحات في Filament ===== */

/* Safari */
@supports (-webkit-appearance: none) {
    .fi-logo,
    .fi-sidebar-header img,
    .fi-topbar img {
        -webkit-transform: translateZ(0) !important;
    }
}

/* Firefox */
@-moz-document url-prefix() {
    .fi-logo,
    .fi-sidebar-header img,
    .fi-topbar img {
        image-rendering: -moz-crisp-edges !important;
    }
}

/* ===== إصلاح مشاكل التحديث التلقائي ===== */

/* منع إعادة تحميل الشعار غير الضرورية */
.fi-logo,
.fi-sidebar-header img,
.fi-topbar img {
    will-change: auto !important;
}

/* إصلاح مشكلة flicker */
.fi-sidebar-header,
.fi-topbar-item {
    transform: translateZ(0) !important;
}

/* ===== تحسينات إضافية للأداء ===== */

/* تحسين تحميل الصور في Filament */
.fi-logo,
.fi-sidebar-header img,
.fi-topbar img {
    loading: eager !important;
    decoding: async !important;
}

/* منع التأخير في العرض */
.fi-sidebar-header .fi-logo,
.fi-topbar .fi-logo {
    transition: none !important;
    animation: none !important;
}

/* ===== إصلاحات خاصة بالشعارات المختلفة ===== */

/* شعارات SVG في Filament */
.fi-logo[src$=".svg"],
.fi-sidebar-header img[src$=".svg"],
.fi-topbar img[src$=".svg"] {
    filter: none !important;
    -webkit-filter: none !important;
}

/* شعارات PNG/JPG في Filament */
.fi-logo[src$=".png"],
.fi-logo[src$=".jpg"],
.fi-sidebar-header img[src$=".png"],
.fi-sidebar-header img[src$=".jpg"],
.fi-topbar img[src$=".png"],
.fi-topbar img[src$=".jpg"] {
    background: transparent !important;
}

/* ===== Fallback للشعارات المفقودة في Filament ===== */

.fi-logo[src=""],
.fi-sidebar-header img[src=""],
.fi-topbar img[src=""] {
    background-image: url('/images/logo-fallback.svg') !important;
    background-size: contain !important;
    background-repeat: no-repeat !important;
    background-position: center !important;
    min-width: 100px !important;
    min-height: 30px !important;
}

/* ===== إصلاح مشاكل التخطيط ===== */

/* إصلاح مشكلة flexbox في brand area */
.fi-sidebar-header,
.fi-topbar-item {
    display: flex !important;
    align-items: center !important;
    justify-content: flex-start !important;
}

/* إصلاح مشكلة النص بجانب الشعار */
.fi-sidebar-header .fi-logo + *,
.fi-topbar .fi-logo + * {
    margin-left: 8px !important;
}

/* ===== تحسينات خاصة بالطباعة ===== */

@media print {
    .fi-logo,
    .fi-sidebar-header img,
    .fi-topbar img {
        max-height: 20px !important;
        max-width: 80px !important;
        filter: grayscale(100%) !important;
    }
}
