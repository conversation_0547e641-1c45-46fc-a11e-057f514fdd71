/* Advanced Image Selector Styles */
.advanced-image-selector {
    --primary-color: rgb(59 130 246);
    --primary-color-rgb: 59, 130, 246;
    --success-color: rgb(34 197 94);
    --success-color-rgb: 34, 197, 94;
}

/* Grid Layout Enhancements */
.advanced-image-selector .image-grid {
    display: grid;
    gap: 0.75rem;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
}

@media (min-width: 640px) {
    .advanced-image-selector .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    }
}

@media (min-width: 768px) {
    .advanced-image-selector .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    }
}

@media (min-width: 1024px) {
    .advanced-image-selector .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

/* Image Container Styles */
.advanced-image-selector .image-container {
    position: relative;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    background: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.advanced-image-selector .image-container:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.advanced-image-selector .image-container.primary {
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.2);
}

.advanced-image-selector .image-container.primary:hover {
    box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.3), 
                0 10px 25px -3px rgba(0, 0, 0, 0.1);
}

/* Image Styles */
.advanced-image-selector .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    aspect-ratio: 1 / 1;
}

.advanced-image-selector .image-container:hover .product-image {
    transform: scale(1.05);
}

/* Primary Badge Styles */
.advanced-image-selector .primary-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: linear-gradient(135deg, var(--success-color), rgb(22 163 74));
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    z-index: 10;
}

.advanced-image-selector .primary-badge svg {
    width: 0.75rem;
    height: 0.75rem;
}

/* Hover Overlay Styles */
.advanced-image-selector .hover-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 1rem;
}

.advanced-image-selector .image-container:hover .hover-overlay {
    opacity: 1;
}

.advanced-image-selector .select-button {
    background: rgba(255, 255, 255, 0.95);
    color: rgb(31 41 55);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transform: translateY(0.5rem);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.advanced-image-selector .image-container:hover .select-button {
    transform: translateY(0);
    background: white;
}

.advanced-image-selector .select-button:hover {
    background: var(--primary-color);
    color: white;
}

.advanced-image-selector .select-button svg {
    width: 0.75rem;
    height: 0.75rem;
}

/* Loading State */
.advanced-image-selector .loading-overlay {
    position: absolute;
    inset: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
}

.advanced-image-selector .loading-spinner {
    width: 1.5rem;
    height: 1.5rem;
    border: 2px solid var(--primary-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Image Info Styles */
.advanced-image-selector .image-info {
    margin-top: 0.5rem;
    text-align: center;
}

.advanced-image-selector .image-number {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgb(107 114 128);
}

.advanced-image-selector .primary-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-top: 0.125rem;
}

/* Header Styles */
.advanced-image-selector .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.advanced-image-selector .header-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: rgb(55 65 81);
}

.advanced-image-selector .header-title svg {
    width: 1.25rem;
    height: 1.25rem;
    color: var(--primary-color);
}

.advanced-image-selector .image-count {
    font-size: 0.75rem;
    color: rgb(107 114 128);
    background: rgb(243 244 246);
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
}

/* Help Text Styles */
.advanced-image-selector .help-text {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: rgb(107 114 128);
    background: rgb(239 246 255);
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.advanced-image-selector .help-text svg {
    width: 1rem;
    height: 1rem;
    color: rgb(59 130 246);
    flex-shrink: 0;
}

/* Dark Mode Support */
.dark .advanced-image-selector {
    --primary-color: rgb(96 165 250);
    --primary-color-rgb: 96, 165, 250;
}

.dark .advanced-image-selector .image-container {
    background: rgb(31 41 55);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

.dark .advanced-image-selector .header-title {
    color: rgb(209 213 219);
}

.dark .advanced-image-selector .image-count {
    background: rgb(55 65 81);
    color: rgb(156 163 175);
}

.dark .advanced-image-selector .help-text {
    background: rgba(59 130 246, 0.1);
    color: rgb(156 163 175);
}

.dark .advanced-image-selector .image-number {
    color: rgb(156 163 175);
}

/* RTL Support */
[dir="rtl"] .advanced-image-selector .primary-badge {
    right: auto;
    left: 0.5rem;
}

[dir="rtl"] .advanced-image-selector .header-title {
    flex-direction: row-reverse;
}

/* Responsive Adjustments */
@media (max-width: 640px) {
    .advanced-image-selector .image-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .advanced-image-selector .select-button {
        font-size: 0.625rem;
        padding: 0.375rem 0.5rem;
    }
    
    .advanced-image-selector .primary-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }
}

/* Animation Enhancements */
.advanced-image-selector .image-container {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(1rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States for Accessibility */
.advanced-image-selector .image-container:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.advanced-image-selector .select-button:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}
