/* تحسينات الموقع للأجهزة المحمولة - Mobile Enhancements */

/* تحسينات عامة للجوال */
@media (max-width: 768px) {
    /* تحسين السلايدر الرئيسي */
    .hero-slider {
        margin-bottom: 0;
    }

    .hero-slider .swiper-slide {
        min-height: 400px;
    }

    .hero-slider .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* تحسين النصوص في السلايدر */
    .hero-slider h1 {
        font-size: 1.75rem !important;
        line-height: 1.2;
        margin-bottom: 0.75rem !important;
    }

    .hero-slider p {
        font-size: 0.95rem !important;
        line-height: 1.4;
        margin-bottom: 1.5rem !important;
    }

    /* تحسين الأزرار */
    .hero-slider .flex {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .hero-slider a {
        width: auto;
        text-align: center;
        padding: 0.75rem 1.5rem !important;
        font-size: 0.9rem !important;
        font-weight: 500;
    }

    /* تحسين المنتجات */
    .product-grid {
        gap: 0.5rem !important;
    }

    /* تحسين أقسام المحتوى */
    section {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }

    .container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* تحسين العناوين */
    h2 {
        font-size: 1.5rem !important;
        margin-bottom: 1rem !important;
    }

    h3 {
        font-size: 1.25rem !important;
    }

    /* تحسين النصوص التوضيحية */
    .text-gray-600 {
        font-size: 0.875rem !important;
        line-height: 1.4;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 480px) {
    /* تحسين السلايدر */
    .hero-slider .swiper-slide {
        min-height: 350px;
    }

    .hero-slider h1 {
        font-size: 1.5rem !important;
        margin-bottom: 0.5rem !important;
    }

    .hero-slider p {
        font-size: 0.875rem !important;
        margin-bottom: 1rem !important;
    }

    .hero-slider a {
        padding: 0.625rem 1.25rem !important;
        font-size: 0.875rem !important;
    }

    /* تحسين الفئات */
    .grid {
        gap: 0.5rem !important;
    }

    /* تحسين البطاقات */
    .rounded-lg {
        border-radius: 0.5rem !important;
    }

    /* تحسين النصوص */
    h2 {
        font-size: 1.25rem !important;
    }

    .text-gray-600 {
        font-size: 0.8rem !important;
    }

    /* تحسين المسافات */
    .py-8,
    .py-12 {
        padding-top: 1.5rem !important;
        padding-bottom: 1.5rem !important;
    }

    .mb-8,
    .mb-12 {
        margin-bottom: 1.5rem !important;
    }

    .mt-8,
    .mt-10 {
        margin-top: 1.5rem !important;
    }
}

/* تحسينات للشاشات الصغيرة جداً (iPhone SE وما شابه) */
@media (max-width: 375px) {
    .hero-slider .swiper-slide {
        min-height: 320px;
    }

    .hero-slider h1 {
        font-size: 1.375rem !important;
    }

    .hero-slider p {
        font-size: 0.8rem !important;
    }

    .container {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* تحسين الفئات للشاشات الصغيرة */
    .categories-grid .relative {
        height: 200px !important;
    }

    /* تحسين أسعار الذهب */
    .gold-prices-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.5rem !important;
    }

    .gold-price-card {
        padding: 0.5rem !important;
    }

    .gold-price-card h3 {
        font-size: 0.875rem !important;
    }

    .gold-price-card .text-lg {
        font-size: 1rem !important;
    }
}

/* تحسينات خاصة بالسلايدر */
.hero-slider .swiper-pagination {
    bottom: 1rem !important;
    left: 0;
    right: 0;
    text-align: center;
}

.hero-slider .swiper-pagination-bullet {
    width: 10px !important;
    height: 10px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    opacity: 1 !important;
    margin: 0 4px !important;
}

.hero-slider .swiper-pagination-bullet-active {
    background: white !important;
    transform: scale(1.2);
}

/* تحسين التفاعل مع اللمس */
.touch-friendly {
    min-height: 44px;
    min-width: 44px;
}

/* تحسين الأزرار للمس */
button,
.btn,
a.btn {
    min-height: 44px;
    padding: 0.75rem 1rem;
    touch-action: manipulation;
}

/* تحسين التمرير */
.scrollable {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* تحسين النوافذ المنبثقة للموبايل */
@media (max-width: 640px) {
    .modal,
    .popup {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
        overflow-y: auto;
    }
}

/* تحسين الخطوط للقراءة على الموبايل */
@media (max-width: 480px) {
    body {
        font-size: 14px;
        line-height: 1.5;
    }

    .text-sm {
        font-size: 12px !important;
    }

    .text-xs {
        font-size: 11px !important;
    }
}

/* تحسين الجداول للموبايل */
@media (max-width: 640px) {
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: 0.5rem;
    }

    table {
        min-width: 100%;
        font-size: 0.875rem;
    }

    th,
    td {
        padding: 0.5rem !important;
        white-space: nowrap;
    }
}

/* تحسين الصور للموبايل */
.responsive-img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* تحسين الفيديو للموبايل */
.responsive-video {
    width: 100%;
    height: auto;
    max-width: 100%;
}

/* إخفاء عناصر غير ضرورية على الموبايل */
@media (max-width: 768px) {
    .desktop-only {
        display: none !important;
    }

    .mobile-hidden {
        display: none !important;
    }
}

/* إظهار عناصر خاصة بالموبايل فقط */
.mobile-only {
    display: none;
}

@media (max-width: 768px) {
    .mobile-only {
        display: block;
    }

    .mobile-flex {
        display: flex;
    }

    .mobile-inline {
        display: inline;
    }
}

/* تحسين التركيز للوحة المفاتيح */
@media (max-width: 768px) {
    .focus\:outline-none:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }
}

/* تحسين الرسوم المتحركة للموبايل */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسين الطباعة للموبايل */
@media print {
    .no-print {
        display: none !important;
    }

    .hero-slider {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}
