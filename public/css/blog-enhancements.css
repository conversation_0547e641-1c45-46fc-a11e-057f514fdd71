/* تحسينات المدونة - Blog Enhancements */

/* تأثيرات عامة */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* تأثيرات التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* تأثيرات البطاقات */
.blog-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.blog-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* تأثيرات الصور */
.lazy-image {
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.lazy-image.loaded {
    opacity: 1;
}

.image-hover {
    transition: transform 0.7s ease;
}

.image-hover:hover {
    transform: scale(1.05);
}

/* تأثيرات النصوص */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* تأثيرات الأزرار */
.btn-hover {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-hover::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.5s;
}

.btn-hover:hover::before {
    left: 100%;
}

/* شريط التقدم في القراءة */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(to right, #3b82f6, #8b5cf6);
    z-index: 9999;
    transition: width 0.3s ease;
}

/* تحسينات المحتوى */
.post-content {
    font-family: "Cairo", sans-serif;
    line-height: 1.8;
}

.post-content img {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
    transition: transform 0.3s ease;
}

.post-content img:hover {
    transform: scale(1.02);
}

.post-content h1,
.post-content h2,
.post-content h3 {
    margin-top: 3rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.post-content h2::before {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, #3b82f6, #8b5cf6);
    border-radius: 2px;
}

.post-content blockquote {
    border-left: 4px solid #3b82f6;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    margin: 2rem 0;
    border-radius: 8px;
    position: relative;
    font-style: italic;
}

.post-content blockquote::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 15px;
    font-size: 3rem;
    color: #3b82f6;
    font-weight: bold;
}

/* تحسينات أزرار المشاركة */
.share-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.share-button:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.share-button::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.share-button:hover::after {
    width: 100px;
    height: 100px;
}

/* تأثيرات التنقل */
.nav-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.nav-card:hover {
    border-color: #3b82f6;
    transform: translateX(8px);
}

/* تحسينات الـ Sidebar */
.sidebar-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* تأثيرات Parallax للصور */
.parallax-image {
    transition: transform 0.1s ease-out;
}

/* تحسينات الألوان */
.primary-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.secondary-gradient {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* تحسينات RTL */
[dir="rtl"] .blog-card:hover {
    transform: translateY(-8px);
}

[dir="rtl"] .nav-card:hover {
    transform: translateX(-8px);
}

/* تحسينات Responsive */
@media (max-width: 768px) {
    .blog-card:hover {
        transform: translateY(-4px);
    }

    .share-button {
        padding: 0.75rem;
    }

    .post-content {
        font-size: 1rem;
        line-height: 1.7;
    }
}

/* تأثيرات التحميل التدريجي */
.fade-in {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-delay {
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.2s;
    opacity: 0;
}

.fade-in-delay-2 {
    animation: fadeInUp 0.6s ease forwards;
    animation-delay: 0.4s;
    opacity: 0;
}

/* تحسينات التحميل */
.loading-state {
    opacity: 0.7;
    pointer-events: none;
}

.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* تحسينات الإشعارات */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #10b981;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10000;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.error {
    background: #ef4444;
}

/* تحسينات الطباعة */
@media print {
    .sidebar,
    .share-button,
    .nav-card {
        display: none !important;
    }

    .post-content {
        max-width: none !important;
    }
}
