/* تحسينات عرض الشعار - Logo Display Enhancements */

/* ===== إعدادات عامة للشعار ===== */
.logo-container {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.site-logo {
    max-height: 60px;
    width: auto;
    height: auto;
    object-fit: contain !important;
    object-position: center;
    transition: all 0.3s ease;
}

/* ===== شعار الواجهة الأمامية ===== */
.frontend-logo {
    max-height: 48px;
    max-width: 200px;
    width: auto;
    height: auto;
    object-fit: contain !important;
    object-position: left center;
}

/* شعار أكبر للصفحة الرئيسية */
.hero-logo {
    max-height: 80px;
    max-width: 300px;
    width: auto;
    height: auto;
    object-fit: contain !important;
    object-position: center;
}

/* ===== شعار لوحة التحكم (Filament) ===== */
.fi-logo {
    max-height: 40px !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center !important;
}

/* تحسين عرض الشعار في sidebar */
.fi-sidebar-nav .fi-logo {
    max-height: 35px !important;
    max-width: 150px !important;
}

/* تحسين عرض الشعار في topbar */
.fi-topbar .fi-logo {
    max-height: 32px !important;
    max-width: 120px !important;
}

/* ===== تحسينات responsive ===== */
@media (max-width: 768px) {
    .site-logo {
        max-height: 40px;
    }
    
    .frontend-logo {
        max-height: 36px;
        max-width: 150px;
    }
    
    .hero-logo {
        max-height: 60px;
        max-width: 200px;
    }
    
    .fi-logo {
        max-height: 28px !important;
        max-width: 100px !important;
    }
}

@media (max-width: 480px) {
    .site-logo {
        max-height: 32px;
    }
    
    .frontend-logo {
        max-height: 28px;
        max-width: 120px;
    }
    
    .hero-logo {
        max-height: 48px;
        max-width: 160px;
    }
    
    .fi-logo {
        max-height: 24px !important;
        max-width: 80px !important;
    }
}

/* ===== تحسينات خاصة للشعارات المختلفة ===== */

/* شعارات SVG */
.site-logo[src$=".svg"],
.frontend-logo[src$=".svg"],
.hero-logo[src$=".svg"],
.fi-logo[src$=".svg"] {
    filter: none;
    -webkit-filter: none;
}

/* شعارات PNG/JPG مع خلفية شفافة */
.site-logo[src$=".png"],
.frontend-logo[src$=".png"] {
    background: transparent;
}

/* ===== تأثيرات hover للشعار ===== */
.logo-container:hover .site-logo,
.logo-container:hover .frontend-logo {
    transform: scale(1.05);
    filter: brightness(1.1);
}

/* ===== إصلاح مشاكل العرض الشائعة ===== */

/* منع اقتصاص الشعار */
.logo-container,
.fi-logo-container {
    overflow: visible !important;
    white-space: nowrap;
}

/* إصلاح مشكلة aspect ratio */
.site-logo,
.frontend-logo,
.hero-logo,
.fi-logo {
    aspect-ratio: auto !important;
    min-width: 0;
    min-height: 0;
}

/* إصلاح مشاكل flexbox */
.logo-container {
    flex-shrink: 0;
    flex-grow: 0;
}

/* ===== تحسينات خاصة بـ Filament ===== */

/* إصلاح عرض الشعار في brand area */
.fi-sidebar-header .fi-logo,
.fi-topbar-item .fi-logo {
    display: block !important;
    max-width: none !important;
    width: auto !important;
    height: auto !important;
}

/* إصلاح مشكلة overflow في Filament */
.fi-sidebar-header,
.fi-topbar-item {
    overflow: visible !important;
}

/* تحسين spacing حول الشعار */
.fi-sidebar-header .fi-logo {
    margin: 8px 0;
}

.fi-topbar-item .fi-logo {
    margin: 4px 8px;
}

/* ===== تحسينات الأداء ===== */

/* تحسين تحميل الصور */
.site-logo,
.frontend-logo,
.hero-logo,
.fi-logo {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    image-rendering: auto;
}

/* تحسين GPU acceleration */
.site-logo,
.frontend-logo,
.hero-logo {
    will-change: transform;
    transform: translateZ(0);
}

/* ===== Dark mode support ===== */
@media (prefers-color-scheme: dark) {
    .site-logo,
    .frontend-logo,
    .hero-logo {
        filter: brightness(0.9);
    }
}

/* Dark mode في Filament */
.dark .fi-logo {
    filter: brightness(0.9);
}

/* ===== إصلاحات خاصة للمتصفحات ===== */

/* Safari */
@supports (-webkit-appearance: none) {
    .site-logo,
    .frontend-logo,
    .hero-logo,
    .fi-logo {
        -webkit-transform: translateZ(0);
    }
}

/* Firefox */
@-moz-document url-prefix() {
    .site-logo,
    .frontend-logo,
    .hero-logo,
    .fi-logo {
        image-rendering: -moz-crisp-edges;
    }
}

/* ===== Fallback للشعارات المفقودة ===== */
.site-logo[src=""],
.frontend-logo[src=""],
.hero-logo[src=""] {
    background-image: url('/images/logo-fallback.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    min-width: 120px;
    min-height: 40px;
}

/* ===== تحسينات إضافية للجودة ===== */
.site-logo,
.frontend-logo,
.hero-logo,
.fi-logo {
    /* منع التشويش عند التكبير */
    image-rendering: high-quality;
    
    /* تحسين الحواف */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    /* منع التمدد غير المرغوب */
    flex-shrink: 0;
    
    /* ضمان الوضوح */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}
