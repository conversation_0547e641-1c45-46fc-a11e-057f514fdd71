<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تجاوب شريط التنقل العلوي</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* نفس الـ CSS من الملف الأصلي */
        .top-bar-sticky {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .top-bar-link {
            position: relative;
            overflow: hidden;
        }

        .top-bar-link::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background-color: rgba(255, 255, 255, 0.8);
            transition: width 0.3s ease;
        }

        .top-bar-link:hover::before {
            width: 100%;
        }

        .social-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .social-icon:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .top-bar-mobile {
                padding: 8px 0;
            }

            .social-icon {
                width: 28px;
                height: 28px;
            }

            .contact-info-mobile {
                font-size: 0.875rem;
            }

            .top-bar-container {
                padding: 0.5rem 1rem;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً */
        @media (max-width: 480px) {
            .top-bar-container {
                padding: 0.5rem 0.75rem;
            }

            .contact-info-mobile {
                font-size: 0.8rem;
            }

            .social-icon {
                width: 24px;
                height: 24px;
            }

            .mobile-social-icons {
                gap: 0.5rem;
            }
        }

        /* تحسينات للشاشات الصغيرة جداً (320px) */
        @media (max-width: 320px) {
            .top-bar-container {
                padding: 0.5rem;
            }

            .contact-info-mobile {
                font-size: 0.75rem;
            }

            .mobile-social-icons {
                gap: 0.25rem;
            }

            .social-icon {
                width: 20px;
                height: 20px;
            }
        }

        .top-bar-scrolled {
            background: linear-gradient(135deg, #D4AF37 0%, #B8941F 100%);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        /* تحسين الشريط العلوي للهواتف */
        @media (max-width: 640px) {
            body {
                padding-top: 3rem;
            }

            #top-bar {
                padding: 0.5rem 0;
            }
        }

        /* تحسينات القائمة الجانبية المحمولة */
        #mobile-side-menu {
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        .side-menu-category {
            border-bottom: 1px solid #f3f4f6;
        }

        .side-menu-category:last-child {
            border-bottom: none;
        }

        .side-menu-subcategories {
            background-color: #f9fafb;
        }

        #close-side-menu {
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: background-color 0.2s;
        }

        #close-side-menu:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .side-menu-toggle {
            padding: 0.25rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }

        .side-menu-toggle:hover {
            background-color: #f3f4f6;
        }

        /* إضافة ألوان Tailwind المخصصة */
        .bg-primary-500 {
            background-color: #D4AF37;
        }

        .text-primary-500 {
            color: #D4AF37;
        }

        /* عرض أحجام الشاشة للاختبار */
        .screen-size-indicator {
            position: fixed;
            top: 60px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex flex-col pt-12">
    <!-- مؤشر حجم الشاشة -->
    <div class="screen-size-indicator" id="screenSize">
        الشاشة: <span id="screenWidth"></span>px
    </div>

    <!-- Top Bar - Sticky -->
    <div id="top-bar" class="bg-primary-500 text-white py-2 fixed top-0 left-0 right-0 z-[9999] shadow-md top-bar-sticky transition-all duration-300 min-h-[48px] flex items-center">
        <div class="container mx-auto px-4 flex justify-between items-center top-bar-container">
            <!-- Contact Info - Left Side -->
            <div class="flex items-center space-x-4 space-x-reverse flex-shrink-0">
                <a href="tel:+201000404777" class="text-sm hover:text-white/80 transition-colors duration-200 top-bar-link flex items-center contact-info-mobile">
                    <i class="fas fa-phone-alt ml-1 text-white"></i>
                    <span class="hidden sm:inline font-medium">01000404777+</span>
                    <span class="sm:hidden font-medium">01000404777+</span>
                </a>
                <a href="mailto:<EMAIL>" class="hidden sm:flex text-sm hover:text-white/80 transition-colors duration-200 top-bar-link">
                    <i class="fas fa-envelope ml-1"></i> <EMAIL>
                </a>
            </div>

            <!-- Center - Social Media Icons -->
            <div class="hidden lg:flex items-center space-x-6 space-x-reverse flex-1 justify-center">
                <a href="#" target="_blank" class="text-white hover:text-blue-200 transition-colors duration-200 social-icon" title="فيسبوك">
                    <i class="fab fa-facebook-f text-lg"></i>
                </a>
                <a href="#" target="_blank" class="text-white hover:text-pink-200 transition-colors duration-200 social-icon" title="إنستغرام">
                    <i class="fab fa-instagram text-lg"></i>
                </a>
                <a href="#" target="_blank" class="text-white hover:text-blue-300 transition-colors duration-200 social-icon" title="تويتر">
                    <i class="fab fa-twitter text-lg"></i>
                </a>
            </div>

            <!-- Right Side - Quick Links & Language -->
            <div class="flex items-center space-x-4 space-x-reverse flex-shrink-0">
                <!-- Quick Links - Hidden on mobile -->
                <div class="hidden xl:flex items-center space-x-3 space-x-reverse">
                    <a href="#" class="text-sm hover:text-white/80 transition-colors duration-200">
                        <i class="fas fa-map-marker-alt ml-1"></i> المتاجر
                    </a>
                    <a href="#" class="text-sm hover:text-white/80 transition-colors duration-200">
                        <i class="fas fa-info-circle ml-1"></i> من نحن
                    </a>
                    <a href="#" class="text-sm hover:text-white/80 transition-colors duration-200">
                        <i class="fas fa-phone-alt ml-1"></i> اتصل بنا
                    </a>
                </div>

                <!-- Mobile Social Media Icons - Only visible on mobile -->
                <div class="flex lg:hidden items-center space-x-3 space-x-reverse mobile-social-icons">
                    <a href="#" target="_blank" class="text-white hover:text-blue-200 transition-colors duration-200 social-icon">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="#" target="_blank" class="text-white hover:text-pink-200 transition-colors duration-200 social-icon">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" target="_blank" class="text-white hover:text-blue-300 transition-colors duration-200 social-icon">
                        <i class="fab fa-twitter"></i>
                    </a>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button id="mobile-menu-toggle" class="md:hidden text-white focus:outline-none ml-2 p-1 rounded hover:bg-white/10 transition-colors duration-200">
                    <i class="fas fa-bars text-lg"></i>
                </button>

                <!-- Language Switcher - Hidden on mobile -->
                <div class="hidden md:block relative group dropdown-container">
                    <button class="text-sm hover:text-white/80 flex items-center dropdown-toggle transition-colors duration-200">
                        <i class="fas fa-globe ml-1"></i> اللغة
                        <i class="fas fa-chevron-down ml-1 text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <main class="flex-1 p-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8">اختبار تجاوب شريط التنقل العلوي</h1>
            
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">أحجام الشاشة للاختبار:</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-red-100 p-4 rounded">
                        <h3 class="font-semibold">320px</h3>
                        <p class="text-sm">هواتف صغيرة</p>
                    </div>
                    <div class="bg-orange-100 p-4 rounded">
                        <h3 class="font-semibold">375px</h3>
                        <p class="text-sm">iPhone SE</p>
                    </div>
                    <div class="bg-yellow-100 p-4 rounded">
                        <h3 class="font-semibold">414px</h3>
                        <p class="text-sm">iPhone Plus</p>
                    </div>
                    <div class="bg-green-100 p-4 rounded">
                        <h3 class="font-semibold">768px</h3>
                        <p class="text-sm">تابلت</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4">الميزات المحسنة:</h2>
                <ul class="list-disc list-inside space-y-2">
                    <li>إصلاح تكرار أيقونة الهاتف</li>
                    <li>تحسين التجاوب للشاشات الصغيرة</li>
                    <li>تحسين عرض وسائل التواصل الاجتماعي</li>
                    <li>تحسين زر القائمة المحمولة</li>
                    <li>تحسين المسافات والأحجام</li>
                </ul>
            </div>
        </div>
    </main>

    <script>
        // عرض حجم الشاشة الحالي
        function updateScreenSize() {
            const width = window.innerWidth;
            document.getElementById('screenWidth').textContent = width;
        }

        window.addEventListener('resize', updateScreenSize);
        updateScreenSize();

        // محاكاة وظائف القائمة المحمولة
        document.getElementById('mobile-menu-toggle').addEventListener('click', function() {
            alert('تم النقر على زر القائمة المحمولة - في الموقع الفعلي ستفتح القائمة الجانبية');
        });
    </script>
</body>
</html>
