<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الزووم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        
        .test-image {
            width: 300px;
            height: 200px;
            object-fit: cover;
            cursor: zoom-in;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal img {
            max-width: 90%;
            max-height: 90%;
            cursor: zoom-in;
            transition: transform 0.3s ease;
        }
        
        .close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            color: white;
            font-size: 30px;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .test-btn {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>اختبار وظيفة الزووم</h1>
    
    <button class="test-btn" onclick="testZoom()">اختبار الزووم</button>
    
    <div>
        <h3>انقر على الصورة لفتح الزووم:</h3>
        <img src="https://via.placeholder.com/400x300/FFD700/000000?text=صورة+اختبار" 
             class="test-image" 
             onclick="openModal(this.src)" 
             alt="صورة اختبار">
    </div>
    
    <!-- Modal -->
    <div id="imageModal" class="modal">
        <span class="close-btn" onclick="closeModal()">&times;</span>
        <img id="modalImage" src="" alt="" onclick="toggleZoom(this)">
    </div>
    
    <script>
        let isZoomed = false;
        
        function testZoom() {
            console.log('Test zoom button clicked');
            alert('اختبار الزووم - انقر على الصورة لفتح المودال');
        }
        
        function openModal(imageSrc) {
            console.log('Opening modal for:', imageSrc);
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            modalImage.src = imageSrc;
            modalImage.style.transform = 'scale(1)';
            modalImage.style.cursor = 'zoom-in';
            isZoomed = false;
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            console.log('Modal opened successfully');
        }
        
        function closeModal() {
            console.log('Closing modal');
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            
            modalImage.style.transform = 'scale(1)';
            isZoomed = false;
        }
        
        function toggleZoom(img) {
            console.log('Toggle zoom called, current state:', isZoomed);
            
            if (isZoomed) {
                img.style.transform = 'scale(1)';
                img.style.cursor = 'zoom-in';
                isZoomed = false;
                console.log('Zoomed out');
            } else {
                img.style.transform = 'scale(2)';
                img.style.cursor = 'zoom-out';
                isZoomed = true;
                console.log('Zoomed in');
            }
        }
        
        // Close modal on ESC key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
        
        // Close modal on background click
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        console.log('Test zoom page loaded successfully');
    </script>
</body>
</html>
