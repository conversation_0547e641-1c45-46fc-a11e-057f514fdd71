/**
 * تحسينات المدونة - Blog Enhancements JavaScript
 * Features: Lazy Loading, Reading Progress, Share Functions, Animations
 */

class BlogEnhancements {
    constructor() {
        this.init();
    }

    init() {
        // انتظار تحميل DOM
        if (document.readyState === "loading") {
            document.addEventListener("DOMContentLoaded", () =>
                this.setupFeatures()
            );
        } else {
            this.setupFeatures();
        }
    }

    setupFeatures() {
        this.setupLazyLoading();
        this.setupReadingProgress();
        this.setupSmoothScrolling();
        this.setupShareButtons();
        this.setupAnimations();
        this.setupImageZoom();
        this.setupSearchFilters();
        this.setupNotifications();
    }

    // Lazy Loading للصور
    setupLazyLoading() {
        const images = document.querySelectorAll(".lazy-image");

        if ("IntersectionObserver" in window) {
            const imageObserver = new IntersectionObserver(
                (entries, observer) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.classList.add("loaded");
                            observer.unobserve(img);
                        }
                    });
                },
                {
                    threshold: 0.1,
                    rootMargin: "50px",
                }
            );

            images.forEach((img) => imageObserver.observe(img));
        } else {
            // Fallback للمتصفحات القديمة
            images.forEach((img) => img.classList.add("loaded"));
        }
    }

    // شريط تقدم القراءة
    setupReadingProgress() {
        const progressBar = document.querySelector(".reading-progress");
        const article = document.getElementById("post-content");

        if (!progressBar || !article) return;

        const updateProgress = () => {
            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;

            const articleBottom = articleTop + articleHeight;
            const windowBottom = scrollTop + windowHeight;

            if (scrollTop >= articleTop) {
                const progress = Math.min(
                    100,
                    ((windowBottom - articleTop) / articleHeight) * 100
                );
                progressBar.style.width = progress + "%";
            } else {
                progressBar.style.width = "0%";
            }
        };

        window.addEventListener("scroll", this.throttle(updateProgress, 10));
        updateProgress();
    }

    // التمرير السلس
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
            anchor.addEventListener("click", function (e) {
                e.preventDefault();
                const target = document.querySelector(
                    this.getAttribute("href")
                );
                if (target) {
                    target.scrollIntoView({
                        behavior: "smooth",
                        block: "start",
                    });
                }
            });
        });
    }

    // أزرار المشاركة
    setupShareButtons() {
        // تتبع المشاركات
        document.querySelectorAll(".share-button[href]").forEach((button) => {
            button.addEventListener("click", (e) => {
                const platform = this.getSharePlatform(button.href);
                this.trackShare(platform);

                // تأثير بصري
                button.style.transform = "scale(0.95)";
                setTimeout(() => {
                    button.style.transform = "scale(1)";
                }, 150);
            });
        });

        // زر نسخ الرابط
        window.copyToClipboard = (text) => {
            if (navigator.clipboard) {
                navigator.clipboard
                    .writeText(text)
                    .then(() => {
                        this.showNotification(
                            "تم نسخ الرابط بنجاح!",
                            "success"
                        );
                        this.updateCopyButton();
                    })
                    .catch(() => {
                        this.fallbackCopyTextToClipboard(text);
                    });
            } else {
                this.fallbackCopyTextToClipboard(text);
            }
        };
    }

    // الحصول على منصة المشاركة
    getSharePlatform(url) {
        if (url.includes("facebook")) return "facebook";
        if (url.includes("twitter")) return "twitter";
        if (url.includes("linkedin")) return "linkedin";
        if (url.includes("whatsapp")) return "whatsapp";
        return "unknown";
    }

    // تتبع المشاركات
    trackShare(platform) {
        console.log("مشاركة عبر:", platform);

        // يمكنك هنا إضافة كود Google Analytics أو أي نظام تتبع آخر
        if (typeof gtag !== "undefined") {
            gtag("event", "share", {
                method: platform,
                content_type: "article",
            });
        }

        // تحديث عداد المشاركات
        this.updateShareCount();
    }

    // تحديث عداد المشاركات
    updateShareCount() {
        const shareCount = document.getElementById("share-count");
        if (shareCount) {
            const currentCount = parseInt(
                shareCount.textContent.match(/\d+/)?.[0] || 0
            );
            shareCount.textContent = `${currentCount + 1} مشاركة`;
        }
    }

    // تحديث زر النسخ
    updateCopyButton() {
        const copyButtons = document.querySelectorAll(
            'button[onclick*="copyToClipboard"]'
        );
        copyButtons.forEach((button) => {
            const originalIcon = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-lg"></i>';
            button.classList.add("bg-green-500", "hover:bg-green-600");
            button.classList.remove("bg-gray-600", "hover:bg-gray-700");

            setTimeout(() => {
                button.innerHTML = originalIcon;
                button.classList.remove("bg-green-500", "hover:bg-green-600");
                button.classList.add("bg-gray-600", "hover:bg-gray-700");
            }, 2000);
        });
    }

    // نسخ النص (fallback)
    fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            document.execCommand("copy");
            this.showNotification("تم نسخ الرابط بنجاح!", "success");
            this.updateCopyButton();
        } catch (err) {
            this.showNotification("فشل في نسخ الرابط", "error");
        }

        document.body.removeChild(textArea);
    }

    // الحركات والتأثيرات
    setupAnimations() {
        const animatedElements = document.querySelectorAll(".fade-in");

        if ("IntersectionObserver" in window) {
            const animationObserver = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            entry.target.style.animationDelay =
                                Math.random() * 0.3 + "s";
                            entry.target.classList.add("fade-in");
                        }
                    });
                },
                {
                    threshold: 0.1,
                }
            );

            animatedElements.forEach((el) => {
                animationObserver.observe(el);
            });
        }

        // تأثيرات hover للبطاقات
        const cards = document.querySelectorAll(".blog-card");
        cards.forEach((card) => {
            card.addEventListener("mouseenter", () => {
                card.style.transform = "translateY(-8px)";
            });

            card.addEventListener("mouseleave", () => {
                card.style.transform = "translateY(0)";
            });
        });
    }

    // تكبير الصور
    setupImageZoom() {
        const images = document.querySelectorAll(".post-content img");

        images.forEach((img) => {
            img.addEventListener("click", () => {
                this.createImageModal(img);
            });

            img.style.cursor = "zoom-in";
        });
    }

    // إنشاء نافذة تكبير الصورة
    createImageModal(img) {
        const modal = document.createElement("div");
        modal.className =
            "fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4";
        modal.style.animation = "fadeIn 0.3s ease";

        const modalImg = document.createElement("img");
        modalImg.src = img.src;
        modalImg.alt = img.alt;
        modalImg.className = "max-w-full max-h-full object-contain rounded-lg";
        modalImg.style.animation = "scaleIn 0.3s ease";

        const closeBtn = document.createElement("button");
        closeBtn.innerHTML = '<i class="fas fa-times text-2xl"></i>';
        closeBtn.className =
            "absolute top-4 right-4 text-white bg-black bg-opacity-50 p-2 rounded-full hover:bg-opacity-75 transition-all";

        modal.appendChild(modalImg);
        modal.appendChild(closeBtn);
        document.body.appendChild(modal);

        // إغلاق النافذة
        const closeModal = () => {
            modal.style.animation = "fadeOut 0.3s ease";
            setTimeout(() => {
                document.body.removeChild(modal);
            }, 300);
        };

        closeBtn.addEventListener("click", closeModal);
        modal.addEventListener("click", (e) => {
            if (e.target === modal) closeModal();
        });

        document.addEventListener("keydown", function onKeyDown(e) {
            if (e.key === "Escape") {
                closeModal();
                document.removeEventListener("keydown", onKeyDown);
            }
        });
    }

    // فلاتر البحث
    setupSearchFilters() {
        const searchInput = document.querySelector('input[name="search"]');
        if (!searchInput) return;

        let searchTimeout;
        searchInput.addEventListener("input", (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });
    }

    // تنفيذ البحث
    performSearch(query) {
        if (query.length < 2) return;

        // هنا يمكنك إضافة منطق البحث الفوري
        console.log("البحث عن:", query);

        // مثال: إخفاء/إظهار المقالات بناءً على البحث
        const posts = document.querySelectorAll(".blog-card");
        posts.forEach((post) => {
            const title = post
                .querySelector("h3, h4")
                ?.textContent.toLowerCase();
            const excerpt = post.querySelector("p")?.textContent.toLowerCase();

            if (
                title?.includes(query.toLowerCase()) ||
                excerpt?.includes(query.toLowerCase())
            ) {
                post.style.display = "block";
                post.style.animation = "fadeIn 0.3s ease";
            } else {
                post.style.display = "none";
            }
        });
    }

    // نظام الإشعارات
    setupNotifications() {
        // إنشاء container للإشعارات
        if (!document.getElementById("notifications-container")) {
            const container = document.createElement("div");
            container.id = "notifications-container";
            container.className = "fixed top-4 right-4 z-50 space-y-2";
            document.body.appendChild(container);
        }
    }

    // إظهار إشعار
    showNotification(message, type = "success") {
        const container = document.getElementById("notifications-container");
        if (!container) return;

        const notification = document.createElement("div");
        notification.className = `notification ${type} show`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${
                    type === "success" ? "check" : "exclamation-triangle"
                } mr-2"></i>
                <span>${message}</span>
                <button class="ml-3 text-white opacity-75 hover:opacity-100" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        // إزالة الإشعار تلقائياً بعد 5 ثوانٍ
        setTimeout(() => {
            notification.classList.remove("show");
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    // تحسين الأداء - Throttle
    throttle(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // تحسين الأداء - Debounce
    debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    }
}

// تهيئة التحسينات
const blogEnhancements = new BlogEnhancements();

// إضافة أنماط CSS للحركات
const styles = `
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.notification {
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement("style");
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

// تصدير الكلاس للاستخدام العالمي
window.BlogEnhancements = BlogEnhancements;
