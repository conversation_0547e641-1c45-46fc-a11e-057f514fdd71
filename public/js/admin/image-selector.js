/**
 * Advanced Image Selector Component for Filament
 * Handles primary image selection with enhanced UX
 */

class AdvancedImageSelector {
    constructor(config = {}) {
        this.config = {
            primaryIndex: 0,
            images: [],
            fieldName: 'primary_image_index',
            enableLogging: false,
            animationDuration: 300,
            ...config
        };
        
        this.state = {
            primaryIndex: this.config.primaryIndex,
            images: [...this.config.images],
            loading: null,
            initialized: false
        };
        
        this.init();
    }
    
    /**
     * Initialize the component
     */
    init() {
        if (this.state.initialized) return;
        
        this.log('Initializing Advanced Image Selector', this.config);
        
        this.updateHiddenField();
        this.setupEventListeners();
        this.validateState();
        
        this.state.initialized = true;
        this.log('Advanced Image Selector initialized successfully');
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Listen for Livewire updates
        document.addEventListener('livewire:updated', () => {
            this.handleLivewireUpdate();
        });
        
        // Listen for form changes
        document.addEventListener('DOMContentLoaded', () => {
            this.watchFormChanges();
        });
        
        // Listen for custom events
        document.addEventListener('primary-image-changed', (event) => {
            this.handlePrimaryImageChanged(event);
        });
        
        // Listen for images updated event
        document.addEventListener('images-updated', (event) => {
            this.handleImagesUpdated(event);
        });
    }
    
    /**
     * Select primary image
     */
    selectPrimary(index) {
        if (this.state.loading === index) {
            this.log('Image selection already in progress', { index });
            return;
        }
        
        if (!this.isValidIndex(index)) {
            this.log('Invalid index provided', { index, totalImages: this.state.images.length });
            return;
        }
        
        this.log('Selecting primary image', { oldIndex: this.state.primaryIndex, newIndex: index });
        
        // Set loading state
        this.setLoading(index);
        
        // Update state
        const oldIndex = this.state.primaryIndex;
        this.state.primaryIndex = index;
        
        // Update UI
        this.updateUI(oldIndex, index);
        
        // Update hidden field
        this.updateHiddenField();
        
        // Trigger validation
        this.triggerValidation();
        
        // Dispatch custom event
        this.dispatchEvent('primary-image-selected', {
            oldIndex,
            newIndex: index,
            imageUrl: this.getImageUrl(this.state.images[index])
        });
        
        // Clear loading state
        setTimeout(() => {
            this.clearLoading();
        }, this.config.animationDuration);
    }
    
    /**
     * Update UI elements
     */
    updateUI(oldIndex, newIndex) {
        // Update primary badges
        this.updatePrimaryBadges(oldIndex, newIndex);
        
        // Update image containers
        this.updateImageContainers(oldIndex, newIndex);
        
        // Update image info
        this.updateImageInfo(oldIndex, newIndex);
    }
    
    /**
     * Update primary badges
     */
    updatePrimaryBadges(oldIndex, newIndex) {
        const containers = document.querySelectorAll('.advanced-image-selector .image-container');
        
        containers.forEach((container, index) => {
            const badge = container.querySelector('.primary-badge');
            const overlay = container.querySelector('.hover-overlay');
            
            if (index === newIndex) {
                // Show primary badge
                if (badge) badge.style.display = 'flex';
                if (overlay) overlay.style.display = 'none';
                container.classList.add('primary');
            } else {
                // Hide primary badge
                if (badge) badge.style.display = 'none';
                if (overlay) overlay.style.display = 'flex';
                container.classList.remove('primary');
            }
        });
    }
    
    /**
     * Update image containers
     */
    updateImageContainers(oldIndex, newIndex) {
        const containers = document.querySelectorAll('.advanced-image-selector .image-container');
        
        containers.forEach((container, index) => {
            if (index === newIndex) {
                container.classList.add('primary');
                container.setAttribute('aria-selected', 'true');
            } else {
                container.classList.remove('primary');
                container.setAttribute('aria-selected', 'false');
            }
        });
    }
    
    /**
     * Update image info
     */
    updateImageInfo(oldIndex, newIndex) {
        const infoElements = document.querySelectorAll('.advanced-image-selector .image-info');
        
        infoElements.forEach((info, index) => {
            const primaryLabel = info.querySelector('.primary-label');
            
            if (index === newIndex) {
                if (primaryLabel) primaryLabel.style.display = 'block';
            } else {
                if (primaryLabel) primaryLabel.style.display = 'none';
            }
        });
    }
    
    /**
     * Update hidden field
     */
    updateHiddenField() {
        const hiddenField = document.querySelector(`input[name="${this.config.fieldName}"]`);
        if (hiddenField) {
            hiddenField.value = this.state.primaryIndex;
            
            // Trigger events for Filament
            hiddenField.dispatchEvent(new Event('input', { bubbles: true }));
            hiddenField.dispatchEvent(new Event('change', { bubbles: true }));
            
            this.log('Hidden field updated', { 
                fieldName: this.config.fieldName, 
                value: this.state.primaryIndex 
            });
        } else {
            this.log('Hidden field not found', { fieldName: this.config.fieldName });
        }
    }
    
    /**
     * Trigger validation
     */
    triggerValidation() {
        const event = new CustomEvent('primary-image-changed', {
            detail: {
                primaryIndex: this.state.primaryIndex,
                fieldName: this.config.fieldName,
                imageUrl: this.getPrimaryImageUrl()
            }
        });
        
        document.dispatchEvent(event);
        this.log('Validation triggered', event.detail);
    }
    
    /**
     * Get image URL
     */
    getImageUrl(imagePath) {
        if (!imagePath) return '';
        
        if (imagePath.startsWith('http')) {
            return imagePath;
        }
        
        if (imagePath.startsWith('/storage/')) {
            return imagePath;
        }
        
        return `/storage/${imagePath}`;
    }
    
    /**
     * Get primary image URL
     */
    getPrimaryImageUrl() {
        const primaryImage = this.state.images[this.state.primaryIndex];
        return primaryImage ? this.getImageUrl(primaryImage) : '';
    }
    
    /**
     * Validate current state
     */
    validateState() {
        if (this.state.images.length === 0) {
            this.state.primaryIndex = 0;
            return;
        }
        
        if (this.state.primaryIndex >= this.state.images.length) {
            this.state.primaryIndex = 0;
            this.updateHiddenField();
        }
        
        if (!this.state.images[this.state.primaryIndex]) {
            this.state.primaryIndex = this.findFirstValidIndex();
            this.updateHiddenField();
        }
    }
    
    /**
     * Find first valid image index
     */
    findFirstValidIndex() {
        for (let i = 0; i < this.state.images.length; i++) {
            if (this.state.images[i] && this.state.images[i].trim() !== '') {
                return i;
            }
        }
        return 0;
    }
    
    /**
     * Check if index is valid
     */
    isValidIndex(index) {
        return index >= 0 && 
               index < this.state.images.length && 
               this.state.images[index] && 
               this.state.images[index].trim() !== '';
    }
    
    /**
     * Set loading state
     */
    setLoading(index) {
        this.state.loading = index;
        
        const container = document.querySelector(
            `.advanced-image-selector .image-container:nth-child(${index + 1})`
        );
        
        if (container) {
            const loadingOverlay = container.querySelector('.loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        }
    }
    
    /**
     * Clear loading state
     */
    clearLoading() {
        const loadingIndex = this.state.loading;
        this.state.loading = null;
        
        if (loadingIndex !== null) {
            const container = document.querySelector(
                `.advanced-image-selector .image-container:nth-child(${loadingIndex + 1})`
            );
            
            if (container) {
                const loadingOverlay = container.querySelector('.loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        }
    }
    
    /**
     * Handle Livewire updates
     */
    handleLivewireUpdate() {
        this.log('Livewire updated, refreshing state');
        // Re-validate state after Livewire updates
        setTimeout(() => {
            this.validateState();
        }, 100);
    }
    
    /**
     * Handle primary image changed event
     */
    handlePrimaryImageChanged(event) {
        this.log('Primary image changed event received', event.detail);
    }
    
    /**
     * Handle images updated event
     */
    handleImagesUpdated(event) {
        this.log('Images updated event received', event.detail);
        
        if (event.detail && event.detail.images) {
            this.updateImages(event.detail.images);
        }
    }
    
    /**
     * Update images array
     */
    updateImages(newImages) {
        this.state.images = Array.isArray(newImages) ? [...newImages] : [];
        this.validateState();
        this.log('Images updated', { totalImages: this.state.images.length });
    }
    
    /**
     * Watch for form changes
     */
    watchFormChanges() {
        const imageField = document.querySelector('input[name="product_images"]');
        if (imageField) {
            imageField.addEventListener('change', (event) => {
                this.handleImageFieldChange(event);
            });
        }
    }
    
    /**
     * Handle image field changes
     */
    handleImageFieldChange(event) {
        this.log('Image field changed', event);
        // Handle changes to the main image field
    }
    
    /**
     * Dispatch custom event
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        document.dispatchEvent(event);
    }
    
    /**
     * Log messages (if logging enabled)
     */
    log(message, data = null) {
        if (this.config.enableLogging) {
            console.log(`[AdvancedImageSelector] ${message}`, data);
        }
    }
    
    /**
     * Get current state
     */
    getState() {
        return { ...this.state };
    }
    
    /**
     * Get total images count
     */
    getTotalImages() {
        return this.state.images.length;
    }
    
    /**
     * Check if has multiple images
     */
    hasMultipleImages() {
        return this.state.images.length > 1;
    }
}

// Export for use in Alpine.js
window.AdvancedImageSelector = AdvancedImageSelector;
