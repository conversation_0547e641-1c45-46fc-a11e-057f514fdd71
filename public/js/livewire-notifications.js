// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Helper function to find Livewire component
function findLivewireComponent(componentName) {
    try {
        // Wait for Livewire to be ready
        if (!window.Livewire) {
            console.warn("Livewire not loaded yet");
            return null;
        }

        const components = window.Livewire.all();

        // Try to find by component name first
        for (let component of components) {
            if (
                component.name === componentName ||
                (component.fingerprint &&
                    component.fingerprint.name === componentName)
            ) {
                return component;
            }
        }

        // Try to find by checking the element's wire:id
        const elements = document.querySelectorAll("[wire\\:id]");
        for (let element of elements) {
            const wireId = element.getAttribute("wire:id");
            const component = window.Livewire.find(wireId);
            if (
                component &&
                (component.name === componentName ||
                    (component.fingerprint &&
                        component.fingerprint.name === componentName))
            ) {
                return component;
            }
        }

        // Fallback to first component if specific not found
        console.warn(
            `Component '${componentName}' not found, using first available component`
        );
        return components.length > 0 ? components[0] : null;
    } catch (error) {
        console.error("Error finding Livewire component:", error);
        return null;
    }
}

// Legacy filter functions - DEPRECATED
// هذه الدوال لم تعد مستخدمة مع النظام الجديد للفلاتر
// تم الاحتفاظ بها للتوافق مع الكود القديم فقط
window.setFilter = function (componentName, property, value) {
    console.warn(
        "setFilter is deprecated. Use Livewire wire:model.live instead."
    );
    try {
        console.log(`Setting filter: ${componentName}.${property} = ${value}`);
        const component = findLivewireComponent(componentName);
        if (component) {
            component.set(property, value);
            console.log(`Filter set successfully: ${property} = ${value}`);
        } else {
            console.warn("Livewire component not found:", componentName);
            // Try alternative approach - find by class or data attribute
            const elements = document.querySelectorAll(
                `[data-component="${componentName}"], .${componentName}`
            );
            if (elements.length > 0) {
                console.log("Found element, trying alternative approach");
                // Dispatch custom event as fallback
                elements[0].dispatchEvent(
                    new CustomEvent("filter-change", {
                        detail: { property, value },
                    })
                );
            }
        }
    } catch (error) {
        console.error("Error setting filter:", error);
    }
};

window.callMethod = function (componentName, method, ...args) {
    console.warn("callMethod is deprecated. Use Livewire wire:click instead.");
    try {
        console.log(
            `Calling method: ${componentName}.${method}(${args.join(", ")})`
        );
        const component = findLivewireComponent(componentName);
        if (component) {
            component.call(method, ...args);
            console.log(`Method called successfully: ${method}`);
        } else {
            console.warn("Livewire component not found:", componentName);
        }
    } catch (error) {
        console.error("Error calling method:", error);
    }
};

// Quantity sync function
function syncQuantityInput() {
    const quantityInput = document.getElementById("quantity-input");
    if (quantityInput) {
        // Listen for Livewire updates
        document.addEventListener("livewire:updated", function (event) {
            const component = event.detail.component;
            if (
                component.name === "product-detail" &&
                component.data.quantity !== undefined
            ) {
                quantityInput.value = component.data.quantity;
            }
        });
    }
}

// Livewire Notifications Handler
document.addEventListener("DOMContentLoaded", function () {
    // Initialize quantity sync
    syncQuantityInput();

    // الاستماع لأحداث Livewire
    document.addEventListener("livewire:init", () => {
        // الاستماع لرسائل النجاح والخطأ
        Livewire.on("show-message", (event) => {
            const data = event[0] || event;
            showNotification(data.message, data.type);
        });

        // الاستماع لتحديث السلة
        Livewire.on("cart-updated", (event) => {
            const data = event[0] || event;
            updateCartCount(data.count);
        });

        // الاستماع لتحديث المفضلة
        Livewire.on("wishlist-updated", (event) => {
            const data = event[0] || event;
            updateWishlistCount(data.count);
        });
    });
});

// دالة عرض الإشعارات
function showNotification(message, type = "info") {
    // إنشاء عنصر الإشعار
    const notification = document.createElement("div");
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="notification-icon ${getNotificationIcon(type)}"></i>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="closeNotification(this)">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 500px;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        font-family: 'Cairo', sans-serif;
        direction: rtl;
    `;

    // تطبيق ألوان حسب النوع
    switch (type) {
        case "success":
            notification.style.background =
                "linear-gradient(135deg, #10b981 0%, #059669 100%)";
            notification.style.color = "white";
            break;
        case "error":
            notification.style.background =
                "linear-gradient(135deg, #ef4444 0%, #dc2626 100%)";
            notification.style.color = "white";
            break;
        case "warning":
            notification.style.background =
                "linear-gradient(135deg, #f59e0b 0%, #d97706 100%)";
            notification.style.color = "white";
            break;
        default:
            notification.style.background =
                "linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)";
            notification.style.color = "white";
    }

    // إضافة إلى الصفحة
    document.body.appendChild(notification);

    // تحريك الإشعار للداخل
    setTimeout(() => {
        notification.style.transform = "translateX(0)";
    }, 100);

    // إزالة الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        closeNotification(notification.querySelector(".notification-close"));
    }, 5000);
}

// دالة إغلاق الإشعار
function closeNotification(closeBtn) {
    const notification = closeBtn.closest(".notification");
    notification.style.transform = "translateX(100%)";
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// دالة الحصول على أيقونة الإشعار
function getNotificationIcon(type) {
    switch (type) {
        case "success":
            return "fas fa-check-circle";
        case "error":
            return "fas fa-exclamation-circle";
        case "warning":
            return "fas fa-exclamation-triangle";
        default:
            return "fas fa-info-circle";
    }
}

// دالة تحديث عداد السلة
function updateCartCount(count = null) {
    if (count === null) {
        // جلب العدد من الخادم
        fetch("/api/cart/count")
            .then((response) => response.json())
            .then((data) => {
                updateCartCountDisplay(data.count);
            })
            .catch((error) =>
                console.error("Error fetching cart count:", error)
            );
    } else {
        updateCartCountDisplay(count);
    }
}

// دالة تحديث عرض عداد السلة
function updateCartCountDisplay(count) {
    const cartCountElements = document.querySelectorAll(
        ".cart-count, [data-cart-count]"
    );
    cartCountElements.forEach((element) => {
        element.textContent = count;
        element.style.display = count > 0 ? "inline" : "none";
    });
}

// دالة تحديث عداد المفضلة
function updateWishlistCount(count = null) {
    if (count === null) {
        // جلب العدد من الخادم
        fetch("/api/wishlist/count")
            .then((response) => response.json())
            .then((data) => {
                updateWishlistCountDisplay(data.count);
            })
            .catch((error) =>
                console.error("Error fetching wishlist count:", error)
            );
    } else {
        updateWishlistCountDisplay(count);
    }
}

// دالة تحديث عرض عداد المفضلة
function updateWishlistCountDisplay(count) {
    const wishlistCountElements = document.querySelectorAll(
        ".wishlist-count, [data-wishlist-count]"
    );
    wishlistCountElements.forEach((element) => {
        element.textContent = count;
        element.style.display = count > 0 ? "inline" : "none";
    });
}

// تحديث العدادات عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function () {
    updateCartCount();
    updateWishlistCount();
});
