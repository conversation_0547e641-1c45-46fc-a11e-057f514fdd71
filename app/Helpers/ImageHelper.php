<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class ImageHelper
{
    /**
     * Get the full URL for an image stored in the public disk
     *
     * @param string|null $imagePath
     * @param string|null $defaultImage
     * @return string
     */
    public static function getImageUrl(?string $imagePath, ?string $defaultImage = null): string
    {
        if (empty($imagePath)) {
            return $defaultImage ? asset($defaultImage) : asset('images/placeholder.png');
        }

        // Check if the image exists in storage
        if (Storage::disk('public')->exists($imagePath)) {
            return Storage::disk('public')->url($imagePath);
        }

        // If image doesn't exist, return default
        return $defaultImage ? asset($defaultImage) : asset('images/placeholder.png');
    }

    /**
     * Get site logo URL with fallback support
     *
     * @param string|null $logoPath
     * @return string
     */
    public static function getSiteLogo(?string $logoPath): string
    {
        // إذا كان هناك شعار مخصص، استخدمه
        if (!empty($logoPath) && Storage::disk('public')->exists($logoPath)) {
            return asset('storage/' . $logoPath);
        }

        // البحث عن شعار افتراضي في المجلد العام
        $fallbackLogos = [
            'images/logo.png',
            'images/logo.jpg',
            'images/logo.jpeg',
            'images/logo.svg',
            'images/logo-fallback.svg'
        ];

        foreach ($fallbackLogos as $fallback) {
            if (file_exists(public_path($fallback))) {
                return asset($fallback);
            }
        }

        // إذا لم يوجد أي شعار، إرجاع SVG fallback
        return asset('images/logo-fallback.svg');
    }

    /**
     * Get site favicon URL
     *
     * @param string|null $faviconPath
     * @return string
     */
    public static function getSiteFavicon(?string $faviconPath): string
    {
        return self::getImageUrl($faviconPath, 'images/favicon.png');
    }

    /**
     * Get product image URL
     *
     * @param string|null $imagePath
     * @return string
     */
    public static function getProductImage(?string $imagePath): string
    {
        return self::getImageUrl($imagePath, 'images/product-placeholder.png');
    }

    /**
     * Get category image URL
     *
     * @param string|null $imagePath
     * @return string
     */
    public static function getCategoryImage(?string $imagePath): string
    {
        return self::getImageUrl($imagePath, 'images/category-placeholder.png');
    }

    /**
     * Get user avatar URL
     *
     * @param string|null $imagePath
     * @param string $userName
     * @return string
     */
    public static function getUserAvatar(?string $imagePath, string $userName = 'User'): string
    {
        if (empty($imagePath)) {
            // Generate avatar using UI Avatars service
            return 'https://ui-avatars.com/api/?name=' . urlencode($userName) . '&color=7F9CF5&background=EBF4FF&size=128';
        }

        return self::getImageUrl($imagePath);
    }

    /**
     * Check if an image exists in storage
     *
     * @param string|null $imagePath
     * @return bool
     */
    public static function imageExists(?string $imagePath): bool
    {
        if (empty($imagePath)) {
            return false;
        }

        return Storage::disk('public')->exists($imagePath);
    }

    /**
     * Delete an image from storage
     *
     * @param string|null $imagePath
     * @return bool
     */
    public static function deleteImage(?string $imagePath): bool
    {
        if (empty($imagePath)) {
            return false;
        }

        if (Storage::disk('public')->exists($imagePath)) {
            return Storage::disk('public')->delete($imagePath);
        }

        return false;
    }

    /**
     * Get image size information
     *
     * @param string|null $imagePath
     * @return array|null
     */
    public static function getImageInfo(?string $imagePath): ?array
    {
        if (empty($imagePath) || !self::imageExists($imagePath)) {
            return null;
        }

        $fullPath = Storage::disk('public')->path($imagePath);

        if (!file_exists($fullPath)) {
            return null;
        }

        $imageInfo = getimagesize($fullPath);

        if ($imageInfo === false) {
            return null;
        }

        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'type' => $imageInfo[2],
            'mime' => $imageInfo['mime'],
            'size' => filesize($fullPath),
            'url' => self::getImageUrl($imagePath)
        ];
    }
}
