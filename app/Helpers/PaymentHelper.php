<?php

namespace App\Helpers;

use App\Services\SuperAdminSettingsService;

class PaymentHelper
{
    protected $superAdminSettings;

    public function __construct()
    {
        $this->superAdminSettings = app(SuperAdminSettingsService::class);
    }

    /**
     * الحصول على طرق الدفع المتاحة
     *
     * @return array
     */
    public function getAvailablePaymentMethods(): array
    {
        $methods = [];

        if ($this->superAdminSettings->isCreditCardEnabled()) {
            $methods['credit_card'] = [
                'name' => __('بطاقة ائتمانية'),
                'icon' => 'credit-card',
                'description' => __('ادفع بأمان باستخدام بطاقتك الائتمانية'),
                'enabled' => true
            ];
        }

        if ($this->superAdminSettings->isPaypalEnabled()) {
            $methods['paypal'] = [
                'name' => __('PayPal'),
                'icon' => 'paypal',
                'description' => __('ادفع بأمان باستخدام حساب PayPal'),
                'enabled' => true
            ];
        }

        if ($this->superAdminSettings->isBankTransferEnabled()) {
            $methods['bank_transfer'] = [
                'name' => __('تحويل بنكي'),
                'icon' => 'bank',
                'description' => __('ادفع عن طريق التحويل البنكي'),
                'enabled' => true
            ];
        }

        if ($this->superAdminSettings->isCashOnDeliveryEnabled()) {
            $methods['cash_on_delivery'] = [
                'name' => __('الدفع عند الاستلام'),
                'icon' => 'cash',
                'description' => __('ادفع نقداً عند استلام الطلب'),
                'enabled' => true
            ];
        }

        if ($this->superAdminSettings->isFawryEnabled()) {
            $methods['fawry'] = [
                'name' => __('فوري'),
                'icon' => 'fawry',
                'description' => __('ادفع باستخدام خدمة فوري'),
                'enabled' => true
            ];
        }

        return $methods;
    }

    /**
     * فحص إذا كانت طريقة دفع معينة متاحة
     *
     * @param string $method
     * @return bool
     */
    public function isPaymentMethodAvailable(string $method): bool
    {
        $availableMethods = $this->getAvailablePaymentMethods();
        return isset($availableMethods[$method]) && $availableMethods[$method]['enabled'];
    }

    /**
     * الحصول على إعدادات Stripe
     *
     * @return array
     */
    public function getStripeConfig(): array
    {
        return [
            'key' => $this->superAdminSettings->getStripeKey(),
            'secret' => $this->superAdminSettings->getStripeSecret(),
            'sandbox' => $this->superAdminSettings->isStripeSandboxMode(),
        ];
    }

    /**
     * الحصول على إعدادات PayPal
     *
     * @return array
     */
    public function getPaypalConfig(): array
    {
        return [
            'client_id' => $this->superAdminSettings->getPaypalClientId(),
            'secret' => $this->superAdminSettings->getPaypalSecret(),
            'sandbox' => $this->superAdminSettings->isPaypalSandboxMode(),
        ];
    }

    /**
     * فحص إذا كان هناك أي طريقة دفع متاحة
     *
     * @return bool
     */
    public function hasAvailablePaymentMethods(): bool
    {
        return count($this->getAvailablePaymentMethods()) > 0;
    }

    /**
     * الحصول على رسالة عدم توفر طرق الدفع
     *
     * @return string
     */
    public function getNoPaymentMethodsMessage(): string
    {
        return __('عذراً، لا توجد طرق دفع متاحة حالياً. يرجى المحاولة لاحقاً أو التواصل معنا.');
    }
}
