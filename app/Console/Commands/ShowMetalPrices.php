<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;
use Carbon\Carbon;

class ShowMetalPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'prices:show
                            {--date=today : التاريخ (today, yesterday, أو YYYY-MM-DD)}
                            {--metal= : نوع المعدن المحدد (gold, silver, platinum)}
                            {--latest : عرض آخر الأسعار المتاحة}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'عرض أسعار المعادن الحالية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📊 أسعار المعادن الحالية');
        $this->newLine();

        if ($this->option('latest')) {
            return $this->showLatestPrices();
        }

        $date = $this->getDateFromOption();
        $metal = $this->option('metal');

        if ($metal) {
            return $this->showSpecificMetal($metal, $date);
        }

        return $this->showAllPrices($date);
    }

    private function showLatestPrices()
    {
        $this->info('📅 آخر الأسعار المتاحة:');
        $this->newLine();

        $latestDate = MetalPrice::where('currency', 'EGP')->max('price_date');

        if (!$latestDate) {
            $this->warn('⚠️  لا توجد أسعار متاحة في قاعدة البيانات');
            return 1;
        }

        $this->info("التاريخ: " . Carbon::parse($latestDate)->format('Y-m-d (l)'));
        $this->newLine();

        return $this->showAllPrices($latestDate);
    }

    private function showAllPrices($date)
    {
        // أسعار الذهب
        $this->showGoldPrices($date);
        $this->newLine();

        // أسعار الفضة
        $this->showSilverPrices($date);

        return 0;
    }

    private function showGoldPrices($date)
    {
        $goldPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $date)
            ->orderByRaw("FIELD(purity, '24K', '22K', '21K', '18K', '14K')")
            ->get();

        if ($goldPrices->isEmpty()) {
            $this->warn('⚠️  لا توجد أسعار ذهب لتاريخ: ' . $date);
            return;
        }

        $this->info('🥇 أسعار الذهب:');

        $tableData = [];
        foreach ($goldPrices as $price) {
            $tableData[] = [
                $price->purity,
                number_format($price->price_per_gram, 2) . ' ج.م',
                number_format($price->price_per_ounce, 2) . ' ج.م',
                Carbon::parse($price->updated_at)->diffForHumans()
            ];
        }

        $this->table(
            ['العيار', 'سعر الجرام', 'سعر الأوقية', 'آخر تحديث'],
            $tableData
        );
    }

    private function showSilverPrices($date)
    {
        $silverPrices = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $date)
            ->orderBy('purity')
            ->get();

        if ($silverPrices->isEmpty()) {
            $this->warn('⚠️  لا توجد أسعار فضة لتاريخ: ' . $date);
            return;
        }

        $this->info('🥈 أسعار الفضة:');

        $tableData = [];
        foreach ($silverPrices as $price) {
            $tableData[] = [
                'فضة ' . $price->purity,
                number_format($price->price_per_gram, 2) . ' ج.م',
                number_format($price->price_per_ounce, 2) . ' ج.م',
                Carbon::parse($price->updated_at)->diffForHumans()
            ];
        }

        $this->table(
            ['النوع', 'سعر الجرام', 'سعر الأوقية', 'آخر تحديث'],
            $tableData
        );
    }



    private function showSpecificMetal($metal, $date)
    {
        $this->info("📊 أسعار {$metal} لتاريخ: {$date}");
        $this->newLine();

        switch ($metal) {
            case 'gold':
                $this->showGoldPrices($date);
                break;
            case 'silver':
                $this->showSilverPrices($date);
                break;
            default:
                $this->error('❌ نوع المعدن غير صحيح. استخدم: gold أو silver');
                return 1;
        }

        return 0;
    }

    private function getDateFromOption()
    {
        $dateOption = $this->option('date');

        switch ($dateOption) {
            case 'today':
                return Carbon::today()->format('Y-m-d');
            case 'yesterday':
                return Carbon::yesterday()->format('Y-m-d');
            default:
                try {
                    return Carbon::parse($dateOption)->format('Y-m-d');
                } catch (\Exception) {
                    $this->warn("⚠️  تنسيق التاريخ غير صحيح، سيتم استخدام تاريخ اليوم");
                    return Carbon::today()->format('Y-m-d');
                }
        }
    }
}
