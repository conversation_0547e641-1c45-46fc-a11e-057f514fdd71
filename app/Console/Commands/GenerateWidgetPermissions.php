<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;

class GenerateWidgetPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shield:generate-widget-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنشاء صلاحيات مخصصة للويدجت للتحكم فيها من خلال الأدوار';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎯 إنشاء صلاحيات الويدجت المخصصة...');
        $this->newLine();

        // قائمة الويدجت مع أسمائها العربية
        $widgets = [
            'MainStatsOverview' => 'الإحصائيات الرئيسية',
            'ContentStatsWidget' => 'إحصائيات المحتوى',
            'EngagementStatsWidget' => 'إحصائيات التفاعل',
            'MetalPricesWidget' => 'أسعار المعادن',
            'RecentOrdersWidget' => 'الطلبات الحديثة',
            'UpcomingAppointmentsWidget' => 'المواعيد القادمة',
            'SalesChartWidget' => 'مخطط المبيعات',
            'SystemStatusWidget' => 'حالة النظام',
        ];

        $createdCount = 0;
        $existingCount = 0;

        foreach ($widgets as $widgetClass => $arabicName) {
            $permissionName = "widget_{$widgetClass}";
            
            $permission = Permission::firstOrCreate(
                [
                    'name' => $permissionName,
                    'guard_name' => 'web'
                ]
            );

            if ($permission->wasRecentlyCreated) {
                $this->line("✅ تم إنشاء صلاحية: {$permissionName} ({$arabicName})");
                $createdCount++;
            } else {
                $this->line("ℹ️ الصلاحية موجودة: {$permissionName} ({$arabicName})");
                $existingCount++;
            }
        }

        $this->newLine();
        $this->info("📊 ملخص العملية:");
        $this->line("   ✅ تم إنشاء {$createdCount} صلاحية جديدة");
        $this->line("   ℹ️ {$existingCount} صلاحية موجودة مسبقاً");
        $this->line("   📈 إجمالي صلاحيات الويدجت: " . count($widgets));

        $this->newLine();
        $this->comment('💡 يمكنك الآن إدارة هذه الصلاحيات من خلال:');
        $this->line('   - صفحة الأدوار في لوحة التحكم');
        $this->line('   - تعديل الأدوار وإضافة/إزالة صلاحيات الويدجت');
        $this->line('   - كل ويدجت له صلاحية منفصلة للتحكم الدقيق');

        return Command::SUCCESS;
    }
}
