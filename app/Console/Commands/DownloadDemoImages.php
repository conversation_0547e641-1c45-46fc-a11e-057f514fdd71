<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class DownloadDemoImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:download-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download demo images for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to download demo images...');

        // Ensure directories exist
        $this->ensureDirectoriesExist();

        // Download product images (jewelry)
        $this->downloadProductImages();

        // Download category images
        $this->downloadCategoryImages();

        // Download hero/slider images
        $this->downloadHeroImages();

        // Download testimonial images
        $this->downloadTestimonialImages();

        // Download Instagram-style images
        $this->downloadInstagramImages();

        // Download logo
        $this->downloadLogo();

        $this->info('All demo images have been downloaded successfully!');
    }

    /**
     * Ensure all required directories exist.
     */
    private function ensureDirectoriesExist()
    {
        $directories = [
            'public/images',
            'public/images/products',
            'public/images/categories',
            'public/images/hero',
            'public/images/testimonials',
            'public/images/instagram',
            'public/images/payment',
        ];

        foreach ($directories as $directory) {
            if (!File::exists($directory)) {
                File::makeDirectory($directory, 0755, true);
                $this->info("Created directory: {$directory}");
            }
        }
    }

    /**
     * Download product images (jewelry).
     */
    private function downloadProductImages()
    {
        $this->info('Downloading product images...');

        $jewelryImages = [
            'https://images.unsplash.com/photo-1617038260897-41a1f14a8ca0?q=80&w=600&auto=format&fit=crop', // Gold ring
            'https://images.unsplash.com/photo-1611652022419-a9419f74343d?q=80&w=600&auto=format&fit=crop', // Diamond ring
            'https://images.unsplash.com/photo-1599643477877-530eb83abc8e?q=80&w=600&auto=format&fit=crop', // Gold necklace
            'https://images.unsplash.com/photo-1602173574767-37ac01994b2a?q=80&w=600&auto=format&fit=crop', // Bracelet
            'https://images.unsplash.com/photo-1603561596112-0a132b757442?q=80&w=600&auto=format&fit=crop', // Earrings
            'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?q=80&w=600&auto=format&fit=crop', // Gold set
            'https://images.unsplash.com/photo-1605100804763-247f67b3557e?q=80&w=600&auto=format&fit=crop', // Diamond necklace
            'https://images.unsplash.com/photo-1608042314453-ae338d80c427?q=80&w=600&auto=format&fit=crop', // Luxury watch
            'https://images.unsplash.com/photo-1633810542706-90e5ff7557be?q=80&w=600&auto=format&fit=crop', // Gold bracelet
            'https://images.unsplash.com/photo-1611591437281-460bfbe1220a?q=80&w=600&auto=format&fit=crop', // Pendant
            'https://images.unsplash.com/photo-1586104195538-050b9f74f58e?q=80&w=600&auto=format&fit=crop', // Luxury earrings
            'https://images.unsplash.com/photo-1589207212797-cfd578ff60b4?q=80&w=600&auto=format&fit=crop', // Diamond bracelet
        ];

        foreach ($jewelryImages as $index => $url) {
            $filename = 'product-' . ($index + 1) . '.jpg';
            $this->downloadImage($url, 'public/images/products/' . $filename);
        }
    }

    /**
     * Download category images.
     */
    private function downloadCategoryImages()
    {
        $this->info('Downloading category images...');

        $categoryImages = [
            'https://images.unsplash.com/photo-1605100804763-247f67b3557e?q=80&w=600&auto=format&fit=crop', // Necklaces
            'https://images.unsplash.com/photo-1611652022419-a9419f74343d?q=80&w=600&auto=format&fit=crop', // Rings
            'https://images.unsplash.com/photo-1603561596112-0a132b757442?q=80&w=600&auto=format&fit=crop', // Earrings
            'https://images.unsplash.com/photo-1602173574767-37ac01994b2a?q=80&w=600&auto=format&fit=crop', // Bracelets
            'https://images.unsplash.com/photo-1608042314453-ae338d80c427?q=80&w=600&auto=format&fit=crop', // Watches
            'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?q=80&w=600&auto=format&fit=crop', // Sets
        ];

        foreach ($categoryImages as $index => $url) {
            $filename = 'category-' . ($index + 1) . '.jpg';
            $this->downloadImage($url, 'public/images/categories/' . $filename);
        }
    }

    /**
     * Download hero/slider images.
     */
    private function downloadHeroImages()
    {
        $this->info('Downloading hero/slider images...');

        $heroImages = [
            'https://images.unsplash.com/photo-1601121141461-9d6647bca1ed?q=80&w=1200&auto=format&fit=crop', // Jewelry display
            'https://images.unsplash.com/photo-1584302179602-e4c3d3fd629d?q=80&w=1200&auto=format&fit=crop', // Gold jewelry
            'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?q=80&w=1200&auto=format&fit=crop', // Luxury jewelry
        ];

        foreach ($heroImages as $index => $url) {
            $filename = 'hero-' . ($index + 1) . '.jpg';
            $this->downloadImage($url, 'public/images/hero/' . $filename);
        }
    }

    /**
     * Download testimonial images.
     */
    private function downloadTestimonialImages()
    {
        $this->info('Downloading testimonial images...');

        $testimonialImages = [
            'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=300&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=300&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=300&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=300&auto=format&fit=crop',
        ];

        foreach ($testimonialImages as $index => $url) {
            $filename = 'user-' . ($index + 1) . '.jpg';
            $this->downloadImage($url, 'public/images/testimonials/' . $filename);
        }
    }

    /**
     * Download Instagram-style images.
     */
    private function downloadInstagramImages()
    {
        $this->info('Downloading Instagram-style images...');

        $instagramImages = [
            'https://images.unsplash.com/photo-1611591437281-460bfbe1220a?q=80&w=400&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1599643477877-530eb83abc8e?q=80&w=400&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1617038260897-41a1f14a8ca0?q=80&w=400&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1603561596112-0a132b757442?q=80&w=400&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1605100804763-247f67b3557e?q=80&w=400&auto=format&fit=crop',
            'https://images.unsplash.com/photo-1608042314453-ae338d80c427?q=80&w=400&auto=format&fit=crop',
        ];

        foreach ($instagramImages as $index => $url) {
            $filename = 'post-' . ($index + 1) . '.jpg';
            $this->downloadImage($url, 'public/images/instagram/' . $filename);
        }
    }

    /**
     * Download logo.
     */
    private function downloadLogo()
    {
        $this->info('Downloading logo...');
        
        // Gold jewelry logo
        $logoUrl = 'https://img.freepik.com/free-vector/elegant-jewelry-logo-template_23-2149557872.jpg?w=300&t=st=1714932000~exp=1714932600~hmac=5e2e4e7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e7b7e';
        
        $this->downloadImage($logoUrl, 'public/images/logo.png');
    }

    /**
     * Download an image from URL and save it to the specified path.
     */
    private function downloadImage($url, $path)
    {
        try {
            $response = Http::timeout(30)->get($url);
            
            if ($response->successful()) {
                File::put($path, $response->body());
                $this->line("Downloaded: " . basename($path));
            } else {
                $this->error("Failed to download image from {$url}. Status: " . $response->status());
            }
        } catch (\Exception $e) {
            $this->error("Error downloading image from {$url}: " . $e->getMessage());
        }
    }
}
