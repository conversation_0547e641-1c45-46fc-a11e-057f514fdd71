<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;

class InitSiteSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'site:init-settings';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize site settings with default values';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Initializing site settings...');

        // Check if settings already exist
        $settings = SiteSetting::first();

        if ($settings) {
            if ($this->confirm('Site settings already exist. Do you want to reset them to default values?', false)) {
                $settings->delete();
            } else {
                $this->info('Operation cancelled.');
                return;
            }
        }

        // Create default settings
        $settings = SiteSetting::create([
            // Información básica del sitio
            'site_name' => 'مجوهرات مكة جولد جروب',
            'site_description' => 'متجر مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية',

            // Información de contacto
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+20 ************',
            'address' => 'القاهرة، مصر',

            // Redes sociales
            'facebook_url' => 'https://www.facebook.com/makkahgold',
            'instagram_url' => 'https://www.instagram.com/makkahgold',
            'twitter_url' => 'https://www.twitter.com/makkahgold',
            'whatsapp_number' => '+201234567890',

            // Precios del oro
            'gold_price_24k' => 3200.00,
            'gold_price_21k' => 2800.00,
            'gold_price_18k' => 2400.00,

            // Texto del pie de página
            'footer_text' => '&copy; ' . date('Y') . ' مجوهرات مكة جولد جروب. جميع الحقوق محفوظة.',

            // Configuración de SEO
            'meta_title' => 'مجوهرات مكة جولد جروب - مجوهرات ذهبية وفضية فاخرة',
            'meta_description' => 'متجر مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية بتصاميم فريدة وجودة عالية',
            'meta_keywords' => 'مجوهرات, ذهب, فضة, خواتم, أساور, قلائد, هدايا, مكة جولد',

            // Configuración de la tienda
            'maintenance_mode' => false,
            'maintenance_message' => 'الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.',

            // Configuración de envío
            'shipping_cost' => 50.00,
            'free_shipping_threshold' => 1000.00,

            // Configuración de impuestos
            'tax_rate' => 14.00,
            'prices_include_tax' => true,

            // Configuración de la página de inicio
            'show_featured_products' => true,
            'show_new_arrivals' => true,
            'show_categories' => true,
            'show_gold_prices' => true,
            'show_features' => true,
            'show_testimonials' => true,
            'show_newsletter' => true,
        ]);

        $this->info('Site settings initialized successfully!');
        $this->table(
            ['Setting', 'Value'],
            [
                ['site_name', $settings->site_name],
                ['contact_email', $settings->contact_email],
                ['maintenance_mode', $settings->maintenance_mode ? 'Yes' : 'No'],
            ]
        );
    }
}
