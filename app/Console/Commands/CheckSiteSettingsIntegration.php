<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use App\Helpers\ImageHelper;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Storage;

class CheckSiteSettingsIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:site-settings-integration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'فحص شامل لتكامل إعدادات الموقع مع الواجهة الأمامية';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء فحص تكامل إعدادات الموقع...');
        $this->newLine();

        // 1. فحص قاعدة البيانات
        $this->checkDatabase();
        
        // 2. فحص الصور
        $this->checkImages();
        
        // 3. فحص مشاركة البيانات مع العروض
        $this->checkViewSharing();
        
        // 4. فحص معلومات الموقع الأساسية
        $this->checkBasicInfo();
        
        // 5. فحص معلومات الاتصال
        $this->checkContactInfo();
        
        // 6. فحص وسائل التواصل الاجتماعي
        $this->checkSocialMedia();
        
        // 7. فحص أسعار الذهب
        $this->checkGoldPrices();
        
        // 8. فحص إعدادات SEO
        $this->checkSEOSettings();
        
        // 9. فحص الإعدادات الوظيفية
        $this->checkFunctionalSettings();
        
        // 10. فحص النصوص المخصصة
        $this->checkCustomTexts();

        $this->newLine();
        $this->info('✅ انتهى فحص تكامل إعدادات الموقع');
    }

    private function checkDatabase()
    {
        $this->info('📊 فحص قاعدة البيانات...');
        
        $settings = SiteSetting::first();
        
        if (!$settings) {
            $this->error('❌ لا توجد إعدادات في قاعدة البيانات!');
            return;
        }
        
        $this->info('✅ تم العثور على إعدادات الموقع في قاعدة البيانات');
        $this->line('   📝 اسم الموقع: ' . ($settings->site_name ?? 'غير محدد'));
        $this->line('   📝 وصف الموقع: ' . ($settings->site_description ?? 'غير محدد'));
        $this->newLine();
    }

    private function checkImages()
    {
        $this->info('🖼️  فحص الصور...');
        
        $settings = SiteSetting::first();
        
        // فحص الشعار
        if ($settings->logo) {
            $logoExists = Storage::disk('public')->exists($settings->logo);
            $logoUrl = ImageHelper::getSiteLogo($settings->logo);
            
            if ($logoExists) {
                $this->info('✅ الشعار موجود في التخزين');
                $this->line('   🔗 مسار الشعار: ' . $settings->logo);
                $this->line('   🌐 رابط الشعار: ' . $logoUrl);
            } else {
                $this->error('❌ الشعار غير موجود في التخزين: ' . $settings->logo);
            }
        } else {
            $this->warn('⚠️  لم يتم رفع شعار للموقع');
        }
        
        // فحص الأيقونة
        if ($settings->favicon) {
            $faviconExists = Storage::disk('public')->exists($settings->favicon);
            $faviconUrl = ImageHelper::getSiteFavicon($settings->favicon);
            
            if ($faviconExists) {
                $this->info('✅ الأيقونة موجودة في التخزين');
                $this->line('   🔗 مسار الأيقونة: ' . $settings->favicon);
                $this->line('   🌐 رابط الأيقونة: ' . $faviconUrl);
            } else {
                $this->error('❌ الأيقونة غير موجودة في التخزين: ' . $settings->favicon);
            }
        } else {
            $this->warn('⚠️  لم يتم رفع أيقونة للموقع');
        }
        
        $this->newLine();
    }

    private function checkViewSharing()
    {
        $this->info('🔄 فحص مشاركة البيانات مع العروض...');
        
        $sharedData = View::getShared();
        
        if (isset($sharedData['settings'])) {
            $this->info('✅ إعدادات الموقع مشاركة مع جميع العروض');
            $settings = $sharedData['settings'];
            $this->line('   📝 نوع البيانات: ' . get_class($settings));
        } else {
            $this->error('❌ إعدادات الموقع غير مشاركة مع العروض!');
        }
        
        $this->newLine();
    }

    private function checkBasicInfo()
    {
        $this->info('ℹ️  فحص معلومات الموقع الأساسية...');
        
        $settings = SiteSetting::first();
        
        $fields = [
            'site_name' => 'اسم الموقع',
            'site_description' => 'وصف الموقع',
            'logo' => 'الشعار',
            'favicon' => 'الأيقونة'
        ];
        
        foreach ($fields as $field => $label) {
            if (!empty($settings->$field)) {
                $this->info("✅ {$label}: " . $settings->$field);
            } else {
                $this->warn("⚠️  {$label}: غير محدد");
            }
        }
        
        $this->newLine();
    }

    private function checkContactInfo()
    {
        $this->info('📞 فحص معلومات الاتصال...');
        
        $settings = SiteSetting::first();
        
        $fields = [
            'contact_email' => 'البريد الإلكتروني',
            'contact_phone' => 'رقم الهاتف',
            'address' => 'العنوان',
            'whatsapp_number' => 'رقم واتساب'
        ];
        
        foreach ($fields as $field => $label) {
            if (!empty($settings->$field)) {
                $this->info("✅ {$label}: " . $settings->$field);
            } else {
                $this->warn("⚠️  {$label}: غير محدد");
            }
        }
        
        $this->newLine();
    }

    private function checkSocialMedia()
    {
        $this->info('📱 فحص وسائل التواصل الاجتماعي...');
        
        $settings = SiteSetting::first();
        
        $fields = [
            'facebook_url' => 'فيسبوك',
            'instagram_url' => 'انستغرام',
            'twitter_url' => 'تويتر',
            'youtube_url' => 'يوتيوب',
            'tiktok_url' => 'تيك توك',
            'linkedin_url' => 'لينكد إن'
        ];
        
        foreach ($fields as $field => $label) {
            if (!empty($settings->$field)) {
                $this->info("✅ {$label}: " . $settings->$field);
            } else {
                $this->warn("⚠️  {$label}: غير محدد");
            }
        }
        
        $this->newLine();
    }

    private function checkGoldPrices()
    {
        $this->info('💰 فحص أسعار الذهب...');
        
        $settings = SiteSetting::first();
        
        $fields = [
            'gold_price_24k' => 'سعر الذهب عيار 24',
            'gold_price_21k' => 'سعر الذهب عيار 21',
            'gold_price_18k' => 'سعر الذهب عيار 18'
        ];
        
        foreach ($fields as $field => $label) {
            if (!empty($settings->$field)) {
                $this->info("✅ {$label}: " . $settings->$field . ' ج.م');
            } else {
                $this->warn("⚠️  {$label}: غير محدد");
            }
        }
        
        // فحص إعداد عرض أسعار الذهب
        if ($settings->show_gold_prices) {
            $this->info('✅ عرض أسعار الذهب مفعل في الصفحة الرئيسية');
        } else {
            $this->warn('⚠️  عرض أسعار الذهب معطل في الصفحة الرئيسية');
        }
        
        $this->newLine();
    }

    private function checkSEOSettings()
    {
        $this->info('🔍 فحص إعدادات SEO...');
        
        $settings = SiteSetting::first();
        
        $fields = [
            'meta_title' => 'عنوان الميتا',
            'meta_description' => 'وصف الميتا',
            'meta_keywords' => 'الكلمات المفتاحية'
        ];
        
        foreach ($fields as $field => $label) {
            if (!empty($settings->$field)) {
                $this->info("✅ {$label}: " . substr($settings->$field, 0, 50) . '...');
            } else {
                $this->warn("⚠️  {$label}: غير محدد");
            }
        }
        
        $this->newLine();
    }

    private function checkFunctionalSettings()
    {
        $this->info('⚙️  فحص الإعدادات الوظيفية...');
        
        $settings = SiteSetting::first();
        
        $booleanFields = [
            'show_ratings' => 'عرض التقييمات',
            'show_wishlist' => 'عرض المفضلة',
            'enable_guest_checkout' => 'الشراء كزائر',
            'enable_local_pickup' => 'الاستلام من المتجر',
            'show_featured_products' => 'عرض المنتجات المميزة',
            'show_new_arrivals' => 'عرض المنتجات الجديدة',
            'show_categories' => 'عرض الفئات',
            'show_features' => 'عرض ميزات الموقع',
            'show_testimonials' => 'عرض آراء العملاء',
            'show_newsletter' => 'عرض النشرة البريدية',
            'enable_multilingual' => 'تعدد اللغات'
        ];
        
        foreach ($booleanFields as $field => $label) {
            $value = $settings->$field ?? false;
            $status = $value ? '✅ مفعل' : '❌ معطل';
            $this->line("   {$label}: {$status}");
        }
        
        $this->newLine();
    }

    private function checkCustomTexts()
    {
        $this->info('📝 فحص النصوص المخصصة...');
        
        $settings = SiteSetting::first();
        
        $fields = [
            'footer_text' => 'نص التذييل',
            'maintenance_message' => 'رسالة الصيانة',
            'custom_header_scripts' => 'نصوص الرأس المخصصة',
            'custom_footer_scripts' => 'نصوص التذييل المخصصة'
        ];
        
        foreach ($fields as $field => $label) {
            if (!empty($settings->$field)) {
                $this->info("✅ {$label}: محدد");
            } else {
                $this->warn("⚠️  {$label}: غير محدد");
            }
        }
        
        $this->newLine();
    }
}
