<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use App\Models\MetalPrice;
use App\Models\Product;
use App\Models\Category;
use App\Models\Feature;
use App\Models\Testimonial;
use App\Helpers\ImageHelper;
use Illuminate\Support\Facades\Http;

class TestFrontendIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:frontend-integration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار شامل لتكامل الواجهة الأمامية مع إعدادات الموقع';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 بدء اختبار تكامل الواجهة الأمامية...');
        $this->newLine();

        // 1. اختبار الصفحة الرئيسية
        $this->testHomePage();
        
        // 2. اختبار أسعار الذهب
        $this->testGoldPrices();
        
        // 3. اختبار المنتجات
        $this->testProducts();
        
        // 4. اختبار الفئات
        $this->testCategories();
        
        // 5. اختبار الميزات
        $this->testFeatures();
        
        // 6. اختبار آراء العملاء
        $this->testTestimonials();
        
        // 7. اختبار الصور
        $this->testImages();
        
        // 8. اختبار الروابط
        $this->testLinks();

        $this->newLine();
        $this->info('✅ انتهى اختبار تكامل الواجهة الأمامية');
    }

    private function testHomePage()
    {
        $this->info('🏠 اختبار الصفحة الرئيسية...');
        
        $settings = SiteSetting::first();
        
        // اختبار إعدادات عرض الأقسام
        $sections = [
            'show_featured_products' => 'المنتجات المميزة',
            'show_new_arrivals' => 'المنتجات الجديدة',
            'show_categories' => 'الفئات',
            'show_gold_prices' => 'أسعار الذهب',
            'show_features' => 'ميزات الموقع',
            'show_testimonials' => 'آراء العملاء',
            'show_newsletter' => 'النشرة البريدية'
        ];
        
        foreach ($sections as $setting => $name) {
            $enabled = $settings->$setting ?? false;
            $status = $enabled ? '✅ مفعل' : '❌ معطل';
            $this->line("   {$name}: {$status}");
        }
        
        $this->newLine();
    }

    private function testGoldPrices()
    {
        $this->info('💰 اختبار أسعار الذهب...');
        
        $settings = SiteSetting::first();
        
        if ($settings->show_gold_prices) {
            $this->info('✅ عرض أسعار الذهب مفعل');
            
            // فحص وجود أسعار في قاعدة البيانات
            $goldPrices = MetalPrice::where('metal_type', 'gold')
                ->where('currency', 'EGP')
                ->get();
                
            if ($goldPrices->count() > 0) {
                $this->info("✅ تم العثور على {$goldPrices->count()} سعر ذهب في قاعدة البيانات");
                
                foreach ($goldPrices as $price) {
                    $this->line("   📊 {$price->purity}: {$price->price_per_gram} ج.م/جرام");
                }
            } else {
                $this->warn('⚠️  لا توجد أسعار ذهب في قاعدة البيانات');
            }
            
            // فحص أسعار الذهب من الإعدادات
            $staticPrices = [
                'gold_price_24k' => 'عيار 24',
                'gold_price_21k' => 'عيار 21',
                'gold_price_18k' => 'عيار 18'
            ];
            
            foreach ($staticPrices as $field => $label) {
                if ($settings->$field) {
                    $this->info("✅ {$label}: {$settings->$field} ج.م");
                } else {
                    $this->warn("⚠️  {$label}: غير محدد في الإعدادات");
                }
            }
        } else {
            $this->warn('⚠️  عرض أسعار الذهب معطل');
        }
        
        $this->newLine();
    }

    private function testProducts()
    {
        $this->info('🛍️  اختبار المنتجات...');
        
        $settings = SiteSetting::first();
        
        // اختبار المنتجات المميزة
        if ($settings->show_featured_products) {
            $featuredProducts = Product::where('is_featured', true)
                ->where('is_active', true)
                ->take(8)
                ->get();
                
            if ($featuredProducts->count() > 0) {
                $this->info("✅ تم العثور على {$featuredProducts->count()} منتج مميز");
            } else {
                $this->warn('⚠️  لا توجد منتجات مميزة');
            }
        }
        
        // اختبار المنتجات الجديدة
        if ($settings->show_new_arrivals) {
            $newProducts = Product::where('is_active', true)
                ->orderBy('created_at', 'desc')
                ->take(8)
                ->get();
                
            if ($newProducts->count() > 0) {
                $this->info("✅ تم العثور على {$newProducts->count()} منتج جديد");
            } else {
                $this->warn('⚠️  لا توجد منتجات جديدة');
            }
        }
        
        $this->newLine();
    }

    private function testCategories()
    {
        $this->info('📂 اختبار الفئات...');
        
        $settings = SiteSetting::first();
        
        if ($settings->show_categories) {
            $categories = Category::where('parent_id', null)
                ->where('is_active', true)
                ->withCount('products')
                ->take(6)
                ->get();
                
            if ($categories->count() > 0) {
                $this->info("✅ تم العثور على {$categories->count()} فئة رئيسية");
                
                foreach ($categories as $category) {
                    $this->line("   📁 {$category->name}: {$category->products_count} منتج");
                }
            } else {
                $this->warn('⚠️  لا توجد فئات رئيسية');
            }
        } else {
            $this->warn('⚠️  عرض الفئات معطل');
        }
        
        $this->newLine();
    }

    private function testFeatures()
    {
        $this->info('⭐ اختبار ميزات الموقع...');
        
        $settings = SiteSetting::first();
        
        if ($settings->show_features) {
            $features = Feature::where('is_active', true)
                ->orderBy('order')
                ->get();
                
            if ($features->count() > 0) {
                $this->info("✅ تم العثور على {$features->count()} ميزة");
                
                foreach ($features as $feature) {
                    $this->line("   ⭐ {$feature->title_ar}");
                }
            } else {
                $this->warn('⚠️  لا توجد ميزات مفعلة');
            }
        } else {
            $this->warn('⚠️  عرض الميزات معطل');
        }
        
        $this->newLine();
    }

    private function testTestimonials()
    {
        $this->info('💬 اختبار آراء العملاء...');
        
        $settings = SiteSetting::first();
        
        if ($settings->show_testimonials) {
            $testimonials = Testimonial::where('is_active', true)
                ->orderBy('order')
                ->take(3)
                ->get();
                
            if ($testimonials->count() > 0) {
                $this->info("✅ تم العثور على {$testimonials->count()} رأي عميل");
                
                foreach ($testimonials as $testimonial) {
                    $this->line("   💬 {$testimonial->client_name_ar}: {$testimonial->rating}/5 نجوم");
                }
            } else {
                $this->warn('⚠️  لا توجد آراء عملاء مفعلة');
            }
        } else {
            $this->warn('⚠️  عرض آراء العملاء معطل');
        }
        
        $this->newLine();
    }

    private function testImages()
    {
        $this->info('🖼️  اختبار الصور...');
        
        $settings = SiteSetting::first();
        
        // اختبار الشعار
        if ($settings->logo) {
            $logoUrl = ImageHelper::getSiteLogo($settings->logo);
            $this->info("✅ الشعار: {$logoUrl}");
            
            // اختبار إمكانية الوصول للصورة
            try {
                $response = Http::timeout(5)->get($logoUrl);
                if ($response->successful()) {
                    $this->info('✅ الشعار متاح عبر HTTP');
                } else {
                    $this->error('❌ الشعار غير متاح عبر HTTP');
                }
            } catch (\Exception $e) {
                $this->error('❌ خطأ في الوصول للشعار: ' . $e->getMessage());
            }
        } else {
            $this->warn('⚠️  لم يتم رفع شعار');
        }
        
        // اختبار الأيقونة
        if ($settings->favicon) {
            $faviconUrl = ImageHelper::getSiteFavicon($settings->favicon);
            $this->info("✅ الأيقونة: {$faviconUrl}");
            
            try {
                $response = Http::timeout(5)->get($faviconUrl);
                if ($response->successful()) {
                    $this->info('✅ الأيقونة متاحة عبر HTTP');
                } else {
                    $this->error('❌ الأيقونة غير متاحة عبر HTTP');
                }
            } catch (\Exception $e) {
                $this->error('❌ خطأ في الوصول للأيقونة: ' . $e->getMessage());
            }
        } else {
            $this->warn('⚠️  لم يتم رفع أيقونة');
        }
        
        $this->newLine();
    }

    private function testLinks()
    {
        $this->info('🔗 اختبار الروابط...');
        
        $settings = SiteSetting::first();
        
        $socialLinks = [
            'facebook_url' => 'فيسبوك',
            'instagram_url' => 'انستغرام',
            'twitter_url' => 'تويتر',
            'youtube_url' => 'يوتيوب',
            'tiktok_url' => 'تيك توك',
            'linkedin_url' => 'لينكد إن'
        ];
        
        foreach ($socialLinks as $field => $name) {
            if ($settings->$field) {
                $this->info("✅ {$name}: {$settings->$field}");
            } else {
                $this->warn("⚠️  {$name}: غير محدد");
            }
        }
        
        $this->newLine();
    }
}
