<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;
use Carbon\Carbon;

class UpdateMetalPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'prices:update
                            {--metal=gold : نوع المعدن (gold, silver, platinum)}
                            {--purity=24K : العيار}
                            {--price= : السعر الجديد}
                            {--date=today : التاريخ (today, yesterday, أو YYYY-MM-DD)}
                            {--currency=EGP : العملة}
                            {--interactive : وضع تفاعلي}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تحديث أسعار المعادن بسهولة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 أداة تحديث أسعار المعادن');
        $this->newLine();

        if ($this->option('interactive')) {
            return $this->interactiveMode();
        }

        return $this->quickUpdate();
    }

    private function interactiveMode()
    {
        $this->info('📝 الوضع التفاعلي - تحديث الأسعار');
        $this->newLine();

        // اختيار نوع المعدن
        $metal = $this->choice(
            'اختر نوع المعدن:',
            ['gold' => 'ذهب', 'silver' => 'فضة'],
            'gold'
        );

        // اختيار العيار حسب نوع المعدن
        $purities = $this->getPuritiesForMetal($metal);
        $purity = $this->choice('اختر العيار:', $purities);

        // إدخال السعر
        $price = $this->ask('أدخل السعر الجديد (بالجنيه المصري):');

        if (!is_numeric($price) || $price <= 0) {
            $this->error('❌ السعر يجب أن يكون رقم موجب');
            return 1;
        }

        // اختيار التاريخ
        $dateChoice = $this->choice(
            'اختر التاريخ:',
            ['today' => 'اليوم', 'yesterday' => 'أمس', 'custom' => 'تاريخ مخصص'],
            'today'
        );

        $date = $this->getDateFromChoice($dateChoice);

        // تأكيد التحديث
        $this->table(
            ['المعدن', 'العيار', 'السعر', 'التاريخ', 'العملة'],
            [[$metal, $purity, number_format($price, 2), $date, 'EGP']]
        );

        if ($this->confirm('هل تريد تحديث السعر؟')) {
            return $this->updatePrice($metal, $purity, $price, $date);
        }

        $this->info('تم إلغاء العملية');
        return 0;
    }

    private function quickUpdate()
    {
        $metal = $this->option('metal');
        $purity = $this->option('purity');
        $price = $this->option('price');
        $date = $this->option('date');

        if (!$price) {
            $this->error('❌ يجب تحديد السعر باستخدام --price');
            return 1;
        }

        if (!is_numeric($price) || $price <= 0) {
            $this->error('❌ السعر يجب أن يكون رقم موجب');
            return 1;
        }

        $date = $this->getDateFromChoice($date);

        return $this->updatePrice($metal, $purity, $price, $date);
    }

    private function updatePrice($metal, $purity, $price, $date)
    {
        try {
            // البحث عن السعر الموجود
            $existingPrice = MetalPrice::where('metal_type', $metal)
                ->where('purity', $purity)
                ->where('currency', 'EGP')
                ->whereDate('price_date', $date)
                ->first();

            if ($existingPrice) {
                // تحديث السعر الموجود
                $oldPrice = $existingPrice->price_per_gram;
                $existingPrice->update([
                    'price_per_gram' => $price,
                    'price_per_ounce' => $price * 31.1035,
                    'updated_at' => now(),
                ]);

                $this->info("✅ تم تحديث السعر بنجاح!");
                $this->info("   المعدن: {$metal} - العيار: {$purity}");
                $this->info("   السعر القديم: " . number_format($oldPrice, 2) . " ج.م");
                $this->info("   السعر الجديد: " . number_format($price, 2) . " ج.م");
                $this->info("   التاريخ: {$date}");
            } else {
                // إنشاء سعر جديد
                MetalPrice::create([
                    'metal_type' => $metal,
                    'purity' => $purity,
                    'price_per_gram' => $price,
                    'price_per_ounce' => $price * 31.1035,
                    'currency' => 'EGP',
                    'price_date' => $date,
                    'source' => 'manual',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                $this->info("✅ تم إضافة السعر الجديد بنجاح!");
                $this->info("   المعدن: {$metal} - العيار: {$purity}");
                $this->info("   السعر: " . number_format($price, 2) . " ج.م");
                $this->info("   التاريخ: {$date}");
            }

            return 0;
        } catch (\Exception $e) {
            $this->error("❌ حدث خطأ: " . $e->getMessage());
            return 1;
        }
    }

    private function getPuritiesForMetal($metal)
    {
        switch ($metal) {
            case 'gold':
                return ['24K', '22K', '21K', '18K', '14K'];
            case 'silver':
                return ['999', '925'];
            default:
                return ['24K'];
        }
    }

    private function getDateFromChoice($choice)
    {
        switch ($choice) {
            case 'today':
                return Carbon::today()->format('Y-m-d');
            case 'yesterday':
                return Carbon::yesterday()->format('Y-m-d');
            case 'custom':
                $customDate = $this->ask('أدخل التاريخ (YYYY-MM-DD):');
                try {
                    return Carbon::parse($customDate)->format('Y-m-d');
                } catch (\Exception) {
                    $this->error('❌ تنسيق التاريخ غير صحيح');
                    return Carbon::today()->format('Y-m-d');
                }
            default:
                // إذا كان التاريخ مخصص مباشرة
                try {
                    return Carbon::parse($choice)->format('Y-m-d');
                } catch (\Exception) {
                    return Carbon::today()->format('Y-m-d');
                }
        }
    }
}
