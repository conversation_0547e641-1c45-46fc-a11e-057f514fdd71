<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class TestCleanArchitecture extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:clean-architecture';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار التحسينات المعمارية وتعدد اللغات';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء اختبار التحسينات المعمارية...');
        $this->newLine();

        // 1. اختبار إزالة @php من View
        $this->testNoPhpInView();

        // 2. اختبار ملفات الترجمة
        $this->testTranslationFiles();

        // 3. اختبار Controller المحسن
        $this->testImprovedController();

        // 4. اختبار تعدد اللغات
        $this->testMultiLanguageSupport();

        $this->newLine();
        $this->info('✅ انتهى اختبار التحسينات المعمارية');
    }

    private function testNoPhpInView()
    {
        $this->info('🚫 اختبار إزالة @php من ملف View...');

        $viewPath = resource_path('views/frontend/metal-prices.blade.php');
        $viewContent = File::get($viewPath);

        // البحث عن كتل @php
        $phpBlocks = preg_match_all('/@php/', $viewContent);

        if ($phpBlocks === 0) {
            $this->info('✅ لا توجد كتل @php في ملف View');
        } else {
            $this->error("❌ تم العثور على {$phpBlocks} كتلة @php في ملف View");
        }

        // التحقق من استخدام المتغيرات المعالجة
        $processedVariables = [
            'purity_label',
            'formatted_price_per_gram',
            'formatted_price_per_ounce',
            'formatted_change_percent',
            'currency_symbol',
            'direction_label'
        ];

        foreach ($processedVariables as $variable) {
            if (strpos($viewContent, $variable) !== false) {
                $this->info("✅ يستخدم المتغير المعالج: {$variable}");
            } else {
                $this->warn("⚠️  لا يستخدم المتغير المعالج: {$variable}");
            }
        }

        $this->newLine();
    }

    private function testTranslationFiles()
    {
        $this->info('🌐 اختبار ملفات الترجمة...');

        $languages = ['ar', 'en'];
        $translationKeys = [
            'page_title',
            'page_subtitle',
            'gold_prices_title',
            'silver_prices_title',
            'platinum_prices_title',
            'table_headers.purity',
            'table_headers.price_per_gram',
            'chart.title',
            'chart.metals.gold_24k',
            'disclaimer.title',
            'messages.no_gold_prices'
        ];

        foreach ($languages as $lang) {
            $langPath = base_path("lang/{$lang}/metal_prices.php");

            if (File::exists($langPath)) {
                $this->info("✅ ملف الترجمة موجود: {$lang}");

                $translations = include $langPath;

                foreach ($translationKeys as $key) {
                    $value = data_get($translations, $key);
                    if ($value) {
                        $this->info("   ✅ {$lang}.{$key}: {$value}");
                    } else {
                        $this->error("   ❌ مفتاح الترجمة مفقود: {$lang}.{$key}");
                    }
                }
            } else {
                $this->error("❌ ملف الترجمة مفقود: {$lang}");
            }
        }

        $this->newLine();
    }

    private function testImprovedController()
    {
        $this->info('🎛️  اختبار Controller المحسن...');

        $controllerPath = app_path('Http/Controllers/Frontend/HomeController.php');
        $controllerContent = File::get($controllerPath);

        // التحقق من وجود الدوال المساعدة الجديدة
        $helperMethods = [
            'getProcessedGoldPrices',
            'getProcessedSilverPrices',
            'getProcessedPlatinumPrices',
            'formatLastUpdate',
            'getTranslations'
        ];

        foreach ($helperMethods as $method) {
            if (strpos($controllerContent, "function {$method}") !== false) {
                $this->info("✅ دالة مساعدة موجودة: {$method}");
            } else {
                $this->error("❌ دالة مساعدة مفقودة: {$method}");
            }
        }

        // التحقق من استخدام الترجمة في Controller
        if (strpos($controllerContent, "__('metal_prices.") !== false) {
            $this->info('✅ Controller يستخدم نظام الترجمة');
        } else {
            $this->error('❌ Controller لا يستخدم نظام الترجمة');
        }

        // التحقق من معالجة البيانات
        $dataProcessing = [
            'purity_label',
            'formatted_price_per_gram',
            'formatted_change_percent',
            'currency_symbol'
        ];

        foreach ($dataProcessing as $field) {
            if (strpos($controllerContent, "'{$field}'") !== false) {
                $this->info("✅ معالجة البيانات: {$field}");
            } else {
                $this->warn("⚠️  معالجة البيانات مفقودة: {$field}");
            }
        }

        $this->newLine();
    }

    private function testMultiLanguageSupport()
    {
        $this->info('🌍 اختبار دعم تعدد اللغات...');

        // اختبار اللغة العربية
        app()->setLocale('ar');
        $arTranslation = __('metal_prices.page_title');
        $this->info("✅ العربية - عنوان الصفحة: {$arTranslation}");

        // اختبار اللغة الإنجليزية
        app()->setLocale('en');
        $enTranslation = __('metal_prices.page_title');
        $this->info("✅ الإنجليزية - عنوان الصفحة: {$enTranslation}");

        // التحقق من اختلاف الترجمات
        if ($arTranslation !== $enTranslation) {
            $this->info('✅ الترجمات مختلفة بين اللغات');
        } else {
            $this->warn('⚠️  الترجمات متشابهة بين اللغات');
        }

        // اختبار ترجمات العيارات
        app()->setLocale('ar');
        $arPurity = __('metal_prices.purities.24K');
        app()->setLocale('en');
        $enPurity = __('metal_prices.purities.24K');

        $this->info("✅ عيار 24K - العربية: {$arPurity}");
        $this->info("✅ عيار 24K - الإنجليزية: {$enPurity}");

        // إعادة تعيين اللغة الافتراضية
        app()->setLocale(config('app.locale'));

        $this->newLine();
    }
}
