<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;
use App\Models\SiteSetting;

class TestMetalPricesPage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:metal-prices-page';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار شامل لصفحة أسعار المعادن المحسنة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء اختبار صفحة أسعار المعادن المحسنة...');
        $this->newLine();

        // 1. اختبار البيانات في قاعدة البيانات
        $this->testDatabaseData();

        // 2. اختبار تقييد العملة للجنيه المصري
        $this->testCurrencyRestriction();

        // 3. اختبار عيارات الذهب
        $this->testGoldPurities();

        // 4. اختبار حساب التغييرات
        $this->testPriceChanges();

        // 5. اختبار الإحصائيات
        $this->testStatistics();

        // 6. اختبار بيانات الرسم البياني
        $this->testChartData();

        // 7. اختبار التكامل مع إعدادات الموقع
        $this->testSiteSettingsIntegration();

        $this->newLine();
        $this->info('✅ انتهى اختبار صفحة أسعار المعادن');
    }

    private function testDatabaseData()
    {
        $this->info('📊 اختبار البيانات في قاعدة البيانات...');

        // فحص أسعار الذهب بالجنيه المصري
        $goldPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->get();

        if ($goldPrices->count() > 0) {
            $this->info("✅ تم العثور على {$goldPrices->count()} سعر ذهب بالجنيه المصري");

            $purities = $goldPrices->pluck('purity')->unique();
            $this->line('   📝 العيارات المتاحة: ' . $purities->implode(', '));
        } else {
            $this->warn('⚠️  لا توجد أسعار ذهب بالجنيه المصري');
        }

        // فحص أسعار الفضة
        $silverPrices = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->get();

        if ($silverPrices->count() > 0) {
            $this->info("✅ تم العثور على {$silverPrices->count()} سعر فضة بالجنيه المصري");
        } else {
            $this->warn('⚠️  لا توجد أسعار فضة بالجنيه المصري');
        }



        $this->newLine();
    }

    private function testCurrencyRestriction()
    {
        $this->info('💱 اختبار تقييد العملة للجنيه المصري...');

        // فحص أن جميع الأسعار بالجنيه المصري فقط
        $nonEgpPrices = MetalPrice::where('currency', '!=', 'EGP')->count();

        if ($nonEgpPrices > 0) {
            $this->warn("⚠️  يوجد {$nonEgpPrices} سعر بعملات أخرى غير الجنيه المصري");
        } else {
            $this->info('✅ جميع الأسعار بالجنيه المصري فقط');
        }

        // فحص آخر تاريخ للأسعار
        $latestDate = MetalPrice::where('currency', 'EGP')->max('price_date');
        if ($latestDate) {
            $this->info("✅ آخر تحديث للأسعار: {$latestDate}");
        } else {
            $this->warn('⚠️  لا يوجد تاريخ لآخر تحديث');
        }

        $this->newLine();
    }

    private function testGoldPurities()
    {
        $this->info('🥇 اختبار عيارات الذهب...');

        $requiredPurities = ['24K', '22K', '21K', '18K', '14K'];
        $latestDate = MetalPrice::where('currency', 'EGP')->max('price_date');

        foreach ($requiredPurities as $purity) {
            $price = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $latestDate)
                ->first();

            if ($price) {
                $this->info("✅ {$purity}: {$price->price_per_gram} ج.م/جرام");
            } else {
                $this->warn("⚠️  {$purity}: غير متاح");
            }
        }

        $this->newLine();
    }

    private function testPriceChanges()
    {
        $this->info('📈 اختبار حساب التغييرات...');

        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');

        $todayPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $today)
            ->count();

        $yesterdayPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $yesterday)
            ->count();

        if ($todayPrices > 0 && $yesterdayPrices > 0) {
            $this->info('✅ يمكن حساب التغييرات (توجد أسعار لليوم والأمس)');

            // حساب تغيير عينة
            $todayGold24 = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', '24K')
                ->whereDate('price_date', $today)
                ->first();

            $yesterdayGold24 = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', '24K')
                ->whereDate('price_date', $yesterday)
                ->first();

            if ($todayGold24 && $yesterdayGold24) {
                $change = $todayGold24->price_per_gram - $yesterdayGold24->price_per_gram;
                $changePercent = ($change / $yesterdayGold24->price_per_gram) * 100;

                $direction = $change > 0 ? '📈 ارتفاع' : ($change < 0 ? '📉 انخفاض' : '➡️ ثبات');
                $this->line("   مثال - ذهب 24K: {$direction} بنسبة " . number_format($changePercent, 2) . '%');
            }
        } else {
            $this->warn('⚠️  لا يمكن حساب التغييرات (نقص في البيانات)');
        }

        $this->newLine();
    }

    private function testStatistics()
    {
        $this->info('📊 اختبار الإحصائيات...');

        $latestDate = MetalPrice::where('currency', 'EGP')->max('price_date');

        // إحصائيات الذهب
        $goldPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $latestDate)
            ->get();

        if ($goldPrices->count() > 0) {
            $highest = $goldPrices->max('price_per_gram');
            $lowest = $goldPrices->min('price_per_gram');
            $average = $goldPrices->avg('price_per_gram');

            $this->info('✅ إحصائيات الذهب:');
            $this->line("   📊 أعلى سعر: " . number_format($highest, 2) . ' ج.م');
            $this->line("   📊 أقل سعر: " . number_format($lowest, 2) . ' ج.م');
            $this->line("   📊 المتوسط: " . number_format($average, 2) . ' ج.م');
        } else {
            $this->warn('⚠️  لا توجد بيانات كافية لحساب إحصائيات الذهب');
        }

        $this->newLine();
    }

    private function testChartData()
    {
        $this->info('📈 اختبار بيانات الرسم البياني...');

        $periods = ['7', '30', '90', '180', '365'];
        $metals = ['gold' => ['24K', '21K', '18K'], 'silver' => ['999']];

        foreach ($periods as $period) {
            $startDate = now()->subDays($period);
            $hasData = false;

            foreach ($metals as $metalType => $purities) {
                foreach ($purities as $purity) {
                    $count = MetalPrice::where('metal_type', $metalType)
                        ->where('currency', 'EGP')
                        ->where('purity', $purity)
                        ->whereBetween('price_date', [$startDate, now()])
                        ->count();

                    if ($count > 0) {
                        $hasData = true;
                        break 2;
                    }
                }
            }

            if ($hasData) {
                $this->info("✅ فترة {$period} يوم: توجد بيانات للرسم البياني");
            } else {
                $this->warn("⚠️  فترة {$period} يوم: لا توجد بيانات كافية");
            }
        }

        $this->newLine();
    }

    private function testSiteSettingsIntegration()
    {
        $this->info('⚙️  اختبار التكامل مع إعدادات الموقع...');

        $settings = SiteSetting::first();

        if ($settings) {
            $this->info('✅ إعدادات الموقع متاحة');
            $this->line('   📝 اسم الموقع: ' . ($settings->site_name ?? 'غير محدد'));
            $this->line('   📝 الشعار: ' . ($settings->logo ? 'متاح' : 'غير متاح'));
            $this->line('   📝 معلومات الاتصال: ' . ($settings->contact_phone ? 'متاحة' : 'غير متاحة'));
        } else {
            $this->warn('⚠️  إعدادات الموقع غير متاحة');
        }

        $this->newLine();
    }
}
