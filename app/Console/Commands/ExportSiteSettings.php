<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\File;

class ExportSiteSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'site:export-settings {path? : Path to export the settings JSON file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export site settings to a JSON file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Exporting site settings...');

        // Get settings
        $settings = SiteSetting::first();

        if (!$settings) {
            $this->error('No site settings found to export.');
            return 1;
        }

        // Convert to array
        $settingsArray = $settings->toArray();
        
        // Remove sensitive data
        unset($settingsArray['id']);
        unset($settingsArray['created_at']);
        unset($settingsArray['updated_at']);
        
        // Mask sensitive information
        if (isset($settingsArray['mail_password']) && $settingsArray['mail_password']) {
            $settingsArray['mail_password'] = '********';
        }

        // Get export path
        $path = $this->argument('path');
        if (!$path) {
            $path = storage_path('app/settings_' . date('Y-m-d_His') . '.json');
        }

        // Ensure directory exists
        $directory = dirname($path);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        // Export to JSON file
        File::put($path, json_encode($settingsArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));

        $this->info('Site settings exported successfully to: ' . $path);
        return 0;
    }
}
