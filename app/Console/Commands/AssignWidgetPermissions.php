<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AssignWidgetPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'widgets:assign-permissions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إضافة صلاحيات الوصول للويدجت للأدوار المختلفة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 بدء إضافة صلاحيات الويدجت للأدوار...');
        $this->newLine();

        // قائمة الصلاحيات المطلوبة للويدجت
        $widgetPermissions = [
            'view_any_user' => 'عرض إحصائيات المستخدمين',
            'view_any_product' => 'عرض إحصائيات المنتجات',
            'view_any_order' => 'عرض إحصائيات الطلبات والمبيعات',
            'view_any_appointment' => 'عرض إحصائيات المواعيد',
            'view_any_metal::price' => 'عرض إحصائيات أسعار المعادن',
            'view_any_blog::post' => 'عرض إحصائيات مقالات المدونة',
            'view_any_page' => 'عرض إحصائيات الصفحات',
            'view_any_category' => 'عرض إحصائيات الفئات',
            'view_any_feature' => 'عرض إحصائيات الميزات',
            'view_any_testimonial' => 'عرض إحصائيات الشهادات',
            'view_any_home::slider' => 'عرض إحصائيات شرائح الصفحة الرئيسية',
            'view_any_job' => 'عرض إحصائيات الوظائف',
            'view_any_job::application' => 'عرض إحصائيات طلبات التوظيف',
            'view_any_newsletter' => 'عرض إحصائيات النشرة البريدية',
            'view_any_review' => 'عرض إحصائيات التقييمات',
            'view_any_store' => 'عرض إحصائيات المتاجر',
            'view_any_super::admin::setting' => 'عرض حالة النظام (Super Admin فقط)',
        ];

        // إنشاء الصلاحيات إذا لم تكن موجودة
        $this->info('📝 إنشاء الصلاحيات المطلوبة...');
        foreach ($widgetPermissions as $permission => $description) {
            $permissionModel = Permission::firstOrCreate(
                ['name' => $permission, 'guard_name' => 'web'],
                ['name' => $permission, 'guard_name' => 'web']
            );
            
            if ($permissionModel->wasRecentlyCreated) {
                $this->line("   ✅ تم إنشاء صلاحية: {$permission}");
            } else {
                $this->line("   ℹ️ الصلاحية موجودة: {$permission}");
            }
        }

        $this->newLine();

        // تعريف الأدوار وصلاحياتها
        $rolePermissions = [
            'super_admin' => array_keys($widgetPermissions), // جميع الصلاحيات
            'admin' => [
                'view_any_user',
                'view_any_product',
                'view_any_order',
                'view_any_appointment',
                'view_any_metal::price',
                'view_any_blog::post',
                'view_any_page',
                'view_any_category',
                'view_any_feature',
                'view_any_testimonial',
                'view_any_home::slider',
                'view_any_job',
                'view_any_job::application',
                'view_any_newsletter',
                'view_any_review',
                'view_any_store',
                // لا يشمل view_any_super::admin::setting
            ],
            'manager' => [
                'view_any_user',
                'view_any_product',
                'view_any_order',
                'view_any_appointment',
                'view_any_metal::price',
                'view_any_blog::post',
                'view_any_page',
                'view_any_category',
                'view_any_job',
                'view_any_job::application',
                'view_any_review',
                'view_any_store',
            ],
            'editor' => [
                'view_any_blog::post',
                'view_any_page',
                'view_any_category',
                'view_any_feature',
                'view_any_testimonial',
                'view_any_home::slider',
            ],
            'sales' => [
                'view_any_user',
                'view_any_product',
                'view_any_order',
                'view_any_appointment',
                'view_any_review',
                'view_any_store',
            ],
        ];

        // إضافة الصلاحيات للأدوار
        $this->info('👥 إضافة الصلاحيات للأدوار...');
        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::where('name', $roleName)->first();
            
            if (!$role) {
                $this->warn("   ⚠️ الدور غير موجود: {$roleName}");
                continue;
            }

            $this->line("   🔧 معالجة دور: {$roleName}");
            
            $addedCount = 0;
            $existingCount = 0;
            
            foreach ($permissions as $permissionName) {
                if (!$role->hasPermissionTo($permissionName)) {
                    $role->givePermissionTo($permissionName);
                    $addedCount++;
                } else {
                    $existingCount++;
                }
            }
            
            $this->line("      ✅ تم إضافة {$addedCount} صلاحية جديدة");
            $this->line("      ℹ️ {$existingCount} صلاحية موجودة مسبقاً");
            $this->line("      📊 إجمالي الصلاحيات: " . count($permissions));
        }

        $this->newLine();

        // عرض ملخص الصلاحيات لكل دور
        $this->info('📊 ملخص صلاحيات الويدجت لكل دور:');
        $this->newLine();

        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Role::where('name', $roleName)->first();
            if (!$role) continue;

            $this->line("🎭 دور: {$roleName}");
            $this->line("   📈 الويدجت المتاحة:");
            
            $widgetAccess = [
                'MainStatsOverview' => in_array('view_any_user', $permissions) || in_array('view_any_product', $permissions),
                'ContentStatsWidget' => in_array('view_any_blog::post', $permissions) || in_array('view_any_page', $permissions),
                'EngagementStatsWidget' => in_array('view_any_job', $permissions) || in_array('view_any_review', $permissions),
                'MetalPricesWidget' => in_array('view_any_metal::price', $permissions),
                'RecentOrdersWidget' => in_array('view_any_order', $permissions),
                'UpcomingAppointmentsWidget' => in_array('view_any_appointment', $permissions),
                'SalesChartWidget' => in_array('view_any_order', $permissions),
                'SystemStatusWidget' => in_array('view_any_super::admin::setting', $permissions),
            ];

            foreach ($widgetAccess as $widget => $hasAccess) {
                $status = $hasAccess ? '✅' : '❌';
                $this->line("      {$status} {$widget}");
            }
            
            $this->newLine();
        }

        $this->info('🎉 تم الانتهاء من إضافة صلاحيات الويدجت بنجاح!');
        $this->newLine();
        
        $this->comment('💡 يمكنك الآن زيارة لوحة التحكم للتحقق من ظهور الويدجت حسب صلاحيات كل دور.');
        
        return Command::SUCCESS;
    }
}
