<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;
use Carbon\Carbon;

class TestGoldPricesDisplay extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:gold-prices-display';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار شامل لعرض أسعار الذهب والتحقق من صحة البيانات';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء اختبار عرض أسعار الذهب...');
        $this->newLine();

        // 1. اختبار وجود جميع عيارات الذهب
        $this->testAllGoldPurities();
        
        // 2. اختبار جلب البيانات من قاعدة البيانات
        $this->testDatabaseDataRetrieval();
        
        // 3. اختبار ترتيب العيارات
        $this->testPurityOrdering();
        
        // 4. اختبار حساب التغييرات
        $this->testPriceChangesCalculation();
        
        // 5. اختبار عرض المؤشرات
        $this->testPriceIndicators();
        
        // 6. اختبار البيانات المفقودة
        $this->testMissingDataHandling();
        
        // 7. اختبار بيانات الرسم البياني
        $this->testChartData();

        $this->newLine();
        $this->info('✅ انتهى اختبار عرض أسعار الذهب');
    }

    private function testAllGoldPurities()
    {
        $this->info('🥇 اختبار وجود جميع عيارات الذهب...');
        
        $requiredPurities = ['24K', '22K', '21K', '18K', '14K'];
        $today = now()->format('Y-m-d');
        
        $foundPurities = [];
        $missingPurities = [];
        
        foreach ($requiredPurities as $purity) {
            $price = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $today)
                ->first();
                
            if ($price) {
                $foundPurities[] = $purity;
                $this->info("   ✅ {$purity}: {$price->price_per_gram} ج.م/جرام");
            } else {
                $missingPurities[] = $purity;
                $this->warn("   ⚠️  {$purity}: غير متاح");
            }
        }
        
        if (empty($missingPurities)) {
            $this->info('✅ جميع عيارات الذهب المطلوبة متاحة');
        } else {
            $this->warn('⚠️  عيارات مفقودة: ' . implode(', ', $missingPurities));
        }
        
        $this->newLine();
    }

    private function testDatabaseDataRetrieval()
    {
        $this->info('📊 اختبار جلب البيانات من قاعدة البيانات...');
        
        // اختبار أن جميع الأسعار من قاعدة البيانات
        $totalPrices = MetalPrice::where('currency', 'EGP')->count();
        $goldPrices = MetalPrice::gold()->where('currency', 'EGP')->count();
        $silverPrices = MetalPrice::silver()->where('currency', 'EGP')->count();
        $platinumPrices = MetalPrice::platinum()->where('currency', 'EGP')->count();
        
        $this->info("   📈 إجمالي الأسعار: {$totalPrices}");
        $this->info("   🥇 أسعار الذهب: {$goldPrices}");
        $this->info("   🥈 أسعار الفضة: {$silverPrices}");
        $this->info("   ⚪ أسعار البلاتين: {$platinumPrices}");
        
        // اختبار أن جميع الأسعار بالجنيه المصري
        $nonEgpPrices = MetalPrice::where('currency', '!=', 'EGP')->count();
        
        if ($nonEgpPrices === 0) {
            $this->info('✅ جميع الأسعار بالجنيه المصري فقط');
        } else {
            $this->warn("⚠️  يوجد {$nonEgpPrices} سعر بعملات أخرى");
        }
        
        // اختبار آخر تاريخ متاح
        $latestDate = MetalPrice::where('currency', 'EGP')->max('price_date');
        if ($latestDate) {
            $this->info("   📅 آخر تاريخ متاح: {$latestDate}");
        } else {
            $this->warn('⚠️  لا يوجد تاريخ متاح');
        }
        
        $this->newLine();
    }

    private function testPurityOrdering()
    {
        $this->info('📋 اختبار ترتيب العيارات...');
        
        $expectedOrder = ['24K', '22K', '21K', '18K', '14K'];
        $today = now()->format('Y-m-d');
        
        $actualPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $today)
            ->get();
            
        // ترتيب حسب الترتيب المطلوب
        $orderedPrices = collect();
        foreach ($expectedOrder as $purity) {
            $price = $actualPrices->where('purity', $purity)->first();
            if ($price) {
                $orderedPrices->push($price);
            }
        }
        
        $this->info('   📊 الترتيب الحالي:');
        foreach ($orderedPrices as $index => $price) {
            $position = $index + 1;
            $this->line("   {$position}. {$price->purity}: {$price->price_per_gram} ج.م");
        }
        
        // التحقق من الترتيب الصحيح (من الأعلى للأقل)
        $isCorrectOrder = true;
        for ($i = 0; $i < $orderedPrices->count() - 1; $i++) {
            if ($orderedPrices[$i]->price_per_gram < $orderedPrices[$i + 1]->price_per_gram) {
                $isCorrectOrder = false;
                break;
            }
        }
        
        if ($isCorrectOrder) {
            $this->info('✅ الترتيب صحيح (من الأعلى إلى الأقل)');
        } else {
            $this->warn('⚠️  الترتيب غير صحيح');
        }
        
        $this->newLine();
    }

    private function testPriceChangesCalculation()
    {
        $this->info('📈 اختبار حساب التغييرات...');
        
        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');
        
        $goldPurities = ['24K', '22K', '21K', '18K', '14K'];
        $changesFound = 0;
        
        foreach ($goldPurities as $purity) {
            $todayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $today)
                ->first();
                
            $yesterdayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $yesterday)
                ->first();
                
            if ($todayPrice && $yesterdayPrice) {
                $change = $todayPrice->price_per_gram - $yesterdayPrice->price_per_gram;
                $changePercent = ($change / $yesterdayPrice->price_per_gram) * 100;
                
                $direction = $change > 0 ? '📈 ارتفاع' : ($change < 0 ? '📉 انخفاض' : '➡️ ثبات');
                $changeText = $change > 0 ? '+' . number_format($change, 2) : number_format($change, 2);
                
                $this->info("   ✅ {$purity}: {$direction} بـ {$changeText} ج.م (" . number_format($changePercent, 2) . "%)");
                $changesFound++;
            } else {
                $this->warn("   ⚠️  {$purity}: لا يمكن حساب التغيير (بيانات مفقودة)");
            }
        }
        
        if ($changesFound === count($goldPurities)) {
            $this->info('✅ تم حساب التغييرات لجميع العيارات');
        } else {
            $this->warn("⚠️  تم حساب التغييرات لـ {$changesFound} من " . count($goldPurities) . " عيارات فقط");
        }
        
        $this->newLine();
    }

    private function testPriceIndicators()
    {
        $this->info('🎯 اختبار مؤشرات الأسعار...');
        
        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');
        
        $indicators = [
            'up' => 0,
            'down' => 0,
            'stable' => 0
        ];
        
        $goldPurities = ['24K', '22K', '21K', '18K', '14K'];
        
        foreach ($goldPurities as $purity) {
            $todayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $today)
                ->first();
                
            $yesterdayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $yesterday)
                ->first();
                
            if ($todayPrice && $yesterdayPrice) {
                $change = $todayPrice->price_per_gram - $yesterdayPrice->price_per_gram;
                
                if ($change > 0) {
                    $indicators['up']++;
                } elseif ($change < 0) {
                    $indicators['down']++;
                } else {
                    $indicators['stable']++;
                }
            }
        }
        
        $this->info("   📈 ارتفاع: {$indicators['up']} عيار");
        $this->info("   📉 انخفاض: {$indicators['down']} عيار");
        $this->info("   ➡️ ثبات: {$indicators['stable']} عيار");
        
        if (array_sum($indicators) > 0) {
            $this->info('✅ مؤشرات الأسعار تعمل بشكل صحيح');
        } else {
            $this->warn('⚠️  لا توجد مؤشرات أسعار');
        }
        
        $this->newLine();
    }

    private function testMissingDataHandling()
    {
        $this->info('❓ اختبار التعامل مع البيانات المفقودة...');
        
        $requiredPurities = ['24K', '22K', '21K', '18K', '14K'];
        $today = now()->format('Y-m-d');
        
        $missingCount = 0;
        
        foreach ($requiredPurities as $purity) {
            $price = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $today)
                ->first();
                
            if (!$price) {
                $missingCount++;
                $this->warn("   ⚠️  {$purity}: بيانات مفقودة - يجب عرض 'غير متاح' أو سعر افتراضي");
            }
        }
        
        if ($missingCount === 0) {
            $this->info('✅ جميع البيانات متاحة - لا توجد بيانات مفقودة');
        } else {
            $this->warn("⚠️  {$missingCount} عيار مفقود - تأكد من التعامل معها في العرض");
        }
        
        $this->newLine();
    }

    private function testChartData()
    {
        $this->info('📊 اختبار بيانات الرسم البياني...');
        
        $periods = [7, 30];
        $hasChartData = false;
        
        foreach ($periods as $days) {
            $startDate = now()->subDays($days);
            
            $dataCount = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', '24K')
                ->whereBetween('price_date', [$startDate, now()])
                ->count();
                
            if ($dataCount > 0) {
                $this->info("   ✅ فترة {$days} يوم: {$dataCount} نقطة بيانات");
                $hasChartData = true;
            } else {
                $this->warn("   ⚠️  فترة {$days} يوم: لا توجد بيانات");
            }
        }
        
        if ($hasChartData) {
            $this->info('✅ بيانات الرسم البياني متاحة');
        } else {
            $this->warn('⚠️  لا توجد بيانات كافية للرسم البياني');
        }
        
        $this->newLine();
    }
}
