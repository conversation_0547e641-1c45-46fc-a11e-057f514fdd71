<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;

class ActivateLatestPrices extends Command
{
    protected $signature = 'prices:activate-latest';
    protected $description = 'تفعيل أحدث سعر لكل عيار وإلغاء تفعيل الباقي';

    public function handle()
    {
        $this->info('🔄 تطبيق منطق التفعيل على الأسعار الموجودة...');

        // إلغاء تفعيل جميع الأسعار أولاً
        MetalPrice::query()->update(['is_active' => false]);
        $this->line('❌ تم إلغاء تفعيل جميع الأسعار');

        // جلب جميع المعادن والعيارات الفريدة
        $combinations = MetalPrice::select('metal_type', 'purity', 'currency')
            ->distinct()
            ->get();

        $activatedCount = 0;

        foreach ($combinations as $combination) {
            // العثور على أحدث سعر لهذا المعدن والعيار
            $latestPrice = MetalPrice::where('metal_type', $combination->metal_type)
                ->where('purity', $combination->purity)
                ->where('currency', $combination->currency)
                ->orderBy('created_at', 'desc')
                ->first();

            if ($latestPrice) {
                $latestPrice->update(['is_active' => true]);
                $activatedCount++;
                
                $this->line("✅ تم تفعيل: {$latestPrice->metal_type} {$latestPrice->purity} - {$latestPrice->price_per_gram} ج.م ({$latestPrice->created_at->format('Y-m-d H:i')})");
            }
        }

        $this->info("✨ تم تفعيل {$activatedCount} سعر (أحدث سعر لكل عيار)");
        
        // عرض ملخص الأسعار المفعلة
        $this->table(
            ['المعدن', 'العيار', 'السعر/جرام', 'التاريخ', 'الحالة'],
            MetalPrice::where('is_active', true)
                ->orderBy('metal_type')
                ->orderBy('purity')
                ->get()
                ->map(function ($price) {
                    return [
                        $price->metal_type === 'gold' ? '🥇 ذهب' : '🥈 فضة',
                        $price->purity,
                        number_format($price->price_per_gram, 2) . ' ج.م',
                        $price->created_at->format('Y-m-d H:i'),
                        '✅ مفعل'
                    ];
                })
                ->toArray()
        );

        return 0;
    }
}
