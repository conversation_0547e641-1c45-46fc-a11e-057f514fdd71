<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use App\Services\SettingsService;
use Illuminate\Support\Facades\Cache;

class CheckMaintenanceStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'maintenance:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the current maintenance mode status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Clear cache first
        Cache::forget('site_settings');
        $this->info('Cache cleared');
        
        // Get the settings service
        $settingsService = app(SettingsService::class);
        
        // Get maintenance mode status
        $maintenanceMode = $settingsService->get('maintenance_mode', false);
        $maintenanceMessage = $settingsService->get('maintenance_message', 'No message set');
        
        $this->info('Maintenance Mode: ' . ($maintenanceMode ? 'Enabled' : 'Disabled'));
        $this->info('Maintenance Message: ' . $maintenanceMessage);
        
        // Get from database directly
        $settings = SiteSetting::first();
        if ($settings) {
            $this->info('Database Maintenance Mode: ' . ($settings->maintenance_mode ? 'Enabled' : 'Disabled'));
            $this->info('Database Maintenance Message: ' . $settings->maintenance_message);
        } else {
            $this->error('No settings found in database');
        }
        
        return Command::SUCCESS;
    }
}
