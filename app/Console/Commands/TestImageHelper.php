<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\ImageHelper;
use App\Models\SiteSetting;

class TestImageHelper extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:image-helper';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the ImageHelper functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing ImageHelper functionality...');

        // Get site settings
        $settings = SiteSetting::first();

        if (!$settings) {
            $this->error('No site settings found!');
            return;
        }

        $this->info('Site Settings found:');
        $this->line('Logo: ' . ($settings->logo ?? 'null'));
        $this->line('Favicon: ' . ($settings->favicon ?? 'null'));

        // Test logo URL
        $logoUrl = ImageHelper::getSiteLogo($settings->logo);
        $this->info('Logo URL: ' . $logoUrl);

        // Test favicon URL
        $faviconUrl = ImageHelper::getSiteFavicon($settings->favicon);
        $this->info('Favicon URL: ' . $faviconUrl);

        // Test if images exist
        $logoExists = ImageHelper::imageExists($settings->logo);
        $faviconExists = ImageHelper::imageExists($settings->favicon);

        $this->info('Logo exists: ' . ($logoExists ? 'Yes' : 'No'));
        $this->info('Favicon exists: ' . ($faviconExists ? 'Yes' : 'No'));

        // Test image info
        if ($logoExists) {
            $logoInfo = ImageHelper::getImageInfo($settings->logo);
            $this->info('Logo info: ' . json_encode($logoInfo, JSON_PRETTY_PRINT));
        }

        if ($faviconExists) {
            $faviconInfo = ImageHelper::getImageInfo($settings->favicon);
            $this->info('Favicon info: ' . json_encode($faviconInfo, JSON_PRETTY_PRINT));
        }

        $this->info('Test completed!');
    }
}
