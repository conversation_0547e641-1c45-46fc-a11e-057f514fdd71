<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SettingsService;
use Illuminate\Support\Facades\View;

class TestMaintenancePage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'maintenance:test-page';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the maintenance page rendering';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing maintenance page rendering...');
        
        // Get the settings service
        $settingsService = app(SettingsService::class);
        
        // Get maintenance message and settings
        $message = $settingsService->get('maintenance_message', 'The site is currently under maintenance. Please check back later.');
        $settings = $settingsService->all();
        
        try {
            // Render the view to a string
            $view = View::make('frontend.maintenance', [
                'message' => $message,
                'settings' => $settings
            ])->render();
            
            $this->info('Maintenance page rendered successfully!');
            $this->info('Message being displayed: ' . $message);
            
            // Save the rendered view to a file for inspection
            $path = storage_path('app/maintenance-test.html');
            file_put_contents($path, $view);
            
            $this->info('Rendered page saved to: ' . $path);
            
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Error rendering maintenance page: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
