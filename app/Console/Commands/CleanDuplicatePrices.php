<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;

class CleanDuplicatePrices extends Command
{
    protected $signature = 'prices:clean-duplicates';
    protected $description = 'تنظيف الأسعار المكررة والاحتفاظ بأحدثها فقط';

    public function handle()
    {
        $this->info('🧹 تنظيف الأسعار المكررة...');

        // جلب جميع الأسعار مجمعة حسب المعدن والعيار
        $duplicates = MetalPrice::selectRaw('metal_type, purity, COUNT(*) as count')
            ->groupBy('metal_type', 'purity')
            ->having('count', '>', 1)
            ->get();

        if ($duplicates->isEmpty()) {
            $this->info('✅ لا توجد أسعار مكررة');
            return 0;
        }

        $totalDeleted = 0;

        foreach ($duplicates as $duplicate) {
            $this->line("🔍 معالجة {$duplicate->metal_type} {$duplicate->purity} ({$duplicate->count} أسعار)");

            // جلب جميع الأسعار لهذا المعدن والعيار
            $prices = MetalPrice::where('metal_type', $duplicate->metal_type)
                ->where('purity', $duplicate->purity)
                ->orderBy('created_at', 'desc')
                ->get();

            // الاحتفاظ بأحدث سعر وحذف الباقي
            $latestPrice = $prices->first();
            $oldPrices = $prices->skip(1);

            foreach ($oldPrices as $oldPrice) {
                $this->line("  ❌ حذف سعر قديم: {$oldPrice->price_per_gram} ج.م ({$oldPrice->created_at->format('Y-m-d H:i')})");
                $oldPrice->delete();
                $totalDeleted++;
            }

            $this->line("  ✅ تم الاحتفاظ بـ: {$latestPrice->price_per_gram} ج.م ({$latestPrice->created_at->format('Y-m-d H:i')})");
        }

        $this->info("✨ تم حذف {$totalDeleted} سعر مكرر");
        $this->info("📊 إجمالي الأسعار المتبقية: " . MetalPrice::count());

        return 0;
    }
}
