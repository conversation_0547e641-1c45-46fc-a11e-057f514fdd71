<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;

class UpdateRealisticPrices extends Command
{
    protected $signature = 'prices:update-realistic';
    protected $description = 'تحديث الأسعار لتكون منطقية ومتسقة';

    public function handle()
    {
        $this->info('🔄 تحديث الأسعار لتكون منطقية...');

        // حذف الأسعار القديمة
        MetalPrice::truncate();
        $this->line('🗑️ تم حذف الأسعار القديمة');

        // أسعار الذهب المنطقية (بناءً على أسعار السوق المصري)
        $goldPrices = [
            ['purity' => '24K', 'price' => 2879.00], // الذهب الخالص
            ['purity' => '22K', 'price' => 2650.00], // 91.7% نقاء
            ['purity' => '21K', 'price' => 2520.00], // 87.5% نقاء  
            ['purity' => '18K', 'price' => 2159.00], // 75% نقاء
            ['purity' => '14K', 'price' => 1679.00], // 58.3% نقاء
        ];

        $this->info('📊 إضافة أسعار الذهب...');
        foreach ($goldPrices as $gold) {
            MetalPrice::create([
                'metal_type' => 'gold',
                'purity' => $gold['purity'],
                'price_per_gram' => $gold['price'],
                'price_per_ounce' => round($gold['price'] * 31.1035, 2),
                'currency' => 'EGP',
                'price_date' => now(),
                'source' => 'manual',
                'is_active' => true,
            ]);
            
            $this->line("✅ {$gold['purity']}: " . number_format($gold['price'], 2) . " ج.م/جرام");
        }

        // أسعار الفضة المنطقية
        $silverPrices = [
            ['purity' => '999', 'price' => 44.27], // الفضة الخالصة
            ['purity' => '925', 'price' => 40.95], // الفضة الإسترليني
        ];

        $this->info('📊 إضافة أسعار الفضة...');
        foreach ($silverPrices as $silver) {
            MetalPrice::create([
                'metal_type' => 'silver',
                'purity' => $silver['purity'],
                'price_per_gram' => $silver['price'],
                'price_per_ounce' => round($silver['price'] * 31.1035, 2),
                'currency' => 'EGP',
                'price_date' => now(),
                'source' => 'manual',
                'is_active' => true,
            ]);
            
            $this->line("✅ {$silver['purity']}: " . number_format($silver['price'], 2) . " ج.م/جرام");
        }

        $this->info('✨ تم تحديث جميع الأسعار بنجاح!');
        
        // عرض ملخص
        $this->table(
            ['المعدن', 'العيار', 'السعر/جرام', 'السعر/أوقية'],
            array_merge(
                collect($goldPrices)->map(fn($g) => ['🥇 ذهب', $g['purity'], number_format($g['price'], 2) . ' ج.م', number_format($g['price'] * 31.1035, 2) . ' ج.م'])->toArray(),
                collect($silverPrices)->map(fn($s) => ['🥈 فضة', $s['purity'], number_format($s['price'], 2) . ' ج.م', number_format($s['price'] * 31.1035, 2) . ' ج.م'])->toArray()
            )
        );

        return 0;
    }
}
