<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Category;
use App\Models\Product;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class GenerateDemoData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'demo:generate {--force : Force regeneration of demo data}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate demo data for the application including categories and products with images';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('force') || $this->confirm('This will generate demo data. Existing data may be overwritten. Continue?', true)) {
            $this->info('Generating demo data...');

            // Download demo images first
            $this->call('demo:download-images');

            // Generate categories
            $this->generateCategories();

            // Generate products
            $this->generateProducts();

            // Update site settings
            $this->updateSiteSettings();

            $this->info('Demo data generated successfully!');
        }
    }

    /**
     * Generate categories with images.
     */
    private function generateCategories()
    {
        $this->info('Generating categories...');

        $categories = [
            [
                'name_ar' => 'خواتم',
                'name_en' => 'Rings',
                'description_ar' => 'تشكيلة متنوعة من الخواتم الذهبية والفضية بتصاميم عصرية وكلاسيكية',
                'description_en' => 'A diverse collection of gold and silver rings with modern and classic designs',
                'image' => 'category-2.jpg',
                'slug' => 'rings',
            ],
            [
                'name_ar' => 'أساور',
                'name_en' => 'Bracelets',
                'description_ar' => 'أساور ذهبية وفضية بتصاميم فريدة تناسب جميع المناسبات',
                'description_en' => 'Gold and silver bracelets with unique designs suitable for all occasions',
                'image' => 'category-4.jpg',
                'slug' => 'bracelets',
            ],
            [
                'name_ar' => 'قلادات',
                'name_en' => 'Necklaces',
                'description_ar' => 'قلادات ذهبية وفضية بتصاميم متنوعة تناسب جميع الأذواق',
                'description_en' => 'Gold and silver necklaces with various designs to suit all tastes',
                'image' => 'category-1.jpg',
                'slug' => 'necklaces',
            ],
            [
                'name_ar' => 'أقراط',
                'name_en' => 'Earrings',
                'description_ar' => 'أقراط ذهبية وفضية بتصاميم عصرية وكلاسيكية',
                'description_en' => 'Gold and silver earrings with modern and classic designs',
                'image' => 'category-3.jpg',
                'slug' => 'earrings',
            ],
            [
                'name_ar' => 'ساعات',
                'name_en' => 'Watches',
                'description_ar' => 'ساعات فاخرة بتصاميم أنيقة وعصرية',
                'description_en' => 'Luxury watches with elegant and modern designs',
                'image' => 'category-5.jpg',
                'slug' => 'watches',
            ],
            [
                'name_ar' => 'أطقم',
                'name_en' => 'Sets',
                'description_ar' => 'أطقم ذهبية وفضية متكاملة تناسب جميع المناسبات',
                'description_en' => 'Complete gold and silver sets suitable for all occasions',
                'image' => 'category-6.jpg',
                'slug' => 'sets',
            ],
        ];

        foreach ($categories as $categoryData) {
            $category = Category::updateOrCreate(
                ['slug' => $categoryData['slug']],
                [
                    'name_ar' => $categoryData['name_ar'],
                    'name_en' => $categoryData['name_en'],
                    'description_ar' => $categoryData['description_ar'],
                    'description_en' => $categoryData['description_en'],
                    'image' => $categoryData['image'],
                    'is_active' => true,
                ]
            );

            $this->line("Category created: {$category->name}");
        }
    }

    /**
     * Generate products with images.
     */
    private function generateProducts()
    {
        $this->info('Generating products...');

        $products = [
            [
                'name' => 'خاتم ذهب عيار 21',
                'name_en' => '21K Gold Ring',
                'description' => 'خاتم ذهب عيار 21 بتصميم أنيق وعصري، مناسب للمناسبات الخاصة',
                'description_en' => '21K gold ring with an elegant and modern design, suitable for special occasions',
                'price' => 4500,
                'sale_price' => 4200,
                'category' => 'rings',
                'image' => 'product-1.jpg',
                'sku' => 'RING-001',
                'weight' => 5.2,
                'featured' => true,
                'material_type' => 'gold',
                'metal_purity' => '21K',
            ],
            [
                'name' => 'خاتم ألماس فاخر',
                'name_en' => 'Luxury Diamond Ring',
                'description' => 'خاتم ألماس فاخر بتصميم فريد، مثالي للخطوبة أو الزفاف',
                'description_en' => 'Luxury diamond ring with a unique design, perfect for engagement or wedding',
                'price' => 12000,
                'sale_price' => 11500,
                'category' => 'rings',
                'image' => 'product-2.jpg',
                'sku' => 'RING-002',
                'weight' => 4.8,
                'featured' => true,
            ],
            [
                'name' => 'قلادة ذهب عيار 21',
                'name_en' => '21K Gold Necklace',
                'description' => 'قلادة ذهب عيار 21 بتصميم أنيق، مناسبة للمناسبات الخاصة',
                'description_en' => '21K gold necklace with an elegant design, suitable for special occasions',
                'price' => 8500,
                'sale_price' => 8000,
                'category' => 'necklaces',
                'image' => 'product-3.jpg',
                'sku' => 'NECK-001',
                'weight' => 12.5,
                'featured' => true,
            ],
            [
                'name' => 'سوار ذهب عيار 18',
                'name_en' => '18K Gold Bracelet',
                'description' => 'سوار ذهب عيار 18 بتصميم عصري، مناسب للاستخدام اليومي',
                'description_en' => '18K gold bracelet with a modern design, suitable for daily use',
                'price' => 5500,
                'sale_price' => 5200,
                'category' => 'bracelets',
                'image' => 'product-4.jpg',
                'sku' => 'BRAC-001',
                'weight' => 8.3,
                'featured' => false,
            ],
            [
                'name' => 'أقراط ذهب عيار 21',
                'name_en' => '21K Gold Earrings',
                'description' => 'أقراط ذهب عيار 21 بتصميم أنيق، مناسبة للمناسبات الخاصة',
                'description_en' => '21K gold earrings with an elegant design, suitable for special occasions',
                'price' => 3500,
                'sale_price' => 3300,
                'category' => 'earrings',
                'image' => 'product-5.jpg',
                'sku' => 'EAR-001',
                'weight' => 4.2,
                'featured' => false,
            ],
            [
                'name' => 'طقم ذهب كامل عيار 21',
                'name_en' => '21K Complete Gold Set',
                'description' => 'طقم ذهب كامل عيار 21 يتضمن قلادة وأقراط وخاتم وسوار، مثالي للمناسبات الخاصة',
                'description_en' => '21K complete gold set including necklace, earrings, ring, and bracelet, perfect for special occasions',
                'price' => 25000,
                'sale_price' => 23500,
                'category' => 'sets',
                'image' => 'product-6.jpg',
                'sku' => 'SET-001',
                'weight' => 35.8,
                'featured' => true,
            ],
            [
                'name' => 'قلادة ألماس فاخرة',
                'name_en' => 'Luxury Diamond Necklace',
                'description' => 'قلادة ألماس فاخرة بتصميم فريد، مثالية للمناسبات الخاصة',
                'description_en' => 'Luxury diamond necklace with a unique design, perfect for special occasions',
                'price' => 18000,
                'sale_price' => 17000,
                'category' => 'necklaces',
                'image' => 'product-7.jpg',
                'sku' => 'NECK-002',
                'weight' => 15.2,
                'featured' => true,
            ],
            [
                'name' => 'ساعة ذهبية فاخرة',
                'name_en' => 'Luxury Gold Watch',
                'description' => 'ساعة ذهبية فاخرة بتصميم أنيق، مناسبة للمناسبات الرسمية',
                'description_en' => 'Luxury gold watch with an elegant design, suitable for formal occasions',
                'price' => 35000,
                'sale_price' => 32000,
                'category' => 'watches',
                'image' => 'product-8.jpg',
                'sku' => 'WATCH-001',
                'weight' => 85.5,
                'featured' => true,
            ],
            [
                'name' => 'سوار ذهب مرصع بالألماس',
                'name_en' => 'Diamond-Studded Gold Bracelet',
                'description' => 'سوار ذهب مرصع بالألماس بتصميم فاخر، مثالي للمناسبات الخاصة',
                'description_en' => 'Diamond-studded gold bracelet with a luxurious design, perfect for special occasions',
                'price' => 15000,
                'sale_price' => 14200,
                'category' => 'bracelets',
                'image' => 'product-9.jpg',
                'sku' => 'BRAC-002',
                'weight' => 12.8,
                'featured' => true,
            ],
            [
                'name' => 'قلادة ذهب مع قلب',
                'name_en' => 'Gold Heart Pendant',
                'description' => 'قلادة ذهب مع قلب بتصميم رومانسي، هدية مثالية للأحباء',
                'description_en' => 'Gold heart pendant with a romantic design, perfect gift for loved ones',
                'price' => 4200,
                'sale_price' => 3900,
                'category' => 'necklaces',
                'image' => 'product-10.jpg',
                'sku' => 'NECK-003',
                'weight' => 5.6,
                'featured' => false,
            ],
            [
                'name' => 'أقراط ألماس فاخرة',
                'name_en' => 'Luxury Diamond Earrings',
                'description' => 'أقراط ألماس فاخرة بتصميم أنيق، مناسبة للمناسبات الخاصة',
                'description_en' => 'Luxury diamond earrings with an elegant design, suitable for special occasions',
                'price' => 9500,
                'sale_price' => 9000,
                'category' => 'earrings',
                'image' => 'product-11.jpg',
                'sku' => 'EAR-002',
                'weight' => 3.8,
                'featured' => true,
            ],
            [
                'name' => 'سوار ألماس فاخر',
                'name_en' => 'Luxury Diamond Bracelet',
                'description' => 'سوار ألماس فاخر بتصميم أنيق، مناسب للمناسبات الخاصة',
                'description_en' => 'Luxury diamond bracelet with an elegant design, suitable for special occasions',
                'price' => 12500,
                'sale_price' => 11800,
                'category' => 'bracelets',
                'image' => 'product-12.jpg',
                'sku' => 'BRAC-003',
                'weight' => 9.2,
                'featured' => true,
            ],
        ];

        foreach ($products as $productData) {
            $category = Category::where('slug', $productData['category'])->first();

            if ($category) {
                $product = Product::updateOrCreate(
                    ['sku' => $productData['sku']],
                    [
                        'name_ar' => $productData['name'],
                        'name_en' => $productData['name_en'],
                        'description_ar' => $productData['description'],
                        'description_en' => $productData['description_en'],
                        'price' => $productData['price'],
                        'sale_price' => $productData['sale_price'],
                        'category_id' => $category->id,
                        'image' => $productData['image'],
                        'weight' => $productData['weight'],
                        'is_active' => true,
                        'featured' => $productData['featured'],
                        'stock' => rand(5, 50),
                        'slug' => Str::slug($productData['name_en']),
                        'metal_purity' => $productData['metal_purity'] ?? '21K',
                        'material_type' => $productData['material_type'] ?? 'gold',
                        'stock_quantity' => rand(5, 50),
                        'is_featured' => $productData['featured'],
                    ]
                );

                $this->line("Product created: {$product->name}");
            } else {
                $this->error("Category not found: {$productData['category']}");
            }
        }
    }

    /**
     * Update site settings with demo data.
     */
    private function updateSiteSettings()
    {
        $this->info('Updating site settings...');

        $settings = SiteSetting::first();

        if ($settings) {
            $settings->update([
                'site_name' => 'مكة جولد',
                'site_description' => 'متجر مجوهرات مكة جولد - أفضل مجوهرات ذهبية وفضية',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+20 ************',
                'address' => 'القاهرة، مصر',
                'facebook_url' => 'https://facebook.com/makkahgold',
                'instagram_url' => 'https://instagram.com/makkahgold',
                'twitter_url' => 'https://twitter.com/makkahgold',
                'whatsapp_number' => '+201234567890',
                'gold_price_24k' => 2850,
                'gold_price_21k' => 2500,
                'gold_price_18k' => 2150,
                'meta_title' => 'مكة جولد - متجر مجوهرات ذهبية وفضية',
                'meta_description' => 'متجر مكة جولد للمجوهرات الذهبية والفضية بأفضل الأسعار وأرقى التصاميم',
                'meta_keywords' => 'مجوهرات، ذهب، فضة، خواتم، أساور، قلادات، أقراط، هدايا',
                'maintenance_mode' => false,
                'shipping_cost' => 50,
                'free_shipping_threshold' => 5000,
                'tax_rate' => 14,
                'prices_include_tax' => true,
                'show_featured_products' => true,
                'show_new_arrivals' => true,
                'show_categories' => true,
                'show_gold_prices' => true,
                'footer_text' => '&copy; ' . date('Y') . ' مكة جولد للمجوهرات. جميع الحقوق محفوظة.',
                'show_cookie_banner' => true,
                'cookie_banner_text' => 'هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.',
                'cookie_banner_button_text' => 'أوافق',
                'enable_social_sharing' => true,
                'share_on_facebook' => true,
                'share_on_twitter' => true,
                'share_on_whatsapp' => true,
            ]);

            $this->line("Site settings updated successfully");
        } else {
            $this->error("Site settings not found");
        }
    }
}
