<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class MakeSuperAdminCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'make:super-admin {--email=} {--name=} {--password=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنشاء مستخدم سوبر أدمن جديد';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('إنشاء مستخدم سوبر أدمن جديد');
        $this->info('=======================');

        // الحصول على البيانات من المستخدم أو من الخيارات
        $email = $this->option('email') ?: $this->ask('البريد الإلكتروني');
        $name = $this->option('name') ?: $this->ask('الاسم');
        $password = $this->option('password') ?: $this->secret('كلمة المرور');

        // التحقق من صحة البيانات
        $validator = Validator::make([
            'email' => $email,
            'name' => $name,
            'password' => $password,
        ], [
            'email' => 'required|email|unique:users,email',
            'name' => 'required|string|max:255',
            'password' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            $this->error('خطأ في البيانات المدخلة:');
            foreach ($validator->errors()->all() as $error) {
                $this->error('- ' . $error);
            }
            return 1;
        }

        try {
            // إنشاء دور السوبر أدمن إذا لم يكن موجوداً
            $superAdminRole = Role::firstOrCreate([
                'name' => 'super_admin',
                'guard_name' => 'web'
            ]);

            // إنشاء المستخدم
            $user = User::create([
                'name' => $name,
                'email' => $email,
                'password' => Hash::make($password),
                'email_verified_at' => now(),
            ]);

            // تعيين دور السوبر أدمن
            $user->assignRole('super_admin');

            $this->info('تم إنشاء السوبر أدمن بنجاح!');
            $this->info('البريد الإلكتروني: ' . $email);
            $this->info('الاسم: ' . $name);
            $this->warn('تأكد من حفظ كلمة المرور في مكان آمن.');

            return 0;

        } catch (\Exception $e) {
            $this->error('حدث خطأ أثناء إنشاء السوبر أدمن: ' . $e->getMessage());
            return 1;
        }
    }
}
