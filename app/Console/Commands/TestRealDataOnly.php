<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;

class TestRealDataOnly extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:real-data-only';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار أن الصفحة تعرض البيانات الحقيقية فقط من قاعدة البيانات';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء اختبار عرض البيانات الحقيقية فقط...');
        $this->newLine();

        // 1. اختبار عدم وجود أسعار افتراضية
        $this->testNoDefaultPrices();
        
        // 2. اختبار عرض البيانات الموجودة فقط
        $this->testOnlyExistingData();
        
        // 3. اختبار الرسم البياني
        $this->testChartRealDataOnly();
        
        // 4. اختبار حذف بعض البيانات
        $this->testMissingDataHandling();

        $this->newLine();
        $this->info('✅ انتهى اختبار البيانات الحقيقية');
    }

    private function testNoDefaultPrices()
    {
        $this->info('❌ اختبار عدم وجود أسعار افتراضية...');
        
        $today = now()->format('Y-m-d');
        
        // الحصول على أسعار الذهب
        $goldPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->whereDate('price_date', $today)
            ->get();
            
        $this->info("   📊 عدد أسعار الذهب الحقيقية: {$goldPrices->count()}");
        
        // التحقق من أن جميع الأسعار من قاعدة البيانات
        foreach ($goldPrices as $price) {
            if (isset($price->is_default) && $price->is_default) {
                $this->error("❌ تم العثور على سعر افتراضي: {$price->purity}");
            } else {
                $this->info("✅ {$price->purity}: سعر حقيقي من قاعدة البيانات");
            }
        }
        
        $this->newLine();
    }

    private function testOnlyExistingData()
    {
        $this->info('📊 اختبار عرض البيانات الموجودة فقط...');
        
        $today = now()->format('Y-m-d');
        
        // قائمة العيارات المطلوبة
        $requiredPurities = ['24K', '22K', '21K', '18K', '14K'];
        $existingPurities = [];
        $missingPurities = [];
        
        foreach ($requiredPurities as $purity) {
            $price = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $today)
                ->first();
                
            if ($price) {
                $existingPurities[] = $purity;
                $this->info("✅ {$purity}: موجود في قاعدة البيانات");
            } else {
                $missingPurities[] = $purity;
                $this->warn("⚠️  {$purity}: غير موجود - يجب عدم عرضه");
            }
        }
        
        $this->info("   📈 العيارات الموجودة: " . implode(', ', $existingPurities));
        $this->warn("   📉 العيارات المفقودة: " . implode(', ', $missingPurities));
        
        $this->newLine();
    }

    private function testChartRealDataOnly()
    {
        $this->info('📈 اختبار الرسم البياني - البيانات الحقيقية فقط...');
        
        $periods = [7, 30, 90];
        
        foreach ($periods as $days) {
            $startDate = now()->subDays($days);
            
            $goldData = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', '24K')
                ->whereDate('price_date', '>=', $startDate)
                ->count();
                
            if ($goldData > 0) {
                $this->info("✅ فترة {$days} يوم: {$goldData} نقطة بيانات حقيقية");
            } else {
                $this->warn("⚠️  فترة {$days} يوم: لا توجد بيانات - يجب عرض رسالة 'لا توجد بيانات'");
            }
        }
        
        $this->newLine();
    }

    private function testMissingDataHandling()
    {
        $this->info('🗑️  اختبار التعامل مع البيانات المفقودة...');
        
        // حفظ البيانات الحالية
        $originalData = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('purity', '22K')
            ->whereDate('price_date', now()->format('Y-m-d'))
            ->get();
            
        // حذف مؤقت لعيار 22K
        MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('purity', '22K')
            ->whereDate('price_date', now()->format('Y-m-d'))
            ->delete();
            
        $this->warn('   🗑️  تم حذف أسعار عيار 22K مؤقتاً');
        
        // التحقق من عدم وجود العيار
        $missingPrice = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('purity', '22K')
            ->whereDate('price_date', now()->format('Y-m-d'))
            ->first();
            
        if (!$missingPrice) {
            $this->info('✅ عيار 22K غير موجود - يجب عدم عرضه في الصفحة');
        } else {
            $this->error('❌ عيار 22K ما زال موجود!');
        }
        
        // استعادة البيانات
        foreach ($originalData as $data) {
            MetalPrice::create($data->toArray());
        }
        
        $this->info('   ♻️  تم استعادة البيانات المحذوفة');
        
        $this->newLine();
    }
}
