<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use App\Services\SettingsService;
use Illuminate\Support\Facades\Cache;

class DisableMaintenanceMode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'maintenance:disable';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Disable maintenance mode in the site settings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the settings service
        $settingsService = app(SettingsService::class);
        
        // Update the maintenance_mode setting
        $settingsService->set('maintenance_mode', false);
        
        // Clear the cache to ensure the setting is updated
        Cache::forget('site_settings');
        
        $this->info('Maintenance mode has been disabled successfully.');
        
        return Command::SUCCESS;
    }
}
