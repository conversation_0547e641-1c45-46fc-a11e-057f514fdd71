<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;

class ImportSiteSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'site:import-settings {path : Path to the settings JSON file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import site settings from a JSON file';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Importing site settings...');

        // Get import path
        $path = $this->argument('path');
        
        // Check if file exists
        if (!File::exists($path)) {
            $this->error('File not found: ' . $path);
            return 1;
        }

        // Read JSON file
        $json = File::get($path);
        $settingsArray = json_decode($json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error('Invalid JSON file: ' . json_last_error_msg());
            return 1;
        }

        // Get current settings
        $settings = SiteSetting::first();
        
        if (!$settings) {
            $this->info('Creating new settings...');
            $settings = new SiteSetting();
        } else {
            $this->info('Updating existing settings...');
        }

        // Don't overwrite mail_password if it's masked
        if (isset($settingsArray['mail_password']) && $settingsArray['mail_password'] === '********') {
            unset($settingsArray['mail_password']);
        }

        // Update settings
        $settings->fill($settingsArray);
        $settings->save();
        
        // Clear cache
        Cache::forget('site_settings');

        $this->info('Site settings imported successfully!');
        $this->table(
            ['Setting', 'Value'],
            [
                ['site_name', $settings->site_name],
                ['contact_email', $settings->contact_email],
                ['maintenance_mode', $settings->maintenance_mode ? 'Yes' : 'No'],
            ]
        );
        
        return 0;
    }
}
