<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;

class AddTestPrices extends Command
{
    protected $signature = 'prices:add-test';
    protected $description = 'إضافة أسعار تجريبية للمعادن';

    public function handle()
    {
        $this->info('إضافة أسعار تجريبية...');

        // أسعار الذهب
        $goldPrices = [
            ['purity' => '24K', 'price' => 2879.00],
            ['purity' => '22K', 'price' => 2650.00],
            ['purity' => '21K', 'price' => 2520.00],
            ['purity' => '18K', 'price' => 2159.00],
            ['purity' => '14K', 'price' => 1679.00],
        ];

        foreach ($goldPrices as $gold) {
            MetalPrice::create([
                'metal_type' => 'gold',
                'purity' => $gold['purity'],
                'price_per_gram' => $gold['price'],
                'price_per_ounce' => round($gold['price'] * 31.1035, 2),
                'currency' => 'EGP',
                'price_date' => now(),
                'source' => 'test_data',
                'is_active' => true,
            ]);
            
            $this->line("✅ تم إضافة سعر الذهب {$gold['purity']}: {$gold['price']} ج.م");
        }

        // أسعار الفضة
        $silverPrices = [
            ['purity' => '999', 'price' => 44.27],
            ['purity' => '925', 'price' => 40.95],
        ];

        foreach ($silverPrices as $silver) {
            MetalPrice::create([
                'metal_type' => 'silver',
                'purity' => $silver['purity'],
                'price_per_gram' => $silver['price'],
                'price_per_ounce' => round($silver['price'] * 31.1035, 2),
                'currency' => 'EGP',
                'price_date' => now(),
                'source' => 'test_data',
                'is_active' => true,
            ]);
            
            $this->line("✅ تم إضافة سعر الفضة {$silver['purity']}: {$silver['price']} ج.م");
        }

        $this->info('✨ تم إضافة جميع الأسعار التجريبية بنجاح!');
        
        // عرض إجمالي الأسعار
        $totalPrices = MetalPrice::count();
        $this->info("📊 إجمالي الأسعار في قاعدة البيانات: {$totalPrices}");

        return 0;
    }
}
