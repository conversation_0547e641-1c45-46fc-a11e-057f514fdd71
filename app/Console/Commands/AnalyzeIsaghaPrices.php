<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;
use Illuminate\Support\Facades\Http;

class AnalyzeIsaghaPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'metal-prices:analyze-isagha {--add : Add missing purities to database}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze all metal purities available on iSagha and compare with database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 تحليل جميع العيارات المتاحة في موقع iSagha...');

        // جميع العيارات والأسعار من iSagha (بناءً على التحليل الفعلي)
        $isaghaPrices = $this->getIsaghaPrices();

        // العيارات الموجودة في قاعدة البيانات
        $existingPurities = $this->getExistingPurities();

        // تحليل العيارات المفقودة
        $missingPurities = $this->findMissingPurities($isaghaPrices, $existingPurities);

        // عرض التحليل
        $this->displayAnalysis($isaghaPrices, $existingPurities, $missingPurities);

        // إضافة العيارات المفقودة إذا طُلب ذلك
        if ($this->option('add') && !empty($missingPurities)) {
            $this->addMissingPurities($missingPurities);
        } elseif (!empty($missingPurities)) {
            $this->askToAddMissingPurities($missingPurities);
        }

        return 0;
    }

    /**
     * الحصول على جميع الأسعار من iSagha
     */
    private function getIsaghaPrices()
    {
        return [
            'gold' => [
                '24K' => 5342.75,
                '22K' => 4897.5,
                '21K' => 4675,
                '18K' => 4007.25,
                '14K' => 3116.75,
                '12K' => 2671.5,
                '9K' => 2003.5,
            ],
            'silver' => [
                '999' => 59,
                '925' => 54.75,
                '900' => 53.25,
                '800' => 47.25,
                '600' => 35.5,
            ],
            'gold_coins' => [
                'جنيه_ذهب' => 37400,
                'نصف_جنيه_ذهب' => 18700, // تقدير بناءً على نصف القيمة
                'ربع_جنيه_ذهب' => 9350,  // تقدير بناءً على ربع القيمة
            ]
        ];
    }

    /**
     * الحصول على العيارات الموجودة في قاعدة البيانات
     */
    private function getExistingPurities()
    {
        $existing = [
            'gold' => [],
            'silver' => [],
            'gold_coins' => []
        ];

        // عيارات الذهب الموجودة
        $existing['gold'] = MetalPrice::where('metal_type', 'gold')
            ->where('currency', 'EGP')
            ->distinct()
            ->pluck('purity')
            ->toArray();

        // عيارات الفضة الموجودة
        $existing['silver'] = MetalPrice::where('metal_type', 'silver')
            ->where('currency', 'EGP')
            ->distinct()
            ->pluck('purity')
            ->toArray();

        // الجنيهات الذهبية الموجودة (إذا كانت مخزنة بطريقة مختلفة)
        $existing['gold_coins'] = MetalPrice::where('metal_type', 'gold_coin')
            ->where('currency', 'EGP')
            ->distinct()
            ->pluck('purity')
            ->toArray();

        return $existing;
    }

    /**
     * العثور على العيارات المفقودة
     */
    private function findMissingPurities($isaghaPrices, $existingPurities)
    {
        $missing = [
            'gold' => [],
            'silver' => [],
            'gold_coins' => []
        ];

        // العيارات المفقودة للذهب
        foreach ($isaghaPrices['gold'] as $purity => $price) {
            if (!in_array($purity, $existingPurities['gold'])) {
                $missing['gold'][$purity] = $price;
            }
        }

        // العيارات المفقودة للفضة
        foreach ($isaghaPrices['silver'] as $purity => $price) {
            if (!in_array($purity, $existingPurities['silver'])) {
                $missing['silver'][$purity] = $price;
            }
        }

        // الجنيهات المفقودة
        foreach ($isaghaPrices['gold_coins'] as $coin => $price) {
            if (!in_array($coin, $existingPurities['gold_coins'])) {
                $missing['gold_coins'][$coin] = $price;
            }
        }

        return $missing;
    }

    /**
     * عرض تحليل العيارات
     */
    private function displayAnalysis($isaghaPrices, $existingPurities, $missingPurities)
    {
        $this->info("\n📊 تحليل العيارات المتاحة في iSagha:");

        // عرض عيارات الذهب
        $this->info("\n🥇 عيارات الذهب في iSagha:");
        foreach ($isaghaPrices['gold'] as $purity => $price) {
            $status = in_array($purity, $existingPurities['gold']) ? '✅ موجود' : '❌ مفقود';
            $this->line("   {$purity}: {$price} ج.م - {$status}");
        }

        // عرض عيارات الفضة
        $this->info("\n🥈 عيارات الفضة في iSagha:");
        foreach ($isaghaPrices['silver'] as $purity => $price) {
            $status = in_array($purity, $existingPurities['silver']) ? '✅ موجود' : '❌ مفقود';
            $this->line("   عيار {$purity}: {$price} ج.م - {$status}");
        }

        // عرض الجنيهات الذهبية
        $this->info("\n🪙 الجنيهات الذهبية في iSagha:");
        foreach ($isaghaPrices['gold_coins'] as $coin => $price) {
            $status = in_array($coin, $existingPurities['gold_coins']) ? '✅ موجود' : '❌ مفقود';
            $coinName = str_replace('_', ' ', $coin);
            $this->line("   {$coinName}: {$price} ج.م - {$status}");
        }

        // ملخص العيارات المفقودة
        $totalMissing = count($missingPurities['gold']) + count($missingPurities['silver']) + count($missingPurities['gold_coins']);

        if ($totalMissing > 0) {
            $this->warn("\n⚠️  يوجد {$totalMissing} عيار/منتج مفقود في قاعدة البيانات");
        } else {
            $this->info("\n✅ جميع العيارات موجودة في قاعدة البيانات");
        }
    }

    /**
     * السؤال عن إضافة العيارات المفقودة
     */
    private function askToAddMissingPurities($missingPurities)
    {
        $this->info("\n🤔 هل تريد إضافة العيارات المفقودة إلى قاعدة البيانات؟");

        if ($this->confirm('إضافة العيارات المفقودة؟')) {
            $this->addMissingPurities($missingPurities);
        } else {
            $this->info('تم إلغاء العملية. يمكنك إضافة العيارات لاحقاً باستخدام --add');
        }
    }

    /**
     * إضافة العيارات المفقودة إلى قاعدة البيانات
     */
    private function addMissingPurities($missingPurities)
    {
        $this->info("\n🔄 جاري إضافة العيارات المفقودة...");

        $addedCount = 0;
        $today = now()->format('Y-m-d');

        // إضافة عيارات الذهب المفقودة
        foreach ($missingPurities['gold'] as $purity => $price) {
            MetalPrice::create([
                'metal_type' => 'gold',
                'purity' => $purity,
                'price_per_gram' => $price,
                'price_per_ounce' => $price * 31.1035,
                'currency' => 'EGP',
                'price_date' => $today,
                'source' => 'isagha',
                'is_active' => true,
            ]);
            $this->line("   ✅ أضيف ذهب {$purity}: {$price} ج.م");
            $addedCount++;
        }

        // إضافة عيارات الفضة المفقودة
        foreach ($missingPurities['silver'] as $purity => $price) {
            MetalPrice::create([
                'metal_type' => 'silver',
                'purity' => $purity,
                'price_per_gram' => $price,
                'price_per_ounce' => $price * 31.1035,
                'currency' => 'EGP',
                'price_date' => $today,
                'source' => 'isagha',
                'is_active' => true,
            ]);
            $this->line("   ✅ أضيف فضة عيار {$purity}: {$price} ج.م");
            $addedCount++;
        }

        // إضافة الجنيهات الذهبية المفقودة (كـ gold مع purity خاص)
        foreach ($missingPurities['gold_coins'] as $coin => $price) {
            MetalPrice::create([
                'metal_type' => 'gold',
                'purity' => $coin, // استخدام اسم الجنيه كـ purity
                'price_per_gram' => $price / 8.5, // تقدير وزن الجنيه الذهب (8.5 جرام)
                'price_per_ounce' => $price / 8.5 * 31.1035,
                'currency' => 'EGP',
                'price_date' => $today,
                'source' => 'isagha',
                'is_active' => true,
            ]);
            $coinName = str_replace('_', ' ', $coin);
            $this->line("   ✅ أضيف {$coinName}: {$price} ج.م");
            $addedCount++;
        }

        $this->info("\n🎉 تم إضافة {$addedCount} عيار/منتج جديد بنجاح!");
    }
}
