<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MetalPrice;
use Carbon\Carbon;

class SeedGoldPricesTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:gold-prices-test-data {--clean : Clean existing data before seeding}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إضافة بيانات تجريبية لأسعار الذهب لليومين الماضيين (أمس واليوم)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔄 بدء إضافة البيانات التجريبية لأسعار الذهب...');
        $this->newLine();

        // تنظيف البيانات الموجودة إذا طُلب ذلك
        if ($this->option('clean')) {
            $this->cleanExistingData();
        }

        // إضافة بيانات اليوم
        $this->addTodayPrices();
        
        // إضافة بيانات الأمس
        $this->addYesterdayPrices();
        
        // إضافة بيانات الفضة والبلاتين
        $this->addOtherMetalsPrices();

        $this->newLine();
        $this->info('✅ تم إضافة جميع البيانات التجريبية بنجاح!');
        
        // عرض ملخص البيانات المضافة
        $this->showDataSummary();
    }

    private function cleanExistingData()
    {
        $this->warn('🧹 تنظيف البيانات الموجودة...');
        
        $deletedCount = MetalPrice::where('currency', 'EGP')
            ->whereDate('price_date', '>=', now()->subDays(2))
            ->delete();
            
        $this->line("   تم حذف {$deletedCount} سجل من البيانات السابقة");
        $this->newLine();
    }

    private function addTodayPrices()
    {
        $this->info('📅 إضافة أسعار اليوم...');
        
        $today = now()->format('Y-m-d');
        
        // أسعار الذهب لليوم (أسعار واقعية للسوق المصري)
        $goldPrices = [
            '24K' => 2850.00,  // عيار 24
            '22K' => 2610.00,  // عيار 22
            '21K' => 2493.75,  // عيار 21
            '18K' => 2137.50,  // عيار 18
            '14K' => 1662.50,  // عيار 14
        ];

        foreach ($goldPrices as $purity => $pricePerGram) {
            $this->createGoldPrice($today, $purity, $pricePerGram);
            $this->line("   ✅ {$purity}: {$pricePerGram} ج.م/جرام");
        }
        
        $this->newLine();
    }

    private function addYesterdayPrices()
    {
        $this->info('📅 إضافة أسعار الأمس...');
        
        $yesterday = now()->subDay()->format('Y-m-d');
        
        // أسعار الذهب للأمس (مع تغييرات طفيفة لاختبار المؤشرات)
        $goldPrices = [
            '24K' => 2820.00,  // انخفض بـ 30 ج.م (-1.05%)
            '22K' => 2590.00,  // انخفض بـ 20 ج.م (-0.77%)
            '21K' => 2478.75,  // انخفض بـ 15 ج.م (-0.60%)
            '18K' => 2125.00,  // انخفض بـ 12.50 ج.م (-0.59%)
            '14K' => 1650.00,  // انخفض بـ 12.50 ج.م (-0.76%)
        ];

        foreach ($goldPrices as $purity => $pricePerGram) {
            $this->createGoldPrice($yesterday, $purity, $pricePerGram);
            $this->line("   ✅ {$purity}: {$pricePerGram} ج.م/جرام");
        }
        
        $this->newLine();
    }

    private function addOtherMetalsPrices()
    {
        $this->info('🥈 إضافة أسعار الفضة والبلاتين...');
        
        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');
        
        // أسعار الفضة
        $silverPrices = [
            $today => ['999' => 45.50],      // اليوم
            $yesterday => ['999' => 44.80],  // الأمس (ارتفاع *****%)
        ];
        
        // أسعار البلاتين
        $platinumPrices = [
            $today => ['950' => 1680.00],      // اليوم
            $yesterday => ['950' => 1675.00],  // الأمس (ارتفاع +0.30%)
        ];
        
        // إضافة أسعار الفضة
        foreach ($silverPrices as $date => $prices) {
            foreach ($prices as $purity => $price) {
                $this->createMetalPrice($date, 'silver', $purity, $price);
                $dateLabel = $date === $today ? 'اليوم' : 'الأمس';
                $this->line("   ✅ فضة {$purity} ({$dateLabel}): {$price} ج.م/جرام");
            }
        }
        
        // إضافة أسعار البلاتين
        foreach ($platinumPrices as $date => $prices) {
            foreach ($prices as $purity => $price) {
                $this->createMetalPrice($date, 'platinum', $purity, $price);
                $dateLabel = $date === $today ? 'اليوم' : 'الأمس';
                $this->line("   ✅ بلاتين {$purity} ({$dateLabel}): {$price} ج.م/جرام");
            }
        }
        
        $this->newLine();
    }

    private function createGoldPrice($date, $purity, $pricePerGram)
    {
        $this->createMetalPrice($date, 'gold', $purity, $pricePerGram);
    }

    private function createMetalPrice($date, $metalType, $purity, $pricePerGram)
    {
        // حساب سعر الأوقية
        $pricePerOunce = $pricePerGram * 31.1035;
        
        // التحقق من وجود السعر مسبقاً
        $existing = MetalPrice::where('metal_type', $metalType)
            ->where('purity', $purity)
            ->where('currency', 'EGP')
            ->whereDate('price_date', $date)
            ->first();
            
        if ($existing) {
            // تحديث السعر الموجود
            $existing->update([
                'price_per_gram' => $pricePerGram,
                'price_per_ounce' => $pricePerOunce,
            ]);
        } else {
            // إنشاء سعر جديد
            MetalPrice::create([
                'metal_type' => $metalType,
                'purity' => $purity,
                'price_per_gram' => $pricePerGram,
                'price_per_ounce' => $pricePerOunce,
                'currency' => 'EGP',
                'price_date' => $date,
                'source' => 'test_data',
            ]);
        }
    }

    private function showDataSummary()
    {
        $this->info('📊 ملخص البيانات المضافة:');
        
        $today = now()->format('Y-m-d');
        $yesterday = now()->subDay()->format('Y-m-d');
        
        // عدد أسعار اليوم
        $todayCount = MetalPrice::where('currency', 'EGP')
            ->whereDate('price_date', $today)
            ->count();
            
        // عدد أسعار الأمس
        $yesterdayCount = MetalPrice::where('currency', 'EGP')
            ->whereDate('price_date', $yesterday)
            ->count();
            
        $this->line("   📅 أسعار اليوم ({$today}): {$todayCount} سعر");
        $this->line("   📅 أسعار الأمس ({$yesterday}): {$yesterdayCount} سعر");
        
        // عرض أسعار الذهب مع التغييرات
        $this->newLine();
        $this->info('💰 أسعار الذهب مع التغييرات:');
        
        $goldPurities = ['24K', '22K', '21K', '18K', '14K'];
        
        foreach ($goldPurities as $purity) {
            $todayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $today)
                ->first();
                
            $yesterdayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('price_date', $yesterday)
                ->first();
                
            if ($todayPrice && $yesterdayPrice) {
                $change = $todayPrice->price_per_gram - $yesterdayPrice->price_per_gram;
                $changePercent = ($change / $yesterdayPrice->price_per_gram) * 100;
                
                $direction = $change > 0 ? '📈' : ($change < 0 ? '📉' : '➡️');
                $changeText = $change > 0 ? '+' . number_format($change, 2) : number_format($change, 2);
                
                $this->line("   {$direction} {$purity}: {$todayPrice->price_per_gram} ج.م ({$changeText} ج.م، " . number_format($changePercent, 2) . "%)");
            }
        }
    }
}
