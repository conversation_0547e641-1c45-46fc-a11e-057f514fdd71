<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SiteSetting;
use App\Models\Testimonial;

class AddMissingTestData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:missing-test-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إضافة البيانات التجريبية المفقودة لاختبار التكامل';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📝 إضافة البيانات التجريبية المفقودة...');
        $this->newLine();

        // 1. إضافة آراء العملاء
        $this->addTestimonials();
        
        // 2. تحديث روابط وسائل التواصل الاجتماعي
        $this->updateSocialMediaLinks();
        
        // 3. إضافة الكلمات المفتاحية
        $this->addSEOKeywords();
        
        // 4. إضافة رقم واتساب
        $this->addWhatsAppNumber();

        $this->newLine();
        $this->info('✅ تم إضافة جميع البيانات التجريبية المفقودة');
    }

    private function addTestimonials()
    {
        $this->info('💬 إضافة آراء العملاء...');
        
        $testimonials = [
            [
                'client_name_ar' => 'فاطمة أحمد',
                'client_name_en' => 'Fatima Ahmed',
                'content_ar' => 'تجربة رائعة مع مكة جولد! المجوهرات عالية الجودة والخدمة ممتازة. أنصح الجميع بالتسوق من هنا.',
                'content_en' => 'Amazing experience with Makkah Gold! High quality jewelry and excellent service. I recommend everyone to shop here.',
                'rating' => 5,
                'location_ar' => 'الرياض، السعودية',
                'location_en' => 'Riyadh, Saudi Arabia',
                'is_active' => true,
                'order' => 1
            ],
            [
                'client_name_ar' => 'محمد علي',
                'client_name_en' => 'Mohammed Ali',
                'content_ar' => 'اشتريت خاتم زواج من مكة جولد وكانت التجربة مميزة. التصميم رائع والسعر مناسب.',
                'content_en' => 'I bought a wedding ring from Makkah Gold and the experience was exceptional. Beautiful design and reasonable price.',
                'rating' => 5,
                'location_ar' => 'جدة، السعودية',
                'location_en' => 'Jeddah, Saudi Arabia',
                'is_active' => true,
                'order' => 2
            ],
            [
                'client_name_ar' => 'عائشة محمد',
                'client_name_en' => 'Aisha Mohammed',
                'content_ar' => 'خدمة عملاء ممتازة وتشكيلة واسعة من المجوهرات الذهبية. سأعود للشراء مرة أخرى بالتأكيد.',
                'content_en' => 'Excellent customer service and wide selection of gold jewelry. I will definitely shop again.',
                'rating' => 4,
                'location_ar' => 'الدمام، السعودية',
                'location_en' => 'Dammam, Saudi Arabia',
                'is_active' => true,
                'order' => 3
            ]
        ];

        foreach ($testimonials as $testimonialData) {
            $existing = Testimonial::where('client_name_ar', $testimonialData['client_name_ar'])->first();
            
            if (!$existing) {
                Testimonial::create($testimonialData);
                $this->info("✅ تم إضافة رأي العميل: {$testimonialData['client_name_ar']}");
            } else {
                $this->line("   ⚠️  رأي العميل موجود مسبقاً: {$testimonialData['client_name_ar']}");
            }
        }
        
        $this->newLine();
    }

    private function updateSocialMediaLinks()
    {
        $this->info('📱 تحديث روابط وسائل التواصل الاجتماعي...');
        
        $settings = SiteSetting::first();
        
        if ($settings) {
            $socialLinks = [
                'facebook_url' => 'https://facebook.com/makkahgold',
                'instagram_url' => 'https://instagram.com/makkahgold',
                'twitter_url' => 'https://twitter.com/makkahgold',
                'youtube_url' => 'https://youtube.com/@makkahgold',
                'tiktok_url' => 'https://tiktok.com/@makkahgold',
                'linkedin_url' => 'https://linkedin.com/company/makkahgold'
            ];
            
            foreach ($socialLinks as $field => $url) {
                if (empty($settings->$field)) {
                    $settings->$field = $url;
                    $this->info("✅ تم إضافة رابط: {$field}");
                } else {
                    $this->line("   ⚠️  الرابط موجود مسبقاً: {$field}");
                }
            }
            
            $settings->save();
        }
        
        $this->newLine();
    }

    private function addSEOKeywords()
    {
        $this->info('🔍 إضافة الكلمات المفتاحية...');
        
        $settings = SiteSetting::first();
        
        if ($settings && empty($settings->meta_keywords)) {
            $keywords = 'مجوهرات, ذهب, فضة, خواتم, أساور, قلائد, أقراط, مكة جولد, مجوهرات ذهبية, مجوهرات فضية, مجوهرات راقية, تصاميم عصرية, مجوهرات نسائية, مجوهرات رجالية, خواتم زواج, مجوهرات أطفال';
            
            $settings->meta_keywords = $keywords;
            $settings->save();
            
            $this->info('✅ تم إضافة الكلمات المفتاحية');
        } else {
            $this->line('   ⚠️  الكلمات المفتاحية موجودة مسبقاً');
        }
        
        $this->newLine();
    }

    private function addWhatsAppNumber()
    {
        $this->info('📱 إضافة رقم واتساب...');
        
        $settings = SiteSetting::first();
        
        if ($settings && empty($settings->whatsapp_number)) {
            $settings->whatsapp_number = '+966501234567';
            $settings->save();
            
            $this->info('✅ تم إضافة رقم واتساب');
        } else {
            $this->line('   ⚠️  رقم واتساب موجود مسبقاً');
        }
        
        $this->newLine();
    }
}
