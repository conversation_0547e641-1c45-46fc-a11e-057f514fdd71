<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class AssignRoles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:assign-roles';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'تعيين الأدوار للمستخدمين';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('بدء تعيين الأدوار...');

        // تعيين دور super_admin للسوبر أدمن
        $superAdmin = User::where('email', '<EMAIL>')->first();
        if ($superAdmin && !$superAdmin->hasRole('super_admin')) {
            $superAdmin->assignRole('super_admin');
            $this->info('تم تعيين دور super_admin للسوبر أدمن');
        }

        // تعيين دور user لجميع المستخدمين الآخرين
        $users = User::whereDoesntHave('roles')->get();
        foreach ($users as $user) {
            $user->assignRole('user');
        }
        $this->info("تم تعيين دور user لـ {$users->count()} مستخدم");

        $this->info('تم الانتهاء من تعيين الأدوار بنجاح!');
    }
}
