<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class CheckViewsIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:views-integration';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'فحص استخدام متغيرات إعدادات الموقع في ملفات العروض';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 بدء فحص استخدام إعدادات الموقع في العروض...');
        $this->newLine();

        // فحص الملفات الرئيسية
        $this->checkMainLayoutFile();
        $this->checkHomePageFile();
        $this->checkFooterUsage();
        $this->checkHeaderUsage();
        $this->checkSEOUsage();
        
        $this->newLine();
        $this->info('✅ انتهى فحص العروض');
    }

    private function checkMainLayoutFile()
    {
        $this->info('📄 فحص ملف التخطيط الرئيسي (frontend.blade.php)...');
        
        $layoutPath = resource_path('views/layouts/frontend.blade.php');
        
        if (!File::exists($layoutPath)) {
            $this->error('❌ ملف التخطيط الرئيسي غير موجود!');
            return;
        }
        
        $content = File::get($layoutPath);
        
        // فحص استخدام المتغيرات المهمة
        $checks = [
            '$settings->site_name' => 'اسم الموقع',
            '$settings->meta_description' => 'وصف الميتا',
            '$settings->meta_keywords' => 'الكلمات المفتاحية',
            'ImageHelper::getSiteLogo' => 'الشعار',
            'ImageHelper::getSiteFavicon' => 'الأيقونة',
            '$settings->contact_phone' => 'رقم الهاتف',
            '$settings->contact_email' => 'البريد الإلكتروني',
            '$settings->facebook_url' => 'رابط فيسبوك',
            '$settings->instagram_url' => 'رابط انستغرام',
            '$settings->twitter_url' => 'رابط تويتر',
            '$settings->whatsapp_number' => 'رقم واتساب',
            '$settings->footer_text' => 'نص التذييل',
            '$settings->google_analytics_id' => 'Google Analytics',
            '$settings->facebook_pixel_id' => 'Facebook Pixel',
            '$settings->custom_header_scripts' => 'نصوص الرأس المخصصة',
            '$settings->enable_multilingual' => 'تعدد اللغات'
        ];
        
        foreach ($checks as $variable => $description) {
            if (strpos($content, $variable) !== false) {
                $this->info("✅ {$description}: مستخدم في التخطيط");
            } else {
                $this->warn("⚠️  {$description}: غير مستخدم في التخطيط");
            }
        }
        
        $this->newLine();
    }

    private function checkHomePageFile()
    {
        $this->info('🏠 فحص ملف الصفحة الرئيسية...');
        
        // البحث عن ملف الصفحة الرئيسية
        $possiblePaths = [
            resource_path('views/home.blade.php'),
            resource_path('views/frontend/home.blade.php'),
            resource_path('views/pages/home.blade.php'),
            resource_path('views/welcome.blade.php')
        ];
        
        $homeFile = null;
        foreach ($possiblePaths as $path) {
            if (File::exists($path)) {
                $homeFile = $path;
                break;
            }
        }
        
        if (!$homeFile) {
            $this->warn('⚠️  لم يتم العثور على ملف الصفحة الرئيسية');
            return;
        }
        
        $this->line("📁 تم العثور على الصفحة الرئيسية: " . basename($homeFile));
        
        $content = File::get($homeFile);
        
        // فحص استخدام إعدادات الصفحة الرئيسية
        $homeChecks = [
            '$settings->show_featured_products' => 'عرض المنتجات المميزة',
            '$settings->show_new_arrivals' => 'عرض المنتجات الجديدة',
            '$settings->show_categories' => 'عرض الفئات',
            '$settings->show_gold_prices' => 'عرض أسعار الذهب',
            '$settings->show_features' => 'عرض ميزات الموقع',
            '$settings->show_testimonials' => 'عرض آراء العملاء',
            '$settings->show_newsletter' => 'عرض النشرة البريدية',
            '$settings->gold_price_24k' => 'سعر الذهب عيار 24',
            '$settings->gold_price_21k' => 'سعر الذهب عيار 21',
            '$settings->gold_price_18k' => 'سعر الذهب عيار 18'
        ];
        
        foreach ($homeChecks as $variable => $description) {
            if (strpos($content, $variable) !== false) {
                $this->info("✅ {$description}: مستخدم في الصفحة الرئيسية");
            } else {
                $this->warn("⚠️  {$description}: غير مستخدم في الصفحة الرئيسية");
            }
        }
        
        $this->newLine();
    }

    private function checkFooterUsage()
    {
        $this->info('🦶 فحص استخدام إعدادات التذييل...');
        
        $layoutPath = resource_path('views/layouts/frontend.blade.php');
        
        if (!File::exists($layoutPath)) {
            $this->error('❌ ملف التخطيط غير موجود!');
            return;
        }
        
        $content = File::get($layoutPath);
        
        // البحث عن قسم التذييل
        if (strpos($content, '<footer') !== false) {
            $this->info('✅ تم العثور على قسم التذييل');
            
            $footerChecks = [
                '$settings->footer_text' => 'نص التذييل',
                '$settings->site_name' => 'اسم الموقع في التذييل',
                '$settings->facebook_url' => 'رابط فيسبوك في التذييل',
                '$settings->instagram_url' => 'رابط انستغرام في التذييل',
                '$settings->twitter_url' => 'رابط تويتر في التذييل',
                '$settings->contact_phone' => 'رقم الهاتف في التذييل',
                '$settings->contact_email' => 'البريد الإلكتروني في التذييل',
                '$settings->address' => 'العنوان في التذييل'
            ];
            
            foreach ($footerChecks as $variable => $description) {
                if (strpos($content, $variable) !== false) {
                    $this->info("✅ {$description}: مستخدم");
                } else {
                    $this->warn("⚠️  {$description}: غير مستخدم");
                }
            }
        } else {
            $this->warn('⚠️  لم يتم العثور على قسم التذييل');
        }
        
        $this->newLine();
    }

    private function checkHeaderUsage()
    {
        $this->info('📋 فحص استخدام إعدادات الرأس...');
        
        $layoutPath = resource_path('views/layouts/frontend.blade.php');
        
        if (!File::exists($layoutPath)) {
            $this->error('❌ ملف التخطيط غير موجود!');
            return;
        }
        
        $content = File::get($layoutPath);
        
        // البحث عن قسم الرأس
        if (strpos($content, '<header') !== false) {
            $this->info('✅ تم العثور على قسم الرأس');
            
            $headerChecks = [
                'ImageHelper::getSiteLogo' => 'الشعار في الرأس',
                '$settings->site_name' => 'اسم الموقع في الرأس',
                '$settings->site_description' => 'وصف الموقع في الرأس',
                '$settings->contact_phone' => 'رقم الهاتف في الرأس',
                '$settings->contact_email' => 'البريد الإلكتروني في الرأس'
            ];
            
            foreach ($headerChecks as $variable => $description) {
                if (strpos($content, $variable) !== false) {
                    $this->info("✅ {$description}: مستخدم");
                } else {
                    $this->warn("⚠️  {$description}: غير مستخدم");
                }
            }
        } else {
            $this->warn('⚠️  لم يتم العثور على قسم الرأس');
        }
        
        $this->newLine();
    }

    private function checkSEOUsage()
    {
        $this->info('🔍 فحص استخدام إعدادات SEO...');
        
        $layoutPath = resource_path('views/layouts/frontend.blade.php');
        
        if (!File::exists($layoutPath)) {
            $this->error('❌ ملف التخطيط غير موجود!');
            return;
        }
        
        $content = File::get($layoutPath);
        
        // البحث عن قسم head
        if (strpos($content, '<head>') !== false) {
            $this->info('✅ تم العثور على قسم head');
            
            $seoChecks = [
                'meta name="description"' => 'وصف الميتا',
                'meta name="keywords"' => 'الكلمات المفتاحية',
                '$settings->meta_description' => 'استخدام وصف الميتا من الإعدادات',
                '$settings->meta_keywords' => 'استخدام الكلمات المفتاحية من الإعدادات',
                'ImageHelper::getSiteFavicon' => 'الأيقونة',
                '$settings->google_analytics_id' => 'Google Analytics',
                '$settings->facebook_pixel_id' => 'Facebook Pixel'
            ];
            
            foreach ($seoChecks as $variable => $description) {
                if (strpos($content, $variable) !== false) {
                    $this->info("✅ {$description}: مستخدم");
                } else {
                    $this->warn("⚠️  {$description}: غير مستخدم");
                }
            }
        } else {
            $this->warn('⚠️  لم يتم العثور على قسم head');
        }
        
        $this->newLine();
    }
}
