<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج الشركات
 *
 * @property int $id
 * @property string $name اسم الشركة
 * @property string|null $logo مسار لوجو الشركة
 * @property float $manufacturing_cost_per_gram مصنعية الجرام
 * @property float $refund_value قيمة الاستردادة
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Company extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'logo',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * علاقة الشركة بمنتجاتها
     *
     * @return HasMany
     */
    public function products(): HasMany
    {
        return $this->hasMany(CompanyProduct::class);
    }

    /**
     * علاقة الشركة بمنتجاتها (اسم بديل للتوافق)
     *
     * @return HasMany
     */
    public function companyProducts(): HasMany
    {
        return $this->hasMany(CompanyProduct::class);
    }

    /**
     * الحصول على عدد منتجات الشركة
     *
     * @return int
     */
    public function getProductsCountAttribute(): int
    {
        return $this->products()->count();
    }

    /**
     * الحصول على مسار اللوجو الكامل
     *
     * @return string|null
     */
    public function getLogoUrlAttribute(): ?string
    {
        return $this->logo ? asset('storage/' . $this->logo) : null;
    }
}
