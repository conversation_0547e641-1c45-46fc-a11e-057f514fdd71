<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProductImage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'product_id',
        'image_path',
        'image_path_sm',
        'image_path_md',
        'image_path_lg',
        'is_primary',
        'sort_order',
        'alt_text_ar',
        'alt_text_en',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
    ];

    /**
     * Get the product that owns the image.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the alt text based on current locale.
     */
    public function getAltTextAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->alt_text_ar : $this->alt_text_en;
    }
}
