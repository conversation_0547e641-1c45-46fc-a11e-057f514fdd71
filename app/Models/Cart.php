<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Cart extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'subtotal',
        'tax',
        'shipping',
        'total',
        'coupon_code',
        'discount',
    ];

    /**
     * Get the user that owns the cart.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the items for the cart.
     */
    public function items(): HasMany
    {
        return $this->hasMany(CartItem::class);
    }

    /**
     * Calculate the cart totals.
     */
    public function calculateTotals(): void
    {
        $subtotal = $this->items->sum('total');
        $tax = $subtotal * 0.14; // 14% tax rate for Egypt
        $shipping = $subtotal > 1000 ? 0 : 50; // Free shipping for orders over 1000 EGP
        $discount = 0;

        if ($this->coupon_code) {
            // Apply coupon logic here
            // For now, we'll just use a simple 10% discount
            $discount = $subtotal * 0.1;
        }

        $total = $subtotal + $tax + $shipping - $discount;

        $this->update([
            'subtotal' => $subtotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'discount' => $discount,
            'total' => $total,
        ]);
    }
}
