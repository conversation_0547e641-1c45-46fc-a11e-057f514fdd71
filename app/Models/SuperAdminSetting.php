<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SuperAdminSetting extends Model
{
    use HasFactory;

    /**
     * أحداث النموذج.
     *
     * @var array
     */
    protected $dispatchesEvents = [
        'saved' => \App\Events\SuperAdminSettingUpdated::class,
    ];

    protected $fillable = [
        // ميزات الموقع
        'show_ratings',
        'show_wishlist',
        'enable_guest_checkout',
        'enable_local_pickup',
        'enable_multilingual',

        // الضرائب والأسعار
        'tax_rate',
        'prices_include_tax',
        'min_order_amount',
        'order_prefix',

        // إعدادات التسجيل
        'enable_registration',
        'registration_disabled_message',

        // إعدادات العملات
        'default_currency',
        'supported_currencies',
        'currency_api_key',

        // حالة الموقع والصيانة
        'maintenance_mode',
        'maintenance_message',
        'display_only_mode',

        // إعدادات اللغة
        'default_language',

        // إعدادات الدفع
        'enable_credit_card',
        'enable_paypal',
        'enable_bank_transfer',
        'enable_cash_on_delivery',
        'enable_fawry',

        // إعدادات Stripe
        'stripe_key',
        'stripe_secret',
        'stripe_sandbox_mode',

        // إعدادات PayPal
        'paypal_client_id',
        'paypal_secret',
        'paypal_sandbox_mode',

        // إعدادات الشحن المتقدمة
        'local_pickup_discount',
        'shipping_policy',

        // إعدادات الفواتير
        'enable_invoices',
        'invoice_prefix',
        'company_name_invoice',
        'company_address_invoice',
        'company_tax_id',

        // تكامل وسائل التواصل الاجتماعي
        'enable_social_login',
        'enable_facebook_login',
        'enable_google_login',
        'enable_twitter_login',

        // مفاتيح API لتسجيل الدخول
        'facebook_app_id',
        'facebook_app_secret',
        'google_client_id',
        'google_client_secret',
        'twitter_client_id',
        'twitter_client_secret',

        // مشاركة المنتجات
        'enable_social_sharing',
        'share_on_facebook',
        'share_on_twitter',
        'share_on_whatsapp',
        'share_on_pinterest',
        'share_on_linkedin',

        // Feeds وسائل التواصل
        'show_instagram_feed',
        'instagram_token',
        'instagram_count',
        'show_facebook_feed',
        'facebook_page_id',
        'facebook_count',

        // ملفات تعريف الارتباط والخصوصية
        'show_cookie_banner',
        'cookie_banner_text',
        'cookie_banner_button_text',
        'enable_gdpr_compliance',
        'gdpr_compliance_text',
        'require_marketing_consent',
        'marketing_consent_text',
    ];

    protected $casts = [
        // ميزات الموقع
        'show_ratings' => 'boolean',
        'show_wishlist' => 'boolean',
        'enable_guest_checkout' => 'boolean',
        'enable_local_pickup' => 'boolean',
        'enable_multilingual' => 'boolean',

        // الضرائب والأسعار
        'tax_rate' => 'float',
        'prices_include_tax' => 'boolean',
        'min_order_amount' => 'float',

        // إعدادات التسجيل
        'enable_registration' => 'boolean',

        // إعدادات العملات
        'supported_currencies' => 'json',

        // حالة الموقع والصيانة
        'maintenance_mode' => 'boolean',
        'display_only_mode' => 'boolean',

        // إعدادات الدفع
        'enable_credit_card' => 'boolean',
        'enable_paypal' => 'boolean',
        'enable_bank_transfer' => 'boolean',
        'enable_cash_on_delivery' => 'boolean',
        'enable_fawry' => 'boolean',
        'stripe_sandbox_mode' => 'boolean',
        'paypal_sandbox_mode' => 'boolean',

        // إعدادات الشحن والفواتير
        'local_pickup_discount' => 'float',
        'enable_invoices' => 'boolean',

        // تكامل وسائل التواصل الاجتماعي
        'enable_social_login' => 'boolean',
        'enable_facebook_login' => 'boolean',
        'enable_google_login' => 'boolean',
        'enable_twitter_login' => 'boolean',

        // مشاركة المنتجات
        'enable_social_sharing' => 'boolean',
        'share_on_facebook' => 'boolean',
        'share_on_twitter' => 'boolean',
        'share_on_whatsapp' => 'boolean',
        'share_on_pinterest' => 'boolean',
        'share_on_linkedin' => 'boolean',

        // Feeds وسائل التواصل
        'show_instagram_feed' => 'boolean',
        'instagram_count' => 'integer',
        'show_facebook_feed' => 'boolean',
        'facebook_count' => 'integer',

        // ملفات تعريف الارتباط والخصوصية
        'show_cookie_banner' => 'boolean',
        'enable_gdpr_compliance' => 'boolean',
        'require_marketing_consent' => 'boolean',
    ];

    /**
     * الحصول على الإعدادات الافتراضية
     *
     * @return array
     */
    public static function getDefaults(): array
    {
        return [
            // ميزات الموقع
            'show_ratings' => true,
            'show_wishlist' => true,
            'enable_guest_checkout' => true,
            'enable_local_pickup' => false,
            'enable_multilingual' => true,

            // الضرائب والأسعار
            'tax_rate' => 14.00,
            'prices_include_tax' => true,
            'min_order_amount' => 0.00,
            'order_prefix' => 'MGJ-',

            // إعدادات التسجيل
            'enable_registration' => true,
            'registration_disabled_message' => 'التسجيل غير متاح حالياً، يرجى المحاولة لاحقاً.',

            // إعدادات العملات
            'default_currency' => 'EGP',
            'supported_currencies' => ['EGP', 'USD', 'EUR'],
            'currency_api_key' => null,

            // حالة الموقع والصيانة
            'maintenance_mode' => false,
            'maintenance_message' => 'الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.',
            'display_only_mode' => false,

            // إعدادات اللغة
            'default_language' => 'ar',

            // إعدادات الدفع
            'enable_credit_card' => true,
            'enable_paypal' => true,
            'enable_bank_transfer' => false,
            'enable_cash_on_delivery' => false,
            'enable_fawry' => false,

            // إعدادات Stripe
            'stripe_key' => null,
            'stripe_secret' => null,
            'stripe_sandbox_mode' => true,

            // إعدادات PayPal
            'paypal_client_id' => null,
            'paypal_secret' => null,
            'paypal_sandbox_mode' => true,

            // إعدادات الشحن المتقدمة
            'local_pickup_discount' => 0.00,
            'shipping_policy' => null,

            // إعدادات الفواتير
            'enable_invoices' => true,
            'invoice_prefix' => 'INV-',
            'company_name_invoice' => null,
            'company_address_invoice' => null,
            'company_tax_id' => null,

            // تكامل وسائل التواصل الاجتماعي
            'enable_social_login' => false,
            'enable_facebook_login' => false,
            'enable_google_login' => false,
            'enable_twitter_login' => false,

            // مفاتيح API لتسجيل الدخول
            'facebook_app_id' => null,
            'facebook_app_secret' => null,
            'google_client_id' => null,
            'google_client_secret' => null,
            'twitter_client_id' => null,
            'twitter_client_secret' => null,

            // مشاركة المنتجات
            'enable_social_sharing' => true,
            'share_on_facebook' => true,
            'share_on_twitter' => true,
            'share_on_whatsapp' => true,
            'share_on_pinterest' => false,
            'share_on_linkedin' => false,

            // Feeds وسائل التواصل
            'show_instagram_feed' => false,
            'instagram_token' => null,
            'instagram_count' => 6,
            'show_facebook_feed' => false,
            'facebook_page_id' => null,
            'facebook_count' => 5,

            // ملفات تعريف الارتباط والخصوصية
            'show_cookie_banner' => true,
            'cookie_banner_text' => 'هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.',
            'cookie_banner_button_text' => 'أوافق',
            'enable_gdpr_compliance' => false,
            'gdpr_compliance_text' => 'نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية وفقاً للائحة العامة لحماية البيانات (GDPR).',
            'require_marketing_consent' => false,
            'marketing_consent_text' => 'أوافق على تلقي رسائل تسويقية من مكة جولد عبر البريد الإلكتروني والرسائل القصيرة.',
        ];
    }

    /**
     * الحصول على أول سجل أو إنشاء واحد جديد بالقيم الافتراضية
     *
     * @return SuperAdminSetting
     */
    public static function firstOrCreateWithDefaults(): SuperAdminSetting
    {
        return static::firstOrCreate([], static::getDefaults());
    }

    /**
     * مسح التخزين المؤقت عند الحفظ
     */
    protected static function booted()
    {
        static::saved(function () {
            Cache::forget('super_admin_settings');
        });

        static::deleted(function () {
            Cache::forget('super_admin_settings');
        });
    }
}
