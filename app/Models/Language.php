<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\File;

class Language extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'native_name',
        'is_rtl',
        'is_default',
        'is_active',
    ];

    protected $casts = [
        'is_rtl' => 'boolean',
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get all available languages
     *
     * @return array
     */
    public static function getAvailableLanguages(): array
    {
        return ['ar', 'en'];
    }

    /**
     * Get all translations
     *
     * @return array
     */
    public static function getTranslations(): array
    {
        $translations = [];
        $languages = self::getAvailableLanguages();

        foreach ($languages as $lang) {
            $jsonPath = resource_path("lang/{$lang}.json");
            if (File::exists($jsonPath)) {
                $jsonTranslations = json_decode(File::get($jsonPath), true);
                foreach ($jsonTranslations as $key => $value) {
                    if (!isset($translations[$key])) {
                        $translations[$key] = [
                            'key' => $key,
                        ];
                    }
                    $translations[$key][$lang] = $value;
                }
            }
        }

        return array_values($translations);
    }

    /**
     * Update a translation
     *
     * @param string $key
     * @param array $data
     * @return void
     */
    public static function updateTranslation(string $key, array $data): void
    {
        $languages = self::getAvailableLanguages();

        foreach ($languages as $lang) {
            if (isset($data[$lang])) {
                $jsonPath = resource_path("lang/{$lang}.json");
                if (File::exists($jsonPath)) {
                    $jsonTranslations = json_decode(File::get($jsonPath), true);
                    $jsonTranslations[$key] = $data[$lang];
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
            }
        }
    }

    /**
     * Add a new translation
     *
     * @param array $data
     * @return void
     */
    public static function addTranslation(array $data): void
    {
        $languages = self::getAvailableLanguages();

        foreach ($languages as $lang) {
            if (isset($data[$lang])) {
                $jsonPath = resource_path("lang/{$lang}.json");
                if (File::exists($jsonPath)) {
                    $jsonTranslations = json_decode(File::get($jsonPath), true);
                    $jsonTranslations[$data['key']] = $data[$lang];
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
            }
        }
    }

    /**
     * Delete a translation
     *
     * @param string $key
     * @return void
     */
    public static function deleteTranslation(string $key): void
    {
        $languages = self::getAvailableLanguages();

        foreach ($languages as $lang) {
            $jsonPath = resource_path("lang/{$lang}.json");
            if (File::exists($jsonPath)) {
                $jsonTranslations = json_decode(File::get($jsonPath), true);
                if (isset($jsonTranslations[$key])) {
                    unset($jsonTranslations[$key]);
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
            }
        }
    }
}
