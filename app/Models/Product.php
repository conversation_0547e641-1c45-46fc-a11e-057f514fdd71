<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'slug',
        'category_id',
        'weight',
        'metal_purity',
        'material_type',
        'metal_type_id',
        'metal_purity_id',
        'price',
        'old_price',
        'discount_percentage',
        'stock_quantity',
        'is_featured',
        'is_active',
        'show_price',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'weight' => 'decimal:3',
        'price' => 'decimal:2',
        'old_price' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'show_price' => 'boolean',
    ];

    /**
     * Get the category that owns the product.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the metal type that owns the product.
     */
    public function metal()
    {
        return $this->belongsTo(MetalType::class, 'metal_type_id');
    }

    /**
     * Get the metal purity that owns the product.
     */
    public function metalPurity()
    {
        return $this->belongsTo(MetalPurity::class, 'metal_purity_id');
    }

    /**
     * Get the images for the product.
     */
    public function images()
    {
        return $this->hasMany(ProductImage::class);
    }

    /**
     * Get the primary image for the product.
     */
    public function primaryImage()
    {
        return $this->hasOne(ProductImage::class)->where('is_primary', true)->orderBy('sort_order');
    }

    /**
     * Get the reviews for the product.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the order items for the product.
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the product name based on current locale.
     */
    public function getNameAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get the product description based on current locale.
     */
    public function getDescriptionAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->description_ar : $this->description_en;
    }

    /**
     * Get the average rating for the product.
     */
    public function getAverageRatingAttribute()
    {
        return $this->reviews()->avg('rating') ?: 0;
    }

    /**
     * Get the rating for the product.
     */
    public function getRatingAttribute()
    {
        return $this->getAverageRatingAttribute();
    }

    /**
     * Get the reviews count for the product.
     */
    public function getReviewsCountAttribute()
    {
        return $this->reviews()->count();
    }

    /**
     * Get the primary image URL for backward compatibility.
     */
    public function getImageAttribute()
    {
        $primaryImage = $this->primaryImage;
        return $primaryImage ? $primaryImage->image_path : null;
    }

    /**
     * Get the primary image URL with full path.
     */
    public function getPrimaryImageUrlAttribute()
    {
        $primaryImage = $this->primaryImage;
        if (!$primaryImage || !$primaryImage->image_path) {
            return null;
        }

        // إذا كان المسار يبدأ بـ http، فهو URL كامل
        if (str_starts_with($primaryImage->image_path, 'http')) {
            return $primaryImage->image_path;
        }

        // إذا كان يبدأ بـ /storage/، فهو مسار كامل
        if (str_starts_with($primaryImage->image_path, '/storage/')) {
            return url($primaryImage->image_path);
        }

        // إنشاء URL كامل
        return url('/storage/' . $primaryImage->image_path);
    }

    /**
     * Get all image URLs for the product.
     */
    public function getImageUrlsAttribute()
    {
        return $this->images->map(function ($image) {
            if (!$image->image_path) {
                return null;
            }

            if (str_starts_with($image->image_path, 'http')) {
                return $image->image_path;
            }

            if (str_starts_with($image->image_path, '/storage/')) {
                return url($image->image_path);
            }

            return url('/storage/' . $image->image_path);
        })->filter()->values();
    }

    /**
     * Check if product has images.
     */
    public function hasImages()
    {
        return $this->images()->count() > 0;
    }

    /**
     * Check if product has primary image.
     */
    public function hasPrimaryImage()
    {
        return $this->primaryImage !== null;
    }

    /**
     * إنشاء slug فريد للمنتج
     */
    public static function generateUniqueSlug(string $name, ?int $excludeId = null): string
    {
        $baseSlug = \Illuminate\Support\Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        // التحقق من وجود slug مشابه
        while (self::slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * التحقق من وجود slug في قاعدة البيانات
     */
    public static function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = self::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * التحقق من وجود اسم عربي في قاعدة البيانات
     */
    public static function nameArExists(string $name, ?int $excludeId = null): bool
    {
        $query = self::where('name_ar', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * التحقق من وجود اسم إنجليزي في قاعدة البيانات
     */
    public static function nameEnExists(string $name, ?int $excludeId = null): bool
    {
        $query = self::where('name_en', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * التحقق من تفرد الاسم العربي
     */
    public function isNameArUnique(string $name): bool
    {
        return !self::nameArExists($name, $this->id);
    }

    /**
     * التحقق من تفرد الاسم الإنجليزي
     */
    public function isNameEnUnique(string $name): bool
    {
        return !self::nameEnExists($name, $this->id);
    }
}
