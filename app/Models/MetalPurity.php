<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MetalPurity extends Model
{
    use HasFactory;

    protected $fillable = [
        'metal_type_id',
        'name',
        'name_ar',
        'purity_percentage',
        'weight_grams',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'purity_percentage' => 'decimal:2',
        'weight_grams' => 'decimal:3',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * العلاقة مع نوع المعدن
     */
    public function metalType(): BelongsTo
    {
        return $this->belongsTo(MetalType::class);
    }

    /**
     * العلاقة مع أسعار المعادن
     */
    public function metalPrices(): HasMany
    {
        return $this->hasMany(MetalPrice::class, 'purity', 'name');
    }

    /**
     * Scope للعيارات النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للترتيب حسب sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name_ar');
    }

    /**
     * Scope للعيارات حسب نوع المعدن
     */
    public function scopeForMetalType($query, $metalTypeId)
    {
        return $query->where('metal_type_id', $metalTypeId);
    }
}
