<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class MetalType extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * العلاقة مع العيارات
     */
    public function purities(): HasMany
    {
        return $this->hasMany(MetalPurity::class)->orderBy('sort_order');
    }

    /**
     * العلاقة مع أسعار المعادن
     */
    public function metalPrices(): HasMany
    {
        return $this->hasMany(MetalPrice::class, 'metal_type', 'name');
    }

    /**
     * Scope للمعادن النشطة فقط
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope للترتيب حسب sort_order
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name_ar');
    }
}
