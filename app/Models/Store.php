<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Store extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name_ar',
        'name_en',
        'address_ar',
        'address_en',
        'city_ar',
        'city_en',
        'country_ar',
        'country_en',
        'phone',
        'email',
        'working_hours_ar',
        'working_hours_en',
        'latitude',
        'longitude',
        'image',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'is_active' => 'boolean',
    ];

    /**
     * Get the appointments for the store.
     */
    public function appointments()
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the orders for the store.
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the store name based on current locale.
     */
    public function getNameAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->name_ar : $this->name_en;
    }

    /**
     * Get the store address based on current locale.
     */
    public function getAddressAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->address_ar : $this->address_en;
    }

    /**
     * Get the store city based on current locale.
     */
    public function getCityAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->city_ar : $this->city_en;
    }

    /**
     * Get the store country based on current locale.
     */
    public function getCountryAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->country_ar : $this->country_en;
    }

    /**
     * Get the store working hours based on current locale.
     */
    public function getWorkingHoursAttribute()
    {
        $locale = app()->getLocale();
        return $locale == 'ar' ? $this->working_hours_ar : $this->working_hours_en;
    }

    /**
     * Check if the store has any orders.
     *
     * @return bool
     */
    public function hasOrders()
    {
        return $this->orders()->count() > 0;
    }

    /**
     * Get the count of orders for this store.
     *
     * @return int
     */
    public function getOrdersCount()
    {
        return $this->orders()->count();
    }

    /**
     * Safe delete method that handles related orders.
     *
     * @param int|null $transferToStoreId Store ID to transfer orders to, or null to set orders' store_id to null
     * @return bool
     */
    public function safeDelete($transferToStoreId = null)
    {
        // Begin transaction
        \DB::beginTransaction();

        try {
            // If there are orders and a transfer store is specified
            if ($this->hasOrders() && $transferToStoreId) {
                // Make sure the transfer store exists
                $transferStore = Store::find($transferToStoreId);
                if (!$transferStore) {
                    throw new \Exception('المتجر المحدد للنقل غير موجود');
                }

                // Transfer all orders to the specified store
                $this->orders()->update(['store_id' => $transferToStoreId]);
            }

            // Delete the store
            $result = $this->delete();

            // Commit transaction
            \DB::commit();

            return $result;
        } catch (\Exception $e) {
            // Rollback transaction on error
            \DB::rollBack();
            throw $e;
        }
    }

    /**
     * Set the latitude attribute with validation
     */
    public function setLatitudeAttribute($value)
    {
        if (is_null($value) || $value === '') {
            $this->attributes['latitude'] = null;
        } else {
            // Ensure the value is within valid range
            $latitude = (float) $value;
            if ($latitude >= -90 && $latitude <= 90) {
                $this->attributes['latitude'] = $latitude;
            } else {
                $this->attributes['latitude'] = null;
            }
        }
    }

    /**
     * Set the longitude attribute with validation
     */
    public function setLongitudeAttribute($value)
    {
        if (is_null($value) || $value === '') {
            $this->attributes['longitude'] = null;
        } else {
            // Ensure the value is within valid range
            $longitude = (float) $value;
            if ($longitude >= -180 && $longitude <= 180) {
                $this->attributes['longitude'] = $longitude;
            } else {
                $this->attributes['longitude'] = null;
            }
        }
    }

    /**
     * Check if store has valid coordinates
     */
    public function hasValidCoordinates()
    {
        return !is_null($this->latitude) && !is_null($this->longitude);
    }

    /**
     * Get Google Maps URL for the store location
     */
    public function getGoogleMapsUrlAttribute()
    {
        if ($this->hasValidCoordinates()) {
            return "https://www.google.com/maps?q={$this->latitude},{$this->longitude}";
        }
        return null;
    }
}
