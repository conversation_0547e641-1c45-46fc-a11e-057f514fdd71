<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SearchLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'query',
        'filters',
        'results_count',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'filters' => 'array',
    ];

    /**
     * الحصول على البحثات الشائعة
     */
    public static function getPopularSearches($limit = 10)
    {
        return static::selectRaw('query, COUNT(*) as search_count')
            ->where('created_at', '>=', now()->subDays(30))
            ->where('results_count', '>', 0)
            ->groupBy('query')
            ->orderByDesc('search_count')
            ->limit($limit)
            ->pluck('query');
    }

    /**
     * تسجيل بحث جديد
     */
    public static function logSearch($query, $filters = [], $resultsCount = 0)
    {
        return static::create([
            'query' => $query,
            'filters' => $filters,
            'results_count' => $resultsCount,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }
}
