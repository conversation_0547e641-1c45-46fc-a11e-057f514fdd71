<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * نموذج أنواع المنتجات
 *
 * @property int $id
 * @property string $name اسم نوع المنتج
 * @property string $type نوع المنتج (سبيكة أو عملة)
 * @property float $weight الوزن بالجرام
 * @property string $metal_purity عيار المعدن
 * @property float $manufacturing_cost مصنعية المنتج
 * @property float $refund_value قيمة الاسترداد
 * @property string|null $description وصف المنتج
 * @property bool $is_active حالة النشاط
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class ProductType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'weight',
        'metal_purity',
        'manufacturing_cost',
        'refund_value',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'weight' => 'decimal:3',
        'manufacturing_cost' => 'decimal:2',
        'refund_value' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * علاقة نوع المنتج بمنتجات الشركات
     *
     * @return HasMany
     */
    public function companyProducts(): HasMany
    {
        return $this->hasMany(CompanyProduct::class);
    }

    /**
     * الحصول على نوع المنتج مع الأيقونة
     *
     * @return string
     */
    public function getTypeWithIconAttribute(): string
    {
        $icons = [
            'سبيكة' => '🟨',
            'عملة' => '🪙'
        ];

        return ($icons[$this->type] ?? '') . ' ' . $this->type;
    }

    /**
     * الحصول على العيار مع التنسيق
     *
     * @return string
     */
    public function getFormattedPurityAttribute(): string
    {
        return 'عيار ' . $this->metal_purity;
    }

    /**
     * الحصول على الوزن مع الوحدة
     *
     * @return string
     */
    public function getFormattedWeightAttribute(): string
    {
        return $this->weight . ' جرام';
    }

    /**
     * الحصول على الاسم الكامل للمنتج
     *
     * @return string
     */
    public function getFullNameAttribute(): string
    {
        return $this->name . ' - ' . $this->type_with_icon . ' - ' . $this->formatted_purity;
    }

    /**
     * فلترة المنتجات النشطة
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * فلترة حسب نوع المنتج
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * فلترة حسب العيار
     */
    public function scopeOfPurity($query, string $purity)
    {
        return $query->where('metal_purity', $purity);
    }
}
