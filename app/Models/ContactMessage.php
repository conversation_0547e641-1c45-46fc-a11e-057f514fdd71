<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactMessage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'status',
        'reply_message',
        'replied_at',
        'replied_by',
        'ip_address',
        'user_agent',
        'read_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'replied_at' => 'datetime',
        'read_at' => 'datetime',
    ];

    /**
     * Get the user who replied to this message.
     */
    public function repliedBy()
    {
        return $this->belongsTo(User::class, 'replied_by');
    }

    /**
     * Scope a query to only include new messages.
     */
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    /**
     * Scope a query to only include read messages.
     */
    public function scopeRead($query)
    {
        return $query->where('status', 'read');
    }

    /**
     * Scope a query to only include replied messages.
     */
    public function scopeReplied($query)
    {
        return $query->where('status', 'replied');
    }

    /**
     * Mark message as read.
     */
    public function markAsRead()
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    /**
     * Mark message as replied.
     */
    public function markAsReplied($replyMessage, $userId)
    {
        $this->update([
            'status' => 'replied',
            'reply_message' => $replyMessage,
            'replied_at' => now(),
            'replied_by' => $userId,
        ]);
    }

    /**
     * Get status label in Arabic.
     */
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'new' => 'جديدة',
            'read' => 'مقروءة',
            'replied' => 'تم الرد',
            default => 'غير محدد',
        };
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'new' => 'warning',
            'read' => 'info',
            'replied' => 'success',
            default => 'gray',
        };
    }
}
