<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class Job extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'career_jobs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'description',
        'requirements',
        'responsibilities',
        'location',
        'type',
        'department',
        'salary_min',
        'salary_max',
        'is_active',
        'is_featured',
        'expires_at',
        'translations',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'salary_min' => 'decimal:2',
        'salary_max' => 'decimal:2',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'expires_at' => 'datetime',
        'translations' => 'array',
    ];

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the applications for the job.
     */
    public function applications()
    {
        return $this->hasMany(JobApplication::class);
    }

    /**
     * Get the translated title.
     *
     * @return string
     */
    public function getTranslatedTitleAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['title'])) {
            return $this->translations[$locale]['title'];
        }
        return $this->title;
    }

    /**
     * Get the translated description.
     *
     * @return string
     */
    public function getTranslatedDescriptionAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['description'])) {
            return $this->translations[$locale]['description'];
        }
        return $this->description;
    }

    /**
     * Get the translated requirements.
     *
     * @return string
     */
    public function getTranslatedRequirementsAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['requirements'])) {
            return $this->translations[$locale]['requirements'];
        }
        return $this->requirements;
    }

    /**
     * Get the translated responsibilities.
     *
     * @return string
     */
    public function getTranslatedResponsibilitiesAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['responsibilities'])) {
            return $this->translations[$locale]['responsibilities'];
        }
        return $this->responsibilities;
    }

    /**
     * Get the translated location.
     *
     * @return string
     */
    public function getTranslatedLocationAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['location'])) {
            return $this->translations[$locale]['location'];
        }
        return $this->location;
    }

    /**
     * Get the translated type.
     *
     * @return string
     */
    public function getTranslatedTypeAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['type'])) {
            return $this->translations[$locale]['type'];
        }
        return $this->type;
    }

    /**
     * Get the translated department.
     *
     * @return string
     */
    public function getTranslatedDepartmentAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['department'])) {
            return $this->translations[$locale]['department'];
        }
        return $this->department;
    }

    /**
     * Scope a query to only include active jobs.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include featured jobs.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope a query to only include jobs that have not expired.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNotExpired($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('expires_at')
                  ->orWhere('expires_at', '>=', now());
        });
    }

    /**
     * Scope a query to only include available jobs.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->active()->notExpired();
    }
}
