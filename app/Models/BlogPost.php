<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class BlogPost extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'featured_image',
        'author_id',
        'category_id',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'is_featured',
        'is_active',
        'views',
        'published_at',
        'translations',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'views' => 'integer',
        'published_at' => 'datetime',
        'translations' => 'array',
    ];

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get the author that owns the blog post.
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get the category that owns the blog post.
     */
    public function category()
    {
        return $this->belongsTo(BlogCategory::class);
    }



    /**
     * Get the translated title.
     *
     * @return string
     */
    public function getTranslatedTitleAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['title'])) {
            return $this->translations[$locale]['title'];
        }
        return $this->title;
    }

    /**
     * Get the translated excerpt.
     *
     * @return string
     */
    public function getTranslatedExcerptAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['excerpt'])) {
            return $this->translations[$locale]['excerpt'];
        }
        return $this->excerpt;
    }

    /**
     * Get the translated content.
     *
     * @return string
     */
    public function getTranslatedContentAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['content'])) {
            return $this->translations[$locale]['content'];
        }
        return $this->content;
    }

    /**
     * Get the translated meta title.
     *
     * @return string
     */
    public function getTranslatedMetaTitleAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['meta_title'])) {
            return $this->translations[$locale]['meta_title'];
        }
        return $this->meta_title ?? $this->title;
    }

    /**
     * Get the translated meta description.
     *
     * @return string
     */
    public function getTranslatedMetaDescriptionAttribute()
    {
        $locale = App::getLocale();
        if (isset($this->translations[$locale]['meta_description'])) {
            return $this->translations[$locale]['meta_description'];
        }
        return $this->meta_description;
    }

    /**
     * Scope a query to only include active blog posts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to only include published blog posts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('is_active', true)
                    ->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope a query to only include featured blog posts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Increment the view count.
     *
     * @return void
     */
    public function incrementViews()
    {
        $this->increment('views');
    }
}
