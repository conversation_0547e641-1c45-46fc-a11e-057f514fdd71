<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Faq extends Model
{
    use HasFactory;

    protected $fillable = [
        'question_ar',
        'question_en',
        'answer_ar',
        'answer_en',
        'category',
        'sort_order',
        'is_active',
        'is_featured',
        'views',
        'helpful_votes',
        'tags',
        'slug',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'tags' => 'array',
    ];

    /**
     * إنشاء slug تلقائياً عند الحفظ
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($faq) {
            if (empty($faq->slug)) {
                $faq->slug = Str::slug($faq->question_ar);
            }
        });

        static::updating(function ($faq) {
            if (empty($faq->slug)) {
                $faq->slug = Str::slug($faq->question_ar);
            }
        });
    }

    /**
     * الحصول على السؤال المترجم
     */
    public function getTranslatedQuestionAttribute()
    {
        return app()->getLocale() == 'ar' ?
            ($this->question_ar ?? $this->question_en) :
            ($this->question_en ?? $this->question_ar);
    }

    /**
     * الحصول على الإجابة المترجمة
     */
    public function getTranslatedAnswerAttribute()
    {
        return app()->getLocale() == 'ar' ?
            ($this->answer_ar ?? $this->answer_en) :
            ($this->answer_en ?? $this->answer_ar);
    }

    /**
     * زيادة عدد المشاهدات
     */
    public function incrementViews()
    {
        $this->increment('views');
    }

    /**
     * زيادة الأصوات المفيدة
     */
    public function incrementHelpfulVotes()
    {
        $this->increment('helpful_votes');
    }

    /**
     * الحصول على الأسئلة النشطة
     */
    public static function active()
    {
        return static::where('is_active', true);
    }

    /**
     * الحصول على الأسئلة المميزة
     */
    public static function featured()
    {
        return static::where('is_featured', true)->where('is_active', true);
    }

    /**
     * الحصول على الأسئلة حسب التصنيف
     */
    public static function byCategory($category)
    {
        return static::where('category', $category)->where('is_active', true);
    }
}
