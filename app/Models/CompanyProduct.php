<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * نموذج منتجات الشركات
 *
 * @property int $id
 * @property int $company_id معرف الشركة
 * @property int $product_type_id معرف نوع المنتج
 * @property float $manufacturing_cost_per_gram مصنعية الجرام
 * @property float $refund_value_per_gram قيمة الاسترداد للجرام
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property Company $company الشركة المالكة للمنتج
 * @property ProductType $productType نوع المنتج
 */
class CompanyProduct extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'company_id',
        'product_type_id',
        'manufacturing_cost_per_gram',
        'refund_value_per_gram',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'company_id' => 'integer',
        'product_type_id' => 'integer',
        'manufacturing_cost_per_gram' => 'decimal:2',
        'refund_value_per_gram' => 'decimal:2',
    ];

    /**
     * علاقة المنتج بالشركة المالكة له
     *
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * علاقة المنتج بنوع المنتج
     *
     * @return BelongsTo
     */
    public function productType(): BelongsTo
    {
        return $this->belongsTo(ProductType::class);
    }

    /**
     * الحصول على اسم المنتج الكامل
     *
     * @return string
     */
    public function getNameAttribute(): string
    {
        return ($this->company?->name ?? 'غير محدد') . ' - ' . ($this->productType?->name ?? 'غير محدد');
    }

    /**
     * الحصول على اسم الشركة
     *
     * @return string
     */
    public function getCompanyNameAttribute(): string
    {
        return $this->company?->name ?? 'غير محدد';
    }

    /**
     * الحصول على الوزن مع الوحدة
     *
     * @return string
     */
    public function getFormattedWeightAttribute(): string
    {
        return $this->productType?->formatted_weight ?? 'غير محدد';
    }

    /**
     * حساب التكلفة الإجمالية للمنتج (الوزن × مصنعية الجرام)
     *
     * @return float
     */
    public function getTotalCostAttribute(): float
    {
        $weight = $this->productType?->weight ?? 0;
        return $weight * $this->manufacturing_cost_per_gram;
    }

    /**
     * حساب قيمة الاسترداد الإجمالية (الوزن × قيمة الاسترداد للجرام)
     *
     * @return float
     */
    public function getTotalRefundValueAttribute(): float
    {
        $weight = $this->productType?->weight ?? 0;
        return $weight * $this->refund_value_per_gram;
    }

    /**
     * الحصول على قيمة الاسترداد (للتوافق مع الكود القديم)
     *
     * @return float
     */
    public function getRefundValueAttribute(): float
    {
        return $this->total_refund_value;
    }

    /**
     * الحصول على نوع المنتج مع الأيقونة
     *
     * @return string
     */
    public function getProductTypeWithIconAttribute(): string
    {
        return $this->productType?->type_with_icon ?? 'غير محدد';
    }

    /**
     * الحصول على العيار مع التنسيق
     *
     * @return string
     */
    public function getFormattedPurityAttribute(): string
    {
        return $this->productType?->formatted_purity ?? 'غير محدد';
    }

    /**
     * الحصول على الوصف الكامل للمنتج
     *
     * @return string
     */
    public function getFullDescriptionAttribute(): string
    {
        return $this->name . ' - ' . $this->product_type_with_icon . ' - ' . $this->formatted_purity . ' - ' . $this->formatted_weight;
    }

    /**
     * الحصول على الوصف (للتوافق مع الكود القديم)
     *
     * @return string
     */
    public function getDescriptionAttribute(): string
    {
        $companyName = is_object($this->company) ? $this->company->name : 'غير محدد';
        $productName = is_object($this->productType) ? $this->productType->name : 'غير محدد';
        return "منتج {$productName} من شركة {$companyName}";
    }

    /**
     * الحصول على نوع المنتج (النص فقط)
     *
     * @return string
     */
    public function getProductTypeNameAttribute(): string
    {
        return $this->productType?->type ?? 'غير محدد';
    }

    /**
     * الحصول على العيار
     *
     * @return string
     */
    public function getMetalPurityAttribute(): string
    {
        return $this->productType?->metal_purity ?? 'غير محدد';
    }

    /**
     * الحصول على الوزن
     *
     * @return float
     */
    public function getWeightAttribute(): float
    {
        return $this->productType?->weight ?? 0;
    }
}
