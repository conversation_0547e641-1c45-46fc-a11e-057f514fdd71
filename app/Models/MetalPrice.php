<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MetalPrice extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'metal_type',
        'purity',
        'price_per_gram',
        'purchase_price_per_gram',
        'price_per_ounce',
        'purchase_price_per_ounce',
        'price_per_piece',
        'purchase_price_per_piece',
        'currency',
        'price_date',
        'source',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price_per_gram' => 'decimal:2',
        'purchase_price_per_gram' => 'decimal:2',
        'price_per_ounce' => 'decimal:2',
        'purchase_price_per_ounce' => 'decimal:2',
        'price_per_piece' => 'decimal:2',
        'purchase_price_per_piece' => 'decimal:2',
        'price_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Scope a query to only include gold prices.
     */
    public function scopeGold($query)
    {
        return $query->where('metal_type', 'gold');
    }

    /**
     * Scope a query to only include silver prices.
     */
    public function scopeSilver($query)
    {
        return $query->where('metal_type', 'silver');
    }

    /**
     * Scope a query to only include platinum prices.
     */
    public function scopePlatinum($query)
    {
        return $query->where('metal_type', 'platinum');
    }

    /**
     * Scope a query to only include gold coin prices.
     */
    public function scopeGoldCoin($query)
    {
        return $query->where('metal_type', 'gold_coin');
    }

    /**
     * Boot method to apply auto-activation system
     */
    protected static function boot()
    {
        parent::boot();

        // تطبيق نظام التفعيل التلقائي عند إنشاء سعر جديد
        static::created(function ($metalPrice) {
            if ($metalPrice->is_active) {
                static::applyAutoActivation($metalPrice);
            }
        });

        // تطبيق نظام التفعيل التلقائي عند تحديث السعر
        static::updated(function ($metalPrice) {
            if ($metalPrice->is_active && $metalPrice->wasChanged('is_active')) {
                static::applyAutoActivation($metalPrice);
            }
        });
    }

    /**
     * تطبيق نظام التفعيل التلقائي
     */
    public static function applyAutoActivation(MetalPrice $newPrice)
    {
        // إلغاء تفعيل جميع الأسعار الأخرى لنفس المعدن والعيار
        static::where('metal_type', $newPrice->metal_type)
            ->where('purity', $newPrice->purity)
            ->where('currency', $newPrice->currency)
            ->where('id', '!=', $newPrice->id)
            ->update(['is_active' => false]);
    }

    /**
     * Scope a query to only include prices for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('price_date', $date);
    }

    /**
     * Scope a query to only include latest prices.
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }
}
