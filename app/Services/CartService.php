<?php

namespace App\Services;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CartService
{
    /**
     * Get the current cart.
     */
    public function getCart()
    {
        $userId = Auth::id();
        $sessionId = Session::getId();

        $cart = Cart::where(function ($query) use ($userId, $sessionId) {
            if ($userId) {
                $query->where('user_id', $userId);
            } else {
                $query->where('session_id', $sessionId);
            }
        })->with('items.product')->first();

        if (!$cart) {
            $cart = Cart::create([
                'user_id' => $userId,
                'session_id' => $userId ? null : $sessionId,
            ]);
        } elseif ($userId && $cart->session_id && !$cart->user_id) {
            // If user just logged in, update the cart with their user_id
            $cart->update(['user_id' => $userId, 'session_id' => null]);
        }

        return $cart;
    }

    /**
     * Add a product to the cart.
     */
    public function addToCart(Product $product, int $quantity = 1, array $options = [])
    {
        $cart = $this->getCart();
        
        // Check if the product is already in the cart
        $cartItem = $cart->items()->where('product_id', $product->id)->first();
        
        if ($cartItem) {
            // Update quantity if the product is already in the cart
            $cartItem->quantity += $quantity;
            $cartItem->save();
            $cartItem->calculateTotal();
        } else {
            // Add new item to the cart
            $cartItem = $cart->items()->create([
                'product_id' => $product->id,
                'quantity' => $quantity,
                'price' => $product->price,
                'total' => $product->price * $quantity,
                'options' => $options,
            ]);
        }
        
        // Recalculate cart totals
        $cart->calculateTotals();
        
        return $cart;
    }

    /**
     * Update cart item quantity.
     */
    public function updateCartItem(CartItem $cartItem, int $quantity)
    {
        if ($quantity <= 0) {
            $this->removeCartItem($cartItem);
            return $this->getCart();
        }
        
        $cartItem->quantity = $quantity;
        $cartItem->save();
        $cartItem->calculateTotal();
        
        // Recalculate cart totals
        $cartItem->cart->calculateTotals();
        
        return $cartItem->cart;
    }

    /**
     * Remove an item from the cart.
     */
    public function removeCartItem(CartItem $cartItem)
    {
        $cart = $cartItem->cart;
        $cartItem->delete();
        
        // Recalculate cart totals
        $cart->calculateTotals();
        
        return $cart;
    }

    /**
     * Clear the cart.
     */
    public function clearCart()
    {
        $cart = $this->getCart();
        $cart->items()->delete();
        
        // Reset cart totals
        $cart->update([
            'subtotal' => 0,
            'tax' => 0,
            'shipping' => 0,
            'discount' => 0,
            'total' => 0,
            'coupon_code' => null,
        ]);
        
        return $cart;
    }

    /**
     * Apply a coupon to the cart.
     */
    public function applyCoupon(string $couponCode)
    {
        $cart = $this->getCart();
        
        // Here you would validate the coupon code
        // For now, we'll just apply a simple 10% discount
        $cart->coupon_code = $couponCode;
        $cart->save();
        
        // Recalculate cart totals with the coupon
        $cart->calculateTotals();
        
        return $cart;
    }

    /**
     * Remove a coupon from the cart.
     */
    public function removeCoupon()
    {
        $cart = $this->getCart();
        $cart->coupon_code = null;
        $cart->save();
        
        // Recalculate cart totals without the coupon
        $cart->calculateTotals();
        
        return $cart;
    }

    /**
     * Get the cart count.
     */
    public function getCartCount()
    {
        $cart = $this->getCart();
        return $cart->items->sum('quantity');
    }
}
