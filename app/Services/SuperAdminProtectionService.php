<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class SuperAdminProtectionService
{
    /**
     * اسم دور Super Admin
     */
    const SUPER_ADMIN_ROLE = 'super_admin';

    /**
     * التحقق من أن المستخدم الحالي هو Super Admin
     */
    public static function isSuperAdmin(): bool
    {
        $user = Auth::user();
        return $user && $user->hasRole(self::SUPER_ADMIN_ROLE);
    }

    /**
     * التحقق من أن المستخدم المحدد هو Super Admin
     */
    public static function isUserSuperAdmin($user): bool
    {
        if (!$user) {
            return false;
        }

        return $user->hasRole(self::SUPER_ADMIN_ROLE);
    }

    /**
     * فلترة المستخدمين لإخفاء Super Admins
     */
    public static function filterUsers(Builder $query): Builder
    {
        // إذا كان المستخدم الحالي Super Admin، يرى الجميع
        if (self::isSuperAdmin()) {
            return $query;
        }

        // إخفاء مستخدمي Super Admin من المستخدمين العاديين
        return $query->whereDoesntHave('roles', function ($roleQuery) {
            $roleQuery->where('name', self::SUPER_ADMIN_ROLE);
        });
    }

    /**
     * فلترة الأدوار لإخفاء دور Super Admin
     */
    public static function filterRoles(Builder $query): Builder
    {
        // إذا كان المستخدم الحالي Super Admin، يرى جميع الأدوار
        if (self::isSuperAdmin()) {
            return $query;
        }

        // إخفاء دور Super Admin من المستخدمين العاديين
        return $query->where('name', '!=', self::SUPER_ADMIN_ROLE);
    }

    /**
     * فلترة الصلاحيات لإخفاء الصلاحيات الحساسة
     */
    public static function filterPermissions(Builder $query): Builder
    {
        // إذا كان المستخدم الحالي Super Admin، يرى جميع الصلاحيات
        if (self::isSuperAdmin()) {
            return $query;
        }

        // قائمة الصلاحيات المحظورة للمستخدمين العاديين
        $restrictedPermissions = [
            'view_any_super::admin::setting',
            'create_super::admin::setting',
            'update_super::admin::setting',
            'delete_super::admin::setting',
            'manage_super_admin',
            'access_super_admin_panel',
            'manage_system_settings',
            'manage_permissions',
            'manage_roles',
        ];

        return $query->whereNotIn('name', $restrictedPermissions);
    }

    /**
     * الحصول على قائمة المستخدمين المفلترة
     */
    public static function getFilteredUsers()
    {
        $query = User::query();
        return self::filterUsers($query)->get();
    }

    /**
     * الحصول على قائمة الأدوار المفلترة
     */
    public static function getFilteredRoles()
    {
        $query = Role::query();
        return self::filterRoles($query)->get();
    }

    /**
     * الحصول على قائمة الصلاحيات المفلترة
     */
    public static function getFilteredPermissions()
    {
        $currentUser = Auth::user();

        // إذا كان المستخدم الحالي Super Admin، يرى جميع الصلاحيات
        if (self::isSuperAdmin()) {
            return Permission::all();
        }

        // إذا كان المستخدم admin أو أي دور آخر، يرى فقط الصلاحيات التي يملكها
        if ($currentUser) {
            // جلب جميع الصلاحيات التي يملكها المستخدم الحالي عبر الأدوار
            $userRoles = $currentUser->roles;
            $userPermissions = collect();

            foreach ($userRoles as $role) {
                $userPermissions = $userPermissions->merge($role->permissions);
            }

            // إضافة الصلاحيات المباشرة للمستخدم
            $userPermissions = $userPermissions->merge($currentUser->permissions);

            // إزالة التكرار
            $userPermissions = $userPermissions->unique('id');

            // فلترة الصلاحيات الحساسة
            $restrictedPermissions = [
                'view_any_super::admin::setting',
                'create_super::admin::setting',
                'update_super::admin::setting',
                'delete_super::admin::setting',
                'manage_super_admin',
                'access_super_admin_panel',
                'manage_system_settings',
                'manage_permissions',
                'manage_roles',
            ];

            return $userPermissions->filter(function ($permission) use ($restrictedPermissions) {
                return !in_array($permission->name, $restrictedPermissions);
            });
        }

        // إذا لم يكن هناك مستخدم مسجل دخول، إرجاع مجموعة فارغة
        return collect();
    }

    /**
     * التحقق من إمكانية الوصول لمستخدم معين
     */
    public static function canAccessUser($user): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه الوصول للجميع
        if (self::isSuperAdmin()) {
            return true;
        }

        // إذا كان المستخدم المطلوب Super Admin، منع الوصول
        if (self::isUserSuperAdmin($user)) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية الوصول لدور معين
     */
    public static function canAccessRole($role): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه الوصول لجميع الأدوار
        if (self::isSuperAdmin()) {
            return true;
        }

        // إذا كان الدور هو Super Admin، منع الوصول
        if ($role && $role->name === self::SUPER_ADMIN_ROLE) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية الوصول لصلاحية معينة
     */
    public static function canAccessPermission($permission): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه الوصول لجميع الصلاحيات
        if (self::isSuperAdmin()) {
            return true;
        }

        // قائمة الصلاحيات المحظورة
        $restrictedPermissions = [
            'view_any_super::admin::setting',
            'create_super::admin::setting',
            'update_super::admin::setting',
            'delete_super::admin::setting',
            'manage_super_admin',
            'access_super_admin_panel',
            'manage_system_settings',
            'manage_permissions',
            'manage_roles',
        ];

        if ($permission && in_array($permission->name, $restrictedPermissions)) {
            return false;
        }

        return true;
    }

    /**
     * فلترة الإحصائيات لإخفاء بيانات Super Admin
     */
    public static function getFilteredUserCount(): int
    {
        $query = User::query();
        return self::filterUsers($query)->count();
    }

    /**
     * فلترة الإحصائيات لإخفاء أدوار Super Admin
     */
    public static function getFilteredRoleCount(): int
    {
        $query = Role::query();
        return self::filterRoles($query)->count();
    }

    /**
     * فلترة الإحصائيات لإخفاء صلاحيات Super Admin
     */
    public static function getFilteredPermissionCount(): int
    {
        $query = Permission::query();
        return self::filterPermissions($query)->count();
    }

    /**
     * التحقق من إمكانية تعديل مستخدم معين
     */
    public static function canEditUser($user): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه تعديل الجميع
        if (self::isSuperAdmin()) {
            return true;
        }

        // منع تعديل مستخدمي Super Admin
        if (self::isUserSuperAdmin($user)) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية حذف مستخدم معين
     */
    public static function canDeleteUser($user): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه حذف الجميع (عدا نفسه)
        if (self::isSuperAdmin()) {
            $currentUser = Auth::user();
            return $user->id !== $currentUser->id;
        }

        // منع حذف مستخدمي Super Admin
        if (self::isUserSuperAdmin($user)) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية تعديل دور معين
     */
    public static function canEditRole($role): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه تعديل جميع الأدوار
        if (self::isSuperAdmin()) {
            return true;
        }

        // منع تعديل دور Super Admin
        if ($role && $role->name === self::SUPER_ADMIN_ROLE) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية حذف دور معين
     */
    public static function canDeleteRole($role): bool
    {
        // إذا كان المستخدم الحالي Super Admin، يمكنه حذف الأدوار (عدا Super Admin)
        if (self::isSuperAdmin()) {
            return $role && $role->name !== self::SUPER_ADMIN_ROLE;
        }

        // منع حذف دور Super Admin
        if ($role && $role->name === self::SUPER_ADMIN_ROLE) {
            return false;
        }

        return true;
    }

    /**
     * فلترة خيارات الأدوار في النماذج
     */
    public static function getAvailableRolesForForms()
    {
        return self::getFilteredRoles()->pluck('name', 'id')->toArray();
    }

    /**
     * فلترة خيارات الصلاحيات في النماذج
     */
    public static function getAvailablePermissionsForForms()
    {
        return self::getFilteredPermissions()->pluck('name', 'id')->toArray();
    }
}
