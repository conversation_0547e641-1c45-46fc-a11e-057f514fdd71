<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CurrencyService
{
    protected $apiKey;
    protected $baseCurrency;
    protected $supportedCurrencies = ['EGP', 'USD', 'EUR', 'GBP', 'AED'];
    
    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->apiKey = config('services.currency_converter.api_key');
        $this->baseCurrency = config('services.currency_converter.base_currency', 'EGP');
    }
    
    /**
     * Get exchange rates for all supported currencies.
     *
     * @return array
     */
    public function getExchangeRates()
    {
        // Check if rates are cached
        if (Cache::has('exchange_rates')) {
            return Cache::get('exchange_rates');
        }
        
        try {
            // If API key is not set, use fallback rates
            if (empty($this->apiKey)) {
                return $this->getFallbackRates();
            }
            
            // Build currencies string
            $currencies = implode(',', $this->supportedCurrencies);
            
            // Make API request
            $response = Http::get('https://api.exchangerate-api.com/v4/latest/' . $this->baseCurrency, [
                'apiKey' => $this->apiKey,
            ]);
            
            if ($response->successful()) {
                $data = $response->json();
                $rates = $data['rates'];
                
                // Cache the rates for 24 hours
                Cache::put('exchange_rates', $rates, now()->addHours(24));
                
                return $rates;
            }
            
            // If API request fails, use fallback rates
            return $this->getFallbackRates();
            
        } catch (\Exception $e) {
            Log::error('Currency conversion error: ' . $e->getMessage());
            return $this->getFallbackRates();
        }
    }
    
    /**
     * Convert amount from one currency to another.
     *
     * @param  float  $amount
     * @param  string  $from
     * @param  string  $to
     * @return float
     */
    public function convert($amount, $from = null, $to = null)
    {
        $from = $from ?: $this->baseCurrency;
        $to = $to ?: $this->baseCurrency;
        
        // If currencies are the same, return the original amount
        if ($from === $to) {
            return $amount;
        }
        
        $rates = $this->getExchangeRates();
        
        // Convert from source currency to base currency first (if needed)
        if ($from !== $this->baseCurrency) {
            $amount = $amount / $rates[$from];
        }
        
        // Convert from base currency to target currency
        if ($to !== $this->baseCurrency) {
            $amount = $amount * $rates[$to];
        }
        
        return round($amount, 2);
    }
    
    /**
     * Get fallback exchange rates when API is not available.
     *
     * @return array
     */
    protected function getFallbackRates()
    {
        // Fallback rates (as of May 2024)
        $fallbackRates = [
            'EGP' => 1,
            'USD' => 0.0204, // 1 EGP = 0.0204 USD
            'EUR' => 0.0189, // 1 EGP = 0.0189 EUR
            'GBP' => 0.0162, // 1 EGP = 0.0162 GBP
            'AED' => 0.0749, // 1 EGP = 0.0749 AED
        ];
        
        // Cache the fallback rates for 24 hours
        Cache::put('exchange_rates', $fallbackRates, now()->addHours(24));
        
        return $fallbackRates;
    }
    
    /**
     * Get all supported currencies.
     *
     * @return array
     */
    public function getSupportedCurrencies()
    {
        return $this->supportedCurrencies;
    }
    
    /**
     * Format price with currency symbol.
     *
     * @param  float  $amount
     * @param  string  $currency
     * @return string
     */
    public function formatPrice($amount, $currency = null)
    {
        $currency = $currency ?: $this->baseCurrency;
        
        $symbols = [
            'EGP' => 'ج.م',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
            'AED' => 'د.إ',
        ];
        
        $symbol = $symbols[$currency] ?? '';
        
        // Format based on locale
        if (app()->getLocale() == 'ar') {
            return number_format($amount, 2) . ' ' . $symbol;
        } else {
            return $symbol . ' ' . number_format($amount, 2);
        }
    }
}
