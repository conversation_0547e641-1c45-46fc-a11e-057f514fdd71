<?php

namespace App\Services;

use App\Models\SuperAdminSetting;
use Illuminate\Support\Facades\Cache;

class SuperAdminSettingsService
{
    /**
     * وقت التخزين المؤقت للإعدادات (بالثواني)
     */
    protected const CACHE_TIME = 300; // 5 دقائق

    /**
     * مفتاح التخزين المؤقت للإعدادات
     */
    protected const CACHE_KEY = 'super_admin_settings';

    /**
     * الحصول على جميع إعدادات السوبر أدمن
     *
     * @param bool $forceRefresh إجبار تحديث الإعدادات من قاعدة البيانات
     * @return \App\Models\SuperAdminSetting
     */
    public function all($forceRefresh = false)
    {
        if ($forceRefresh) {
            Cache::forget(self::CACHE_KEY);
        }

        return Cache::remember(self::CACHE_KEY, self::CACHE_TIME, function () {
            return SuperAdminSetting::firstOrCreateWithDefaults();
        });
    }

    /**
     * مسح التخزين المؤقت للإعدادات
     *
     * @return bool
     */
    public function clearCache()
    {
        return Cache::forget(self::CACHE_KEY);
    }

    /**
     * الحصول على قيمة إعداد محددة
     *
     * @param string $key
     * @param mixed $default
     * @param bool $forceRefresh إجبار تحديث الإعدادات من قاعدة البيانات
     * @return mixed
     */
    public function get(string $key, $default = null, $forceRefresh = false)
    {
        $settings = $this->all($forceRefresh);

        return $settings->$key ?? $default;
    }

    /**
     * تعيين قيمة إعداد محددة
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function set(string $key, $value)
    {
        $settings = SuperAdminSetting::firstOrCreateWithDefaults();
        $settings->$key = $value;
        $settings->save();

        // مسح التخزين المؤقت
        Cache::forget(self::CACHE_KEY);

        return true;
    }

    /**
     * تحديث عدة إعدادات في نفس الوقت
     *
     * @param array $data
     * @return bool
     */
    public function update(array $data)
    {
        $settings = SuperAdminSetting::firstOrCreateWithDefaults();
        $settings->update($data);

        // مسح التخزين المؤقت
        Cache::forget(self::CACHE_KEY);

        return true;
    }

    /**
     * التحقق من تفعيل التسجيل
     *
     * @return bool
     */
    public function isRegistrationEnabled(): bool
    {
        return $this->get('enable_registration', true);
    }

    /**
     * الحصول على رسالة تعطيل التسجيل
     *
     * @return string
     */
    public function getRegistrationDisabledMessage(): string
    {
        return $this->get('registration_disabled_message', 'التسجيل غير متاح حالياً، يرجى المحاولة لاحقاً.');
    }

    /**
     * الحصول على العملة الافتراضية
     *
     * @return string
     */
    public function getDefaultCurrency(): string
    {
        return $this->get('default_currency', 'EGP');
    }

    /**
     * الحصول على العملات المدعومة
     *
     * @return array
     */
    public function getSupportedCurrencies(): array
    {
        return $this->get('supported_currencies', ['EGP', 'USD', 'EUR']);
    }

    /**
     * الحصول على مفتاح API للعملات
     *
     * @return string|null
     */
    public function getCurrencyApiKey(): ?string
    {
        return $this->get('currency_api_key');
    }

    /**
     * التحقق من تفعيل عرض التقييمات
     *
     * @return bool
     */
    public function showRatings(): bool
    {
        return $this->get('show_ratings', true);
    }

    /**
     * التحقق من تفعيل عرض المفضلة
     *
     * @return bool
     */
    public function showWishlist(): bool
    {
        return $this->get('show_wishlist', true);
    }

    /**
     * التحقق من تفعيل الشراء كزائر
     *
     * @return bool
     */
    public function isGuestCheckoutEnabled(): bool
    {
        return $this->get('enable_guest_checkout', true);
    }

    /**
     * التحقق من تفعيل الاستلام من المتجر
     *
     * @return bool
     */
    public function isLocalPickupEnabled(): bool
    {
        return $this->get('enable_local_pickup', false);
    }

    /**
     * التحقق من تفعيل تعدد اللغات
     *
     * @return bool
     */
    public function isMultilingualEnabled(): bool
    {
        return $this->get('enable_multilingual', true);
    }

    /**
     * الحصول على نسبة الضريبة
     *
     * @return float
     */
    public function getTaxRate(): float
    {
        return $this->get('tax_rate', 14.00);
    }

    /**
     * التحقق من شمول الأسعار للضريبة
     *
     * @return bool
     */
    public function pricesIncludeTax(): bool
    {
        return $this->get('prices_include_tax', true);
    }

    /**
     * الحصول على الحد الأدنى لقيمة الطلب
     *
     * @return float
     */
    public function getMinOrderAmount(): float
    {
        return $this->get('min_order_amount', 0.00);
    }

    /**
     * الحصول على بادئة رقم الطلب
     *
     * @return string
     */
    public function getOrderPrefix(): string
    {
        return $this->get('order_prefix', 'MGJ-');
    }

    /**
     * التحقق من تفعيل وضع الصيانة
     *
     * @return bool
     */
    public function isMaintenanceModeEnabled(): bool
    {
        return $this->get('maintenance_mode', false);
    }

    /**
     * الحصول على رسالة الصيانة
     *
     * @return string
     */
    public function getMaintenanceMessage(): string
    {
        return $this->get('maintenance_message', 'الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.');
    }

    /**
     * التحقق من تفعيل وضع العرض فقط
     *
     * @return bool
     */
    public function isDisplayOnlyModeEnabled(): bool
    {
        return $this->get('display_only_mode', false);
    }

    /**
     * الحصول على اللغة الافتراضية
     *
     * @return string
     */
    public function getDefaultLanguage(): string
    {
        return $this->get('default_language', 'ar');
    }

    /**
     * التحقق من تفعيل الدفع بالبطاقة الائتمانية
     *
     * @return bool
     */
    public function isCreditCardEnabled(): bool
    {
        return $this->get('enable_credit_card', true);
    }

    /**
     * التحقق من تفعيل الدفع عبر PayPal
     *
     * @return bool
     */
    public function isPaypalEnabled(): bool
    {
        return $this->get('enable_paypal', true);
    }

    /**
     * التحقق من تفعيل التحويل البنكي
     *
     * @return bool
     */
    public function isBankTransferEnabled(): bool
    {
        return $this->get('enable_bank_transfer', false);
    }

    /**
     * التحقق من تفعيل الدفع عند الاستلام
     *
     * @return bool
     */
    public function isCashOnDeliveryEnabled(): bool
    {
        return $this->get('enable_cash_on_delivery', false);
    }

    /**
     * التحقق من تفعيل الدفع عبر فوري
     *
     * @return bool
     */
    public function isFawryEnabled(): bool
    {
        return $this->get('enable_fawry', false);
    }

    /**
     * الحصول على مفتاح Stripe العام
     *
     * @return string|null
     */
    public function getStripeKey(): ?string
    {
        return $this->get('stripe_key');
    }

    /**
     * الحصول على مفتاح Stripe السري
     *
     * @return string|null
     */
    public function getStripeSecret(): ?string
    {
        return $this->get('stripe_secret');
    }

    /**
     * التحقق من تفعيل وضع الاختبار لـ Stripe
     *
     * @return bool
     */
    public function isStripeSandboxMode(): bool
    {
        return $this->get('stripe_sandbox_mode', true);
    }

    /**
     * الحصول على معرف عميل PayPal
     *
     * @return string|null
     */
    public function getPaypalClientId(): ?string
    {
        return $this->get('paypal_client_id');
    }

    /**
     * الحصول على مفتاح PayPal السري
     *
     * @return string|null
     */
    public function getPaypalSecret(): ?string
    {
        return $this->get('paypal_secret');
    }

    /**
     * التحقق من تفعيل وضع الاختبار لـ PayPal
     *
     * @return bool
     */
    public function isPaypalSandboxMode(): bool
    {
        return $this->get('paypal_sandbox_mode', true);
    }

    /**
     * الحصول على خصم الاستلام من المتجر
     *
     * @return float
     */
    public function getLocalPickupDiscount(): float
    {
        return $this->get('local_pickup_discount', 0.00);
    }

    /**
     * الحصول على سياسة الشحن
     *
     * @return string|null
     */
    public function getShippingPolicy(): ?string
    {
        return $this->get('shipping_policy');
    }

    /**
     * التحقق من تفعيل الفواتير
     *
     * @return bool
     */
    public function isInvoicesEnabled(): bool
    {
        return $this->get('enable_invoices', true);
    }

    /**
     * الحصول على بادئة رقم الفاتورة
     *
     * @return string
     */
    public function getInvoicePrefix(): string
    {
        return $this->get('invoice_prefix', 'INV-');
    }

    /**
     * الحصول على اسم الشركة في الفاتورة
     *
     * @return string|null
     */
    public function getCompanyNameInvoice(): ?string
    {
        return $this->get('company_name_invoice');
    }

    /**
     * الحصول على عنوان الشركة في الفاتورة
     *
     * @return string|null
     */
    public function getCompanyAddressInvoice(): ?string
    {
        return $this->get('company_address_invoice');
    }

    /**
     * الحصول على الرقم الضريبي للشركة
     *
     * @return string|null
     */
    public function getCompanyTaxId(): ?string
    {
        return $this->get('company_tax_id');
    }

    // ===== دوال تكامل وسائل التواصل الاجتماعي =====

    /**
     * التحقق من تفعيل تسجيل الدخول بوسائل التواصل الاجتماعي
     *
     * @return bool
     */
    public function isSocialLoginEnabled(): bool
    {
        return $this->get('enable_social_login', false);
    }

    /**
     * التحقق من تفعيل تسجيل الدخول بفيسبوك
     *
     * @return bool
     */
    public function isFacebookLoginEnabled(): bool
    {
        return $this->get('enable_facebook_login', false);
    }

    /**
     * التحقق من تفعيل تسجيل الدخول بجوجل
     *
     * @return bool
     */
    public function isGoogleLoginEnabled(): bool
    {
        return $this->get('enable_google_login', false);
    }

    /**
     * التحقق من تفعيل تسجيل الدخول بتويتر
     *
     * @return bool
     */
    public function isTwitterLoginEnabled(): bool
    {
        return $this->get('enable_twitter_login', false);
    }

    /**
     * الحصول على معرف تطبيق فيسبوك
     *
     * @return string|null
     */
    public function getFacebookAppId(): ?string
    {
        return $this->get('facebook_app_id');
    }

    /**
     * الحصول على سر تطبيق فيسبوك
     *
     * @return string|null
     */
    public function getFacebookAppSecret(): ?string
    {
        return $this->get('facebook_app_secret');
    }

    /**
     * الحصول على معرف عميل جوجل
     *
     * @return string|null
     */
    public function getGoogleClientId(): ?string
    {
        return $this->get('google_client_id');
    }

    /**
     * الحصول على سر عميل جوجل
     *
     * @return string|null
     */
    public function getGoogleClientSecret(): ?string
    {
        return $this->get('google_client_secret');
    }

    /**
     * الحصول على معرف عميل تويتر
     *
     * @return string|null
     */
    public function getTwitterClientId(): ?string
    {
        return $this->get('twitter_client_id');
    }

    /**
     * الحصول على سر عميل تويتر
     *
     * @return string|null
     */
    public function getTwitterClientSecret(): ?string
    {
        return $this->get('twitter_client_secret');
    }

    // ===== دوال مشاركة المنتجات =====

    /**
     * التحقق من تفعيل مشاركة المنتجات
     *
     * @return bool
     */
    public function isSocialSharingEnabled(): bool
    {
        return $this->get('enable_social_sharing', true);
    }

    /**
     * التحقق من تفعيل المشاركة على فيسبوك
     *
     * @return bool
     */
    public function isShareOnFacebookEnabled(): bool
    {
        return $this->get('share_on_facebook', true);
    }

    /**
     * التحقق من تفعيل المشاركة على تويتر
     *
     * @return bool
     */
    public function isShareOnTwitterEnabled(): bool
    {
        return $this->get('share_on_twitter', true);
    }

    /**
     * التحقق من تفعيل المشاركة على واتساب
     *
     * @return bool
     */
    public function isShareOnWhatsappEnabled(): bool
    {
        return $this->get('share_on_whatsapp', true);
    }

    /**
     * التحقق من تفعيل المشاركة على بينتيريست
     *
     * @return bool
     */
    public function isShareOnPinterestEnabled(): bool
    {
        return $this->get('share_on_pinterest', false);
    }

    /**
     * التحقق من تفعيل المشاركة على لينكد إن
     *
     * @return bool
     */
    public function isShareOnLinkedinEnabled(): bool
    {
        return $this->get('share_on_linkedin', false);
    }





    /**
     * التحقق من تفعيل عرض منشورات إنستغرام
     *
     * @return bool
     */
    public function isInstagramFeedEnabled(): bool
    {
        return $this->get('show_instagram_feed', false);
    }

    /**
     * الحصول على رمز إنستغرام المميز
     *
     * @return string|null
     */
    public function getInstagramToken(): ?string
    {
        return $this->get('instagram_token');
    }

    /**
     * الحصول على عدد منشورات إنستغرام
     *
     * @return int
     */
    public function getInstagramCount(): int
    {
        return $this->get('instagram_count', 6);
    }

    /**
     * التحقق من تفعيل عرض منشورات فيسبوك
     *
     * @return bool
     */
    public function isFacebookFeedEnabled(): bool
    {
        return $this->get('show_facebook_feed', false);
    }

    /**
     * الحصول على معرف صفحة فيسبوك
     *
     * @return string|null
     */
    public function getFacebookPageId(): ?string
    {
        return $this->get('facebook_page_id');
    }

    /**
     * الحصول على عدد منشورات فيسبوك
     *
     * @return int
     */
    public function getFacebookCount(): int
    {
        return $this->get('facebook_count', 5);
    }

    // ===== دوال ملفات تعريف الارتباط والخصوصية =====

    /**
     * التحقق من تفعيل عرض شريط ملفات تعريف الارتباط
     *
     * @return bool
     */
    public function isCookieBannerEnabled(): bool
    {
        return $this->get('show_cookie_banner', true);
    }

    /**
     * الحصول على نص شريط ملفات تعريف الارتباط
     *
     * @return string
     */
    public function getCookieBannerText(): string
    {
        return $this->get('cookie_banner_text', 'هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.');
    }

    /**
     * الحصول على نص زر الموافقة على ملفات تعريف الارتباط
     *
     * @return string
     */
    public function getCookieBannerButtonText(): string
    {
        return $this->get('cookie_banner_button_text', 'أوافق');
    }

    /**
     * التحقق من تفعيل الامتثال لـ GDPR
     *
     * @return bool
     */
    public function isGdprComplianceEnabled(): bool
    {
        return $this->get('enable_gdpr_compliance', false);
    }

    /**
     * الحصول على نص الامتثال لـ GDPR
     *
     * @return string
     */
    public function getGdprComplianceText(): string
    {
        return $this->get('gdpr_compliance_text', 'نحن نحترم خصوصيتك ونلتزم بحماية بياناتك الشخصية وفقاً للائحة العامة لحماية البيانات (GDPR).');
    }

    /**
     * التحقق من تفعيل طلب موافقة التسويق
     *
     * @return bool
     */
    public function isMarketingConsentRequired(): bool
    {
        return $this->get('require_marketing_consent', false);
    }

    /**
     * الحصول على نص موافقة التسويق
     *
     * @return string
     */
    public function getMarketingConsentText(): string
    {
        return $this->get('marketing_consent_text', 'أوافق على تلقي رسائل تسويقية من مكة جولد عبر البريد الإلكتروني والرسائل القصيرة.');
    }
}
