<?php

namespace App\Services;

use App\Models\SiteSetting;
use App\Services\SuperAdminSettingsService;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    /**
     * وقت التخزين المؤقت للإعدادات (بالثواني)
     */
    protected const CACHE_TIME = 300; // 5 دقائق

    /**
     * مفتاح التخزين المؤقت للإعدادات
     */
    protected const CACHE_KEY = 'site_settings';

    /**
     * خدمة إعدادات السوبر أدمن
     *
     * @var \App\Services\SuperAdminSettingsService
     */
    protected $superAdminSettingsService;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->superAdminSettingsService = app(SuperAdminSettingsService::class);
    }

    /**
     * الحصول على جميع إعدادات الموقع
     *
     * @param bool $forceRefresh إجبار تحديث الإعدادات من قاعدة البيانات
     * @return \App\Models\SiteSetting
     */
    public function all($forceRefresh = false)
    {
        if ($forceRefresh) {
            Cache::forget(self::CACHE_KEY);
        }

        return Cache::remember(self::CACHE_KEY, self::CACHE_TIME, function () {
            return SiteSetting::first() ?? SiteSetting::create([
                'site_name' => 'مجوهرات مكة جولد جروب',
                'site_description' => 'متجر مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية',
            ]);
        });
    }

    /**
     * مسح التخزين المؤقت للإعدادات
     *
     * @return bool
     */
    public function clearCache()
    {
        return Cache::forget(self::CACHE_KEY);
    }

    /**
     * الحصول على قيمة إعداد محددة
     *
     * @param string $key
     * @param mixed $default
     * @param bool $forceRefresh إجبار تحديث الإعدادات من قاعدة البيانات
     * @return mixed
     */
    public function get(string $key, $default = null, $forceRefresh = false)
    {
        $settings = $this->all($forceRefresh);

        return $settings->$key ?? $default;
    }

    /**
     * Establecer un valor de configuración
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function set(string $key, $value)
    {
        $settings = SiteSetting::first();

        if (!$settings) {
            $settings = SiteSetting::create([
                'site_name' => 'مجوهرات مكة جولد جروب',
                'site_description' => 'متجر مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية',
                $key => $value,
            ]);
        } else {
            $settings->$key = $value;
            $settings->save();
        }

        // Limpiar caché
        Cache::forget(self::CACHE_KEY);

        return true;
    }

    /**
     * Actualizar múltiples configuraciones a la vez
     *
     * @param array $data
     * @return bool
     */
    public function update(array $data)
    {
        $settings = SiteSetting::first();

        if (!$settings) {
            $settings = SiteSetting::create(array_merge([
                'site_name' => 'مجوهرات مكة جولد جروب',
                'site_description' => 'متجر مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية',
            ], $data));
        } else {
            $settings->update($data);
        }

        // Limpiar caché
        Cache::forget(self::CACHE_KEY);

        return true;
    }

    /**
     * الحصول على إعدادات السوبر أدمن
     *
     * @param bool $forceRefresh إجبار تحديث الإعدادات من قاعدة البيانات
     * @return \App\Models\SuperAdminSetting
     */
    public function superAdminSettings($forceRefresh = false)
    {
        return $this->superAdminSettingsService->all($forceRefresh);
    }

    /**
     * الحصول على قيمة إعداد من إعدادات السوبر أدمن
     *
     * @param string $key
     * @param mixed $default
     * @param bool $forceRefresh إجبار تحديث الإعدادات من قاعدة البيانات
     * @return mixed
     */
    public function getSuperAdminSetting(string $key, $default = null, $forceRefresh = false)
    {
        return $this->superAdminSettingsService->get($key, $default, $forceRefresh);
    }
}
