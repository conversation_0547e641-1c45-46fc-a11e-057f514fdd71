<?php

namespace App\Rules;

use App\Models\Product;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueProductName implements ValidationRule
{
    protected ?int $excludeId;
    protected string $field;

    public function __construct(?int $excludeId = null, string $field = 'name_ar')
    {
        $this->excludeId = $excludeId;
        $this->field = $field;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (empty($value)) {
            return;
        }

        $query = Product::where($this->field, $value);
        
        if ($this->excludeId) {
            $query->where('id', '!=', $this->excludeId);
        }

        if ($query->exists()) {
            $fieldName = $this->field === 'name_ar' ? 'العربي' : 'الإنجليزي';
            $fail("الاسم {$fieldName} \"{$value}\" مستخدم مسبقاً، يرجى اختيار اسم آخر");
        }
    }
}
