<?php

namespace App\Filament\Components;

use Filament\Forms\Components\Component;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Set;
use Filament\Forms\Get;
use Closure;

class ProductImageUpload extends Component
{
    protected string $view = 'filament.components.product-image-upload';

    public static function make(string $name = 'product_images'): static
    {
        return app(static::class, ['name' => $name]);
    }

    public function getChildComponents(): array
    {
        return [
            Grid::make(1)
                ->schema([
                    FileUpload::make('images')
                        ->label('صور المنتج')
                        ->multiple()
                        ->image()
                        ->directory('products')
                        ->maxSize(2048)
                        ->imageEditor()
                        ->imageEditorAspectRatios([
                            '1:1',
                            '4:3',
                            '16:9',
                        ])
                        ->reorderable()
                        ->appendFiles()
                        ->helperText('يمكنك رفع عدة صور. الصورة الأولى ستكون الصورة الرئيسية افتراضياً.')
                        ->afterStateUpdated(function (Set $set, Get $get, $state) {
                            if (is_array($state) && count($state) > 0) {
                                // إذا لم تكن هناك صورة رئيسية محددة، اجعل الأولى رئيسية
                                $primaryImage = $get('primary_image_index');
                                if ($primaryImage === null || $primaryImage >= count($state)) {
                                    $set('primary_image_index', 0);
                                }
                            }
                        })
                        ->live(),

                    ViewField::make('image_selector')
                        ->label('تحديد الصورة الرئيسية')
                        ->view('filament.components.image-selector')
                        ->visible(fn (Get $get) => is_array($get('images')) && count($get('images')) > 1),

                    Hidden::make('primary_image_index')
                        ->default(0),
                ])
        ];
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->columnSpanFull();
    }
}
