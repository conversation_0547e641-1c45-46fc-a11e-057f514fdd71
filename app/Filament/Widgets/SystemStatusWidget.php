<?php

namespace App\Filament\Widgets;

use App\Models\SuperAdminSetting;
use App\Services\SuperAdminProtectionService;
use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class SystemStatusWidget extends Widget
{
    use HasAdvancedWidgetPermissions;

    protected static string $view = 'filament.widgets.system-status-widget';

    protected static ?int $sort = 8;

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 3,
    ];

    protected static ?string $pollingInterval = '60s';

    public static function canView(): bool
    {
        return parent::canView() && (
            SuperAdminProtectionService::isSuperAdmin() ||
            WidgetService::canViewWidget('view_any_super::admin::setting')
        );
    }

    protected static function getRequiredPermission(): ?string
    {
        return 'view_any_super::admin::setting';
    }

    public function getViewData(): array
    {
        // الحصول على أول سجل من إعدادات السوبر أدمن أو إنشاء واحد بالقيم الافتراضية
        $settings = SuperAdminSetting::first();
        if (!$settings) {
            $settings = SuperAdminSetting::create(SuperAdminSetting::getDefaults());
        }

        return [
            'maintenance_mode' => $settings->maintenance_mode ?? false,
            'user_registration' => $settings->enable_registration ?? true,
            'online_payments' => ($settings->enable_credit_card || $settings->enable_paypal || $settings->enable_fawry) ?? true,
            'appointment_booking' => true, // يمكن إضافة حقل مخصص لهذا لاحقاً
            'contact_form' => true, // يمكن إضافة حقل مخصص لهذا لاحقاً
            'newsletter_signup' => true, // يمكن إضافة حقل مخصص لهذا لاحقاً
            'reviews_enabled' => $settings->show_ratings ?? true,
            'wishlist_enabled' => $settings->show_wishlist ?? true,
            'cache_status' => $this->getCacheStatus(),
            'storage_usage' => $this->getStorageUsage(),
            'database_status' => $this->getDatabaseStatus(),
        ];
    }

    private function getCacheStatus(): array
    {
        try {
            cache()->put('test_key', 'test_value', 60);
            $test = cache()->get('test_key');
            cache()->forget('test_key');

            return [
                'status' => $test === 'test_value' ? 'working' : 'error',
                'message' => $test === 'test_value' ? 'يعمل بشكل طبيعي' : 'خطأ في الكاش',
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'خطأ في الاتصال بالكاش',
            ];
        }
    }

    private function getStorageUsage(): array
    {
        try {
            $storagePath = storage_path();
            $totalSpace = disk_total_space($storagePath);
            $freeSpace = disk_free_space($storagePath);
            $usedSpace = $totalSpace - $freeSpace;
            $usagePercentage = round(($usedSpace / $totalSpace) * 100, 1);

            return [
                'total' => $this->formatBytes($totalSpace),
                'used' => $this->formatBytes($usedSpace),
                'free' => $this->formatBytes($freeSpace),
                'percentage' => $usagePercentage,
                'status' => $usagePercentage > 90 ? 'critical' : ($usagePercentage > 75 ? 'warning' : 'good'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'لا يمكن قراءة معلومات التخزين',
            ];
        }
    }

    private function getDatabaseStatus(): array
    {
        try {
            $start = microtime(true);
            DB::connection()->getPdo();
            $connectionTime = round((microtime(true) - $start) * 1000, 2);

            return [
                'status' => 'connected',
                'connection_time' => $connectionTime,
                'message' => "متصل ({$connectionTime}ms)",
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => 'خطأ في الاتصال بقاعدة البيانات',
            ];
        }
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_super::admin::setting',
        ];
    }
}
