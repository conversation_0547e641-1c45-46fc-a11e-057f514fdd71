<?php

namespace App\Filament\Widgets;

use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class EngagementStatsWidget extends BaseWidget
{
    use HasAdvancedWidgetPermissions;

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '60s';

    public static function canView(): bool
    {
        return parent::canView() && (
            WidgetService::canViewWidget('view_any_job') ||
            WidgetService::canViewWidget('view_any_job::application') ||
            WidgetService::canViewWidget('view_any_newsletter') ||
            WidgetService::canViewWidget('view_any_review') ||
            WidgetService::canViewWidget('view_any_store')
        );
    }

    protected function getStats(): array
    {
        $stats = [];
        $jobStats = WidgetService::getJobsStats();
        $engagementStats = WidgetService::getEngagementStats();

        // إحصائيات الوظائف
        if (WidgetService::canViewWidget('view_any_job')) {
            $stats[] = Stat::make('الوظائف المتاحة', $this->formatNumber($jobStats['active_jobs']))
                ->description("إجمالي: {$jobStats['total_jobs']}")
                ->descriptionIcon($this->getWidgetIcon('jobs'))
                ->color($this->getWidgetColor('jobs'))
                ->url($this->getResourceUrl('jobs'));
        }

        // إحصائيات طلبات التوظيف
        if (WidgetService::canViewWidget('view_any_job::application')) {
            $stats[] = Stat::make('طلبات التوظيف', $this->formatNumber($jobStats['total_applications']))
                ->description("معلقة: {$jobStats['pending_applications']}")
                ->descriptionIcon($this->getWidgetIcon('jobs'))
                ->color($this->getWidgetColor('jobs'))
                ->url($this->getResourceUrl('job_applications'));
        }

        // إحصائيات النشرة البريدية
        if (WidgetService::canViewWidget('view_any_newsletter')) {
            $stats[] = Stat::make('مشتركي النشرة', $this->formatNumber($engagementStats['newsletter_subscribers']))
                ->description('المشتركين في النشرة البريدية')
                ->descriptionIcon($this->getWidgetIcon('newsletter'))
                ->color($this->getWidgetColor('engagement'))
                ->url($this->getResourceUrl('newsletters'));
        }

        // إحصائيات التقييمات
        // if (WidgetService::canViewWidget('view_any_review')) {
        //     $averageRating = round($engagementStats['average_rating'], 1);
        //     $stats[] = Stat::make('التقييمات', $this->formatNumber($engagementStats['reviews']))
        //         ->description("متوسط التقييم: {$averageRating}/5")
        //         ->descriptionIcon($this->getWidgetIcon('reviews'))
        //         ->color($this->getWidgetColor('engagement'));
        // }

        // إحصائيات المتاجر
        if (WidgetService::canViewWidget('view_any_store')) {
            $stats[] = Stat::make('المتاجر', $this->formatNumber($engagementStats['stores']))
                ->description('فروع المتاجر')
                ->descriptionIcon($this->getWidgetIcon('stores'))
                ->color($this->getWidgetColor('engagement'))
                ->url($this->getResourceUrl('stores'));
        }

        // إحصائيات قوائم الأمنيات
        // $stats[] = Stat::make('قوائم الأمنيات', $this->formatNumber($engagementStats['wishlists']))
        //     ->description('منتجات مفضلة')
        //     ->descriptionIcon($this->getWidgetIcon('wishlists'))
        //     ->color($this->getWidgetColor('engagement'));

        // إحصائيات السلال النشطة
        // $stats[] = Stat::make('السلال النشطة', $this->formatNumber($engagementStats['active_carts']))
        //     ->description('سلال تحتوي على منتجات')
        //     ->descriptionIcon($this->getWidgetIcon('carts'))
        //     ->color($this->getWidgetColor('engagement'));

        // إحصائيات الإشعارات غير المقروءة
        // if ($engagementStats['notifications'] > 0) {
        //     $stats[] = Stat::make('إشعارات جديدة', $this->formatNumber($engagementStats['notifications']))
        //         ->description('إشعارات غير مقروءة')
        //         ->descriptionIcon($this->getWidgetIcon('notifications'))
        //         ->color('danger');
        // }

        return $stats;
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_job',
            'view_any_job::application',
            'view_any_newsletter',
            'view_any_review',
            'view_any_store',
        ];
    }
}
