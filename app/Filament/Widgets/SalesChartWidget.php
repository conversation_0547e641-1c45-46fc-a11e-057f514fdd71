<?php

namespace App\Filament\Widgets;

use App\Models\Order;
use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Carbon;

class SalesChartWidget extends ChartWidget
{
    use HasAdvancedWidgetPermissions;

    protected static ?string $heading = 'إحصائيات المبيعات الشهرية';

    protected static ?string $description = 'مبيعات آخر 12 شهر';

    protected static ?int $sort = 7;

    protected int | string | array $columnSpan = [
        'md' => 2,
        'xl' => 3,
    ];

    protected static ?string $pollingInterval = '300s';

    public static function canView(): bool
    {
        return parent::canView() && WidgetService::canViewWidget('view_any_order');
    }

    protected static function getRequiredPermission(): ?string
    {
        return 'view_any_order';
    }

    protected function getData(): array
    {
        $data = $this->getSalesData();

        return [
            'datasets' => [
                [
                    'label' => 'المبيعات (جنيه)',
                    'data' => $data['sales'],
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                ],
                [
                    'label' => 'عدد الطلبات',
                    'data' => $data['orders'],
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'borderColor' => 'rgb(16, 185, 129)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'tension' => 0.4,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $data['labels'],
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'x' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'الشهر',
                    ],
                ],
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'المبيعات (جنيه)',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'عدد الطلبات',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }

    private function getSalesData(): array
    {
        $months = collect();
        $salesData = collect();
        $ordersData = collect();

        // جمع بيانات آخر 12 شهر
        for ($i = 11; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $monthName = $date->locale('ar')->format('M Y');

            $monthSales = Order::where('status', 'completed')
                ->whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->sum('total_amount');

            $monthOrders = Order::whereYear('created_at', $date->year)
                ->whereMonth('created_at', $date->month)
                ->count();

            $months->push($monthName);
            $salesData->push($monthSales);
            $ordersData->push($monthOrders);
        }

        return [
            'labels' => $months->toArray(),
            'sales' => $salesData->toArray(),
            'orders' => $ordersData->toArray(),
        ];
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_order',
        ];
    }
}
