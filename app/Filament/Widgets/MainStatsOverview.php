<?php

namespace App\Filament\Widgets;

use App\Services\WidgetService;
use App\Traits\HasAdvancedWidgetPermissions;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class MainStatsOverview extends BaseWidget
{
    use HasAdvancedWidgetPermissions;

    protected static ?int $sort = 1;

    protected int | string | array $columnSpan = 'full';

    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        $stats = [];

        // إحصائيات المستخدمين
        if ($this->canViewUserStats()) {
            $userStats = WidgetService::getUsersStats();
            $stats[] = Stat::make('إجمالي المستخدمين', $this->formatNumber($userStats['total']))
                ->description($this->getTrendDescription($userStats['growth_percentage'], $userStats['growth_trend']))
                ->descriptionIcon($this->getTrendIcon($userStats['growth_trend']))
                ->color($this->getWidgetColor('users'))
                ->chart([7, 12, 8, 15, 10, 18, $userStats['new_this_month']])
                ->url($this->getResourceUrl('users'));
        }

        // إحصائيات المنتجات
        if ($this->canViewProductStats()) {
            $productStats = WidgetService::getProductsStats();
            $stats[] = Stat::make('إجمالي المنتجات', $this->formatNumber($productStats['total']))
                ->description("نشط: {$productStats['active']} | مميز: {$productStats['featured']}")
                ->descriptionIcon($this->getWidgetIcon('products'))
                ->color($this->getWidgetColor('products'))
                ->url($this->getResourceUrl('products'));
        }

        // إحصائيات الطلبات
        if ($this->canViewOrderStats()) {
            $orderStats = WidgetService::getOrdersStats();
            $stats[] = Stat::make('إجمالي الطلبات', $this->formatNumber($orderStats['total']))
                ->description($this->getTrendDescription($orderStats['growth_percentage'], $orderStats['growth_trend']))
                ->descriptionIcon($this->getTrendIcon($orderStats['growth_trend']))
                ->color($this->getWidgetColor('orders'))
                ->chart([5, 8, 12, 7, 15, 10, $orderStats['this_month']])
                ->url($this->getResourceUrl('orders'));
        }

        // إحصائيات الإيرادات
        if ($this->canViewOrderStats()) {
            $orderStats = WidgetService::getOrdersStats();
            $stats[] = Stat::make('إجمالي الإيرادات', $this->formatCurrency($orderStats['total_revenue']))
                ->description("طلبات اليوم: {$orderStats['today']}")
                ->descriptionIcon($this->getWidgetIcon('orders'))
                ->color($this->getWidgetColor('orders'))
                ->url($this->getResourceUrl('orders'));
        }

        // إحصائيات المواعيد
        if ($this->canViewAppointmentStats()) {
            $appointmentStats = WidgetService::getAppointmentsStats();
            $stats[] = Stat::make('إجمالي المواعيد', $this->formatNumber($appointmentStats['total']))
                ->description("قادمة: {$appointmentStats['upcoming']} | اليوم: {$appointmentStats['today']}")
                ->descriptionIcon($this->getWidgetIcon('appointments'))
                ->color($this->getWidgetColor('appointments'))
                ->url($this->getResourceUrl('appointments'));
        }

        // إحصائيات أسعار المعادن
        // if ($this->canViewMetalPriceStats()) {
        //     $metalStats = WidgetService::getMetalPricesStats();
        //     $stats[] = Stat::make('أسعار المعادن', $this->formatNumber($metalStats['active']))
        //         ->description("محدثة اليوم: {$metalStats['today']}")
        //         ->descriptionIcon($this->getWidgetIcon('metal_prices'))
        //         ->color($this->getWidgetColor('metal_prices'))
        //         ->url($this->getResourceUrl('metal_prices'));
        // }

        return $stats;
    }

    /**
     * التحقق من صلاحية عرض إحصائيات المستخدمين
     */
    protected function canViewUserStats(): bool
    {
        return WidgetService::canViewWidget('view_any_user');
    }

    /**
     * التحقق من صلاحية عرض إحصائيات المنتجات
     */
    protected function canViewProductStats(): bool
    {
        return WidgetService::canViewWidget('view_any_product');
    }

    /**
     * التحقق من صلاحية عرض إحصائيات الطلبات
     */
    protected function canViewOrderStats(): bool
    {
        return WidgetService::canViewWidget('view_any_order');
    }

    /**
     * التحقق من صلاحية عرض إحصائيات المواعيد
     */
    protected function canViewAppointmentStats(): bool
    {
        return WidgetService::canViewWidget('view_any_appointment');
    }

    /**
     * التحقق من صلاحية عرض إحصائيات أسعار المعادن
     */
    protected function canViewMetalPriceStats(): bool
    {
        return WidgetService::canViewWidget('view_any_metal::price');
    }

    /**
     * الحصول على الصلاحيات المطلوبة للموارد
     */
    public static function getRequiredResourcePermissions(): array
    {
        return [
            'view_any_user',
            'view_any_product',
            'view_any_order',
            'view_any_appointment',
            'view_any_metal::price',
        ];
    }
}
