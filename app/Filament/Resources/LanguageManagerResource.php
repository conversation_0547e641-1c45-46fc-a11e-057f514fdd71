<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LanguageManagerResource\Pages;
use App\Models\Language;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class LanguageManagerResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Language::class;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static ?string $navigationGroup = 'إعدادات النظام';

    protected static ?int $navigationSort = 74;

    public static function getNavigationLabel(): string
    {
        return 'إدارة اللغات';
    }

    public static function getPluralLabel(): string
    {
        return 'اللغات المتاحة';
    }

    public static function getLabel(): string
    {
        return 'اللغة';
    }

    public static function getNavigationBadge(): ?string
    {
        return Language::count();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات اللغة')
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label('رمز اللغة')
                            ->required()
                            ->maxLength(10)
                            ->placeholder('مثال: ar, en')
                            ->helperText('رمز اللغة المكون من حرفين أو أكثر'),
                        Forms\Components\TextInput::make('name')
                            ->label('اسم اللغة')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('مثال: العربية, English'),
                        Forms\Components\TextInput::make('native_name')
                            ->label('الاسم الأصلي')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('مثال: العربية, English'),
                        Forms\Components\Toggle::make('is_rtl')
                            ->label('من اليمين لليسار')
                            ->helperText('هل هذه اللغة تُكتب من اليمين إلى اليسار؟'),
                        Forms\Components\Toggle::make('is_default')
                            ->label('اللغة الافتراضية')
                            ->helperText('هل هذه هي اللغة الافتراضية للموقع؟'),
                        Forms\Components\Toggle::make('is_active')
                            ->label('نشطة')
                            ->default(true)
                            ->helperText('هل هذه اللغة متاحة للاستخدام؟'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('رمز اللغة')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('اسم اللغة')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('native_name')
                    ->label('الاسم الأصلي')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_rtl')
                    ->label('من اليمين لليسار')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_default')
                    ->label('افتراضية')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشطة')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشطة'),
                Tables\Filters\TernaryFilter::make('is_default')
                    ->label('افتراضية'),
                Tables\Filters\TernaryFilter::make('is_rtl')
                    ->label('من اليمين لليسار'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(fn ($record) => $record instanceof \App\Models\Language),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn ($record) => $record instanceof \App\Models\Language)
                    ->requiresConfirmation()
                    ->modalHeading('حذف اللغة')
                    ->modalDescription('هل أنت متأكد من حذف هذه اللغة؟')
                    ->modalSubmitActionLabel('حذف')
                    ->modalCancelActionLabel('إلغاء'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('حذف اللغات المحددة')
                        ->modalDescription('هل أنت متأكد من حذف اللغات المحددة؟')
                        ->modalSubmitActionLabel('حذف')
                        ->modalCancelActionLabel('إلغاء'),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLanguages::route('/'),
            'create' => Pages\CreateLanguage::route('/create'),
            'edit' => Pages\EditLanguage::route('/{record}/edit'),
        ];
    }
}
