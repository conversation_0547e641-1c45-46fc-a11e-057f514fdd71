<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FaqResource\Pages;
use App\Filament\Resources\FaqResource\RelationManagers;
use App\Models\Faq;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class FaqResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Faq::class;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    protected static ?string $navigationLabel = 'الأسئلة الشائعة';

    protected static ?string $modelLabel = 'سؤال شائع';

    protected static ?string $pluralModelLabel = 'الأسئلة الشائعة';

    protected static ?string $navigationGroup = 'الصفحات القانونية';

    protected static ?int $navigationSort = 22;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات السؤال الأساسية')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('category')
                                    ->label('التصنيف')
                                    ->options([
                                        'general' => 'عام',
                                        'products' => 'المنتجات',
                                        'orders' => 'الطلبات',
                                        'shipping' => 'الشحن',
                                        'returns' => 'الإرجاع والاستبدال',
                                        'payment' => 'الدفع',
                                        'warranty' => 'الضمان',
                                        'care' => 'العناية بالمجوهرات',
                                        'custom' => 'التصميم المخصص',
                                        'sizing' => 'المقاسات',
                                    ])
                                    ->default('general')
                                    ->required(),

                                Forms\Components\TextInput::make('sort_order')
                                    ->label('ترتيب العرض')
                                    ->numeric()
                                    ->default(0)
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true),

                                Forms\Components\Toggle::make('is_featured')
                                    ->label('مميز في الصفحة الرئيسية')
                                    ->default(false),
                            ]),
                    ]),

                Forms\Components\Section::make('السؤال والإجابة')
                    ->schema([
                        Forms\Components\Tabs::make('اللغات')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('العربية')
                                    ->schema([
                                        Forms\Components\TextInput::make('question_ar')
                                            ->label('السؤال بالعربية')
                                            ->required()
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('answer_ar')
                                            ->label('الإجابة بالعربية')
                                            ->required()
                                            ->columnSpanFull(),
                                    ]),

                                Forms\Components\Tabs\Tab::make('الإنجليزية')
                                    ->schema([
                                        Forms\Components\TextInput::make('question_en')
                                            ->label('السؤال بالإنجليزية')
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('answer_en')
                                            ->label('الإجابة بالإنجليزية')
                                            ->columnSpanFull(),
                                    ]),
                            ]),
                    ]),

                Forms\Components\Section::make('معلومات إضافية')
                    ->schema([
                        Forms\Components\TextInput::make('slug')
                            ->label('الرابط الفريد')
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->helperText('سيتم إنشاؤه تلقائياً إذا ترك فارغاً'),

                        Forms\Components\TagsInput::make('tags')
                            ->label('العلامات')
                            ->helperText('أضف علامات لتسهيل البحث'),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question_ar')
                    ->label('السؤال')
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\SelectColumn::make('category')
                    ->label('التصنيف')
                    ->options([
                        'general' => 'عام',
                        'products' => 'المنتجات',
                        'orders' => 'الطلبات',
                        'shipping' => 'الشحن',
                        'returns' => 'الإرجاع والاستبدال',
                        'payment' => 'الدفع',
                        'warranty' => 'الضمان',
                        'care' => 'العناية بالمجوهرات',
                        'custom' => 'التصميم المخصص',
                        'sizing' => 'المقاسات',
                    ])
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('الترتيب')
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_featured')
                    ->label('مميز')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('views')
                    ->label('المشاهدات')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('helpful_votes')
                    ->label('الأصوات المفيدة')
                    ->numeric()
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->label('التصنيف')
                    ->options([
                        'general' => 'عام',
                        'products' => 'المنتجات',
                        'orders' => 'الطلبات',
                        'shipping' => 'الشحن',
                        'returns' => 'الإرجاع والاستبدال',
                        'payment' => 'الدفع',
                        'warranty' => 'الضمان',
                        'care' => 'العناية بالمجوهرات',
                        'custom' => 'التصميم المخصص',
                        'sizing' => 'المقاسات',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->placeholder('الكل')
                    ->trueLabel('نشط')
                    ->falseLabel('غير نشط'),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('مميز')
                    ->placeholder('الكل')
                    ->trueLabel('مميز')
                    ->falseLabel('غير مميز'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('تفعيل المحدد')
                        ->icon('heroicon-o-check-circle')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                        })
                        ->deselectRecordsAfterCompletion(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('إلغاء تفعيل المحدد')
                        ->icon('heroicon-o-x-circle')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        })
                        ->deselectRecordsAfterCompletion(),
                ]),
            ])
            ->defaultSort('sort_order', 'asc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFaqs::route('/'),
            'create' => Pages\CreateFaq::route('/create'),
            'edit' => Pages\EditFaq::route('/{record}/edit'),
        ];
    }
}
