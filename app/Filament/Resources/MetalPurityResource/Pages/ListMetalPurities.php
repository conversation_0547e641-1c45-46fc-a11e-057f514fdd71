<?php

namespace App\Filament\Resources\MetalPurityResource\Pages;

use App\Filament\Resources\MetalPurityResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMetalPurities extends ListRecords
{
    protected static string $resource = MetalPurityResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
