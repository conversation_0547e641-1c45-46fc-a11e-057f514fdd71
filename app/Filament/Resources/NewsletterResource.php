<?php

namespace App\Filament\Resources;

use App\Filament\Resources\NewsletterResource\Pages;
use App\Filament\Resources\NewsletterResource\RelationManagers;
use App\Models\Newsletter;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Traits\HasPermissionFiltering;
use Illuminate\Support\Collection;

class NewsletterResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Newsletter::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static ?string $navigationLabel = 'النشرة البريدية';

    protected static ?string $navigationGroup = 'إدارة الطلبات والعملاء';

    protected static ?int $navigationSort = 34;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المشترك')
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->label('البريد الإلكتروني')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),

                        Forms\Components\TextInput::make('name')
                            ->label('الاسم')
                            ->maxLength(255),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true)
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('معلومات الاشتراك')
                    ->schema([
                        Forms\Components\DateTimePicker::make('subscribed_at')
                            ->label('تاريخ الاشتراك')
                            ->default(now())
                            ->required(),

                        Forms\Components\DateTimePicker::make('unsubscribed_at')
                            ->label('تاريخ إلغاء الاشتراك'),

                        Forms\Components\DateTimePicker::make('created_at')
                            ->label('تاريخ الإنشاء')
                            ->disabled()
                            ->dehydrated(false)
                            ->hiddenOn('create'),

                        Forms\Components\DateTimePicker::make('updated_at')
                            ->label('تاريخ التحديث')
                            ->disabled()
                            ->dehydrated(false)
                            ->hiddenOn('create'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('email')
                    ->label('البريد الإلكتروني')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name')
                    ->label('الاسم')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subscribed_at')
                    ->label('تاريخ الاشتراك')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('unsubscribed_at')
                    ->label('تاريخ إلغاء الاشتراك')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('الحالة')
                    ->options([
                        '1' => 'نشط',
                        '0' => 'غير نشط',
                    ]),

                Tables\Filters\Filter::make('subscribed_at')
                    ->label('تاريخ الاشتراك')
                    ->form([
                        Forms\Components\DatePicker::make('subscribed_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('subscribed_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['subscribed_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('subscribed_at', '>=', $date),
                            )
                            ->when(
                                $data['subscribed_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('subscribed_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('toggle_active')
                    ->label(fn (Newsletter $record): string => $record->is_active ? 'إلغاء تنشيط' : 'تنشيط')
                    ->icon(fn (Newsletter $record): string => $record->is_active ? 'heroicon-o-x-circle' : 'heroicon-o-check-circle')
                    ->color(fn (Newsletter $record): string => $record->is_active ? 'danger' : 'success')
                    ->action(function (Newsletter $record): void {
                        $record->update([
                            'is_active' => !$record->is_active,
                            'unsubscribed_at' => $record->is_active ? now() : null,
                        ]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('تنشيط')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true, 'unsubscribed_at' => null])),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('إلغاء تنشيط')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false, 'unsubscribed_at' => now()])),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNewsletters::route('/'),
            'create' => Pages\CreateNewsletter::route('/create'),
            'edit' => Pages\EditNewsletter::route('/{record}/edit'),
        ];
    }
}
