<?php

namespace App\Filament\Resources\CompanyProductResource\Pages;

use App\Filament\Resources\CompanyProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCompanyProducts extends ListRecords
{
    protected static string $resource = CompanyProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
