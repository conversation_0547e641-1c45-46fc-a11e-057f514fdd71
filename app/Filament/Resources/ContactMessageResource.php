<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactMessageResource\Pages;
use App\Models\ContactMessage;
use App\Traits\HasPermissionFiltering;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactReply;

class ContactMessageResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = ContactMessage::class;

    protected static ?string $modelLabel = 'رسالة اتصال';

    protected static ?string $pluralModelLabel = 'رسائل الاتصال';

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static ?string $navigationGroup = 'إدارة الطلبات والعملاء';

    protected static ?int $navigationSort = 32;

    /**
     * عرض عدد الرسائل الجديدة في القائمة الجانبية
     */
    public static function getNavigationBadge(): ?string
    {
        // التحقق من صلاحية الوصول أولاً
        if (!Auth::user()?->can('view_any_contact::message')) {
            return null;
        }

        $newMessagesCount = ContactMessage::where('status', 'new')->count();

        return $newMessagesCount > 0 ? (string) $newMessagesCount : null;
    }

    /**
     * لون الـ badge في القائمة الجانبية
     */
    public static function getNavigationBadgeColor(): ?string
    {
        $newMessagesCount = ContactMessage::where('status', 'new')->count();

        return $newMessagesCount > 0 ? 'warning' : null;
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المرسل')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('الاسم')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('email')
                            ->label('البريد الإلكتروني')
                            ->email()
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('phone')
                            ->label('رقم الهاتف')
                            ->tel()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('subject')
                            ->label('الموضوع')
                            ->required()
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('محتوى الرسالة')
                    ->schema([
                        Forms\Components\Textarea::make('message')
                            ->label('الرسالة')
                            ->required()
                            ->rows(5)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('حالة الرسالة')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->label('الحالة')
                            ->options([
                                'new' => 'جديدة',
                                'read' => 'مقروءة',
                                'replied' => 'تم الرد',
                            ])
                            ->required()
                            ->default('new'),

                        Forms\Components\DateTimePicker::make('read_at')
                            ->label('تاريخ القراءة')
                            ->displayFormat('Y-m-d H:i')
                            ->timezone('Africa/Cairo'),
                    ])->columns(2),

                Forms\Components\Section::make('الرد على الرسالة')
                    ->schema([
                        Forms\Components\RichEditor::make('reply_message')
                            ->label('رسالة الرد')
                            ->columnSpanFull(),

                        Forms\Components\DateTimePicker::make('replied_at')
                            ->label('تاريخ الرد')
                            ->displayFormat('Y-m-d H:i')
                            ->timezone('Africa/Cairo'),

                        Forms\Components\Select::make('replied_by')
                            ->label('تم الرد بواسطة')
                            ->relationship('repliedBy', 'name')
                            ->searchable()
                            ->preload(),
                    ])->columns(2)
                    ->visible(fn ($record) => $record && $record->status === 'replied'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('الاسم')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('البريد الإلكتروني')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('subject')
                    ->label('الموضوع')
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        return strlen($state) > 50 ? $state : null;
                    }),

                Tables\Columns\TextColumn::make('status')
                    ->label('الحالة')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'new' => 'warning',
                        'read' => 'info',
                        'replied' => 'success',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'new' => 'جديدة',
                        'read' => 'مقروءة',
                        'replied' => 'تم الرد',
                        default => $state,
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإرسال')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('replied_at')
                    ->label('تاريخ الرد')
                    ->dateTime('Y-m-d H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('repliedBy.name')
                    ->label('تم الرد بواسطة')
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('الحالة')
                    ->options([
                        'new' => 'جديدة',
                        'read' => 'مقروءة',
                        'replied' => 'تم الرد',
                    ]),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض')
                    ->visible(fn () => Auth::user()?->can('view_any_contact::message') ?? false),

                Tables\Actions\Action::make('mark_as_read')
                    ->label('تحديد كمقروءة')
                    ->icon('heroicon-o-eye')
                    ->color('info')
                    ->action(function (ContactMessage $record) {
                        $record->markAsRead();
                        Notification::make()
                            ->title('تم تحديد الرسالة كمقروءة')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (ContactMessage $record) =>
                        $record->status === 'new' &&
                        (Auth::user()?->can('update_contact::message') ?? false)
                    ),

                Tables\Actions\Action::make('reply')
                    ->label('رد')
                    ->icon('heroicon-o-chat-bubble-left-right')
                    ->color('success')
                    ->form([
                        Forms\Components\RichEditor::make('reply_message')
                            ->label('رسالة الرد')
                            ->required(),
                    ])
                    ->action(function (ContactMessage $record, array $data) {
                        $record->markAsReplied($data['reply_message'], Auth::id());

                        // إرسال الرد عبر البريد الإلكتروني
                        try {
                            Mail::to($record->email)->send(new ContactReply($record));
                            Notification::make()
                                ->title('تم إرسال الرد بنجاح')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('تم حفظ الرد ولكن فشل في الإرسال')
                                ->body('خطأ: ' . $e->getMessage())
                                ->warning()
                                ->send();
                        }
                    })
                    ->visible(fn (ContactMessage $record) =>
                        $record->status !== 'replied' &&
                        (Auth::user()?->can('reply_contact::message') ?? false)
                    ),

                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->visible(fn () => Auth::user()?->can('update_contact::message') ?? false),

                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->visible(fn () => Auth::user()?->can('delete_contact::message') ?? false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('mark_as_read')
                        ->label('تحديد كمقروءة')
                        ->icon('heroicon-o-eye')
                        ->color('info')
                        ->action(function ($records) {
                            $records->each->markAsRead();
                            Notification::make()
                                ->title('تم تحديد الرسائل كمقروءة')
                                ->success()
                                ->send();
                        })
                        ->visible(fn () => Auth::user()?->can('update_contact::message') ?? false),

                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->visible(fn () => Auth::user()?->can('delete_contact::message') ?? false),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContactMessages::route('/'),
            'create' => Pages\CreateContactMessage::route('/create'),
            'edit' => Pages\EditContactMessage::route('/{record}/edit'),
        ];
    }
}
