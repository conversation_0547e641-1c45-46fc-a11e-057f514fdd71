<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TermsPageResource\Pages;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class TermsPageResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = SiteSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationLabel = 'الشروط والأحكام';

    protected static ?string $modelLabel = 'الشروط والأحكام';

    protected static ?string $pluralModelLabel = 'الشروط والأحكام';

    protected static ?string $navigationGroup = 'الصفحات القانونية';

    protected static ?int $navigationSort = 24;

    protected static bool $shouldRegisterNavigation = true;

    public static function canDelete($record): bool
    {
        return false; // لا يمكن حذف إعدادات الموقع
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('محتوى الشروط والأحكام')
                    ->description('إدارة محتوى صفحة الشروط والأحكام')
                    ->schema([
                        Forms\Components\Tabs::make('اللغات')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('العربية')
                                    ->schema([
                                        Forms\Components\TextInput::make('terms_title_ar')
                                            ->label('عنوان الصفحة بالعربية')
                                            ->default('الشروط والأحكام')
                                            ->required()
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('terms_content_ar')
                                            ->label('محتوى الشروط والأحكام بالعربية')
                                            ->required()
                                            ->columnSpanFull()
                                            ->toolbarButtons([
                                                'bold',
                                                'italic',
                                                'underline',
                                                'strike',
                                                'link',
                                                'heading',
                                                'subheading',
                                                'bulletList',
                                                'orderedList',
                                                'blockquote',
                                                'codeBlock',
                                            ]),

                                        Forms\Components\Textarea::make('terms_meta_description_ar')
                                            ->label('وصف الصفحة للمحركات البحث (عربي)')
                                            ->maxLength(160)
                                            ->rows(3),
                                    ]),

                                Forms\Components\Tabs\Tab::make('الإنجليزية')
                                    ->schema([
                                        Forms\Components\TextInput::make('terms_title_en')
                                            ->label('عنوان الصفحة بالإنجليزية')
                                            ->default('Terms and Conditions')
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('terms_content_en')
                                            ->label('محتوى الشروط والأحكام بالإنجليزية')
                                            ->columnSpanFull()
                                            ->toolbarButtons([
                                                'bold',
                                                'italic',
                                                'underline',
                                                'strike',
                                                'link',
                                                'heading',
                                                'subheading',
                                                'bulletList',
                                                'orderedList',
                                                'blockquote',
                                                'codeBlock',
                                            ]),

                                        Forms\Components\Textarea::make('terms_meta_description_en')
                                            ->label('وصف الصفحة للمحركات البحث (إنجليزي)')
                                            ->maxLength(160)
                                            ->rows(3),
                                    ]),
                            ]),
                    ]),

                Forms\Components\Section::make('إعدادات إضافية')
                    ->schema([
                        Forms\Components\TextInput::make('terms_meta_keywords')
                            ->label('الكلمات المفتاحية')
                            ->placeholder('الشروط والأحكام، قوانين الاستخدام، سياسة الإرجاع')
                            ->helperText('افصل الكلمات بفواصل'),

                        Forms\Components\DateTimePicker::make('terms_last_updated')
                            ->label('تاريخ آخر تحديث')
                            ->default(now())
                            ->displayFormat('Y-m-d H:i'),

                        Forms\Components\Toggle::make('terms_show_last_updated')
                            ->label('إظهار تاريخ آخر تحديث')
                            ->default(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('site_name')
                    ->label('اسم الموقع')
                    ->searchable(),

                Tables\Columns\TextColumn::make('terms_title_ar')
                    ->label('عنوان الشروط والأحكام')
                    ->limit(50),

                Tables\Columns\IconColumn::make('terms_show_last_updated')
                    ->label('إظهار تاريخ التحديث')
                    ->boolean(),

                Tables\Columns\TextColumn::make('terms_last_updated')
                    ->label('آخر تحديث')
                    ->dateTime('Y-m-d H:i'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التعديل')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('preview')
                    ->label('معاينة الصفحة')
                    ->icon('heroicon-o-eye')
                    ->url(route('terms'))
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make()
                    ->label('تحرير'),
            ])
            ->bulkActions([
                // لا توجد إجراءات جماعية لإعدادات الموقع
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageTermsPages::route('/'),
            'edit' => Pages\EditTermsPage::route('/edit'),
        ];
    }
}
