<?php

namespace App\Filament\Resources\CategoryResource\Pages;

use App\Filament\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('عرض'),
            Actions\DeleteAction::make()
                ->label('حذف')
                ->before(function () {
                    if ($this->record->products()->count() > 0) {
                        throw new \Exception('لا يمكن حذف الفئة لأنها تحتوي على منتجات مرتبطة بها.');
                    }
                    if ($this->record->children()->count() > 0) {
                        throw new \Exception('لا يمكن حذف الفئة لأنها تحتوي على فئات فرعية.');
                    }
                }),
        ];
    }

    public function getTitle(): string
    {
        return 'تعديل فئة المنتج';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'تم تحديث الفئة بنجاح';
    }
}
