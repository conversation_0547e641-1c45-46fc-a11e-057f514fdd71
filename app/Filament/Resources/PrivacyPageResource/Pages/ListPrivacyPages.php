<?php

namespace App\Filament\Resources\PrivacyPageResource\Pages;

use App\Filament\Resources\PrivacyPageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPrivacyPages extends ListRecords
{
    protected static string $resource = PrivacyPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
