<?php

namespace App\Filament\Resources\PrivacyPageResource\Pages;

use App\Filament\Resources\PrivacyPageResource;
use App\Models\SiteSetting;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPrivacyPage extends EditRecord
{
    protected static string $resource = PrivacyPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('معاينة الصفحة')
                ->icon('heroicon-o-eye')
                ->url(route('privacy'))
                ->openUrlInNewTab(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // التأكد من أن البيانات تأتي من السجل الأول
        $setting = SiteSetting::first();
        return $setting ? $setting->toArray() : $data;
    }

    protected function handleRecordUpdate($record, array $data): \Illuminate\Database\Eloquent\Model
    {
        // تحديث السجل الأول من site_settings
        $setting = SiteSetting::first();
        if ($setting) {
            $setting->update($data);
            return $setting;
        }

        return SiteSetting::create($data);
    }

    public function mount(int | string $record = null): void
    {
        // استخدام السجل الأول من site_settings
        $setting = SiteSetting::first();
        if (!$setting) {
            $setting = SiteSetting::create([
                'site_name' => 'مجوهرات مكة جولد',
                'privacy_title_ar' => 'سياسة الخصوصية',
                'privacy_title_en' => 'Privacy Policy',
            ]);
        }

        $this->record = $setting;
        $this->fillForm();
    }
}
