<?php

namespace App\Filament\Resources\PrivacyPageResource\Pages;

use App\Filament\Resources\PrivacyPageResource;
use App\Models\SiteSetting;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManagePrivacyPages extends ManageRecords
{
    protected static string $resource = PrivacyPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('معاينة الصفحة')
                ->icon('heroicon-o-eye')
                ->url(route('privacy'))
                ->openUrlInNewTab(),
                
            Actions\Action::make('edit')
                ->label('تحرير سياسة الخصوصية')
                ->icon('heroicon-o-pencil')
                ->url(fn () => PrivacyPageResource::getUrl('edit')),
        ];
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        // إرجاع السجل الأول من site_settings فقط
        return SiteSetting::query()->limit(1);
    }
}
