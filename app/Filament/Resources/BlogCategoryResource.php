<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BlogCategoryResource\Pages;
use App\Models\BlogCategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class BlogCategoryResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = BlogCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?int $navigationSort = 31;

    protected static ?string $navigationGroup = 'إدارة المدونة';

    public static function getNavigationGroup(): string
    {
        return __('إدارة المدونة');
    }

    public static function getNavigationLabel(): string
    {
        return 'تصنيفات المدونة';
    }

    public static function getModelLabel(): string
    {
        return __('تصنيف مدونة');
    }

    public static function getPluralModelLabel(): string
    {
        return __('تصنيفات المدونة');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('المعلومات الأساسية')
                            ->schema([
                                Forms\Components\Tabs::make('اللغات')
                                    ->tabs([
                                        Forms\Components\Tabs\Tab::make('العربية')
                                            ->schema([
                                                Forms\Components\TextInput::make('name_ar')
                                                    ->label('الاسم بالعربية')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                                                        $operation === 'create' ? $set('slug', \Illuminate\Support\Str::slug($state)) : null),

                                                Forms\Components\RichEditor::make('description_ar')
                                                    ->label('الوصف بالعربية')
                                                    ->columnSpanFull(),
                                            ]),

                                        Forms\Components\Tabs\Tab::make('English')
                                            ->schema([
                                                Forms\Components\TextInput::make('name_en')
                                                    ->label('Name in English')
                                                    ->maxLength(255),

                                                Forms\Components\RichEditor::make('description_en')
                                                    ->label('Description in English')
                                                    ->columnSpanFull(),
                                            ]),
                                    ])
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('slug')
                                    ->label('المعرف الفريد (Slug)')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->rules(['alpha_dash']),
                            ])
                            ->columns(2),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('الإعدادات')
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true),

                                Forms\Components\TextInput::make('sort_order')
                                    ->label('ترتيب العرض')
                                    ->numeric()
                                    ->default(0)
                                    ->minValue(0),
                            ]),

                        Forms\Components\Section::make('الإحصائيات')
                            ->schema([
                                Forms\Components\Placeholder::make('posts_count')
                                    ->label('عدد المقالات')
                                    ->content(fn (BlogCategory $record): string => $record->posts()->count() . ' مقال'),

                                Forms\Components\Placeholder::make('created_at')
                                    ->label('تاريخ الإنشاء')
                                    ->content(fn (BlogCategory $record): string => $record->created_at?->format('Y-m-d H:i:s') ?? '-'),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('تاريخ التحديث')
                                    ->content(fn (BlogCategory $record): string => $record->updated_at?->format('Y-m-d H:i:s') ?? '-'),
                            ])
                            ->hiddenOn('create'),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('الاسم بالعربية')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('الاسم بالإنجليزية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('المعرف الفريد')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('تم نسخ المعرف الفريد')
                    ->fontFamily('mono'),

                Tables\Columns\TextColumn::make('posts_count')
                    ->label('عدد المقالات')
                    ->counts('posts')
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('الترتيب')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('sort_order')
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->placeholder('جميع التصنيفات')
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->before(function (BlogCategory $record) {
                        // التحقق من وجود مقالات مرتبطة
                        if ($record->posts()->count() > 0) {
                            throw new \Exception('لا يمكن حذف التصنيف لأنه يحتوي على مقالات مرتبطة به.');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                if ($record->posts()->count() > 0) {
                                    throw new \Exception('لا يمكن حذف بعض التصنيفات لأنها تحتوي على مقالات مرتبطة بها.');
                                }
                            }
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlogCategories::route('/'),
            'create' => Pages\CreateBlogCategory::route('/create'),
            'view' => Pages\ViewBlogCategory::route('/{record}'),
            'edit' => Pages\EditBlogCategory::route('/{record}/edit'),
        ];
    }
}
