<?php

namespace App\Filament\Resources\MetalTypeResource\Pages;

use App\Filament\Resources\MetalTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMetalTypes extends ListRecords
{
    protected static string $resource = MetalTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
