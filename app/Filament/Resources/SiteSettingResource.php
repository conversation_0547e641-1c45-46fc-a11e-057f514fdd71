<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SiteSettingResource\Pages;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Tabs;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use App\Traits\HasPermissionFiltering;

class SiteSettingResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = SiteSetting::class;
    protected static ?string $navigationIcon = 'heroicon-o-cog';
    protected static ?string $navigationLabel = 'إعدادات الموقع';
    protected static ?string $navigationGroup = 'إعدادات النظام';
    protected static ?int $navigationSort = 72;

    public static function getModelLabel(): string
    {
        return __('إعدادات الموقع');
    }

    public static function getPluralModelLabel(): string
    {
        return __('إعدادات الموقع');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('الإعدادات')
                    ->tabs([
                        // التبويب الأول: معلومات الموقع والشركة
                        Tabs\Tab::make('معلومات الموقع والشركة')
                            ->icon('heroicon-o-building-office')
                            ->schema([
                                Section::make('المعلومات الأساسية')
                                    ->description('معلومات الموقع الأساسية والهوية البصرية')
                                    ->schema([
                                        TextInput::make('site_name')
                                            ->label('اسم الموقع')
                                            ->required()
                                            ->maxLength(255)
                                            ->helperText('اسم الموقع الذي سيظهر في جميع الصفحات'),
                                        Textarea::make('site_description')
                                            ->label('وصف الموقع')
                                            ->rows(3)
                                            ->maxLength(500)
                                            ->helperText('وصف مختصر للموقع يظهر في محركات البحث'),
                                        FileUpload::make('logo')
                                            ->label('شعار الموقع')
                                            ->image()
                                            ->disk('public')
                                            ->directory('site-settings')
                                            ->visibility('public')
                                            ->imageResizeMode('contain') // الحفاظ على النسب الأصلية
                                            ->imageResizeTargetWidth('800') // حجم أكبر لجودة أفضل
                                            ->imageResizeTargetHeight('400') // نسبة 2:1 مرنة
                                            ->imagePreviewHeight('120') // معاينة أكبر
                                            ->loadingIndicatorPosition('left')
                                            ->panelLayout('integrated')
                                            ->removeUploadedFileButtonPosition('right')
                                            ->uploadButtonPosition('left')
                                            ->uploadProgressIndicatorPosition('left')
                                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'])
                                            ->maxSize(5120) // 5MB للجودة العالية
                                            ->helperText('سيتم الحفاظ على النسب الأصلية للشعار. يُفضل استخدام صور عالية الجودة. الحد الأقصى: 5MB. يدعم SVG للشعارات المتجهة.'),
                                        FileUpload::make('favicon')
                                            ->label('أيقونة الموقع (Favicon)')
                                            ->image()
                                            ->disk('public')
                                            ->directory('site-settings')
                                            ->visibility('public')
                                            ->imageResizeMode('contain')
                                            ->imageCropAspectRatio('1:1')
                                            ->imageResizeTargetWidth('32')
                                            ->imageResizeTargetHeight('32')
                                            ->imagePreviewHeight('50')
                                            ->loadingIndicatorPosition('left')
                                            ->panelAspectRatio('1:1')
                                            ->panelLayout('integrated')
                                            ->removeUploadedFileButtonPosition('right')
                                            ->uploadButtonPosition('left')
                                            ->uploadProgressIndicatorPosition('left')
                                            ->helperText('الحجم المفضل: 32x32 بكسل. ستظهر في تبويب المتصفح.'),
                                    ])->columns(2),

                                Section::make('معلومات الاتصال')
                                    ->description('بيانات التواصل مع العملاء')
                                    ->schema([
                                        TextInput::make('contact_email')
                                            ->label('البريد الإلكتروني للتواصل')
                                            ->email()
                                            ->helperText('البريد الإلكتروني الرئيسي للتواصل مع العملاء'),
                                        TextInput::make('contact_phone')
                                            ->label('رقم الهاتف للتواصل')
                                            ->tel()
                                            ->helperText('رقم الهاتف الرئيسي للتواصل'),
                                        Textarea::make('address')
                                            ->label('العنوان')
                                            ->rows(3)
                                            ->helperText('العنوان الكامل للشركة أو المتجر'),
                                        TextInput::make('whatsapp_number')
                                            ->label('رقم واتساب')
                                            ->tel()
                                            ->helperText('رقم واتساب للتواصل السريع مع العملاء'),
                                    ])->columns(2),

                                Section::make('المقر الرئيسي')
                                    ->description('معلومات المقر الرئيسي للشركة')
                                    ->schema([
                                        Textarea::make('headquarters_address_ar')
                                            ->label('عنوان المقر الرئيسي (عربي)')
                                            ->rows(2)
                                            ->helperText('العنوان الكامل للمقر الرئيسي باللغة العربية'),
                                        Textarea::make('headquarters_address_en')
                                            ->label('عنوان المقر الرئيسي (إنجليزي)')
                                            ->rows(2)
                                            ->helperText('العنوان الكامل للمقر الرئيسي باللغة الإنجليزية'),
                                        TextInput::make('map_latitude')
                                            ->label('خط العرض (Latitude)')
                                            ->numeric()
                                            ->step(0.0000001)
                                            ->helperText('إحداثي خط العرض للمقر الرئيسي (مثال: 30.0444)'),
                                        TextInput::make('map_longitude')
                                            ->label('خط الطول (Longitude)')
                                            ->numeric()
                                            ->step(0.0000001)
                                            ->helperText('إحداثي خط الطول للمقر الرئيسي (مثال: 31.2357)'),
                                        TextInput::make('map_zoom')
                                            ->label('مستوى التكبير في الخريطة')
                                            ->numeric()
                                            ->default(15)
                                            ->minValue(1)
                                            ->maxValue(20)
                                            ->helperText('مستوى التكبير الافتراضي للخريطة (1-20)'),
                                        TextInput::make('map_marker_title')
                                            ->label('عنوان العلامة في الخريطة')
                                            ->helperText('النص الذي يظهر عند النقر على العلامة في الخريطة'),
                                    ])->columns(2),

                                Section::make('ساعات العمل')
                                    ->description('ساعات العمل الرسمية للشركة')
                                    ->schema([
                                        Textarea::make('working_hours_ar')
                                            ->label('ساعات العمل (عربي)')
                                            ->rows(4)
                                            ->helperText('ساعات العمل باللغة العربية (مثال: السبت - الخميس: 9:00 ص - 10:00 م)')
                                            ->placeholder("السبت - الخميس: 9:00 ص - 10:00 م\nالجمعة: 2:00 م - 10:00 م"),
                                        Textarea::make('working_hours_en')
                                            ->label('ساعات العمل (إنجليزي)')
                                            ->rows(4)
                                            ->helperText('ساعات العمل باللغة الإنجليزية')
                                            ->placeholder("Saturday - Thursday: 9:00 AM - 10:00 PM\nFriday: 2:00 PM - 10:00 PM"),
                                    ])->columns(2),

                                Section::make('وسائل التواصل الاجتماعي')
                                    ->description('روابط حسابات التواصل الاجتماعي')
                                    ->schema([
                                        TextInput::make('facebook_url')
                                            ->label('رابط فيسبوك')
                                            ->url()
                                            ->prefix('https://')
                                            ->placeholder('facebook.com/yourpage'),
                                        TextInput::make('instagram_url')
                                            ->label('رابط انستغرام')
                                            ->url()
                                            ->prefix('https://')
                                            ->placeholder('instagram.com/yourpage'),
                                        TextInput::make('twitter_url')
                                            ->label('رابط تويتر')
                                            ->url()
                                            ->prefix('https://')
                                            ->placeholder('twitter.com/yourpage'),
                                        TextInput::make('youtube_url')
                                            ->label('رابط يوتيوب')
                                            ->url()
                                            ->prefix('https://')
                                            ->placeholder('youtube.com/yourchannel'),
                                        TextInput::make('tiktok_url')
                                            ->label('رابط تيك توك')
                                            ->url()
                                            ->prefix('https://')
                                            ->placeholder('tiktok.com/@yourpage'),
                                        TextInput::make('linkedin_url')
                                            ->label('رابط لينكد إن')
                                            ->url()
                                            ->prefix('https://')
                                            ->placeholder('linkedin.com/company/yourcompany'),
                                    ])->columns(3),

                                // Section::make('أسعار المعادن')
                                //     ->description('أسعار الذهب والمعادن الثمينة')
                                //     ->schema([
                                //         TextInput::make('gold_price_24k')
                                //             ->label('سعر الذهب عيار 24')
                                //             ->numeric()
                                //             ->prefix('ج.م')
                                //             ->step(0.01)
                                //             ->helperText('سعر الجرام للذهب عيار 24'),
                                //         TextInput::make('gold_price_21k')
                                //             ->label('سعر الذهب عيار 21')
                                //             ->numeric()
                                //             ->prefix('ج.م')
                                //             ->step(0.01)
                                //             ->helperText('سعر الجرام للذهب عيار 21'),
                                //         TextInput::make('gold_price_18k')
                                //             ->label('سعر الذهب عيار 18')
                                //             ->numeric()
                                //             ->prefix('ج.م')
                                //             ->step(0.01)
                                //             ->helperText('سعر الجرام للذهب عيار 18'),
                                //     ])->columns(3),
                            ]),

                        // التبويب الثاني: إعدادات المتجر والتشغيل
                        Tabs\Tab::make('إعدادات المتجر والتشغيل')
                            ->icon('heroicon-o-cog-6-tooth')
                            ->schema([

                                Section::make('إعدادات الصفحة الرئيسية')
                                    ->description('تحكم في عرض الأقسام المختلفة في الصفحة الرئيسية')
                                    ->schema([
                                        Forms\Components\Toggle::make('show_featured_products')
                                            ->label('عرض المنتجات المميزة')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_new_arrivals')
                                            ->label('عرض المنتجات الجديدة')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_categories')
                                            ->label('عرض الفئات')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_gold_prices')
                                            ->label('عرض أسعار الذهب')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_silver_prices')
                                            ->label('عرض أسعار الفضة')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_features')
                                            ->label('عرض ميزات الموقع')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_testimonials')
                                            ->label('عرض آراء العملاء')
                                            ->default(true),
                                        Forms\Components\Toggle::make('show_newsletter')
                                            ->label('عرض الاشتراك في النشرة البريدية')
                                            ->default(true),
                                    ])->columns(3),

                            ]),

                        // التبويب الثالث: التسويق و SEO
                        Tabs\Tab::make('التسويق و SEO')
                            ->icon('heroicon-o-megaphone')
                            ->schema([
                                Section::make('تهيئة محركات البحث (SEO)')
                                    ->description('إعدادات تحسين الموقع لمحركات البحث')
                                    ->schema([
                                        TextInput::make('meta_title')
                                            ->label('عنوان الميتا')
                                            ->maxLength(60)
                                            ->helperText('عنوان الصفحة الذي يظهر في نتائج البحث (الحد الأقصى 60 حرف)'),
                                        Textarea::make('meta_description')
                                            ->label('وصف الميتا')
                                            ->helperText('وصف الموقع الذي يظهر في نتائج البحث (الحد الأقصى 160 حرف)')
                                            ->rows(3)
                                            ->maxLength(160),
                                        Textarea::make('meta_keywords')
                                            ->label('الكلمات المفتاحية')
                                            ->helperText('الكلمات المفتاحية مفصولة بفواصل')
                                            ->rows(2)
                                            ->placeholder('مجوهرات, ذهب, فضة, خواتم, أساور'),
                                    ])->columns(1),

                                Section::make('خدمات الخرائط والموقع')
                                    ->description('إعدادات Google Maps وخدمات الموقع')
                                    ->schema([
                                        TextInput::make('google_maps_api_key')
                                            ->label('Google Maps API Key')
                                            ->password()
                                            ->revealable()
                                            ->helperText('مفتاح API الخاص بـ Google Maps لعرض الخرائط في صفحة اتصل بنا')
                                            ->placeholder('AIzaSyBNLrJhOMz6idD05pzfn5lhA-TAw-mAZCU'),
                                        Forms\Components\Toggle::make('show_map')
                                            ->label('عرض الخريطة في صفحة اتصل بنا')
                                            ->default(true)
                                            ->helperText('تفعيل أو إلغاء تفعيل عرض خريطة Google Maps في صفحة اتصل بنا'),
                                    ])->columns(2),

                                Section::make('أدوات التحليلات والتتبع')
                                    ->description('إعدادات أدوات التحليلات وتتبع الزوار')
                                    ->schema([
                                        Forms\Components\TextInput::make('google_analytics_id')
                                            ->label('معرف Google Analytics')
                                            ->helperText('مثال: G-XXXXXXXXXX أو UA-XXXXXXXX-X')
                                            ->placeholder('G-XXXXXXXXXX'),
                                        Forms\Components\TextInput::make('facebook_pixel_id')
                                            ->label('معرف Facebook Pixel')
                                            ->helperText('مثال: XXXXXXXXXXXXXXXXXX')
                                            ->placeholder('123456789012345678'),
                                        Forms\Components\TextInput::make('google_tag_manager_id')
                                            ->label('معرف Google Tag Manager')
                                            ->helperText('مثال: GTM-XXXXXXX')
                                            ->placeholder('GTM-XXXXXXX'),
                                    ])->columns(3),

                                Section::make('النصوص البرمجية المخصصة')
                                    ->description('إضافة نصوص برمجية مخصصة للموقع')
                                    ->schema([
                                        Forms\Components\Textarea::make('custom_header_scripts')
                                            ->label('نصوص برمجية مخصصة في الرأس')
                                            ->helperText('سيتم إضافة هذه النصوص البرمجية في رأس الصفحة (قبل </head>)')
                                            ->rows(4)
                                            ->placeholder('<script>
// كود JavaScript مخصص
</script>'),
                                        Forms\Components\Textarea::make('custom_footer_scripts')
                                            ->label('نصوص برمجية مخصصة في التذييل')
                                            ->helperText('سيتم إضافة هذه النصوص البرمجية في نهاية الصفحة (قبل </body>)')
                                            ->rows(4)
                                            ->placeholder('<script>
// كود JavaScript مخصص
</script>'),
                                    ])->columns(2),

                                Section::make('إعدادات البريد الإلكتروني')
                                    ->description('إعدادات خادم البريد الإلكتروني لإرسال الرسائل')
                                    ->schema([
                                        Forms\Components\TextInput::make('mail_from_address')
                                            ->label('عنوان المرسل')
                                            ->email()
                                            ->helperText('عنوان البريد الإلكتروني الذي سيظهر كمرسل للرسائل')
                                            ->placeholder('<EMAIL>'),
                                        Forms\Components\TextInput::make('mail_from_name')
                                            ->label('اسم المرسل')
                                            ->helperText('الاسم الذي سيظهر كمرسل للرسائل')
                                            ->placeholder('مكة جولد للمجوهرات'),
                                        Forms\Components\TextInput::make('mail_host')
                                            ->label('خادم SMTP')
                                            ->helperText('مثال: smtp.gmail.com')
                                            ->placeholder('smtp.gmail.com'),
                                        Forms\Components\TextInput::make('mail_port')
                                            ->label('منفذ SMTP')
                                            ->numeric()
                                            ->helperText('مثال: 587')
                                            ->placeholder('587'),
                                        Forms\Components\TextInput::make('mail_username')
                                            ->label('اسم المستخدم')
                                            ->helperText('عادة ما يكون عنوان البريد الإلكتروني الكامل')
                                            ->placeholder('<EMAIL>'),
                                        Forms\Components\TextInput::make('mail_password')
                                            ->label('كلمة المرور')
                                            ->password()
                                            ->dehydrateStateUsing(fn ($state) => $state ? $state : null)
                                            ->helperText('اتركه فارغًا إذا كنت لا تريد تغييره'),
                                        Forms\Components\Select::make('mail_encryption')
                                            ->label('التشفير')
                                            ->options([
                                                'tls' => 'TLS',
                                                'ssl' => 'SSL',
                                                '' => 'بدون تشفير',
                                            ])
                                            ->default('tls')
                                            ->helperText('عادة ما يكون TLS'),
                                    ])->columns(2),

                                Section::make('نص التذييل')
                                    ->description('النص الذي يظهر في تذييل الموقع')
                                    ->schema([
                                        Textarea::make('footer_text')
                                            ->label('نص تذييل الصفحة')
                                            ->rows(3)
                                            ->placeholder('&copy; 2024 مكة جولد للمجوهرات. جميع الحقوق محفوظة.')
                                            ->helperText('النص الذي سيظهر في أسفل جميع صفحات الموقع'),
                                    ])->columns(1),
                            ]),

                        // Tabs\Tab::make('النصوص القانونية')
                        //     ->schema([
                        //         Forms\Components\RichEditor::make('privacy_policy')
                        //             ->label('سياسة الخصوصية')
                        //             ->toolbarButtons([
                        //                 'blockquote',
                        //                 'bold',
                        //                 'bulletList',
                        //                 'h2',
                        //                 'h3',
                        //                 'italic',
                        //                 'link',
                        //                 'orderedList',
                        //                 'redo',
                        //                 'strike',
                        //                 'underline',
                        //                 'undo',
                        //             ]),
                        //         Forms\Components\RichEditor::make('terms_conditions')
                        //             ->label('الشروط والأحكام')
                        //             ->toolbarButtons([
                        //                 'blockquote',
                        //                 'bold',
                        //                 'bulletList',
                        //                 'h2',
                        //                 'h3',
                        //                 'italic',
                        //                 'link',
                        //                 'orderedList',
                        //                 'redo',
                        //                 'strike',
                        //                 'underline',
                        //                 'undo',
                        //             ]),
                        //         Forms\Components\RichEditor::make('return_policy')
                        //             ->label('سياسة الإرجاع')
                        //             ->toolbarButtons([
                        //                 'blockquote',
                        //                 'bold',
                        //                 'bulletList',
                        //                 'h2',
                        //                 'h3',
                        //                 'italic',
                        //                 'link',
                        //                 'orderedList',
                        //                 'redo',
                        //                 'strike',
                        //                 'underline',
                        //                 'undo',
                        //             ]),
                        //         Forms\Components\RichEditor::make('shipping_policy_text')
                        //             ->label('سياسة الشحن')
                        //             ->toolbarButtons([
                        //                 'blockquote',
                        //                 'bold',
                        //                 'bulletList',
                        //                 'h2',
                        //                 'h3',
                        //                 'italic',
                        //                 'link',
                        //                 'orderedList',
                        //                 'redo',
                        //                 'strike',
                        //                 'underline',
                        //                 'undo',
                        //             ]),
                        //     ]),

                    ])
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('site_name')
                    ->label('اسم الموقع')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('site_description')
                    ->label('وصف الموقع')
                    ->limit(50),
                IconColumn::make('maintenance_mode')
                    ->label('وضع الصيانة')
                    ->boolean(),
                TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i:s')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // تم حذف زر الحذف لأنه لا يجب السماح بحذف إعدادات الموقع
            ])
            ->bulkActions([
                // تم حذف أزرار الحذف الجماعي لأنه لا يجب السماح بحذف إعدادات الموقع
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageSiteSettings::route('/'),
            'create' => Pages\CreateSiteSetting::route('/create'),
            'edit' => Pages\EditSiteSetting::route('/{record}/edit'),
        ];
    }
}
