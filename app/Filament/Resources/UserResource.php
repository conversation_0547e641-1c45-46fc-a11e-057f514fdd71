<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Collection;
use App\Traits\HasPermissionFiltering;
use App\Traits\HasSuperAdminProtection;
use App\Services\SuperAdminProtectionService;
use Illuminate\Support\Facades\Auth;

class UserResource extends Resource
{
    use HasPermissionFiltering, HasSuperAdminProtection {
        HasSuperAdminProtection::getFilteredActions insteadof HasPermissionFiltering;
        HasSuperAdminProtection::getFilteredBulkActions insteadof HasPermissionFiltering;
    }

    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationLabel = 'المستخدمين';

    protected static ?string $modelLabel = 'مستخدم';

    protected static ?string $pluralModelLabel = 'المستخدمين';

    protected static ?string $navigationGroup = 'إدارة المستخدمين والصلاحيات';

    protected static ?int $navigationSort = 61;

    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات المستخدم الأساسية')
                    ->schema([
                        TextInput::make('name')
                            ->label('الاسم')
                            ->required()
                            ->maxLength(255),

                        TextInput::make('email')
                            ->label('البريد الإلكتروني')
                            ->email()
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),

                        TextInput::make('phone')
                            ->label('رقم الهاتف')
                            ->tel()
                            ->maxLength(20),

                        TextInput::make('password')
                            ->label('كلمة المرور')
                            ->password()
                            ->dehydrateStateUsing(fn ($state) => $state ? Hash::make($state) : null)
                            ->dehydrated(fn ($state) => filled($state))
                            ->required(fn (string $operation): bool => $operation === 'create')
                            ->maxLength(255),

                        Select::make('roles')
                            ->label('الأدوار')
                            ->relationship('roles', 'name', fn (Builder $query) => SuperAdminProtectionService::filterRoles($query))
                            ->multiple()
                            ->preload()
                            ->searchable()
                            ->placeholder('اختر الأدوار')
                            ->helperText('يمكن تعيين أدوار متعددة للمستخدم'),

                        Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),

                        FileUpload::make('profile_image')
                            ->label('صورة الملف الشخصي')
                            ->image()
                            ->directory('users')
                            ->maxSize(2048),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                // إذا كان المستخدم Super Admin، يرى الجميع
                if (SuperAdminProtectionService::isSuperAdmin()) {
                    return $query;
                }

                // إذا كان المستخدم لديه صلاحية view_any_user، يرى المستخدمين العاديين فقط
                if (Auth::user() && Auth::user()->can('view_any_user')) {
                    return SuperAdminProtectionService::filterUsers($query);
                }

                // إذا لم يكن لديه صلاحيات، لا يرى أي مستخدم
                return $query->whereRaw('1 = 0');
            })
            ->columns([
                ImageColumn::make('profile_image')
                    ->label('الصورة')
                    ->circular()
                    ->defaultImageUrl(fn ($record) => 'https://ui-avatars.com/api/?name=' . urlencode($record->name) . '&color=7F9CF5&background=EBF4FF'),

                TextColumn::make('name')
                    ->label('الاسم')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('email')
                    ->label('البريد الإلكتروني')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('phone')
                    ->label('رقم الهاتف')
                    ->searchable(),

                TextColumn::make('roles.name')
                    ->label('الأدوار')
                    ->badge()
                    ->separator(',')
                    ->color('primary')
                    ->searchable(),

                ToggleColumn::make('is_active')
                    ->label('نشط'),

                TextColumn::make('last_login_at')
                    ->label('آخر تسجيل دخول')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('roles')
                    ->label('الأدوار')
                    ->relationship('roles', 'name', fn (Builder $query) => SuperAdminProtectionService::filterRoles($query))
                    ->multiple()
                    ->preload(),

                TernaryFilter::make('is_active')
                    ->label('نشط'),

                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('من تاريخ'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('إلى تاريخ'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->visible(function ($record) {
                        // Super Admin يرى كل شيء
                        if (SuperAdminProtectionService::isSuperAdmin()) {
                            return true;
                        }
                        // المستخدمين الذين لديهم صلاحية update_user يمكنهم تعديل المستخدمين العاديين فقط
                        return Auth::user()->can('update_user') && SuperAdminProtectionService::canEditUser($record);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(function ($record) {
                        // Super Admin يرى كل شيء
                        if (SuperAdminProtectionService::isSuperAdmin()) {
                            return true;
                        }
                        // المستخدمين الذين لديهم صلاحية delete_user يمكنهم حذف المستخدمين العاديين فقط
                        return Auth::user()->can('delete_user') && SuperAdminProtectionService::canDeleteUser($record);
                    }),
                Tables\Actions\Action::make('تغيير كلمة المرور')
                    ->visible(function ($record) {
                        // Super Admin يرى كل شيء
                        if (SuperAdminProtectionService::isSuperAdmin()) {
                            return true;
                        }
                        // المستخدمين الذين لديهم صلاحية update_user يمكنهم تغيير كلمات المرور للمستخدمين العاديين فقط
                        return Auth::user()->can('update_user') && SuperAdminProtectionService::canEditUser($record);
                    })
                    ->form([
                        TextInput::make('password')
                            ->label('كلمة المرور الجديدة')
                            ->password()
                            ->required()
                            ->minLength(8)
                            ->maxLength(255),
                        TextInput::make('password_confirmation')
                            ->label('تأكيد كلمة المرور')
                            ->password()
                            ->required()
                            ->minLength(8)
                            ->maxLength(255)
                            ->same('password'),
                    ])
                    ->action(function (User $record, array $data): void {
                        $record->update([
                            'password' => Hash::make($data['password']),
                        ]);
                    })
                    ->icon('heroicon-o-key'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                if (SuperAdminProtectionService::canDeleteUser($record)) {
                                    $record->delete();
                                }
                            }
                        }),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                if (SuperAdminProtectionService::canEditUser($record)) {
                                    $record->update(['is_active' => true]);
                                }
                            }
                        })
                        ->icon('heroicon-o-check-circle'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(function (Collection $records) {
                            foreach ($records as $record) {
                                if (SuperAdminProtectionService::canEditUser($record)) {
                                    $record->update(['is_active' => false]);
                                }
                            }
                        })
                        ->icon('heroicon-o-x-circle'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
