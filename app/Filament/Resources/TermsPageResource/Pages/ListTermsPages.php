<?php

namespace App\Filament\Resources\TermsPageResource\Pages;

use App\Filament\Resources\TermsPageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTermsPages extends ListRecords
{
    protected static string $resource = TermsPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
