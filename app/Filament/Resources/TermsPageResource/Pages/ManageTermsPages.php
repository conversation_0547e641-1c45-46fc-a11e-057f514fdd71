<?php

namespace App\Filament\Resources\TermsPageResource\Pages;

use App\Filament\Resources\TermsPageResource;
use App\Models\SiteSetting;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;

class ManageTermsPages extends ManageRecords
{
    protected static string $resource = TermsPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('معاينة الصفحة')
                ->icon('heroicon-o-eye')
                ->url(route('terms'))
                ->openUrlInNewTab(),
                
            Actions\Action::make('edit')
                ->label('تحرير الشروط والأحكام')
                ->icon('heroicon-o-pencil')
                ->url(fn () => TermsPageResource::getUrl('edit')),
        ];
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        // إرجاع السجل الأول من site_settings فقط
        return SiteSetting::query()->limit(1);
    }
}
