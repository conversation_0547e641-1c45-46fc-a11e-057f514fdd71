<?php

namespace App\Filament\Resources\AboutPageResource\Pages;

use App\Filament\Resources\AboutPageResource;
use App\Models\SiteSetting;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
class ManageAboutPages extends ManageRecords
{
    protected static string $resource = AboutPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('معاينة الصفحة')
                ->icon('heroicon-o-eye')
                ->url(route('about'))
                ->openUrlInNewTab(),

            Actions\Action::make('edit')
                ->label('تحرير صفحة من نحن')
                ->icon('heroicon-o-pencil')
                ->url(fn () => AboutPageResource::getUrl('edit')),
        ];
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        // إرجاع السجل الأول من site_settings فقط
        return SiteSetting::query()->limit(1);
    }
}
