<?php

namespace App\Filament\Resources\AboutPageResource\Pages;

use App\Filament\Resources\AboutPageResource;
use App\Models\SiteSetting;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAboutPage extends EditRecord
{
    protected static string $resource = AboutPageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('preview')
                ->label('معاينة الصفحة')
                ->icon('heroicon-o-eye')
                ->url(route('about'))
                ->openUrlInNewTab(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // التأكد من أن البيانات تأتي من السجل الأول
        $setting = SiteSetting::first();
        return $setting ? $setting->toArray() : $data;
    }

    protected function handleRecordUpdate($record, array $data): \Illuminate\Database\Eloquent\Model
    {
        // تحديث السجل الأول من site_settings
        $setting = SiteSetting::first();
        if ($setting) {
            $setting->update($data);
            return $setting;
        }
        
        return SiteSetting::create($data);
    }

    public function mount(int | string $record = null): void
    {
        // استخدام السجل الأول من site_settings
        $setting = SiteSetting::first();
        if (!$setting) {
            $setting = SiteSetting::create([
                'site_name' => 'مجوهرات مكة جولد',
                'about_hero_title_ar' => 'مجوهرات مكة جولد',
                'about_hero_title_en' => 'Makkah Gold Jewelry',
            ]);
        }
        
        $this->record = $setting;
        $this->fillForm();
    }
}
