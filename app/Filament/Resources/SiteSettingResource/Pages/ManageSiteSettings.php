<?php

namespace App\Filament\Resources\SiteSettingResource\Pages;

use App\Filament\Resources\SiteSettingResource;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions;
use App\Models\SiteSetting;
use Filament\Notifications\Notification;
use Filament\Forms\Form;

class ManageSiteSettings extends ListRecords
{
    protected static string $resource = SiteSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('إضافة إعدادات جديدة')
                ->visible(function () {
                    return SiteSetting::count() === 0;
                }),
            Actions\Action::make('edit')
                ->label('تعديل الإعدادات')
                ->url(fn () => SiteSetting::count() > 0 ? route('filament.admin.resources.site-settings.edit', ['record' => SiteSetting::first()->id]) : null)
                ->visible(fn () => SiteSetting::count() > 0),
        ];
    }
}
