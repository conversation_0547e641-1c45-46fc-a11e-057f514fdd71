<?php

namespace App\Filament\Resources\SiteSettingResource\Pages;

use App\Filament\Resources\SiteSettingResource;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class EditSiteSetting extends EditRecord
{
    protected static string $resource = SiteSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // تم حذف زر الحذف لأنه لا يجب السماح بحذف إعدادات الموقع
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterSave(): void
    {
        // مسح التخزين المؤقت للإعدادات
        Cache::forget('site_settings');

        // مسح التخزين المؤقت للعروض
        Artisan::call('view:clear');

        // مسح التخزين المؤقت للتطبيق
        Artisan::call('cache:clear');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('تم حفظ الإعدادات')
            ->body('تم حفظ إعدادات الموقع بنجاح وتم مسح التخزين المؤقت.');
    }
}
