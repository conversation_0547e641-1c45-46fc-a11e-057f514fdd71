<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompanyProductResource\Pages;
use App\Models\CompanyProduct;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

/**
 * مورد إدارة منتجات الشركات في لوحة التحكم
 */
class CompanyProductResource extends Resource
{
    protected static ?string $model = CompanyProduct::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationLabel = 'منتجات الشركات';

    protected static ?string $modelLabel = 'منتج شركة';

    protected static ?string $pluralModelLabel = 'منتجات الشركات';

    protected static ?string $navigationGroup = 'إدارة الشركات';

    protected static ?int $navigationSort = 13;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المنتج الأساسية')
                    ->description('أدخل البيانات الأساسية لمنتج الشركة')
                    ->schema([
                        Forms\Components\Select::make('company_id')
                            ->label('الشركة')
                            ->relationship('company', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('اسم الشركة')
                                    ->required()
                                    ->maxLength(255),
                            ])
                            ->helperText('اختر الشركة المالكة للمنتج أو أنشئ شركة جديدة'),

                        Forms\Components\Select::make('product_type_id')
                            ->label('نوع المنتج')
                            ->relationship('productType', 'name')
                            ->required()
                            ->searchable()
                            ->preload()
                            ->getOptionLabelFromRecordUsing(fn ($record) => $record->full_name)
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('اسم نوع المنتج')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Select::make('type')
                                    ->label('نوع المنتج')
                                    ->options([
                                        'سبيكة' => 'سبيكة 🟨',
                                        'عملة' => 'عملة 🪙',
                                    ])
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state) {
                                        if ($state === 'سبيكة') {
                                            $set('metal_purity', '24');
                                        } elseif ($state === 'عملة') {
                                            $set('metal_purity', '21');
                                        }
                                    }),
                                Forms\Components\TextInput::make('weight')
                                    ->label('الوزن (جرام)')
                                    ->required()
                                    ->numeric()
                                    ->step(0.001),
                                Forms\Components\Select::make('metal_purity')
                                    ->label('عيار المعدن')
                                    ->options([
                                        '24' => 'عيار 24',
                                        '21' => 'عيار 21',
                                        '18' => 'عيار 18',
                                    ])
                                    ->required()
                                    ->disabled(),
                            ])
                            ->helperText('اختر نوع المنتج أو أنشئ نوع جديد'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('معلومات التكلفة والأسعار')
                    ->description('أدخل معلومات التكلفة والأسعار لهذا المنتج')
                    ->schema([
                        Forms\Components\TextInput::make('manufacturing_cost_per_gram')
                            ->label('مصنعية الجرام (ج.م)')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->minValue(0)
                            ->placeholder('0.00')
                            ->prefix('ج.م')
                            ->helperText('تكلفة مصنعية الجرام الواحد لهذا المنتج'),

                        Forms\Components\TextInput::make('refund_value_per_gram')
                            ->label('قيمة الاسترداد للجرام (ج.م)')
                            ->required()
                            ->numeric()
                            ->step(0.01)
                            ->minValue(0)
                            ->placeholder('0.00')
                            ->prefix('ج.م')
                            ->helperText('قيمة الاسترداد للجرام الواحد لهذا المنتج'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['company', 'productType']))
            ->columns([
                Tables\Columns\TextColumn::make('company.name')
                    ->label('الشركة')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->color('primary')
                    ->copyable()
                    ->copyMessage('تم نسخ اسم الشركة'),

                Tables\Columns\TextColumn::make('name')
                    ->label('اسم المنتج')
                    ->sortable(false)
                    ->weight('medium')
                    ->copyable()
                    ->copyMessage('تم نسخ اسم المنتج')
                    ->getStateUsing(fn ($record) => $record->name),

                Tables\Columns\TextColumn::make('productType.name')
                    ->label('نوع المنتج')
                    ->searchable()
                    ->sortable()
                    ->weight('medium'),

                Tables\Columns\TextColumn::make('productType.type')
                    ->label('التصنيف')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'سبيكة' => 'warning',
                        'عملة' => 'success',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'سبيكة' => 'heroicon-o-squares-2x2',
                        'عملة' => 'heroicon-o-currency-dollar',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('productType.weight')
                    ->label('الوزن')
                    ->numeric(decimalPlaces: 3)
                    ->sortable()
                    ->suffix(' جرام')
                    ->alignEnd()
                    ->color('info'),

                Tables\Columns\TextColumn::make('productType.metal_purity')
                    ->label('العيار')
                    ->formatStateUsing(fn (string $state): string => "عيار {$state}")
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '24' => 'success',
                        '21' => 'warning',
                        '18' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('manufacturing_cost_per_gram')
                    ->label('مصنعية الجرام')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->alignEnd()
                    ->color('warning'),

                Tables\Columns\TextColumn::make('refund_value_per_gram')
                    ->label('قيمة الاسترداد للجرام')
                    ->numeric(decimalPlaces: 2)
                    ->sortable()
                    ->prefix('ج.م ')
                    ->alignEnd()
                    ->color('success'),

                Tables\Columns\TextColumn::make('total_cost')
                    ->label('التكلفة الإجمالية')
                    ->getStateUsing(fn ($record) => number_format($record->total_cost, 2) . ' ج.م')
                    ->sortable()
                    ->alignEnd()
                    ->color('info'),

                Tables\Columns\TextColumn::make('total_refund_value')
                    ->label('الاسترداد الإجمالي')
                    ->getStateUsing(fn ($record) => number_format($record->total_refund_value, 2) . ' ج.م')
                    ->sortable()
                    ->alignEnd()
                    ->color('primary'),



                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('company_id')
                    ->label('الشركة')
                    ->relationship('company', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\SelectFilter::make('productType.type')
                    ->label('تصنيف المنتج')
                    ->options([
                        'سبيكة' => 'سبيكة 🟨',
                        'عملة' => 'عملة 🪙',
                    ]),

                Tables\Filters\SelectFilter::make('productType.metal_purity')
                    ->label('العيار')
                    ->options([
                        '24' => 'عيار 24',
                        '21' => 'عيار 21',
                        '18' => 'عيار 18',
                    ]),

                Tables\Filters\SelectFilter::make('product_type_id')
                    ->label('نوع المنتج')
                    ->relationship('productType', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('weight_range')
                    ->label('نطاق الوزن')
                    ->form([
                        Forms\Components\TextInput::make('weight_from')
                            ->label('من (جرام)')
                            ->numeric()
                            ->placeholder('0.000'),
                        Forms\Components\TextInput::make('weight_to')
                            ->label('إلى (جرام)')
                            ->numeric()
                            ->placeholder('100.000'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['weight_from'],
                                fn (Builder $query, $weight): Builder => $query->where('weight', '>=', $weight),
                            )
                            ->when(
                                $data['weight_to'],
                                fn (Builder $query, $weight): Builder => $query->where('weight', '<=', $weight),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ])
            ->emptyStateHeading('لا توجد منتجات')
            ->emptyStateDescription('لم يتم إنشاء أي منتجات للشركات بعد. ابدأ بإضافة منتج جديد.')
            ->emptyStateIcon('heroicon-o-cube')
            ->defaultSort('created_at', 'desc')
;
    }

    public static function getRelations(): array
    {
        return [
            // لا توجد علاقات إضافية حالياً
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanyProducts::route('/'),
            'create' => Pages\CreateCompanyProduct::route('/create'),
            'edit' => Pages\EditCompanyProduct::route('/{record}/edit'),
        ];
    }



    /**
     * تحديد ما إذا كان يجب تسجيل هذا المورد في التنقل
     */
    public static function shouldRegisterNavigation(): bool
    {
        return true; // سيتم تطبيق الصلاحيات من خلال Shield
    }

    /**
     * الحصول على عدد منتجات الشركات للعرض في شارة التنقل
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * لون شارة التنقل
     */
    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
