<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MetalPurityResource\Pages;
use App\Models\MetalPurity;
use App\Models\MetalType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;
use App\Traits\HasPermissionFiltering;

class MetalPurityResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = MetalPurity::class;

    protected static ?string $navigationIcon = 'heroicon-o-adjustments-horizontal';

    protected static ?string $navigationLabel = 'عيارات المعادن';

    protected static ?string $modelLabel = 'عيار معدن';

    protected static ?string $pluralModelLabel = 'عيارات المعادن';

    protected static ?string $navigationGroup = 'إدارة أسعار المعادن';

    protected static ?int $navigationSort = 42;

    protected static ?string $recordTitleAttribute = 'name_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات عيار المعدن')
                    ->description('أدخل تفاصيل عيار المعدن')
                    ->icon('heroicon-o-adjustments-horizontal')
                    ->schema([
                        Forms\Components\Select::make('metal_type_id')
                            ->label('نوع المعدن')
                            ->options(MetalType::active()->ordered()->pluck('name_ar', 'id'))
                            ->required()
                            ->reactive()
                            ->helperText('اختر نوع المعدن الذي ينتمي إليه هذا العيار'),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('اسم العيار')
                                    ->required()
                                    ->maxLength(255)
                                    ->helperText('مثل: 24K, 999, جنيه ذهب'),

                                Forms\Components\TextInput::make('name_ar')
                                    ->label('الاسم بالعربية')
                                    ->required()
                                    ->maxLength(255)
                                    ->helperText('مثل: 24 عيار, فضة 999, جنيه ذهب'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('purity_percentage')
                                    ->label('نسبة النقاء (%)')
                                    ->numeric()
                                    ->step(0.01)
                                    ->minValue(0)
                                    ->maxValue(100)
                                    ->suffix('%')
                                    ->helperText('نسبة نقاء المعدن (مثل 99.9 للذهب 24K)'),

                                Forms\Components\TextInput::make('weight_grams')
                                    ->label('الوزن بالجرام')
                                    ->numeric()
                                    ->step(0.001)
                                    ->minValue(0)
                                    ->suffix('جم')
                                    ->visible(function (callable $get) {
                                        $metalType = MetalType::find($get('metal_type_id'));
                                        return $metalType && $metalType->name === 'gold_coin';
                                    })
                                    ->helperText('وزن القطعة بالجرام (للجنيهات الذهبية فقط)'),
                            ]),

                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->rows(3)
                            ->columnSpanFull()
                            ->helperText('وصف مختصر عن العيار'),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('sort_order')
                                    ->label('ترتيب العرض')
                                    ->numeric()
                                    ->default(0)
                                    ->helperText('ترتيب ظهور العيار (الأقل أولاً)'),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true)
                                    ->helperText('تفعيل/إلغاء تفعيل العيار'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('metalType.name_ar')
                    ->label('نوع المعدن')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color(fn ($record) => match($record->metalType->name) {
                        'gold' => 'warning',
                        'silver' => 'gray',
                        'gold_coin' => 'success',
                        default => 'secondary',
                    }),

                Tables\Columns\TextColumn::make('name_ar')
                    ->label('اسم العيار')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('name')
                    ->label('الاسم الإنجليزي')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('gray'),

                Tables\Columns\TextColumn::make('purity_percentage')
                    ->label('نسبة النقاء')
                    ->suffix('%')
                    ->sortable()
                    ->alignCenter()
                    ->placeholder('غير محدد'),

                Tables\Columns\TextColumn::make('weight_grams')
                    ->label('الوزن')
                    ->suffix(' جم')
                    ->sortable()
                    ->alignCenter()
                    ->placeholder('غير محدد'),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('الترتيب')
                    ->sortable()
                    ->alignCenter(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('sort_order')
            ->filters([
                Tables\Filters\SelectFilter::make('metal_type_id')
                    ->label('نوع المعدن')
                    ->options(MetalType::active()->ordered()->pluck('name_ar', 'id'))
                    ->placeholder('جميع الأنواع'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->placeholder('جميع العيارات')
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->color('primary'),
                    Tables\Actions\ReplicateAction::make()
                        ->label('نسخ')
                        ->color('warning'),
                    Tables\Actions\DeleteAction::make()
                        ->color('danger'),
                ])
                    ->label('إجراءات')
                    ->color('gray')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('تفعيل العيارات المحددة')
                        ->modalDescription('هل أنت متأكد من تفعيل جميع العيارات المحددة؟')
                        ->modalSubmitActionLabel('تفعيل'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('إلغاء تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('إلغاء تفعيل العيارات المحددة')
                        ->modalDescription('هل أنت متأكد من إلغاء تفعيل جميع العيارات المحددة؟')
                        ->modalSubmitActionLabel('إلغاء التفعيل'),
                ])
                    ->label('إجراءات مجمعة'),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة أول عيار')
                    ->icon('heroicon-o-plus'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMetalPurities::route('/'),
            'create' => Pages\CreateMetalPurity::route('/create'),
            'edit' => Pages\EditMetalPurity::route('/{record}/edit'),
        ];
    }
}
