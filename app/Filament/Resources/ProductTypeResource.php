<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductTypeResource\Pages;
use App\Models\ProductType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

/**
 * مورد إدارة أنواع المنتجات في لوحة التحكم
 */
class ProductTypeResource extends Resource
{
    protected static ?string $model = ProductType::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationLabel = 'أنواع المنتجات';

    protected static ?string $modelLabel = 'نوع منتج';

    protected static ?string $pluralModelLabel = 'أنواع المنتجات';

    protected static ?string $navigationGroup = 'إدارة الشركات';

    protected static ?int $navigationSort = 12;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات نوع المنتج الأساسية')
                    ->description('أدخل البيانات الأساسية لنوع المنتج')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('اسم نوع المنتج')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('مثل: سبيكة 1 جرام، عملة جنيه')
                            ->helperText('أدخل اسم نوع المنتج كما سيظهر في النظام'),

                        Forms\Components\Select::make('type')
                            ->label('نوع المنتج')
                            ->options([
                                'سبيكة' => 'سبيكة 🟨',
                                'عملة' => 'عملة 🪙',
                            ])
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function (callable $set, $state) {
                                // تحديد العيار تلقائياً حسب النوع
                                if ($state === 'سبيكة') {
                                    $set('metal_purity', '24');
                                } elseif ($state === 'عملة') {
                                    $set('metal_purity', '21');
                                }
                            })
                            ->helperText('السبائك دائماً عيار 24 والعملات دائماً عيار 21'),

                        Forms\Components\TextInput::make('weight')
                            ->label('الوزن (جرام)')
                            ->required()
                            ->numeric()
                            ->step(0.001)
                            ->minValue(0.001)
                            ->placeholder('0.000')
                            ->suffix('جرام')
                            ->helperText('أدخل وزن المنتج بالجرام'),

                        Forms\Components\Select::make('metal_purity')
                            ->label('عيار المعدن')
                            ->options([
                                '24' => 'عيار 24',
                                '21' => 'عيار 21',
                                '18' => 'عيار 18',
                            ])
                            ->required()
                            ->disabled()
                            ->helperText('يتم تحديد العيار تلقائياً حسب نوع المنتج'),
                    ])
                    ->columns(2),



                Forms\Components\Section::make('إعدادات إضافية')
                    ->description('إعدادات نوع المنتج')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true)
                            ->helperText('تحديد ما إذا كان نوع المنتج نشطاً أم لا'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('اسم نوع المنتج')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->copyable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('نوع المنتج')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'سبيكة' => 'warning',
                        'عملة' => 'success',
                        default => 'gray',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        'سبيكة' => 'heroicon-o-squares-2x2',
                        'عملة' => 'heroicon-o-currency-dollar',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->sortable(),

                Tables\Columns\TextColumn::make('weight')
                    ->label('الوزن')
                    ->numeric(decimalPlaces: 3)
                    ->sortable()
                    ->suffix(' جرام')
                    ->alignEnd()
                    ->color('info'),

                Tables\Columns\TextColumn::make('metal_purity')
                    ->label('العيار')
                    ->formatStateUsing(fn (string $state): string => "عيار {$state}")
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '24' => 'success',
                        '21' => 'warning',
                        '18' => 'danger',
                        default => 'gray',
                    })
                    ->sortable(),



                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('نوع المنتج')
                    ->options([
                        'سبيكة' => 'سبيكة 🟨',
                        'عملة' => 'عملة 🪙',
                    ]),

                Tables\Filters\SelectFilter::make('metal_purity')
                    ->label('العيار')
                    ->options([
                        '24' => 'عيار 24',
                        '21' => 'عيار 21',
                        '18' => 'عيار 18',
                    ]),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('حالة النشاط')
                    ->boolean()
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ])
            ->emptyStateHeading('لا توجد أنواع منتجات')
            ->emptyStateDescription('لم يتم إنشاء أي أنواع منتجات بعد. ابدأ بإضافة نوع منتج جديد.')
            ->emptyStateIcon('heroicon-o-squares-2x2')
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductTypes::route('/'),
            'create' => Pages\CreateProductType::route('/create'),
            'edit' => Pages\EditProductType::route('/{record}/edit'),
        ];
    }

    /**
     * تحديد ما إذا كان يجب تسجيل هذا المورد في التنقل
     */
    public static function shouldRegisterNavigation(): bool
    {
        return true; // سيتم تطبيق الصلاحيات من خلال Shield
    }

    /**
     * الحصول على عدد أنواع المنتجات للعرض في شارة التنقل
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * لون شارة التنقل
     */
    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
