<?php

namespace App\Filament\Resources\SettingChangeResource\Pages;

use App\Filament\Resources\SettingChangeResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;

class ViewSettingChange extends ViewRecord
{
    protected static string $resource = SettingChangeResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('تفاصيل التغيير')
                    ->schema([
                        TextInput::make('setting_key')
                            ->label('المفتاح')
                            ->disabled(),
                        Textarea::make('old_value')
                            ->label('القيمة القديمة')
                            ->disabled(),
                        Textarea::make('new_value')
                            ->label('القيمة الجديدة')
                            ->disabled(),
                        TextInput::make('user.name')
                            ->label('المستخدم')
                            ->disabled(),
                        TextInput::make('ip_address')
                            ->label('عنوان IP')
                            ->disabled(),
                        TextInput::make('user_agent')
                            ->label('متصفح المستخدم')
                            ->disabled(),
                        DateTimePicker::make('created_at')
                            ->label('تاريخ التغيير')
                            ->displayFormat('d/m/Y H:i:s')
                            ->disabled(),
                    ])
                    ->columns(2),
            ]);
    }
}
