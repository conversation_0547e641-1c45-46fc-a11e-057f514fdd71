<?php

namespace App\Filament\Resources\SettingChangeResource\Pages;

use App\Filament\Resources\SettingChangeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSettingChanges extends ListRecords
{
    protected static string $resource = SettingChangeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // No actions needed
        ];
    }
}
