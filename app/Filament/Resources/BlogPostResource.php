<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BlogPostResource\Pages;
use App\Models\BlogPost;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class BlogPostResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = BlogPost::class;

    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    protected static ?int $navigationSort = 32;

    protected static ?string $navigationGroup = 'إدارة المدونة';

    public static function getNavigationGroup(): string
    {
        return __('إدارة المدونة');
    }

    public static function getNavigationLabel(): string
    {
        return __('المقالات');
    }

    public static function getModelLabel(): string
    {
        return __('مقال');
    }

    public static function getPluralModelLabel(): string
    {
        return __('المقالات');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('المعلومات الأساسية')
                            ->schema([
                                Forms\Components\TextInput::make('title')
                                    ->label('العنوان')
                                    ->required()
                                    ->maxLength(255)
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                                        $operation === 'create' ? $set('slug', \Illuminate\Support\Str::slug($state)) : null),

                                Forms\Components\TextInput::make('slug')
                                    ->label('المعرف الفريد (Slug)')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true),

                                Forms\Components\Textarea::make('excerpt')
                                    ->label('مقتطف')
                                    ->rows(3)
                                    ->required(),

                                Forms\Components\RichEditor::make('content')
                                    ->label('المحتوى')
                                    ->required()
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('blog')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),
                            ]),

                        Forms\Components\Section::make('SEO')
                            ->schema([
                                Forms\Components\TextInput::make('meta_title')
                                    ->label('عنوان الميتا')
                                    ->maxLength(255),

                                Forms\Components\Textarea::make('meta_description')
                                    ->label('وصف الميتا')
                                    ->rows(3),

                                Forms\Components\TextInput::make('meta_keywords')
                                    ->label('الكلمات المفتاحية')
                                    ->maxLength(255),
                            ]),

                        Forms\Components\Section::make('الترجمة الإنجليزية')
                            ->schema([
                                Forms\Components\TextInput::make('translations.en.title')
                                    ->label('العنوان (إنجليزي)')
                                    ->maxLength(255),

                                Forms\Components\Textarea::make('translations.en.excerpt')
                                    ->label('مقتطف (إنجليزي)')
                                    ->rows(3),

                                Forms\Components\RichEditor::make('translations.en.content')
                                    ->label('المحتوى (إنجليزي)')
                                    ->fileAttachmentsDisk('public')
                                    ->fileAttachmentsDirectory('blog')
                                    ->toolbarButtons([
                                        'blockquote',
                                        'bold',
                                        'bulletList',
                                        'codeBlock',
                                        'h2',
                                        'h3',
                                        'italic',
                                        'link',
                                        'orderedList',
                                        'redo',
                                        'strike',
                                        'underline',
                                        'undo',
                                    ]),

                                Forms\Components\TextInput::make('translations.en.meta_title')
                                    ->label('عنوان الميتا (إنجليزي)')
                                    ->maxLength(255),

                                Forms\Components\Textarea::make('translations.en.meta_description')
                                    ->label('وصف الميتا (إنجليزي)')
                                    ->rows(3),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('الصورة')
                            ->schema([
                                Forms\Components\FileUpload::make('featured_image')
                                    ->label('الصورة الرئيسية')
                                    ->image()
                                    ->directory('blog')
                                    ->maxSize(2048)
                                    ->required(),
                            ]),

                        Forms\Components\Section::make('الحالة')
                            ->schema([
                                Forms\Components\Select::make('author_id')
                                    ->label('الكاتب')
                                    ->relationship('author', 'name')
                                    ->searchable()
                                    ->preload()
                                    ->required(),

                                Forms\Components\Select::make('category_id')
                                    ->label('التصنيف')
                                    ->relationship('category', 'name_ar', fn ($query) => $query->where('is_active', true))
                                    ->searchable()
                                    ->preload()
                                    ->required(),

                                Forms\Components\DateTimePicker::make('published_at')
                                    ->label('تاريخ النشر')
                                    ->nullable(),

                                Forms\Components\Toggle::make('is_featured')
                                    ->label('مميز')
                                    ->default(false),

                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('featured_image')
                    ->label('الصورة')
                    ->circular()
                    ->disk('public'),

                Tables\Columns\TextColumn::make('title')
                    ->label('العنوان')
                    ->searchable()
                    ->sortable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('author.name')
                    ->label('الكاتب')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('category.name_ar')
                    ->label('التصنيف')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_featured')
                    ->label('مميز')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('views')
                    ->label('المشاهدات')
                    ->sortable(),

                Tables\Columns\TextColumn::make('published_at')
                    ->label('تاريخ النشر')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('author_id')
                    ->label('الكاتب')
                    ->relationship('author', 'name'),

                Tables\Filters\SelectFilter::make('category_id')
                    ->label('التصنيف')
                    ->relationship('category', 'name_ar'),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('مميز'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (\Illuminate\Support\Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (\Illuminate\Support\Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-mark'),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlogPosts::route('/'),
            'create' => Pages\CreateBlogPost::route('/create'),
            'view' => Pages\ViewBlogPost::route('/{record}'),
            'edit' => Pages\EditBlogPost::route('/{record}/edit'),
        ];
    }
}
