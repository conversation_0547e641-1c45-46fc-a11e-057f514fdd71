<?php

namespace App\Filament\Resources;

use App\Filament\Resources\RoleResource\Pages;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use App\Traits\HasPermissionFiltering;
use App\Traits\HasSuperAdminProtection;
use App\Services\SuperAdminProtectionService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class RoleResource extends Resource
{
    use HasPermissionFiltering, HasSuperAdminProtection {
        HasSuperAdminProtection::getFilteredActions insteadof HasPermissionFiltering;
        HasSuperAdminProtection::getFilteredBulkActions insteadof HasPermissionFiltering;
    }

    protected static ?string $model = Role::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';
    protected static ?string $navigationLabel = 'الأدوار';
    protected static ?string $navigationGroup = 'إدارة المستخدمين والصلاحيات';
    protected static ?int $navigationSort = 62;

    public static function getModelLabel(): string
    {
        return 'دور';
    }

    public static function getPluralModelLabel(): string
    {
        return 'الأدوار';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الدور الأساسية')
                    ->description('قم بتعيين اسم الدور ونوع الحارس المناسب')
                    ->icon('heroicon-o-identification')
                    ->schema([
                        TextInput::make('name')
                            ->label('اسم الدور (بالإنجليزية)')
                            ->helperText('اسم الدور باللغة الإنجليزية (مثل: admin, manager, user)')
                            ->placeholder('مثال: admin')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->regex('/^[a-z_]+$/')
                            ->validationMessages([
                                'required' => 'اسم الدور مطلوب',
                                'unique' => 'اسم الدور موجود مسبقاً، يرجى اختيار اسم آخر',
                                'max' => 'اسم الدور يجب ألا يتجاوز 255 حرف',
                                'regex' => 'يجب أن يحتوي اسم الدور على أحرف إنجليزية صغيرة وشرطة سفلية فقط',
                            ]),

                        Select::make('guard_name')
                            ->label('نوع الحارس')
                            ->helperText('اختر نوع الحارس المناسب للدور')
                            ->options([
                                'web' => 'واجهة الويب (Web)',
                                'api' => 'واجهة برمجة التطبيقات (API)',
                            ])
                            ->default('web')
                            ->required()
                            ->validationMessages([
                                'required' => 'نوع الحارس مطلوب',
                            ]),
                    ])->columns(1),

                Section::make('الصلاحيات والأذونات')
                    ->description('اختر الصلاحيات التي تريد منحها لهذا الدور. كل مورد في قسم منفصل مع تجميع الصلاحيات حسب النوع لسهولة الإدارة.')
                    ->icon('heroicon-o-shield-check')
                    ->schema([
                        static::getGeneralControlsSchema(),
                        ...static::getResourcePermissionSections(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                // إذا كان المستخدم Super Admin، يرى جميع الأدوار
                if (SuperAdminProtectionService::isSuperAdmin()) {
                    return $query;
                }

                // إذا كان المستخدم لديه صلاحية view_any_role، يرى الأدوار العادية فقط
                if (Auth::user() && Auth::user()->can('view_any_role')) {
                    return SuperAdminProtectionService::filterRoles($query);
                }

                // إذا لم يكن لديه صلاحيات، لا يرى أي دور
                return $query->whereRaw('1 = 0');
            })
            ->columns([
                TextColumn::make('name')
                    ->label('اسم الدور')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->formatStateUsing(fn (string $state): string => static::getRoleDisplayName($state))
                    ->description(fn (Role $record): string => static::getRoleDescription($record->name)),

                TextColumn::make('guard_name')
                    ->label('نوع الحارس')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                        default => $state,
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'web' => 'success',
                        'api' => 'warning',
                        default => 'gray',
                    }),

                TextColumn::make('permissions_count')
                    ->label('عدد الصلاحيات')
                    ->counts('permissions')
                    ->badge()
                    ->color('info')
                    ->sortable(),

                TextColumn::make('users_count')
                    ->label('عدد المستخدمين')
                    ->counts('users')
                    ->badge()
                    ->color('primary')
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('guard_name')
                    ->label('نوع الحارس')
                    ->options([
                        'web' => 'واجهة الويب',
                        'api' => 'واجهة برمجة التطبيقات',
                    ])
                    ->placeholder('جميع الأنواع'),

                Tables\Filters\Filter::make('has_users')
                    ->label('الأدوار المستخدمة')
                    ->query(fn ($query) => $query->has('users'))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض')
                    ->color('info')
                    ->visible(function ($record) {
                        // Super Admin يرى كل شيء
                        if (SuperAdminProtectionService::isSuperAdmin()) {
                            return true;
                        }
                        // المستخدمين الذين لديهم صلاحية view_any_role يمكنهم عرض الأدوار العادية فقط
                        return Auth::user()->can('view_any_role') && SuperAdminProtectionService::canAccessRole($record);
                    }),
                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->color('warning')
                    ->visible(function ($record) {
                        // Super Admin يرى كل شيء
                        if (SuperAdminProtectionService::isSuperAdmin()) {
                            return true;
                        }
                        // المستخدمين الذين لديهم صلاحية update_role يمكنهم تعديل الأدوار العادية فقط
                        return Auth::user()->can('update_role') && SuperAdminProtectionService::canEditRole($record);
                    }),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->color('danger')
                    ->visible(function ($record) {
                        // Super Admin يرى كل شيء
                        if (SuperAdminProtectionService::isSuperAdmin()) {
                            return true;
                        }
                        // المستخدمين الذين لديهم صلاحية delete_role يمكنهم حذف الأدوار العادية فقط
                        return Auth::user()->can('delete_role') && SuperAdminProtectionService::canDeleteRole($record);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('حذف الدور')
                    ->modalDescription('هل أنت متأكد من حذف هذا الدور؟ سيتم إزالة جميع الصلاحيات المرتبطة به.')
                    ->modalSubmitActionLabel('نعم، احذف')
                    ->modalCancelActionLabel('إلغاء')
                    ->before(function (Role $record) {
                        // التحقق من الحماية
                        if (!SuperAdminProtectionService::canDeleteRole($record)) {
                            throw new \Exception('ليس لديك صلاحية لحذف هذا الدور.');
                        }

                        // منع حذف الأدوار التي لها مستخدمين
                        if ($record->users()->count() > 0) {
                            throw new \Exception('لا يمكن حذف هذا الدور لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->requiresConfirmation()
                        ->modalHeading('حذف الأدوار المحددة')
                        ->modalDescription('هل أنت متأكد من حذف الأدوار المحددة؟')
                        ->modalSubmitActionLabel('نعم، احذف')
                        ->modalCancelActionLabel('إلغاء')
                        ->action(function ($records) {
                            foreach ($records as $record) {
                                // التحقق من الحماية
                                if (!SuperAdminProtectionService::canDeleteRole($record)) {
                                    continue; // تخطي الأدوار المحمية
                                }

                                // منع حذف الأدوار التي لها مستخدمين
                                if ($record->users()->count() > 0) {
                                    throw new \Exception('لا يمكن حذف الدور "' . static::getRoleDisplayName($record->name) . '" لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                                }

                                $record->delete();
                            }
                        }),
                ]),
            ])
            ->emptyStateHeading('لا توجد أدوار')
            ->emptyStateDescription('لم يتم إنشاء أي أدوار بعد. ابدأ بإنشاء دور جديد.')
            ->emptyStateIcon('heroicon-o-shield-exclamation');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }



    /**
     * دالة مساعدة لضمان أن البيانات مصفوفة
     */
    protected static function ensureArrayData($livewire, string $key = 'permissions'): array
    {
        $data = $livewire->data[$key] ?? [];
        return is_array($data) ? $data : [];
    }

    /**
     * إنشاء قسم التحكم العام
     */
    protected static function getGeneralControlsSchema()
    {
        return Section::make('🚀 أدوات التحكم السريع')
            ->description('استخدم هذه الأدوات لإدارة الصلاحيات بسرعة وسهولة. يمكنك تحديد أو إلغاء تحديد جميع الصلاحيات، أو التحكم في مجموعات محددة.')
            ->icon('heroicon-o-bolt')
            ->collapsible()
            ->collapsed(false)
            ->schema([
                Actions::make([
                    Action::make('select_all_permissions')
                        ->label('✅ تحديد جميع الصلاحيات')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->size('sm')
                        ->action(function ($set) {
                            try {
                                $availablePermissions = static::getAvailablePermissions();
                                $allPermissionIds = $availablePermissions->pluck('id')->toArray();
                                $set('permissions', $allPermissionIds);
                            } catch (\Exception) {
                                // في حالة حدوث خطأ، تعيين مصفوفة فارغة
                                $set('permissions', []);
                            }
                        })
                        ->requiresConfirmation(false),

                    Action::make('deselect_all_permissions')
                        ->label('❌ إلغاء تحديد الكل')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->size('sm')
                        ->action(function ($set) {
                            $set('permissions', []);
                        })
                        ->requiresConfirmation(false),

                    Action::make('select_basic_permissions')
                        ->label('⚡ الصلاحيات الأساسية')
                        ->icon('heroicon-o-star')
                        ->color('warning')
                        ->size('sm')
                        ->action(function ($set) {
                            try {
                                // تحديد الصلاحيات الأساسية فقط (عرض البيانات)
                                $basicPermissions = static::getAvailablePermissions()
                                    ->filter(function ($permission) {
                                        return str_starts_with($permission->name, 'view_any_');
                                    })
                                    ->pluck('id')
                                    ->toArray();
                                $set('permissions', $basicPermissions);
                            } catch (\Exception) {
                                // في حالة حدوث خطأ، تعيين مصفوفة فارغة
                                $set('permissions', []);
                            }
                        })
                        ->requiresConfirmation(false),
                ])->alignCenter(),
            ]);
    }

    /**
     * إنشاء أقسام الصلاحيات مجمعة حسب المورد - كل مورد في قسم منفصل
     */
    protected static function getResourcePermissionSections(): array
    {
        $resourcePermissions = static::getResourcePermissions();
        $sections = [];

        // ترتيب الموارد حسب الأهمية والاستخدام
        $resourceOrder = [
            // الموارد الأساسية
            'user', 'role', 'permission',
            // إدارة المحتوى
            'product', 'category', 'blog::post', 'blog::category',
            // الصفحات
            'about::page', 'faq', 'privacy::page', 'terms::page', 'page',
            // التفاعل مع العملاء
            'order', 'contact::message', 'appointment', 'newsletter',
            // المعادن
            'metal::price', 'metal::type', 'metal::purity',
            // الوظائف والمتاجر
            'store', 'job', 'job::application',
            // الميزات والمحتوى
            'feature', 'testimonial', 'home::slider',
            // الإعدادات
            'site::setting', 'super::admin::setting', 'setting::change',
            'language', 'language::manager'
        ];

        foreach ($resourceOrder as $resourceKey) {
            if (isset($resourcePermissions[$resourceKey])) {
                $sections[] = static::createIndividualResourceSection($resourceKey, $resourcePermissions[$resourceKey]);
            }
        }

        // إضافة أي موارد أخرى لم تكن في القائمة المرتبة
        foreach ($resourcePermissions as $resourceKey => $resourceData) {
            if (!in_array($resourceKey, $resourceOrder)) {
                $sections[] = static::createIndividualResourceSection($resourceKey, $resourceData);
            }
        }

        return $sections;
    }

    /**
     * إنشاء قسم منفصل لكل مورد مع عرض مبسط للصلاحيات
     */
    protected static function createIndividualResourceSection(string $resourceKey, array $resourceData)
    {
        $resourceInfo = static::getResourceInfo($resourceKey);

        return Section::make($resourceInfo['label'])
            ->description($resourceInfo['description'])
            ->icon($resourceInfo['icon'])
            ->collapsible()
            ->collapsed(true) // مطوي افتراضياً لتوفير المساحة
            ->compact() // تخطيط مضغوط
            ->headerActions([
                Action::make("select_all_{$resourceKey}")
                    ->label('تحديد الكل')
                    ->icon('heroicon-o-check-circle')
                    ->size('sm')
                    ->color('success')
                    ->action(function ($set, $get) use ($resourceData) {
                        $currentPermissions = $get('permissions') ?? [];
                        if (!is_array($currentPermissions)) {
                            $currentPermissions = [];
                        }
                        $resourcePermissionIds = array_keys($resourceData['permissions']);
                        $newPermissions = array_unique(array_merge($currentPermissions, $resourcePermissionIds));
                        $set('permissions', $newPermissions);
                    })
                    ->requiresConfirmation(false)
                    ->extraAttributes(['wire:loading.attr' => 'disabled'])
                    ->tooltip('تحديد جميع صلاحيات هذا المورد'),

                Action::make("deselect_all_{$resourceKey}")
                    ->label('إلغاء تحديد الكل')
                    ->icon('heroicon-o-x-circle')
                    ->size('sm')
                    ->color('danger')
                    ->action(function ($set, $get) use ($resourceData) {
                        $currentPermissions = $get('permissions') ?? [];
                        if (!is_array($currentPermissions)) {
                            $currentPermissions = [];
                        }
                        $resourcePermissionIds = array_keys($resourceData['permissions']);
                        $newPermissions = array_diff($currentPermissions, $resourcePermissionIds);
                        $set('permissions', array_values($newPermissions));
                    })
                    ->requiresConfirmation(false)
                    ->extraAttributes(['wire:loading.attr' => 'disabled'])
                    ->tooltip('إلغاء تحديد جميع صلاحيات هذا المورد'),
            ])
            ->schema([
                static::createPermissionGroupsForResource($resourceKey, $resourceData['permissions'])
            ]);
    }

    /**
     * إنشاء قائمة صلاحيات مبسطة لمورد معين
     */
    protected static function createPermissionGroupsForResource(string $resourceKey, array $permissions)
    {
        // ترتيب الصلاحيات منطقياً (view_any أولاً، ثم create، update، delete، ثم الباقي)
        $sortedPermissions = static::sortPermissionsLogically($permissions);

        // تحديد عدد الأعمدة حسب عدد الصلاحيات
        $permissionCount = count($sortedPermissions);
        $columns = match (true) {
            $permissionCount <= 2 => 2,
            $permissionCount <= 4 => 2,
            $permissionCount <= 8 => 3,
            default => 4
        };

        return CheckboxList::make('permissions')
            ->hiddenLabel()
            ->relationship('permissions', 'name')
            ->options($sortedPermissions)
            ->columns($columns)
            ->gridDirection('row')
            ->bulkToggleable()
            ->searchable()
            ->helperText('اختر الصلاحيات المناسبة لهذا المورد');
    }

    /**
     * ترتيب الصلاحيات بشكل منطقي
     */
    protected static function sortPermissionsLogically(array $permissions): array
    {
        $sorted = [];
        $order = ['عرض', 'إنشاء', 'تعديل', 'حذف'];

        // ترتيب الصلاحيات حسب النوع
        foreach ($order as $type) {
            foreach ($permissions as $id => $label) {
                if (str_contains($label, $type)) {
                    $sorted[$id] = $label;
                    unset($permissions[$id]);
                }
            }
        }

        // إضافة باقي الصلاحيات
        foreach ($permissions as $id => $label) {
            $sorted[$id] = $label;
        }

        return $sorted;
    }





    /**
     * الحصول على معلومات المورد (الاسم، الوصف، الأيقونة)
     */
    protected static function getResourceInfo(string $resourceKey): array
    {
        $resourcesInfo = [
            // إدارة المستخدمين والصلاحيات
            'user' => [
                'label' => '👥 المستخدمين',
                'description' => 'إدارة حسابات المستخدمين وبياناتهم الشخصية',
                'icon' => 'heroicon-o-users',
                'color' => 'blue'
            ],
            'role' => [
                'label' => '🛡️ الأدوار',
                'description' => 'إدارة أدوار المستخدمين وتحديد مستويات الوصول',
                'icon' => 'heroicon-o-shield-check',
                'color' => 'indigo'
            ],
            'permission' => [
                'label' => '🔑 الصلاحيات',
                'description' => 'إدارة صلاحيات النظام والتحكم في الوصول',
                'icon' => 'heroicon-o-key',
                'color' => 'purple'
            ],

            // إدارة المحتوى والمنتجات
            'product' => [
                'label' => '📦 المنتجات',
                'description' => 'إدارة المنتجات والمخزون وتفاصيل المجوهرات',
                'icon' => 'heroicon-o-cube',
                'color' => 'green'
            ],
            'category' => [
                'label' => '📂 فئات المنتجات',
                'description' => 'إدارة تصنيفات وفئات المنتجات',
                'icon' => 'heroicon-o-squares-2x2',
                'color' => 'emerald'
            ],
            'blog::post' => [
                'label' => '📰 مقالات المدونة',
                'description' => 'إدارة مقالات المدونة والمحتوى التعليمي',
                'icon' => 'heroicon-o-newspaper',
                'color' => 'cyan'
            ],
            'blog::category' => [
                'label' => '📑 تصنيفات المدونة',
                'description' => 'إدارة تصنيفات وفئات مقالات المدونة',
                'icon' => 'heroicon-o-rectangle-stack',
                'color' => 'indigo'
            ],

            // الصفحات
            'about::page' => [
                'label' => 'ℹ️ صفحة من نحن',
                'description' => 'إدارة محتوى صفحة من نحن وتاريخ الشركة',
                'icon' => 'heroicon-o-information-circle',
                'color' => 'blue'
            ],
            'faq' => [
                'label' => '❓ الأسئلة الشائعة',
                'description' => 'إدارة الأسئلة الشائعة وإجاباتها',
                'icon' => 'heroicon-o-question-mark-circle',
                'color' => 'amber'
            ],
            'privacy::page' => [
                'label' => '🔒 سياسة الخصوصية',
                'description' => 'إدارة محتوى صفحة سياسة الخصوصية',
                'icon' => 'heroicon-o-shield-check',
                'color' => 'red'
            ],
            'terms::page' => [
                'label' => '📋 الشروط والأحكام',
                'description' => 'إدارة محتوى صفحة الشروط والأحكام',
                'icon' => 'heroicon-o-document-text',
                'color' => 'gray'
            ],
            'page' => [
                'label' => '📄 الصفحات العامة',
                'description' => 'إدارة الصفحات العامة والثابتة للموقع',
                'icon' => 'heroicon-o-document-duplicate',
                'color' => 'teal'
            ],

            // التفاعل مع العملاء
            'order' => [
                'label' => '🛒 الطلبات',
                'description' => 'إدارة طلبات العملاء ومتابعة حالة الطلبات',
                'icon' => 'heroicon-o-shopping-bag',
                'color' => 'orange'
            ],
            'contact::message' => [
                'label' => '✉️ رسائل الاتصال',
                'description' => 'إدارة رسائل العملاء والرد على الاستفسارات',
                'icon' => 'heroicon-o-envelope',
                'color' => 'blue'
            ],
            'appointment' => [
                'label' => '📅 المواعيد',
                'description' => 'إدارة مواعيد العملاء وجدولة الزيارات',
                'icon' => 'heroicon-o-calendar',
                'color' => 'amber'
            ],
            'newsletter' => [
                'label' => '📧 النشرة البريدية',
                'description' => 'إدارة اشتراكات النشرة البريدية',
                'icon' => 'heroicon-o-newspaper',
                'color' => 'fuchsia'
            ],

            // المعادن
            'metal::price' => [
                'label' => '💰 أسعار المعادن',
                'description' => 'إدارة أسعار المعادن الثمينة وتحديثها',
                'icon' => 'heroicon-o-banknotes',
                'color' => 'yellow'
            ],
            'metal::type' => [
                'label' => '🥇 أنواع المعادن',
                'description' => 'إدارة أنواع المعادن المختلفة (ذهب، فضة، إلخ)',
                'icon' => 'heroicon-o-squares-2x2',
                'color' => 'lime'
            ],
            'metal::purity' => [
                'label' => '⚖️ عيارات المعادن',
                'description' => 'إدارة عيارات ونقاوة المعادن',
                'icon' => 'heroicon-o-adjustments-horizontal',
                'color' => 'green'
            ],

            // الوظائف والمتاجر
            'store' => [
                'label' => '🏪 المتاجر',
                'description' => 'إدارة فروع المتاجر ومعلومات المواقع',
                'icon' => 'heroicon-o-building-storefront',
                'color' => 'slate'
            ],
            'job' => [
                'label' => '💼 الوظائف',
                'description' => 'إدارة الوظائف المتاحة ومتطلبات التوظيف',
                'icon' => 'heroicon-o-briefcase',
                'color' => 'violet'
            ],
            'job::application' => [
                'label' => '📝 طلبات الوظائف',
                'description' => 'إدارة طلبات التوظيف ومراجعة السير الذاتية',
                'icon' => 'heroicon-o-document-check',
                'color' => 'purple'
            ],

            // الميزات والمحتوى
            'feature' => [
                'label' => '⭐ الميزات',
                'description' => 'إدارة ميزات الموقع والخدمات المقدمة',
                'icon' => 'heroicon-o-star',
                'color' => 'sky'
            ],
            'testimonial' => [
                'label' => '💬 آراء العملاء',
                'description' => 'إدارة شهادات وآراء العملاء',
                'icon' => 'heroicon-o-chat-bubble-left-ellipsis',
                'color' => 'purple'
            ],
            'home::slider' => [
                'label' => '🖼️ شرائح الصفحة الرئيسية',
                'description' => 'إدارة الصور والشرائح في الصفحة الرئيسية',
                'icon' => 'heroicon-o-photo',
                'color' => 'indigo'
            ],

            // الإعدادات
            'site::setting' => [
                'label' => '⚙️ إعدادات الموقع',
                'description' => 'إدارة الإعدادات العامة للموقع',
                'icon' => 'heroicon-o-cog-6-tooth',
                'color' => 'red'
            ],
            'super::admin::setting' => [
                'label' => '🔧 إعدادات السوبر أدمن',
                'description' => 'إدارة الإعدادات المتقدمة للنظام',
                'icon' => 'heroicon-o-shield-exclamation',
                'color' => 'rose'
            ],
            'setting::change' => [
                'label' => '🕒 تغييرات الإعدادات',
                'description' => 'مراقبة وتتبع تغييرات الإعدادات',
                'icon' => 'heroicon-o-clock',
                'color' => 'orange'
            ],
            'language' => [
                'label' => '🌐 اللغات',
                'description' => 'إدارة لغات النظام والترجمات',
                'icon' => 'heroicon-o-language',
                'color' => 'pink'
            ],
            'language::manager' => [
                'label' => '🔤 مدير اللغات',
                'description' => 'إدارة مدير اللغات والترجمات المتقدمة',
                'icon' => 'heroicon-o-globe-alt',
                'color' => 'violet'
            ],
        ];

        return $resourcesInfo[$resourceKey] ?? [
            'label' => $resourceKey,
            'description' => "إدارة {$resourceKey}",
            'icon' => 'heroicon-o-cog',
            'color' => 'gray'
        ];
    }

    /**
     * ترجمة أسماء الصلاحيات إلى العربية
     */
    protected static function getTranslatedPermissions(): array
    {
        $permissions = Permission::all();
        $translatedPermissions = [];

        foreach ($permissions as $permission) {
            $translatedPermissions[$permission->id] = static::translatePermissionName($permission->name);
        }

        return $translatedPermissions;
    }

    /**
     * ترجمة اسم الصلاحية الواحدة
     */
    protected static function translatePermissionName(string $permissionName): string
    {
        // التعامل مع صلاحيات الصفحات والودجات
        if (str_starts_with($permissionName, 'page_')) {
            $pageName = str_replace('page_', '', $permissionName);
            return "صفحة {$pageName}";
        }

        if (str_starts_with($permissionName, 'widget_')) {
            $widgetName = str_replace('widget_', '', $permissionName);
            return "ودجة {$widgetName}";
        }

        // التعامل مع view_any_ خاص
        if (str_starts_with($permissionName, 'view_any_')) {
            $resource = str_replace('view_any_', '', $permissionName);
            $action = 'view_any';
        } else {
            // تقسيم اسم الصلاحية إلى أجزاء
            $parts = explode('_', $permissionName);

            if (count($parts) < 2) {
                return $permissionName;
            }

            $action = $parts[0]; // الإجراء (create, update, delete)
            $resource = implode('_', array_slice($parts, 1)); // اسم المورد
        }

        // ترجمة الإجراءات المبسطة
        $actionTranslations = [
            'view_any' => 'عرض جميع',
            'create' => 'إنشاء',
            'update' => 'تعديل',
            'delete' => 'حذف',
            'reply' => 'الرد على',
            'page' => 'صفحة',
            'widget' => 'ودجة',
        ];

        // ترجمة الموارد الأساسية
        $resourceTranslations = [
            'user' => 'المستخدمين',
            'role' => 'الأدوار',
            'permission' => 'الصلاحيات',
            'product' => 'المنتجات',
            'category' => 'فئات المنتجات',
            'order' => 'الطلبات',
            'appointment' => 'المواعيد',
            'blog::post' => 'مقالات المدونة',
            'blog::category' => 'تصنيفات المدونة',
            'about::page' => 'صفحة من نحن',
            'faq' => 'الأسئلة الشائعة',
            'privacy::page' => 'سياسة الخصوصية',
            'terms::page' => 'الشروط والأحكام',
            'page' => 'الصفحات العامة',
            'feature' => 'الميزات',
            'testimonial' => 'آراء العملاء',
            'job' => 'الوظائف',
            'job::application' => 'طلبات الوظائف',
            'language' => 'اللغات',
            'language::manager' => 'مدير اللغات',
            'metal::price' => 'أسعار المعادن',
            'metal::purity' => 'عيارات المعادن',
            'metal::type' => 'أنواع المعادن',
            'contact::message' => 'رسائل الاتصال',
            'newsletter' => 'النشرة البريدية',
            'site::setting' => 'إعدادات الموقع',
            'setting::change' => 'تغيير الإعدادات',
            'store' => 'المتاجر',
            'super::admin::setting' => 'إعدادات السوبر أدمن',
            'home::slider' => 'شرائح الصفحة الرئيسية',
            'media' => 'الوسائط',
            'address' => 'العناوين',
            'notification' => 'الإشعارات',
            'review' => 'التقييمات',
            'wishlist' => 'قوائم الرغبات',
            'cart' => 'سلات التسوق',
            'setting' => 'الإعدادات',
        ];

        $translatedAction = $actionTranslations[$action] ?? $action;
        $translatedResource = $resourceTranslations[$resource] ?? $resource;

        return "{$translatedAction} {$translatedResource}";
    }

    /**
     * الحصول على الاسم المعروض للدور
     */
    protected static function getRoleDisplayName(string $roleName): string
    {
        $roleNames = [
            'super_admin' => 'السوبر أدمن',
            'admin' => 'المدير',
            'manager' => 'المدير المساعد',
            'user' => 'المستخدم',
            'customer' => 'العميل',
            'employee' => 'الموظف',
            'moderator' => 'المشرف',
        ];

        return $roleNames[$roleName] ?? $roleName;
    }

    /**
     * الحصول على وصف الدور
     */
    protected static function getRoleDescription(string $roleName): string
    {
        $descriptions = [
            'super_admin' => 'صلاحيات كاملة لإدارة النظام والمستخدمين',
            'admin' => 'صلاحيات إدارية شاملة عدا إعدادات النظام الحساسة',
            'manager' => 'صلاحيات محدودة لإدارة المحتوى والطلبات',
            'user' => 'مستخدم عادي بدون صلاحيات إدارية',
            'customer' => 'عميل يمكنه التسوق وإدارة حسابه',
            'employee' => 'موظف بصلاحيات محددة حسب القسم',
            'moderator' => 'مشرف على المحتوى والتعليقات',
        ];

        return $descriptions[$roleName] ?? 'دور مخصص';
    }

    /**
     * الحصول على الصلاحيات مجمعة حسب المورد
     */
    protected static function getResourcePermissions(): array
    {
        // جلب الصلاحيات المتاحة للمستخدم الحالي فقط
        $permissions = static::getAvailablePermissions();
        $resourcePermissions = [];

        // الصلاحيات المبسطة المسموحة فقط
        $allowedActions = ['view_any', 'create', 'update', 'delete', 'reply', 'page', 'widget'];

        // تجميع الصلاحيات لكل مورد
        foreach ($permissions as $permission) {
            $permissionName = $permission->name;

            // استخراج اسم المورد من اسم الصلاحية
            $resourceKey = static::extractResourceFromPermission($permissionName);

            if ($resourceKey && static::isAllowedPermission($permissionName, $allowedActions)) {
                if (!isset($resourcePermissions[$resourceKey])) {
                    $resourcePermissions[$resourceKey] = [
                        'permissions' => []
                    ];
                }

                $resourcePermissions[$resourceKey]['permissions'][$permission->id] = static::translatePermissionName($permissionName);
            }
        }

        return $resourcePermissions;
    }

    /**
     * استخراج اسم المورد من اسم الصلاحية
     */
    protected static function extractResourceFromPermission(string $permissionName): ?string
    {
        // التعامل مع صلاحيات الصفحات والودجات
        if (str_starts_with($permissionName, 'page_')) {
            return 'page';
        }

        if (str_starts_with($permissionName, 'widget_')) {
            return 'widget';
        }

        // التعامل مع view_any_ خاص
        if (str_starts_with($permissionName, 'view_any_')) {
            return str_replace('view_any_', '', $permissionName);
        }

        // تقسيم اسم الصلاحية إلى أجزاء
        $parts = explode('_', $permissionName);

        if (count($parts) < 2) {
            return null;
        }

        // إزالة الإجراء والحصول على اسم المورد
        array_shift($parts); // إزالة الإجراء (create, update, delete)
        return implode('_', $parts);
    }

    /**
     * تجميع الصلاحيات حسب المورد - الطريقة القديمة (للتوافق)
     */
    protected static function getGroupedPermissions(): array
    {
        // جلب الصلاحيات المتاحة للمستخدم الحالي فقط
        $permissions = static::getAvailablePermissions();
        $groupedPermissions = [];

        // تعريف الموارد المنفصلة مع ترتيب منطقي متدرج
        $resourceGroups = [
            // أ. إدارة المستخدمين والصلاحيات
            'user' => [
                'label' => 'المستخدمين',
                'icon' => 'heroicon-o-users',
                'description' => 'صلاحيات إدارة المستخدمين وحساباتهم',
                'color' => 'blue',
                'category' => 'إدارة المستخدمين والصلاحيات',
            ],
            'role' => [
                'label' => 'الأدوار',
                'icon' => 'heroicon-o-shield-check',
                'description' => 'صلاحيات إدارة أدوار المستخدمين',
                'color' => 'indigo',
                'category' => 'إدارة المستخدمين والصلاحيات',
            ],
            'permission' => [
                'label' => 'الصلاحيات',
                'icon' => 'heroicon-o-key',
                'description' => 'صلاحيات إدارة صلاحيات النظام',
                'color' => 'purple',
                'category' => 'إدارة المستخدمين والصلاحيات',
            ],

            // ب. إدارة المحتوى والمنتجات
            'product' => [
                'label' => 'المنتجات',
                'icon' => 'heroicon-o-cube',
                'description' => 'صلاحيات إدارة المنتجات والمخزون',
                'color' => 'green',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'category' => [
                'label' => 'فئات المنتجات',
                'icon' => 'heroicon-o-squares-2x2',
                'description' => 'صلاحيات إدارة فئات المنتجات',
                'color' => 'emerald',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'blog::post' => [
                'label' => 'مقالات المدونة',
                'icon' => 'heroicon-o-newspaper',
                'description' => 'صلاحيات إدارة مقالات المدونة',
                'color' => 'cyan',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'blog::category' => [
                'label' => 'تصنيفات المدونة',
                'icon' => 'heroicon-o-rectangle-stack',
                'description' => 'صلاحيات إدارة تصنيفات المدونة',
                'color' => 'indigo',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'about::page' => [
                'label' => 'صفحة من نحن',
                'icon' => 'heroicon-o-information-circle',
                'description' => 'صلاحيات إدارة صفحة من نحن',
                'color' => 'blue',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'faq' => [
                'label' => 'الأسئلة الشائعة',
                'icon' => 'heroicon-o-question-mark-circle',
                'description' => 'صلاحيات إدارة الأسئلة الشائعة',
                'color' => 'amber',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'privacy::page' => [
                'label' => 'سياسة الخصوصية',
                'icon' => 'heroicon-o-shield-check',
                'description' => 'صلاحيات إدارة صفحة سياسة الخصوصية',
                'color' => 'red',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'terms::page' => [
                'label' => 'الشروط والأحكام',
                'icon' => 'heroicon-o-document-text',
                'description' => 'صلاحيات إدارة صفحة الشروط والأحكام',
                'color' => 'gray',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'page' => [
                'label' => 'الصفحات العامة',
                'icon' => 'heroicon-o-document-duplicate',
                'description' => 'صلاحيات إدارة الصفحات العامة للموقع',
                'color' => 'teal',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'feature' => [
                'label' => 'الميزات',
                'icon' => 'heroicon-o-star',
                'description' => 'صلاحيات إدارة ميزات الموقع',
                'color' => 'sky',
                'category' => 'إدارة المحتوى والمنتجات',
            ],
            'testimonial' => [
                'label' => 'آراء العملاء',
                'icon' => 'heroicon-o-chat-bubble-left-ellipsis',
                'description' => 'صلاحيات إدارة آراء وشهادات العملاء',
                'color' => 'purple',
                'category' => 'إدارة المحتوى والمنتجات',
            ],

            // ج. إدارة الطلبات والعملاء
            'order' => [
                'label' => 'الطلبات',
                'icon' => 'heroicon-o-shopping-bag',
                'description' => 'صلاحيات إدارة طلبات العملاء',
                'color' => 'orange',
                'category' => 'إدارة الطلبات والعملاء',
            ],
            'contact::message' => [
                'label' => 'رسائل الاتصال',
                'icon' => 'heroicon-o-envelope',
                'description' => 'صلاحيات إدارة رسائل الاتصال والرد عليها',
                'color' => 'blue',
                'category' => 'إدارة الطلبات والعملاء',
            ],
            'appointment' => [
                'label' => 'المواعيد',
                'icon' => 'heroicon-o-calendar',
                'description' => 'صلاحيات إدارة مواعيد العملاء',
                'color' => 'amber',
                'category' => 'إدارة الطلبات والعملاء',
            ],
            'newsletter' => [
                'label' => 'النشرة البريدية',
                'icon' => 'heroicon-o-newspaper',
                'description' => 'صلاحيات إدارة النشرة البريدية',
                'color' => 'fuchsia',
                'category' => 'إدارة الطلبات والعملاء',
            ],

            // د. إدارة أسعار المعادن
            'metal::price' => [
                'label' => 'أسعار المعادن',
                'icon' => 'heroicon-o-banknotes',
                'description' => 'صلاحيات إدارة أسعار المعادن',
                'color' => 'yellow',
                'category' => 'إدارة أسعار المعادن',
            ],
            'metal::type' => [
                'label' => 'أنواع المعادن',
                'icon' => 'heroicon-o-squares-2x2',
                'description' => 'صلاحيات إدارة أنواع المعادن',
                'color' => 'lime',
                'category' => 'إدارة أسعار المعادن',
            ],
            'metal::purity' => [
                'label' => 'عيارات المعادن',
                'icon' => 'heroicon-o-adjustments-horizontal',
                'description' => 'صلاحيات إدارة عيارات المعادن',
                'color' => 'green',
                'category' => 'إدارة أسعار المعادن',
            ],

            // هـ. إدارة المتاجر والوظائف
            'store' => [
                'label' => 'المتاجر',
                'icon' => 'heroicon-o-building-storefront',
                'description' => 'صلاحيات إدارة المتاجر',
                'color' => 'slate',
                'category' => 'إدارة المتاجر والوظائف',
            ],
            'job' => [
                'label' => 'الوظائف',
                'icon' => 'heroicon-o-briefcase',
                'description' => 'صلاحيات إدارة الوظائف المتاحة',
                'color' => 'violet',
                'category' => 'إدارة المتاجر والوظائف',
            ],
            'job::application' => [
                'label' => 'طلبات الوظائف',
                'icon' => 'heroicon-o-document-check',
                'description' => 'صلاحيات إدارة طلبات التوظيف',
                'color' => 'purple',
                'category' => 'إدارة المتاجر والوظائف',
            ],

            // و. إعدادات النظام
            'site::setting' => [
                'label' => 'إعدادات الموقع',
                'icon' => 'heroicon-o-cog-6-tooth',
                'description' => 'صلاحيات إدارة إعدادات الموقع العامة',
                'color' => 'red',
                'category' => 'إعدادات النظام',
            ],
            'super::admin::setting' => [
                'label' => 'إعدادات السوبر أدمن',
                'icon' => 'heroicon-o-shield-exclamation',
                'description' => 'صلاحيات إدارة إعدادات السوبر أدمن',
                'color' => 'rose',
                'category' => 'إعدادات النظام',
            ],
            'setting::change' => [
                'label' => 'تغييرات الإعدادات',
                'icon' => 'heroicon-o-clock',
                'description' => 'صلاحيات مراقبة تغييرات الإعدادات',
                'color' => 'orange',
                'category' => 'إعدادات النظام',
            ],
            'language' => [
                'label' => 'اللغات',
                'icon' => 'heroicon-o-language',
                'description' => 'صلاحيات إدارة لغات النظام',
                'color' => 'pink',
                'category' => 'إعدادات النظام',
            ],
            'language::manager' => [
                'label' => 'مدير اللغات',
                'icon' => 'heroicon-o-globe-alt',
                'description' => 'صلاحيات إدارة مدير اللغات والترجمات',
                'color' => 'violet',
                'category' => 'إعدادات النظام',
            ],
            'home::slider' => [
                'label' => 'شرائح الصفحة الرئيسية',
                'icon' => 'heroicon-o-photo',
                'description' => 'صلاحيات إدارة شرائح الصفحة الرئيسية',
                'color' => 'indigo',
                'category' => 'إعدادات النظام',
            ],
        ];

        // الصلاحيات المبسطة المسموحة فقط
        $allowedActions = ['view_any', 'create', 'update', 'delete', 'reply', 'page', 'widget'];

        // تجميع الصلاحيات لكل مورد منفصل
        foreach ($resourceGroups as $resourceKey => $resourceData) {
            $resourcePermissions = [];

            foreach ($permissions as $permission) {
                $permissionName = $permission->name;

                // التحقق من أن الصلاحية تخص هذا المورد
                if (static::permissionBelongsToResource($permissionName, $resourceKey)) {
                    // التحقق من أن الصلاحية من الصلاحيات الأساسية المسموحة
                    if (static::isAllowedPermission($permissionName, $allowedActions)) {
                        $resourcePermissions[$permission->id] = static::translatePermissionName($permissionName);
                    }
                }
            }

            // إضافة المورد فقط إذا كان له صلاحيات
            if (!empty($resourcePermissions)) {
                $groupedPermissions[$resourceKey] = [
                    'label' => $resourceData['label'],
                    'icon' => $resourceData['icon'],
                    'description' => $resourceData['description'],
                    'color' => $resourceData['color'],
                    'category' => $resourceData['category'],
                    'permissions' => $resourcePermissions,
                ];
            }
        }

        return $groupedPermissions;
    }

    /**
     * التحقق من أن الصلاحية تخص مورد معين
     */
    protected static function permissionBelongsToResource(string $permissionName, string $resourceKey): bool
    {
        // التحقق من أن اسم الصلاحية يحتوي على اسم المورد
        return str_contains($permissionName, $resourceKey);
    }

    /**
     * التحقق من أن الصلاحية من الصلاحيات المسموحة
     */
    protected static function isAllowedPermission(string $permissionName, array $allowedActions): bool
    {
        foreach ($allowedActions as $action) {
            // للصلاحيات المبسطة مثل view_any_user, create_user, etc.
            if (str_starts_with($permissionName, $action . '_')) {
                return true;
            }
            // للصفحات والودجات مثل page_DashboardPage, widget_StatsOverview
            if (str_starts_with($permissionName, $action)) {
                return true;
            }
        }

        return false;
    }

    /**
     * الحصول على الصلاحيات المتاحة للمستخدم الحالي
     */
    public static function getAvailablePermissions()
    {
        // استخدام خدمة الحماية للحصول على الصلاحيات المفلترة
        return SuperAdminProtectionService::getFilteredPermissions();
    }
}
