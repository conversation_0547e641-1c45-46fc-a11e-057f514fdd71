<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AboutPageResource\Pages;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;
class AboutPageResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = SiteSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'الصفحات القانونية';

    protected static ?int $navigationSort = 21;

    protected static bool $shouldRegisterNavigation = true;

    protected static ?string $slug = 'about-page';

    public static function canDelete($record): bool
    {
        return false; // لا نريد حذف الإعدادات
    }

    public static function getNavigationLabel(): string
    {
        return __('صفحة من نحن');
    }

    public static function getModelLabel(): string
    {
        return __('صفحة من نحن');
    }

    public static function getPluralModelLabel(): string
    {
        return __('صفحة من نحن');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // قسم البطل (Hero Section)
                Forms\Components\Section::make('قسم البطل (Hero Section)')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('about_hero_title_ar')
                                    ->label('عنوان الصفحة (عربي)')
                                    ->required()
                                    ->maxLength(255)
                                    ->default('مجوهرات مكة جولد'),

                                Forms\Components\TextInput::make('about_hero_title_en')
                                    ->label('عنوان الصفحة (إنجليزي)')
                                    ->maxLength(255)
                                    ->default('Makkah Gold Jewelry'),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Textarea::make('about_hero_subtitle_ar')
                                    ->label('العنوان الفرعي (عربي)')
                                    ->rows(2)
                                    ->default('رحلة من التميز والجودة في عالم المجوهرات'),

                                Forms\Components\Textarea::make('about_hero_subtitle_en')
                                    ->label('العنوان الفرعي (إنجليزي)')
                                    ->rows(2)
                                    ->default('A journey of excellence and quality in the world of jewelry'),
                            ]),

                        Forms\Components\FileUpload::make('about_hero_image')
                            ->label('صورة البطل')
                            ->image()
                            ->disk('public')
                            ->directory('about')
                            ->imageEditor()
                            ->imageEditorAspectRatios(['16:9'])
                            ->helperText('الحجم المفضل: 1920x1080 بكسل'),
                    ]),

                // معلومات الشركة
                Forms\Components\Section::make('معلومات الشركة')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\RichEditor::make('company_mission_ar')
                                    ->label('نبذة عن الشركة (عربي)')
                                    ->toolbarButtons([
                                        'bold',
                                        'italic',
                                        'bulletList',
                                        'orderedList',
                                    ])
                                    ->default('نحن شركة رائدة في مجال المجوهرات والذهب، نسعى لتقديم أجود أنواع المجوهرات بأعلى معايير الجودة والحرفية.'),

                                Forms\Components\RichEditor::make('company_mission_en')
                                    ->label('نبذة عن الشركة (إنجليزي)')
                                    ->toolbarButtons([
                                        'bold',
                                        'italic',
                                        'bulletList',
                                        'orderedList',
                                    ])
                                    ->default('We are a leading company in the field of jewelry and gold, striving to provide the finest jewelry with the highest standards of quality and craftsmanship.'),
                            ]),

                        Forms\Components\TextInput::make('company_founded_year')
                            ->label('سنة التأسيس')
                            ->numeric()
                            ->minValue(1900)
                            ->maxValue(date('Y'))
                            ->default('1980'),

                        Forms\Components\FileUpload::make('about_story_image')
                            ->label('صورة قصتنا')
                            ->image()
                            ->disk('public')
                            ->directory('about')
                            ->imageEditor()
                            ->imageEditorAspectRatios(['4:3', '16:9'])
                            ->helperText('الحجم المفضل: 800x600 بكسل أو 1200x675 بكسل'),
                    ]),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('about_hero_title_ar')
                    ->label('عنوان الصفحة')
                    ->searchable(),

                Tables\Columns\TextColumn::make('company_founded_year')
                    ->label('سنة التأسيس'),

                Tables\Columns\ImageColumn::make('about_story_image')
                    ->label('صورة قصتنا')
                    ->disk('public')
                    ->size(60),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('تحرير'),
            ])
            ->bulkActions([])
            ->paginated(false);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageAboutPages::route('/'),
            'edit' => Pages\EditAboutPage::route('/edit'),
        ];
    }
}
