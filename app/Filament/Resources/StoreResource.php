<?php

namespace App\Filament\Resources;

use App\Filament\Resources\StoreResource\Pages;
use App\Models\Store;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;
use Illuminate\Support\Collection;
use Filament\Notifications\Notification;

class StoreResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Store::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationLabel = 'المتاجر';

    protected static ?string $modelLabel = 'متجر';

    protected static ?string $pluralModelLabel = 'المتاجر';

    protected static ?string $navigationGroup = 'إدارة المتاجر والوظائف';

    protected static ?int $navigationSort = 51;

    protected static ?string $recordTitleAttribute = 'name_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات المتجر')
                    ->schema([
                        Forms\Components\TextInput::make('name_ar')
                            ->label('الاسم بالعربية')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('name_en')
                            ->label('الاسم بالإنجليزية')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\TextInput::make('phone')
                            ->label('رقم الهاتف')
                            ->tel()
                            ->required()
                            ->maxLength(20),

                        Forms\Components\TextInput::make('email')
                            ->label('البريد الإلكتروني')
                            ->email()
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),
                    ])->columns(2),

                Forms\Components\Section::make('العنوان والموقع')
                    ->schema([
                        Forms\Components\Textarea::make('address_ar')
                            ->label('العنوان بالعربية')
                            ->required()
                            ->maxLength(500),

                        Forms\Components\Textarea::make('address_en')
                            ->label('العنوان بالإنجليزية')
                            ->required()
                            ->maxLength(500),

                        Forms\Components\TextInput::make('city_ar')
                            ->label('المدينة بالعربية')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('city_en')
                            ->label('المدينة بالإنجليزية')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('country_ar')
                            ->label('الدولة بالعربية')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\TextInput::make('country_en')
                            ->label('الدولة بالإنجليزية')
                            ->required()
                            ->maxLength(100),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('latitude')
                                    ->label('خط العرض (Latitude)')
                                    ->numeric()
                                    ->step(0.0000001)
                                    ->minValue(-90)
                                    ->maxValue(90)
                                    ->placeholder('25.276987')
                                    ->helperText('خط العرض يجب أن يكون بين -90 و 90 (مثال: 25.276987)')
                                    ->rule('nullable')
                                    ->rule('numeric')
                                    ->rule('between:-90,90'),

                                Forms\Components\TextInput::make('longitude')
                                    ->label('خط الطول (Longitude)')
                                    ->numeric()
                                    ->step(0.0000001)
                                    ->minValue(-180)
                                    ->maxValue(180)
                                    ->placeholder('46.738586')
                                    ->helperText('خط الطول يجب أن يكون بين -180 و 180 (مثال: 46.738586)')
                                    ->rule('nullable')
                                    ->rule('numeric')
                                    ->rule('between:-180,180'),
                            ]),

                        Forms\Components\Placeholder::make('location_help')
                            ->label('')
                            ->content('💡 يمكنك الحصول على الإحداثيات من Google Maps عبر النقر بالزر الأيمن على الموقع واختيار الإحداثيات.')
                            ->columnSpanFull(),
                    ])->columns(2),

                Forms\Components\Section::make('ساعات العمل')
                    ->schema([
                        Forms\Components\Textarea::make('working_hours_ar')
                            ->label('ساعات العمل بالعربية')
                            ->required()
                            ->maxLength(500),

                        Forms\Components\Textarea::make('working_hours_en')
                            ->label('ساعات العمل بالإنجليزية')
                            ->required()
                            ->maxLength(500),
                    ])->columns(2),

                Forms\Components\Section::make('الصورة')
                    ->schema([
                        Forms\Components\FileUpload::make('image')
                            ->label('صورة المتجر')
                            ->image()
                            ->directory('stores')
                            ->maxSize(2048)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label('الاسم بالعربية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('الاسم بالإنجليزية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('city_ar')
                    ->label('المدينة')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('phone')
                    ->label('رقم الهاتف')
                    ->searchable(),

                Tables\Columns\TextColumn::make('email')
                    ->label('البريد الإلكتروني')
                    ->searchable(),

                Tables\Columns\TextColumn::make('latitude')
                    ->label('خط العرض')
                    ->numeric(decimalPlaces: 6)
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('longitude')
                    ->label('خط الطول')
                    ->numeric(decimalPlaces: 6)
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\ImageColumn::make('image')
                    ->label('الصورة')
                    ->circular(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('city_ar')
                    ->label('المدينة')
                    ->options(function () {
                        return \App\Models\Store::distinct('city_ar')->pluck('city_ar', 'city_ar')->toArray();
                    }),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),
            ])
            ->actions([
                Tables\Actions\Action::make('viewLocation')
                    ->label('عرض الموقع')
                    ->icon('heroicon-o-map-pin')
                    ->color('info')
                    ->url(fn (Store $record): string => $record->google_maps_url ?? '#')
                    ->openUrlInNewTab()
                    ->visible(fn (Store $record): bool => $record->hasValidCoordinates()),
                    
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation()
                    ->modalHeading('حذف المتجر')
                    ->modalDescription(function ($record) {
                        if ($record && method_exists($record, 'hasOrders') && $record->hasOrders()) {
                            return 'هذا المتجر لديه ' . $record->getOrdersCount() . ' طلبات مرتبطة به. ماذا تريد أن تفعل بهذه الطلبات؟';
                        }
                        return 'هل أنت متأكد من حذف هذا المتجر؟';
                    })
                    ->modalSubmitActionLabel('تأكيد')
                    ->modalCancelActionLabel('إلغاء')
                    ->form(function ($record) {
                        // Only show the form if the store has orders
                        if (!$record || !method_exists($record, 'hasOrders') || !$record->hasOrders()) {
                            return [];
                        }

                        // Get all other active stores for the select field
                        $otherStores = Store::where('id', '!=', $record->id)
                            ->where('is_active', true)
                            ->pluck('name_ar', 'id')
                            ->toArray();

                        return [
                            Forms\Components\Radio::make('action')
                                ->label('اختر إجراء للطلبات المرتبطة')
                                ->options([
                                    'set_null' => 'إزالة ارتباط الطلبات بالمتجر',
                                    'transfer' => 'نقل الطلبات إلى متجر آخر',
                                ])
                                ->default('set_null')
                                ->required(),

                            Forms\Components\Select::make('transfer_to_store_id')
                                ->label('نقل الطلبات إلى')
                                ->options($otherStores)
                                ->visible(fn (Forms\Get $get) => $get('action') === 'transfer')
                                ->required(fn (Forms\Get $get) => $get('action') === 'transfer'),
                        ];
                    })
                    ->action(function ($record, array $data) {
                        try {
                            if ($record->hasOrders()) {
                                $transferToStoreId = null;

                                if (isset($data['action']) && $data['action'] === 'transfer' && isset($data['transfer_to_store_id'])) {
                                    $transferToStoreId = $data['transfer_to_store_id'];
                                }

                                $record->safeDelete($transferToStoreId);
                            } else {
                                $record->delete();
                            }

                            Notification::make()
                                ->title('تم حذف المتجر بنجاح')
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('خطأ في حذف المتجر')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->action(function (Collection $records) {
                            $records->each(function ($record) {
                                if ($record->hasOrders()) {
                                    // For bulk actions, we'll just remove the association
                                    $record->orders()->update(['store_id' => null]);
                                }
                                $record->delete();
                            });
                        }),
                    Tables\Actions\BulkAction::make('تفعيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check'),
                    Tables\Actions\BulkAction::make('تعطيل')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-mark'),
                ]),
            ]);
    }

    /**
     * Get the validation attributes for the store resource
     */
    public static function getValidationAttributes(): array
    {
        return [
            'latitude' => 'خط العرض',
            'longitude' => 'خط الطول',
            'name_ar' => 'الاسم بالعربية',
            'name_en' => 'الاسم بالإنجليزية',
            'address_ar' => 'العنوان بالعربية',
            'address_en' => 'العنوان بالإنجليزية',
            'city_ar' => 'المدينة بالعربية',
            'city_en' => 'المدينة بالإنجليزية',
            'country_ar' => 'الدولة بالعربية',
            'country_en' => 'الدولة بالإنجليزية',
            'phone' => 'رقم الهاتف',
            'email' => 'البريد الإلكتروني',
            'working_hours_ar' => 'ساعات العمل بالعربية',
            'working_hours_en' => 'ساعات العمل بالإنجليزية',
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStores::route('/'),
            'create' => Pages\CreateStore::route('/create'),
            'edit' => Pages\EditStore::route('/{record}/edit'),
        ];
    }
}
