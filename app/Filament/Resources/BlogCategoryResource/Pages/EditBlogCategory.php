<?php

namespace App\Filament\Resources\BlogCategoryResource\Pages;

use App\Filament\Resources\BlogCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBlogCategory extends EditRecord
{
    protected static string $resource = BlogCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('عرض'),
            Actions\DeleteAction::make()
                ->label('حذف')
                ->before(function () {
                    if ($this->record->posts()->count() > 0) {
                        throw new \Exception('لا يمكن حذف التصنيف لأنه يحتوي على مقالات مرتبطة به.');
                    }
                }),
        ];
    }

    public function getTitle(): string
    {
        return 'تعديل تصنيف المدونة';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'تم تحديث التصنيف بنجاح';
    }
}
