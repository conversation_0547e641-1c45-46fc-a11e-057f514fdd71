<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    public function getTitle(): string
    {
        return 'إنشاء دور جديد';
    }

    public function getHeading(): string
    {
        return 'إنشاء دور جديد';
    }

    public function getSubheading(): ?string
    {
        return 'قم بإنشاء دور جديد وتعيين الصلاحيات المناسبة له';
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'تم إنشاء الدور بنجاح';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterCreate(): void
    {
        Notification::make()
            ->title('تم إنشاء الدور بنجاح')
            ->body("تم إنشاء الدور '{$this->record->name}' مع " . $this->record->permissions()->count() . " صلاحية")
            ->success()
            ->send();
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction()
                ->label('إنشاء الدور'),
            $this->getCancelFormAction()
                ->label('إلغاء'),
        ];
    }
}
