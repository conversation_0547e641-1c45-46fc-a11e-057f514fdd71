<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Spatie\Permission\Models\Role;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    public function getTitle(): string
    {
        return 'تعديل الدور: ' . $this->record->name;
    }

    public function getHeading(): string
    {
        return 'تعديل الدور';
    }

    public function getSubheading(): ?string
    {
        return 'قم بتعديل بيانات الدور وتحديث الصلاحيات المرتبطة به';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->label('حذف الدور')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('حذف الدور')
                ->modalDescription('هل أنت متأكد من حذف هذا الدور؟ سيتم إزالة جميع الصلاحيات المرتبطة به.')
                ->modalSubmitActionLabel('نعم، احذف')
                ->modalCancelActionLabel('إلغاء')
                ->before(function (Role $record) {
                    // منع حذف دور super_admin
                    if ($record->name === 'super_admin') {
                        throw new \Exception('لا يمكن حذف دور السوبر أدمن لأسباب أمنية');
                    }

                    // منع حذف الأدوار التي لها مستخدمين
                    if ($record->users()->count() > 0) {
                        throw new \Exception('لا يمكن حذف هذا الدور لأنه مُعيَّن لـ ' . $record->users()->count() . ' مستخدم');
                    }
                }),
        ];
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'تم تحديث الدور بنجاح';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterSave(): void
    {
        Notification::make()
            ->title('تم تحديث الدور بنجاح')
            ->body("تم تحديث الدور '{$this->record->name}' مع " . $this->record->permissions()->count() . " صلاحية")
            ->success()
            ->send();
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction()
                ->label('حفظ التغييرات'),
            $this->getCancelFormAction()
                ->label('إلغاء'),
        ];
    }
}
