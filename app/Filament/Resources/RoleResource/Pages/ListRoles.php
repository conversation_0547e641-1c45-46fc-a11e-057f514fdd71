<?php

namespace App\Filament\Resources\RoleResource\Pages;

use App\Filament\Resources\RoleResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRoles extends ListRecords
{
    protected static string $resource = RoleResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('إنشاء دور جديد')
                ->icon('heroicon-o-plus')
                ->color('primary'),
        ];
    }

    public function getTitle(): string
    {
        return 'إدارة الأدوار والصلاحيات';
    }

    public function getHeading(): string
    {
        return 'إدارة الأدوار والصلاحيات';
    }

    public function getSubheading(): ?string
    {
        return 'قم بإنشاء وإدارة الأدوار وتعيين الصلاحيات المناسبة لكل دور';
    }
}
