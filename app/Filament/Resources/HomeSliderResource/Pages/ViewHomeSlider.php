<?php

namespace App\Filament\Resources\HomeSliderResource\Pages;

use App\Filament\Resources\HomeSliderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\IconEntry;

class ViewHomeSlider extends ViewRecord
{
    protected static string $resource = HomeSliderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('تحرير'),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('معلومات الشريحة')
                    ->schema([
                        ImageEntry::make('image')
                            ->label('صورة الشريحة')
                            ->disk('public')
                            ->height(200)
                            ->extraAttributes(['style' => 'object-fit: cover; border-radius: 12px;']),
                        
                        TextEntry::make('title_ar')
                            ->label('العنوان (عربي)')
                            ->size('lg')
                            ->weight('bold'),
                        
                        TextEntry::make('title_en')
                            ->label('العنوان (إنجليزي)')
                            ->size('lg')
                            ->weight('bold'),
                        
                        TextEntry::make('description_ar')
                            ->label('الوصف (عربي)')
                            ->markdown()
                            ->columnSpanFull(),
                        
                        TextEntry::make('description_en')
                            ->label('الوصف (إنجليزي)')
                            ->markdown()
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('إعدادات الزر')
                    ->schema([
                        TextEntry::make('button_text_ar')
                            ->label('نص الزر (عربي)'),
                        
                        TextEntry::make('button_text_en')
                            ->label('نص الزر (إنجليزي)'),
                        
                        TextEntry::make('button_link')
                            ->label('رابط الزر')
                            ->url(fn ($record) => $record->button_link)
                            ->openUrlInNewTab()
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Section::make('إعدادات العرض')
                    ->schema([
                        TextEntry::make('order')
                            ->label('الترتيب')
                            ->badge()
                            ->color('primary'),
                        
                        IconEntry::make('is_active')
                            ->label('الحالة')
                            ->boolean()
                            ->trueIcon('heroicon-o-check-circle')
                            ->falseIcon('heroicon-o-x-circle')
                            ->trueColor('success')
                            ->falseColor('danger'),
                        
                        TextEntry::make('created_at')
                            ->label('تاريخ الإنشاء')
                            ->dateTime('d/m/Y H:i'),
                        
                        TextEntry::make('updated_at')
                            ->label('آخر تحديث')
                            ->dateTime('d/m/Y H:i'),
                    ])
                    ->columns(2),
            ]);
    }
}
