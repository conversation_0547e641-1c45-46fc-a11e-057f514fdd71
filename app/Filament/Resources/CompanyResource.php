<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompanyResource\Pages;
use App\Models\Company;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

/**
 * مورد إدارة الشركات في لوحة التحكم
 */
class CompanyResource extends Resource
{
    protected static ?string $model = Company::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';

    protected static ?string $navigationLabel = 'الشركات';

    protected static ?string $modelLabel = 'شركة';

    protected static ?string $pluralModelLabel = 'الشركات';

    protected static ?string $navigationGroup = 'إدارة الشركات';

    protected static ?int $navigationSort = 11;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الشركة الأساسية')
                    ->description('أدخل البيانات الأساسية للشركة')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('اسم الشركة')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('أدخل اسم الشركة')
                            ->helperText('اسم الشركة كما سيظهر في النظام'),

                        Forms\Components\FileUpload::make('logo')
                            ->label('لوجو الشركة')
                            ->image()
                            ->directory('companies/logos')
                            ->visibility('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '1:1',
                                '16:9',
                                '4:3',
                            ])
                            ->maxSize(2048)
                            ->helperText('اختر صورة لوجو الشركة (الحد الأقصى 2 ميجابايت)'),
                    ])
                    ->columns(2),


            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('logo')
                    ->label('اللوجو')
                    ->circular()
                    ->size(50)
                    ->defaultImageUrl(url('/images/default-company-logo.png'))
                    ->toggleable(),

                Tables\Columns\TextColumn::make('name')
                    ->label('اسم الشركة')
                    ->searchable()
                    ->sortable()
                    ->weight('bold')
                    ->copyable()
                    ->copyMessage('تم نسخ اسم الشركة'),



                Tables\Columns\TextColumn::make('products_count')
                    ->label('عدد المنتجات')
                    ->counts('products')
                    ->sortable()
                    ->alignCenter()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_products')
                    ->label('الشركات التي لديها منتجات')
                    ->query(fn (Builder $query): Builder => $query->has('products')),

                Tables\Filters\Filter::make('no_products')
                    ->label('الشركات بدون منتجات')
                    ->query(fn (Builder $query): Builder => $query->doesntHave('products')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),
                ]),
            ])
            ->emptyStateHeading('لا توجد شركات')
            ->emptyStateDescription('لم يتم إنشاء أي شركات بعد. ابدأ بإضافة شركة جديدة.')
            ->emptyStateIcon('heroicon-o-building-office');
    }

    public static function getRelations(): array
    {
        return [
            // سيتم إضافة RelationManager للمنتجات لاحقاً
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompanies::route('/'),
            'create' => Pages\CreateCompany::route('/create'),
            'edit' => Pages\EditCompany::route('/{record}/edit'),
        ];
    }

    /**
     * تحديد ما إذا كان يجب تسجيل هذا المورد في التنقل
     */
    public static function shouldRegisterNavigation(): bool
    {
        return true; // سيتم تطبيق الصلاحيات من خلال Shield
    }

    /**
     * الحصول على عدد الشركات للعرض في شارة التنقل
     */
    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    /**
     * لون شارة التنقل
     */
    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
