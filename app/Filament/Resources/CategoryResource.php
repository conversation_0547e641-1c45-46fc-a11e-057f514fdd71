<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategoryResource\Pages;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class CategoryResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Category::class;

    protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?int $navigationSort = 12;

    public static function getNavigationLabel(): string
    {
        return 'فئات المنتجات';
    }

    public static function getModelLabel(): string
    {
        return __('فئة منتج');
    }

    public static function getPluralModelLabel(): string
    {
        return __('فئات المنتجات');
    }

    public static function getNavigationGroup(): string
    {
        return __('إدارة المحتوى والمنتجات');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('المعلومات الأساسية')
                            ->schema([
                                Forms\Components\Tabs::make('اللغات')
                                    ->tabs([
                                        Forms\Components\Tabs\Tab::make('العربية')
                                            ->schema([
                                                Forms\Components\TextInput::make('name_ar')
                                                    ->label('الاسم بالعربية')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(fn (string $operation, $state, Forms\Set $set) =>
                                                        $operation === 'create' ? $set('slug', \Illuminate\Support\Str::slug($state)) : null),

                                                Forms\Components\RichEditor::make('description_ar')
                                                    ->label('الوصف بالعربية')
                                                    ->columnSpanFull(),
                                            ]),

                                        Forms\Components\Tabs\Tab::make('English')
                                            ->schema([
                                                Forms\Components\TextInput::make('name_en')
                                                    ->label('Name in English')
                                                    ->maxLength(255),

                                                Forms\Components\RichEditor::make('description_en')
                                                    ->label('Description in English')
                                                    ->columnSpanFull(),
                                            ]),
                                    ])
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('slug')
                                    ->label('المعرف الفريد (Slug)')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->rules(['alpha_dash']),

                                Forms\Components\Select::make('parent_id')
                                    ->label('الفئة الأب')
                                    ->options(Category::where('is_active', true)->whereNull('parent_id')->pluck('name_ar', 'id'))
                                    ->searchable()
                                    ->placeholder('اختر الفئة الأب (اختياري)'),
                            ])
                            ->columns(2),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('الصورة')
                            ->schema([
                                Forms\Components\FileUpload::make('image')
                                    ->label('صورة الفئة')
                                    ->image()
                                    ->disk('public')
                                    ->directory('categories')
                                    ->imageEditor()
                                    ->imageEditorAspectRatios([
                                        '16:9',
                                        '4:3',
                                        '1:1',
                                    ]),
                            ]),

                        Forms\Components\Section::make('الإعدادات')
                            ->schema([
                                Forms\Components\Toggle::make('is_active')
                                    ->label('نشط')
                                    ->default(true),

                                Forms\Components\TextInput::make('order')
                                    ->label('ترتيب العرض')
                                    ->numeric()
                                    ->default(0)
                                    ->minValue(0),
                            ]),

                        Forms\Components\Section::make('الإحصائيات')
                            ->schema([
                                Forms\Components\Placeholder::make('products_count')
                                    ->label('عدد المنتجات')
                                    ->content(fn (Category $record): string => $record->products()->count() . ' منتج'),

                                Forms\Components\Placeholder::make('children_count')
                                    ->label('عدد الفئات الفرعية')
                                    ->content(fn (Category $record): string => $record->children()->count() . ' فئة فرعية'),

                                Forms\Components\Placeholder::make('created_at')
                                    ->label('تاريخ الإنشاء')
                                    ->content(fn (Category $record): string => $record->created_at?->format('Y-m-d H:i:s') ?? '-'),

                                Forms\Components\Placeholder::make('updated_at')
                                    ->label('تاريخ التحديث')
                                    ->content(fn (Category $record): string => $record->updated_at?->format('Y-m-d H:i:s') ?? '-'),
                            ])
                            ->hiddenOn('create'),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image')
                    ->label('الصورة')
                    ->circular()
                    ->disk('public'),

                Tables\Columns\TextColumn::make('name_ar')
                    ->label('الاسم بالعربية')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('name_en')
                    ->label('الاسم بالإنجليزية')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('parent.name_ar')
                    ->label('الفئة الأب')
                    ->sortable()
                    ->placeholder('فئة رئيسية'),

                Tables\Columns\TextColumn::make('slug')
                    ->label('المعرف الفريد')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('تم نسخ المعرف الفريد')
                    ->fontFamily('mono')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('products_count')
                    ->label('عدد المنتجات')
                    ->counts('products')
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('children_count')
                    ->label('الفئات الفرعية')
                    ->counts('children')
                    ->sortable()
                    ->badge()
                    ->color('secondary'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('order')
                    ->label('الترتيب')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('order')
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->placeholder('جميع الفئات')
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط'),

                Tables\Filters\SelectFilter::make('parent_id')
                    ->label('الفئة الأب')
                    ->options(Category::whereNull('parent_id')->pluck('name_ar', 'id'))
                    ->placeholder('جميع الفئات'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تعديل'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->before(function (Category $record) {
                        // التحقق من وجود منتجات أو فئات فرعية مرتبطة
                        if ($record->products()->count() > 0) {
                            throw new \Exception('لا يمكن حذف الفئة لأنها تحتوي على منتجات مرتبطة بها.');
                        }
                        if ($record->children()->count() > 0) {
                            throw new \Exception('لا يمكن حذف الفئة لأنها تحتوي على فئات فرعية.');
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->before(function ($records) {
                            foreach ($records as $record) {
                                if ($record->products()->count() > 0 || $record->children()->count() > 0) {
                                    throw new \Exception('لا يمكن حذف بعض الفئات لأنها تحتوي على منتجات أو فئات فرعية مرتبطة بها.');
                                }
                            }
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategory::route('/create'),
            'view' => Pages\ViewCategory::route('/{record}'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
        ];
    }
}
