<?php

namespace App\Filament\Resources\LanguageManagerResource\Pages;

use App\Filament\Resources\LanguageManagerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLanguages extends ListRecords
{
    protected static string $resource = LanguageManagerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
