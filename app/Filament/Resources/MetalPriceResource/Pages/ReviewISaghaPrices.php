<?php

namespace App\Filament\Resources\MetalPriceResource\Pages;

use App\Filament\Resources\MetalPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Filament\Notifications\Notification;

class ReviewISaghaPrices extends Page
{
    protected static string $resource = MetalPriceResource::class;

    protected static string $view = 'filament.resources.metal-price-resource.pages.review-isagha-prices';

    protected static ?string $title = 'مراجعة الأسعار من iSagha';

    protected static ?string $navigationLabel = 'مراجعة الأسعار';

    protected static bool $shouldRegisterNavigation = false;

    public $prices = [];

    public function mount(): void
    {
        $this->prices = session('pending_isagha_prices', []);

        if (empty($this->prices)) {
            Notification::make()
                ->title('لا توجد أسعار للمراجعة')
                ->body('لم يتم العثور على أسعار معلقة للمراجعة.')
                ->warning()
                ->send();

            $this->redirect(MetalPriceResource::getUrl('index'));
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('save_prices')
                ->label('موافقة وحفظ الأسعار')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action(function () {
                    $result = MetalPriceResource::savePrices($this->prices);
                    session()->forget('pending_isagha_prices');
                    $this->redirect(MetalPriceResource::getUrl('index'));
                    return $result;
                })
                ->requiresConfirmation()
                ->modalHeading('تأكيد حفظ الأسعار')
                ->modalDescription('سيتم تطبيق منطق ذكي للحفظ مع الاحتفاظ بالتاريخ الكامل:
• إضافة الأسعار الجديدة (المعادن/العيارات غير الموجودة)
• إضافة الأسعار المتغيرة (إنشاء سجل جديد والاحتفاظ بالقديم)
• تجاهل الأسعار المطابقة (نفس القيمة الحالية)

جميع الأسعار ستُضاف كسجلات جديدة دون فقدان التاريخ. هل تريد المتابعة؟')
                ->modalSubmitActionLabel('نعم، احفظ الأسعار')
                ->modalCancelActionLabel('إلغاء'),

            Actions\Action::make('cancel')
                ->label('إلغاء')
                ->icon('heroicon-o-x-mark')
                ->color('gray')
                ->action(function () {
                    session()->forget('pending_isagha_prices');
                    $this->redirect(MetalPriceResource::getUrl('index'));
                }),
        ];
    }
}
