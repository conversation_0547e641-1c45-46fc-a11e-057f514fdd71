<?php

namespace App\Filament\Resources\MetalPriceResource\Pages;

use App\Filament\Resources\MetalPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Filament\Notifications\Notification;

class ManualPricePreview extends Page
{
    protected static string $resource = MetalPriceResource::class;

    protected static string $view = 'filament.resources.metal-price-resource.pages.manual-price-preview';

    protected static ?string $title = 'معاينة الأسعار المحسوبة';

    protected static ?string $navigationLabel = 'معاينة الأسعار';

    protected static bool $shouldRegisterNavigation = false;

    public $prices = [];
    public $inputData = [];
    public $summary = [];

    public function mount(): void
    {
        $this->prices = session('manual_price_preview', []);
        $this->inputData = session('manual_price_input', []);
        $this->summary = session('manual_price_summary', []);

        if (empty($this->prices)) {
            Notification::make()
                ->title('لا توجد أسعار للمعاينة')
                ->body('لم يتم العثور على أسعار محسوبة للمعاينة.')
                ->warning()
                ->send();

            $this->redirect(MetalPriceResource::getUrl('index'));
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('save_manual_prices')
                ->label('حفظ الأسعار')
                ->icon('heroicon-o-check')
                ->color('success')
                ->action(function () {
                    $savedData = session('preview_gold_prices');
                    if ($savedData) {
                        $result = MetalPriceResource::processManualGoldUpdate($savedData);
                        session()->forget(['manual_price_preview', 'manual_price_input', 'manual_price_summary', 'preview_gold_prices']);
                        $this->redirect(MetalPriceResource::getUrl('index'));
                        return $result;
                    }
                })
                ->requiresConfirmation()
                ->modalHeading('تأكيد حفظ الأسعار')
                ->modalDescription('هل أنت متأكد من حفظ هذه الأسعار؟ سيتم تطبيق التحديثات على قاعدة البيانات.')
                ->modalSubmitActionLabel('نعم، احفظ الأسعار')
                ->modalCancelActionLabel('إلغاء'),

            Actions\Action::make('edit_manual_data')
                ->label('تعديل البيانات')
                ->icon('heroicon-o-pencil')
                ->color('warning')
                ->action(function () {
                    session()->forget(['manual_price_preview', 'manual_price_input', 'manual_price_summary', 'preview_gold_prices']);
                    
                    Notification::make()
                        ->title('تم إلغاء المعاينة')
                        ->body('يمكنك الآن تعديل البيانات وإعادة المعاينة من صفحة إدارة الأسعار')
                        ->info()
                        ->send();

                    $this->redirect(MetalPriceResource::getUrl('index'));
                }),

            Actions\Action::make('cancel_manual_review')
                ->label('إلغاء')
                ->icon('heroicon-o-x-mark')
                ->color('gray')
                ->action(function () {
                    session()->forget(['manual_price_preview', 'manual_price_input', 'manual_price_summary', 'preview_gold_prices']);
                    $this->redirect(MetalPriceResource::getUrl('index'));
                }),
        ];
    }

    public function getViewData(): array
    {
        return [
            'prices' => collect($this->prices)->map(function ($price) {
                return (object) $price;
            }),
            'inputData' => $this->inputData,
            'summary' => $this->summary,
        ];
    }
}
