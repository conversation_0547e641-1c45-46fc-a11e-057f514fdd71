<?php

namespace App\Filament\Resources\MetalPriceResource\Pages;

use App\Filament\Resources\MetalPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class DebugPrices extends Page
{
    protected static string $resource = MetalPriceResource::class;

    protected static string $view = 'filament.resources.metal-price-resource.pages.debug-prices';

    protected static ?string $title = 'جميع الأسعار المستخرجة من iSagha';

    protected static ?string $navigationLabel = 'تشخيص الأسعار';

    protected static bool $shouldRegisterNavigation = false;

    public $extractedPrices = [];

    public function mount(): void
    {
        $this->extractedPrices = session('debug_scraped_prices', []);

        if (empty($this->extractedPrices)) {
            // محاولة جلب البيانات مباشرة من iSagha
            $this->fetchPricesFromISagha();
        }
    }

    public function fetchPricesFromISagha()
    {
        try {
            $response = \Illuminate\Support\Facades\Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'ar,en-US;q=0.7,en;q=0.3',
                ])
                ->get('https://market.isagha.com/prices');

            if ($response->successful()) {
                $html = $response->body();

                // تسجيل معلومات HTML للتشخيص
                Log::info('iSagha HTML fetched', [
                    'html_length' => strlen($html),
                    'contains_prices' => strpos($html, 'ج.م') !== false,
                    'contains_gold' => strpos($html, 'ذهب') !== false,
                    'contains_silver' => strpos($html, 'فضة') !== false,
                ]);

                $allPrices = MetalPriceResource::extractAllPricesFromHTML($html);

                // تسجيل نتائج الاستخراج
                Log::info('Extraction results', [
                    'extracted_count' => count($allPrices),
                    'prices' => $allPrices
                ]);

                session(['debug_scraped_prices' => $allPrices]);
                $this->extractedPrices = $allPrices;

                if (count($allPrices) > 0) {
                    Notification::make()
                        ->title('تم جلب الأسعار بنجاح')
                        ->body('تم استخراج ' . count($allPrices) . ' سعر من موقع iSagha.')
                        ->success()
                        ->send();
                } else {
                    Notification::make()
                        ->title('لم يتم استخراج أي أسعار')
                        ->body('تم جلب الصفحة (طول: ' . strlen($html) . ' حرف) ولكن لم يتم استخراج أي أسعار. راجع الـ logs للتفاصيل.')
                        ->warning()
                        ->send();
                }

            } else {
                Notification::make()
                    ->title('فشل في جلب البيانات')
                    ->body('كود الاستجابة: ' . $response->status() . '. لم يتم الاتصال بموقع iSagha بنجاح.')
                    ->danger()
                    ->send();
            }
        } catch (\Exception $e) {
            Log::error('Error fetching iSagha prices', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            Notification::make()
                ->title('خطأ في جلب البيانات')
                ->body('حدث خطأ: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('refresh_data')
                ->label('تحديث البيانات')
                ->icon('heroicon-o-arrow-path')
                ->color('info')
                ->action(function () {
                    // إعادة جلب البيانات من iSagha
                    $response = \Illuminate\Support\Facades\Http::timeout(30)
                        ->withHeaders([
                            'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                            'Accept-Language' => 'ar,en-US;q=0.7,en;q=0.3',
                        ])
                        ->get('https://market.isagha.com/prices');

                    if ($response->successful()) {
                        $html = $response->body();
                        $allPrices = MetalPriceResource::extractAllPricesFromHTML($html);

                        session(['debug_scraped_prices' => $allPrices]);
                        $this->extractedPrices = $allPrices;

                        Notification::make()
                            ->title('تم تحديث البيانات')
                            ->body('تم استخراج ' . count($allPrices) . ' سعر جديد من الموقع.')
                            ->success()
                            ->send();
                    } else {
                        Notification::make()
                            ->title('فشل في التحديث')
                            ->body('لم يتم الاتصال بموقع iSagha بنجاح.')
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('clear_data')
                ->label('مسح البيانات')
                ->icon('heroicon-o-trash')
                ->color('danger')
                ->action(function () {
                    session()->forget('debug_scraped_prices');
                    $this->redirect(MetalPriceResource::getUrl('index'));
                })
                ->requiresConfirmation()
                ->modalHeading('مسح البيانات')
                ->modalDescription('هل تريد مسح جميع البيانات المستخرجة؟')
                ->modalSubmitActionLabel('نعم، امسح')
                ->modalCancelActionLabel('إلغاء'),

            Actions\Action::make('back_to_prices')
                ->label('العودة للأسعار')
                ->icon('heroicon-o-arrow-right')
                ->color('gray')
                ->url(MetalPriceResource::getUrl('index')),
        ];
    }

    public function getStats()
    {
        $stats = [
            'total' => count($this->extractedPrices),
            'gold' => 0,
            'silver' => 0,
            'gold_coin' => 0,
            'other' => 0,
            'price_ranges' => [
                'low' => [],
                'medium' => [],
                'high' => [],
            ]
        ];

        foreach ($this->extractedPrices as $price) {
            $text = strtolower($price['purity_text']);

            if (strpos($text, 'ذهب') !== false || strpos($text, 'gold') !== false) {
                if (strpos($text, 'جنيه') !== false) {
                    $stats['gold_coin']++;
                } else {
                    $stats['gold']++;
                }
            } elseif (strpos($text, 'فضة') !== false || strpos($text, 'silver') !== false) {
                $stats['silver']++;
            } else {
                $stats['other']++;
            }

            // تصنيف الأسعار
            $priceValue = $price['price'];
            if ($priceValue < 100) {
                $stats['price_ranges']['low'][] = $priceValue;
            } elseif ($priceValue < 1000) {
                $stats['price_ranges']['medium'][] = $priceValue;
            } else {
                $stats['price_ranges']['high'][] = $priceValue;
            }
        }

        return $stats;
    }
}
