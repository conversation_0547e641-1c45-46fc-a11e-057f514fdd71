<?php

namespace App\Filament\Resources\MetalPriceResource\Pages;

use App\Filament\Resources\MetalPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMetalPrice extends EditRecord
{
    protected static string $resource = MetalPriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // Set automatic fields
        $data['price_date'] = now();
        $data['currency'] = 'EGP';
        $data['source'] = 'manual';

        // Calculate price per ounce automatically
        if (isset($data['price_per_gram'])) {
            $data['price_per_ounce'] = round($data['price_per_gram'] * 31.1035, 2);
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // إذا تم تفعيل هذا السعر، إلغاء تفعيل الأسعار الأخرى لنفس المعدن والعيار
        $record = $this->record;

        if ($record->is_active) {
            \App\Models\MetalPrice::where('metal_type', $record->metal_type)
                ->where('purity', $record->purity)
                ->where('currency', $record->currency)
                ->where('id', '!=', $record->id) // عدا السعر الحالي
                ->update(['is_active' => false]);
        }
    }
}
