<?php

namespace App\Filament\Resources\MetalPriceResource\Pages;

use App\Filament\Resources\MetalPriceResource;
use App\Models\MetalType;
use App\Models\MetalPurity;
use Filament\Actions;
use Filament\Resources\Pages\Page;
use Filament\Notifications\Notification;

class MissingPurities extends Page
{
    protected static string $resource = MetalPriceResource::class;

    protected static string $view = 'filament.resources.metal-price-resource.pages.missing-purities';

    protected static ?string $title = 'العيارات المفقودة من iSagha';

    protected static ?string $navigationLabel = 'العيارات المفقودة';

    protected static bool $shouldRegisterNavigation = false;

    public $missingPurities = [];

    public function mount(): void
    {
        $this->missingPurities = session('missing_purities_from_isagha', []);
        
        if (empty($this->missingPurities)) {
            Notification::make()
                ->title('لا توجد عيارات مفقودة')
                ->body('لم يتم العثور على عيارات مفقودة من آخر عملية جلب.')
                ->warning()
                ->send();
                
            $this->redirect(MetalPriceResource::getUrl('index'));
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('add_all_purities')
                ->label('إضافة جميع العيارات')
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->action(function () {
                    $addedCount = 0;
                    $errors = [];
                    
                    foreach ($this->missingPurities as $missing) {
                        try {
                            // العثور على نوع المعدن
                            $metalType = MetalType::where('name', $missing['metal_type'])->first();
                            
                            if (!$metalType) {
                                $errors[] = "نوع المعدن غير موجود: " . $missing['metal_type'];
                                continue;
                            }
                            
                            // إنشاء العيار الجديد
                            MetalPurity::create([
                                'metal_type_id' => $metalType->id,
                                'name' => $missing['suggested_name'],
                                'name_ar' => $missing['suggested_name_ar'],
                                'is_active' => true,
                                'sort_order' => 999, // سيتم ترتيبها لاحقاً
                            ]);
                            
                            $addedCount++;
                            
                        } catch (\Exception $e) {
                            $errors[] = "خطأ في إضافة " . $missing['suggested_name'] . ": " . $e->getMessage();
                        }
                    }
                    
                    // إرسال إشعار بالنتيجة
                    if ($addedCount > 0) {
                        Notification::make()
                            ->title('تم إضافة العيارات بنجاح')
                            ->body("تم إضافة {$addedCount} عيار جديد.")
                            ->success()
                            ->send();
                            
                        // مسح البيانات من الجلسة
                        session()->forget('missing_purities_from_isagha');
                        
                        // العودة لصفحة الأسعار
                        $this->redirect(MetalPriceResource::getUrl('index'));
                    }
                    
                    if (!empty($errors)) {
                        Notification::make()
                            ->title('بعض الأخطاء في الإضافة')
                            ->body(implode("\n", array_slice($errors, 0, 3)))
                            ->warning()
                            ->send();
                    }
                })
                ->requiresConfirmation()
                ->modalHeading('تأكيد إضافة العيارات')
                ->modalDescription('هل تريد إضافة جميع العيارات المفقودة إلى النظام؟')
                ->modalSubmitActionLabel('نعم، أضف جميع العيارات')
                ->modalCancelActionLabel('إلغاء'),
                
            Actions\Action::make('cancel')
                ->label('تجاهل')
                ->icon('heroicon-o-x-mark')
                ->color('gray')
                ->action(function () {
                    session()->forget('missing_purities_from_isagha');
                    $this->redirect(MetalPriceResource::getUrl('index'));
                }),
        ];
    }

    public function addSinglePurity($index)
    {
        if (!isset($this->missingPurities[$index])) {
            Notification::make()
                ->title('خطأ')
                ->body('العيار غير موجود.')
                ->danger()
                ->send();
            return;
        }
        
        $missing = $this->missingPurities[$index];
        
        try {
            // العثور على نوع المعدن
            $metalType = MetalType::where('name', $missing['metal_type'])->first();
            
            if (!$metalType) {
                Notification::make()
                    ->title('خطأ')
                    ->body('نوع المعدن غير موجود: ' . $missing['metal_type'])
                    ->danger()
                    ->send();
                return;
            }
            
            // إنشاء العيار الجديد
            MetalPurity::create([
                'metal_type_id' => $metalType->id,
                'name' => $missing['suggested_name'],
                'name_ar' => $missing['suggested_name_ar'],
                'is_active' => true,
                'sort_order' => 999,
            ]);
            
            // إزالة العيار من القائمة
            unset($this->missingPurities[$index]);
            $this->missingPurities = array_values($this->missingPurities);
            session(['missing_purities_from_isagha' => $this->missingPurities]);
            
            Notification::make()
                ->title('تم إضافة العيار بنجاح')
                ->body('تم إضافة ' . $missing['suggested_name_ar'])
                ->success()
                ->send();
                
            // إذا لم تعد هناك عيارات مفقودة، العودة للصفحة الرئيسية
            if (empty($this->missingPurities)) {
                session()->forget('missing_purities_from_isagha');
                $this->redirect(MetalPriceResource::getUrl('index'));
            }
            
        } catch (\Exception $e) {
            Notification::make()
                ->title('خطأ في الإضافة')
                ->body('حدث خطأ: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
