<?php

namespace App\Filament\Resources\MetalPriceResource\Pages;

use App\Filament\Resources\MetalPriceResource;
use Filament\Resources\Pages\CreateRecord;

class CreateMetalPrice extends CreateRecord
{
    protected static string $resource = MetalPriceResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Set automatic fields
        $data['price_date'] = now();
        $data['currency'] = 'EGP';
        $data['source'] = 'manual';
        $data['is_active'] = true; // السعر الجديد يكون مفعل دائماً

        // Calculate price per ounce automatically
        if (isset($data['price_per_gram'])) {
            $data['price_per_ounce'] = round($data['price_per_gram'] * 31.1035, 2);
        }

        return $data;
    }

    protected function afterCreate(): void
    {
        // إلغاء تفعيل جميع الأسعار الأخرى لنفس المعدن والعيار
        $record = $this->record;

        \App\Models\MetalPrice::where('metal_type', $record->metal_type)
            ->where('purity', $record->purity)
            ->where('currency', $record->currency)
            ->where('id', '!=', $record->id) // عدا السعر الحالي
            ->update(['is_active' => false]);
    }
}
