<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use App\Traits\HasPermissionFiltering;

class ProductResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?int $navigationSort = 11;

    public static function getNavigationLabel(): string
    {
        return __('المنتجات');
    }

    public static function getModelLabel(): string
    {
        return __('منتج');
    }

    public static function getPluralModelLabel(): string
    {
        return __('المنتجات');
    }

    public static function getNavigationGroup(): string
    {
        return __('إدارة المحتوى والمنتجات');
    }

    protected static ?string $recordTitleAttribute = 'name_ar';

    /**
     * التحقق من وجود slug في قاعدة البيانات
     */
    public static function slugExists(string $slug, ?int $excludeId = null): bool
    {
        $query = Product::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * التحقق من وجود اسم عربي في قاعدة البيانات
     */
    public static function nameArExists(string $name, ?int $excludeId = null): bool
    {
        $query = Product::where('name_ar', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * التحقق من وجود اسم إنجليزي في قاعدة البيانات
     */
    public static function nameEnExists(string $name, ?int $excludeId = null): bool
    {
        $query = Product::where('name_en', $name);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }





    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات أساسية')
                    ->description('المعلومات الأساسية للمنتج')
                    ->icon('heroicon-o-information-circle')
                    ->schema([
                        Forms\Components\TextInput::make('name_ar')
                            ->label('الاسم بالعربية')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->unique(Product::class, 'name_ar', ignoreRecord: true)
                            ->validationMessages([
                                'unique' => 'هذا الاسم مستخدم مسبقاً، يرجى اختيار اسم آخر',
                            ])
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                if ($state) {
                                    // إنشاء slug فريد تلقائياً في الخلفية
                                    $baseSlug = \Illuminate\Support\Str::slug($state);
                                    $slug = $baseSlug;
                                    $counter = 1;

                                    // الحصول على ID المنتج الحالي (في حالة التعديل)
                                    $currentId = $get('id');

                                    // التحقق من تفرد الـ slug
                                    while (self::slugExists($slug, $currentId)) {
                                        $slug = $baseSlug . '-' . $counter;
                                        $counter++;
                                    }

                                    $set('slug', $slug);
                                }
                            }),

                        Forms\Components\Hidden::make('slug')
                            ->required(),

                        Forms\Components\TextInput::make('name_en')
                            ->label('الاسم بالإنجليزية')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->unique(Product::class, 'name_en', ignoreRecord: true)
                            ->validationMessages([
                                'unique' => 'هذا الاسم مستخدم مسبقاً، يرجى اختيار اسم آخر',
                            ]),



                        Forms\Components\Select::make('category_id')
                            ->label('الفئة')
                            ->relationship('category', 'name_ar')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name_ar')
                                    ->label('الاسم بالعربية')
                                    ->required(),
                                Forms\Components\TextInput::make('name_en')
                                    ->label('الاسم بالإنجليزية')
                                    ->required(),
                            ]),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true)
                            ->helperText('تحديد ما إذا كان المنتج نشطاً ومرئياً للعملاء'),

                        Forms\Components\Toggle::make('is_featured')
                            ->label('مميز')
                            ->default(false)
                            ->helperText('المنتجات المميزة تظهر في الصفحة الرئيسية'),

                        Forms\Components\Toggle::make('show_price')
                            ->label('عرض السعر')
                            ->default(true)
                            ->helperText('إذا كان معطلاً، سيظهر "تواصل للسعر" بدلاً من السعر'),
                    ])->columns(2),

                Forms\Components\Section::make('التسعير والمخزون')
                    ->description('معلومات الأسعار والمخزون')
                    ->icon('heroicon-o-currency-dollar')
                    ->schema([
                        Forms\Components\TextInput::make('price')
                            ->label('السعر الأساسي')
                            ->numeric()
                            ->prefix('ج.م')
                            ->required()
                            ->step(0.01),

                        Forms\Components\TextInput::make('old_price')
                            ->label('السعر القديم')
                            ->numeric()
                            ->prefix('ج.م')
                            ->step(0.01)
                            ->nullable()
                            ->helperText('السعر قبل الخصم (اختياري)'),

                        Forms\Components\TextInput::make('discount_percentage')
                            ->label('نسبة الخصم')
                            ->numeric()
                            ->suffix('%')
                            ->step(0.01)
                            ->minValue(0)
                            ->maxValue(100)
                            ->nullable()
                            ->helperText('نسبة الخصم (اختياري)'),

                        Forms\Components\TextInput::make('stock_quantity')
                            ->label('كمية المخزون')
                            ->numeric()
                            ->default(0)
                            ->required()
                            ->minValue(0),
                    ])->columns(2),

                Forms\Components\Section::make('المواصفات التقنية')
                    ->description('المواصفات الفنية للمنتج')
                    ->icon('heroicon-o-cog-6-tooth')
                    ->schema([
                        Forms\Components\TextInput::make('weight')
                            ->label('الوزن')
                            ->numeric()
                            ->suffix('جرام')
                            ->step(0.001)
                            ->default(0)
                            ->required()
                            ->minValue(0)
                            ->helperText('الوزن بالجرام (مطلوب)'),

                        Forms\Components\Select::make('metal_type_id')
                            ->label('نوع المعدن')
                            ->relationship('metal', 'name_ar')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->live()
                            ->afterStateUpdated(function (Forms\Set $set) {
                                // إعادة تعيين العيار عند تغيير نوع المعدن
                                $set('metal_purity_id', null);
                            })
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->label('الاسم بالإنجليزية')
                                    ->required(),
                                Forms\Components\TextInput::make('name_ar')
                                    ->label('الاسم بالعربية')
                                    ->required(),
                                Forms\Components\TextInput::make('color')
                                    ->label('اللون')
                                    ->default('#6b7280'),
                                Forms\Components\TextInput::make('icon')
                                    ->label('الأيقونة')
                                    ->default('fas fa-coins'),
                            ])
                            ->helperText('اختر نوع المعدن من القائمة'),

                        Forms\Components\Select::make('metal_purity_id')
                            ->label('عيار المعدن')
                            ->options(function (Forms\Get $get) {
                                $metalTypeId = $get('metal_type_id');
                                if (!$metalTypeId) {
                                    return [];
                                }
                                return \App\Models\MetalPurity::where('metal_type_id', $metalTypeId)
                                    ->where('is_active', true)
                                    ->orderBy('sort_order')
                                    ->pluck('name_ar', 'id');
                            })
                            ->searchable()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set, Forms\Get $get) {
                                // إعادة تعيين العيار إذا تم تغيير نوع المعدن
                                $metalTypeId = $get('metal_type_id');
                                if ($state && $metalTypeId) {
                                    $purity = \App\Models\MetalPurity::find($state);
                                    if ($purity && $purity->metal_type_id != $metalTypeId) {
                                        $set('metal_purity_id', null);
                                    }
                                }
                            })
                            ->createOptionForm([
                                Forms\Components\Select::make('metal_type_id')
                                    ->label('نوع المعدن')
                                    ->relationship('metalType', 'name_ar')
                                    ->required(),
                                Forms\Components\TextInput::make('name')
                                    ->label('الاسم بالإنجليزية')
                                    ->required(),
                                Forms\Components\TextInput::make('name_ar')
                                    ->label('الاسم بالعربية')
                                    ->required(),
                                Forms\Components\TextInput::make('purity_percentage')
                                    ->label('نسبة النقاء')
                                    ->numeric()
                                    ->step(0.01)
                                    ->suffix('%'),
                            ])
                            ->helperText('اختر عيار المعدن من القائمة (يعتمد على نوع المعدن المحدد)')
                            ->disabled(fn (Forms\Get $get) => !$get('metal_type_id'))
                            ->placeholder('اختر نوع المعدن أولاً'),
                    ])->columns(2),

                Forms\Components\Section::make('الوصف والتفاصيل')
                    ->description('وصف المنتج والتفاصيل الإضافية')
                    ->icon('heroicon-o-document-text')
                    ->schema([
                        Forms\Components\Textarea::make('description_ar')
                            ->label('الوصف بالعربية')
                            ->maxLength(65535)
                            ->rows(4)
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('description_en')
                            ->label('الوصف بالإنجليزية')
                            ->maxLength(65535)
                            ->rows(4)
                            ->required()
                            ->columnSpanFull(),
                    ]),



            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('primaryImage.image_path')
                    ->label('الصورة')
                    ->circular()
                    ->defaultImageUrl(url('/images/products/default.jpg'))
                    ->size(50),

                Tables\Columns\TextColumn::make('name_ar')
                    ->label('الاسم بالعربية')
                    ->searchable()
                    ->sortable()
                    ->weight('medium')
                    ->wrap(),



                Tables\Columns\TextColumn::make('category.name_ar')
                    ->label('الفئة')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('price')
                    ->label('السعر')
                    ->money('EGP')
                    ->sortable()
                    ->weight('bold')
                    ->color('success'),

                Tables\Columns\TextColumn::make('stock_quantity')
                    ->label('المخزون')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        $state == 0 => 'danger',
                        $state < 10 => 'warning',
                        default => 'success',
                    }),

                Tables\Columns\TextColumn::make('weight')
                    ->label('الوزن')
                    ->suffix(' جرام')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('metal.name_ar')
                    ->label('نوع المعدن')
                    ->badge()
                    ->color('warning')
                    ->sortable(),

                Tables\Columns\TextColumn::make('metalPurity.name_ar')
                    ->label('العيار')
                    ->badge()
                    ->color('info')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\IconColumn::make('show_price')
                    ->label('عرض السعر')
                    ->boolean()
                    ->trueIcon('heroicon-o-currency-dollar')
                    ->falseIcon('heroicon-o-chat-bubble-left-right')
                    ->trueColor('success')
                    ->falseColor('warning'),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\IconColumn::make('is_featured')
                    ->label('مميز')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-star')
                    ->trueColor('warning')
                    ->falseColor('gray'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التحديث')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('الفئة')
                    ->relationship('category', 'name_ar')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Tables\Filters\SelectFilter::make('metal_type_id')
                    ->label('نوع المعدن')
                    ->relationship('metal', 'name_ar')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Tables\Filters\SelectFilter::make('metal_purity_id')
                    ->label('عيار المعدن')
                    ->relationship('metalPurity', 'name_ar')
                    ->searchable()
                    ->preload()
                    ->multiple(),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط')
                    ->placeholder('الكل')
                    ->trueLabel('نشط')
                    ->falseLabel('غير نشط'),

                Tables\Filters\TernaryFilter::make('is_featured')
                    ->label('مميز')
                    ->placeholder('الكل')
                    ->trueLabel('مميز')
                    ->falseLabel('غير مميز'),

                Tables\Filters\TernaryFilter::make('show_price')
                    ->label('عرض السعر')
                    ->placeholder('الكل')
                    ->trueLabel('يعرض السعر')
                    ->falseLabel('تواصل للسعر'),

                Tables\Filters\Filter::make('price_range')
                    ->form([
                        Forms\Components\TextInput::make('price_from')
                            ->label('السعر من')
                            ->numeric()
                            ->prefix('ج.م'),
                        Forms\Components\TextInput::make('price_to')
                            ->label('السعر إلى')
                            ->numeric()
                            ->prefix('ج.م'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['price_from'],
                                fn (Builder $query, $price): Builder => $query->where('price', '>=', $price),
                            )
                            ->when(
                                $data['price_to'],
                                fn (Builder $query, $price): Builder => $query->where('price', '<=', $price),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['price_from'] ?? null) {
                            $indicators[] = 'السعر من: ' . number_format($data['price_from']) . ' ج.م';
                        }
                        if ($data['price_to'] ?? null) {
                            $indicators[] = 'السعر إلى: ' . number_format($data['price_to']) . ' ج.م';
                        }
                        return $indicators;
                    }),

                Tables\Filters\Filter::make('low_stock')
                    ->label('مخزون منخفض')
                    ->query(fn (Builder $query): Builder => $query->where('stock_quantity', '<', 10))
                    ->toggle(),

                Tables\Filters\Filter::make('out_of_stock')
                    ->label('نفد المخزون')
                    ->query(fn (Builder $query): Builder => $query->where('stock_quantity', '=', 0))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\ViewAction::make()
                        ->label('عرض')
                        ->icon('heroicon-o-eye'),
                    Tables\Actions\EditAction::make()
                        ->label('تعديل')
                        ->icon('heroicon-o-pencil'),
                    Tables\Actions\Action::make('duplicate')
                        ->label('نسخ')
                        ->icon('heroicon-o-document-duplicate')
                        ->action(function (Product $record) {
                            $newProduct = $record->replicate();
                            $newProduct->name_ar = $record->name_ar . ' (نسخة)';
                            $newProduct->name_en = $record->name_en . ' (Copy)';
                            $newProduct->slug = $record->slug . '-copy-' . time();
                            $newProduct->sku = $record->sku ? $record->sku . '-COPY' : null;
                            $newProduct->save();
                        })
                        ->requiresConfirmation()
                        ->modalHeading('نسخ المنتج')
                        ->modalDescription('هل أنت متأكد من رغبتك في نسخ هذا المنتج؟')
                        ->modalSubmitActionLabel('نسخ'),
                    Tables\Actions\DeleteAction::make()
                        ->label('حذف')
                        ->icon('heroicon-o-trash'),
                ])
                ->label('الإجراءات')
                ->icon('heroicon-m-ellipsis-vertical')
                ->size('sm')
                ->color('gray')
                ->button(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('تعطيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('feature')
                        ->label('تمييز المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_featured' => true]))
                        ->icon('heroicon-o-star')
                        ->color('warning')
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('unfeature')
                        ->label('إلغاء تمييز المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_featured' => false]))
                        ->icon('heroicon-o-star')
                        ->color('gray')
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('show_price')
                        ->label('عرض السعر للمحدد')
                        ->action(fn (Collection $records) => $records->each->update(['show_price' => true]))
                        ->icon('heroicon-o-currency-dollar')
                        ->color('success')
                        ->requiresConfirmation(),

                    Tables\Actions\BulkAction::make('hide_price')
                        ->label('إخفاء السعر للمحدد')
                        ->action(fn (Collection $records) => $records->each->update(['show_price' => false]))
                        ->icon('heroicon-o-chat-bubble-left-right')
                        ->color('warning')
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc')
            ->persistSortInSession()
            ->persistSearchInSession()
            ->persistFiltersInSession()
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            \App\Filament\Resources\ProductResource\RelationManagers\ProductImagesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'create-with-images' => Pages\CreateProductWithImages::route('/{record}/create-with-images'),
            'view' => Pages\ViewProduct::route('/{record}'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
