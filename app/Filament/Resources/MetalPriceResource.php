<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MetalPriceResource\Pages;
use App\Models\MetalPrice;
use App\Models\MetalType;
use App\Models\MetalPurity;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Filament\Notifications\Notification;
use App\Traits\HasPermissionFiltering;

class MetalPriceResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = MetalPrice::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'أسعار المعادن';

    protected static ?string $modelLabel = 'سعر معدن';

    protected static ?string $pluralModelLabel = 'أسعار المعادن';

    protected static ?string $navigationGroup = 'إدارة أسعار المعادن';

    protected static ?int $navigationSort = 43;

    protected static ?string $recordTitleAttribute = 'metal_type';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات سعر المعدن')
                    ->description('أدخل تفاصيل سعر المعدن بدقة')
                    ->icon('heroicon-o-banknotes')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('metal_type')
                                    ->label('نوع المعدن')
                                    ->options(MetalType::active()->ordered()->pluck('name_ar', 'name'))
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(fn (callable $set) => $set('purity', null))
                                    ->helperText('اختر نوع المعدن المراد تحديد سعره'),

                                Forms\Components\Select::make('purity')
                                    ->label('العيار/النقاء')
                                    ->options(function (callable $get) {
                                        $metalTypeName = $get('metal_type');
                                        if (!$metalTypeName) {
                                            return [];
                                        }

                                        $metalType = MetalType::where('name', $metalTypeName)->first();
                                        if (!$metalType) {
                                            return [];
                                        }

                                        return MetalPurity::where('metal_type_id', $metalType->id)
                                            ->active()
                                            ->ordered()
                                            ->pluck('name_ar', 'name');
                                    })
                                    ->required()
                                    ->helperText('اختر العيار المناسب للمعدن المحدد'),
                            ]),

                        Forms\Components\Section::make('أسعار البيع')
                            ->description('أسعار بيع المعدن للعملاء')
                            ->icon('heroicon-o-arrow-trending-up')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('price_per_gram')
                                            ->label('سعر البيع لكل جرام')
                                            ->numeric()
                                            ->prefix('ج.م')
                                            ->step(0.01)
                                            ->minValue(0.01)
                                            ->maxValue(999999.99)
                                            ->required()
                                            ->helperText('سعر بيع الجرام بالجنيه المصري'),

                                        Forms\Components\TextInput::make('price_per_piece')
                                            ->label('سعر البيع لكل قطعة')
                                            ->numeric()
                                            ->prefix('ج.م')
                                            ->step(0.01)
                                            ->minValue(0.01)
                                            ->maxValue(999999.99)
                                            ->visible(function (callable $get) {
                                                return $get('metal_type') === 'gold_coin';
                                            })
                                            ->helperText('سعر بيع القطعة بالجنيه المصري (للجنيهات الذهبية فقط)'),
                                    ]),
                            ]),

                        Forms\Components\Section::make('أسعار الشراء')
                            ->description('أسعار شراء المعدن من العملاء (اختياري)')
                            ->icon('heroicon-o-arrow-trending-down')
                            ->schema([
                                Forms\Components\Grid::make(2)
                                    ->schema([
                                        Forms\Components\TextInput::make('purchase_price_per_gram')
                                            ->label('سعر الشراء لكل جرام')
                                            ->numeric()
                                            ->prefix('ج.م')
                                            ->step(0.01)
                                            ->minValue(0.01)
                                            ->maxValue(999999.99)
                                            ->helperText('سعر شراء الجرام بالجنيه المصري (اختياري)'),

                                        Forms\Components\TextInput::make('purchase_price_per_piece')
                                            ->label('سعر الشراء لكل قطعة')
                                            ->numeric()
                                            ->prefix('ج.م')
                                            ->step(0.01)
                                            ->minValue(0.01)
                                            ->maxValue(999999.99)
                                            ->visible(function (callable $get) {
                                                return $get('metal_type') === 'gold_coin';
                                            })
                                            ->helperText('سعر شراء القطعة بالجنيه المصري (للجنيهات الذهبية فقط)'),
                                    ]),
                            ])
                            ->collapsible(),
                    ]),

                Forms\Components\Toggle::make('is_active')
                    ->label('نشط')
                    ->default(true)
                    ->helperText('تفعيل/إلغاء تفعيل هذا السعر'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('metal_type')
                    ->label('نوع المعدن')
                    ->formatStateUsing(function (string $state): string {
                        $metalType = MetalType::where('name', $state)->first();
                        return $metalType ? $metalType->name_ar : $state;
                    })
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'gold' => 'warning',
                        'silver' => 'gray',
                        'gold_coin' => 'success',
                        default => 'secondary',
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('purity')
                    ->label('العيار')
                    ->formatStateUsing(function (string $state, $record): string {
                        $metalType = MetalType::where('name', $record->metal_type)->first();
                        if ($metalType) {
                            $purity = MetalPurity::where('metal_type_id', $metalType->id)
                                ->where('name', $state)
                                ->first();
                            return $purity ? $purity->name_ar : $state;
                        }
                        return $state;
                    })
                    ->badge()
                    ->color('success')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('price_per_gram')
                    ->label('سعر البيع/جرام')
                    ->money('EGP', locale: 'ar')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->color('success'),

                Tables\Columns\TextColumn::make('purchase_price_per_gram')
                    ->label('سعر الشراء/جرام')
                    ->money('EGP', locale: 'ar')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->color('warning')
                    ->placeholder('غير محدد')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('price_per_piece')
                    ->label('سعر البيع/قطعة')
                    ->money('EGP', locale: 'ar')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->color('success')
                    ->placeholder('غير محدد')
                    ->visible(fn ($record) => $record && $record->metal_type === 'gold_coin'),

                Tables\Columns\TextColumn::make('purchase_price_per_piece')
                    ->label('سعر الشراء/قطعة')
                    ->money('EGP', locale: 'ar')
                    ->sortable()
                    ->searchable()
                    ->weight('bold')
                    ->color('warning')
                    ->placeholder('غير محدد')
                    ->visible(fn ($record) => $record && $record->metal_type === 'gold_coin')
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ السعر')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->description(fn (MetalPrice $record): string =>
                        $record->created_at->diffForHumans()
                    ),

                Tables\Columns\TextColumn::make('source')
                    ->label('المصدر')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'manual' => 'primary',
                        'api' => 'success',
                        'import' => 'warning',
                        'isagha_api' => 'info',
                        'test_data' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'manual' => 'يدوي',
                        'api' => 'API',
                        'import' => 'استيراد',
                        'test_data' => 'بيانات تجريبية',
                        'isagha_api' => 'iSagha API',
                        default => $state,
                    })
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('آخر تحديث')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('metal_type')
                    ->label('نوع المعدن')
                    ->options(MetalType::active()->ordered()->pluck('name_ar', 'name'))
                    ->placeholder('جميع الأنواع'),

                Tables\Filters\SelectFilter::make('purity')
                    ->label('العيار')
                    ->options(function () {
                        $options = [];
                        $metalTypes = MetalType::active()->with('purities')->get();

                        foreach ($metalTypes as $metalType) {
                            foreach ($metalType->purities as $purity) {
                                $options[$purity->name] = $purity->name_ar;
                            }
                        }

                        return $options;
                    })
                    ->placeholder('جميع العيارات'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->placeholder('جميع الأسعار')
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->color('primary'),
                    Tables\Actions\ReplicateAction::make()
                        ->label('نسخ')
                        ->color('warning'),
                    Tables\Actions\DeleteAction::make()
                        ->color('danger'),
                ])
                    ->label('إجراءات')
                    ->color('gray')
                    ->icon('heroicon-m-ellipsis-vertical')
                    ->size('sm'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد'),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => true]))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->modalHeading('تفعيل الأسعار المحددة')
                        ->modalDescription('هل أنت متأكد من تفعيل جميع الأسعار المحددة؟')
                        ->modalSubmitActionLabel('تفعيل'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('إلغاء تفعيل المحدد')
                        ->action(fn (Collection $records) => $records->each->update(['is_active' => false]))
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('إلغاء تفعيل الأسعار المحددة')
                        ->modalDescription('هل أنت متأكد من إلغاء تفعيل جميع الأسعار المحددة؟')
                        ->modalSubmitActionLabel('إلغاء التفعيل'),

                ])
                    ->label('إجراءات مجمعة'),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة أول سعر')
                    ->icon('heroicon-o-plus'),
            ])
            ->headerActions([
                Tables\Actions\Action::make('fetch_isagha_prices')
                    ->label('جلب الأسعار من iSagha')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function () {
                        // جلب البيانات من iSagha
                        $processedPrices = static::fetchAndProcessISaghaPrices();

                        if (empty($processedPrices)) {
                            return;
                        }

                        // عرض البيانات للمراجعة
                        return static::showReviewModal($processedPrices);
                    })
                    ->requiresConfirmation()
                    ->modalHeading('جلب الأسعار من iSagha')
                    ->modalDescription('هل تريد جلب أحدث الأسعار من موقع iSagha؟ سيتم عرض الأسعار للمراجعة قبل الحفظ.')
                    ->modalSubmitActionLabel('جلب الأسعار')
                    ->modalCancelActionLabel('إلغاء'),

                Tables\Actions\Action::make('manual_gold_update')
                    ->label('تحديث أسعار الذهب يدوياً')
                    ->icon('heroicon-o-calculator')
                    ->color('warning')
                    ->form([
                        Forms\Components\TextInput::make('gold_21_price')
                            ->label('سعر البيع لعيار 21  (ج.م)')
                            ->placeholder('مثال: 4640')
                            ->required()
                            ->numeric()
                            ->rules(['min:1000', 'max:10000'])
                            ->helperText('أدخل سعر البيع الحالي لعيار 21 ')
                            ->suffixIcon('heroicon-m-currency-dollar')
                            ->step(0.01),

                        Forms\Components\Select::make('discount_type')
                            ->label('نوع الخصم بين البيع والشراء')
                            ->options([
                                'percentage' => 'نسبة مئوية (%)',
                                'fixed' => 'مبلغ ثابت (ج.م)'
                            ])
                            ->default('percentage')
                            ->required()
                            ->reactive(),

                        Forms\Components\TextInput::make('discount_value')
                            ->label('قيمة الخصم')
                            ->placeholder(function (callable $get) {
                                return $get('discount_type') === 'percentage' ? 'مثال: 1' : 'مثال: 20';
                            })
                            ->helperText('للنسبة المئوية: مثال 1 يعني 1% | للمبلغ الثابت: مثال 20 يعني 20 جنيه')
                            ->required()
                            ->numeric()
                            ->rules(function (callable $get) {
                                $discountType = $get('discount_type');
                                if ($discountType === 'percentage') {
                                    return ['min:0.1', 'max:10'];
                                } else {
                                    return ['min:0.1', 'max:500'];
                                }
                            })
                            ->reactive()
                            ->step(0.01)
                            ->suffix(function (callable $get) {
                                return $get('discount_type') === 'percentage' ? '%' : 'ج.م';
                            }),
                    ])
                    ->action(function (array $data) {
                        // عرض معاينة الأسعار أولاً
                        return static::showPricePreview($data);
                    })
                    ->modalHeading('تحديث أسعار الذهب يدوياً')
                    ->modalDescription('أدخل سعر عيار 21 ونوع وقيمة الخصم لحساب جميع العيارات تلقائياً')
                    ->modalSubmitActionLabel('معاينة الأسعار')
                    ->modalCancelActionLabel('إلغاء')
                    ->modalWidth('md'),

                Tables\Actions\Action::make('manual_silver_update')
                    ->label('تحديث أسعار الفضة يدوياً')
                    ->icon('heroicon-o-calculator')
                    ->color('info')
                    ->form([
                        Forms\Components\TextInput::make('silver_999_price')
                            ->label('سعر البيع لعيار 999 فضة (ج.م)')
                            ->placeholder('مثال: 65')
                            ->required()
                            ->numeric()
                            ->rules(['min:10', 'max:200'])
                            ->helperText('أدخل سعر البيع الحالي لعيار 999 فضة')
                            ->suffixIcon('heroicon-m-currency-dollar')
                            ->step(0.01),

                        Forms\Components\Select::make('discount_type')
                            ->label('نوع الخصم بين البيع والشراء')
                            ->options([
                                'percentage' => 'نسبة مئوية (%)',
                                'fixed' => 'مبلغ ثابت (ج.م)'
                            ])
                            ->default('percentage')
                            ->required()
                            ->reactive(),

                        Forms\Components\TextInput::make('discount_value')
                            ->label('قيمة الخصم')
                            ->placeholder(function (callable $get) {
                                return $get('discount_type') === 'percentage' ? 'مثال: 2' : 'مثال: 3';
                            })
                            ->helperText('للنسبة المئوية: مثال 2 يعني 2% | للمبلغ الثابت: مثال 3 يعني 3 جنيه')
                            ->required()
                            ->numeric()
                            ->rules(function (callable $get) {
                                $discountType = $get('discount_type');
                                if ($discountType === 'percentage') {
                                    return ['min:0.1', 'max:15'];
                                } else {
                                    return ['min:0.1', 'max:20'];
                                }
                            })
                            ->reactive()
                            ->step(0.01)
                            ->suffix(function (callable $get) {
                                return $get('discount_type') === 'percentage' ? '%' : 'ج.م';
                            }),
                    ])
                    ->action(function (array $data) {
                        // عرض معاينة الأسعار أولاً
                        return static::showSilverPricePreview($data);
                    })
                    ->modalHeading('تحديث أسعار الفضة يدوياً')
                    ->modalDescription('أدخل سعر عيار 999 ونوع وقيمة الخصم لحساب جميع العيارات تلقائياً')
                    ->modalSubmitActionLabel('معاينة الأسعار')
                    ->modalCancelActionLabel('إلغاء')
                    ->modalWidth('md'),

                // Tables\Actions\Action::make('debug_scraping')
                //     ->label('تشخيص الجلب')
                //     ->icon('heroicon-o-bug-ant')
                //     ->color('info')
                //     ->action(function () {
                //         // جلب HTML من iSagha
                //         $response = Http::timeout(30)
                //             ->withHeaders([
                //                 'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                //                 'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                //                 'Accept-Language' => 'ar,en-US;q=0.7,en;q=0.3',
                //             ])
                //             ->get('https://market.isagha.com/prices');

                //         if ($response->successful()) {
                //             $html = $response->body();
                //             $allPrices = static::extractAllPricesFromHTML($html);

                //             // حفظ النتائج في الجلسة لعرضها
                //             session(['debug_scraped_prices' => $allPrices]);

                //             Notification::make()
                //                 ->title('تم استخراج الأسعار للتشخيص')
                //                 ->body('تم استخراج ' . count($allPrices) . ' سعر من الموقع.')
                //                 ->info()
                //                 ->actions([
                //                     \Filament\Notifications\Actions\Action::make('view_debug')
                //                         ->label('عرض جميع الأسعار')
                //                         ->url(static::getUrl('debug-prices'))
                //                         ->button(),
                //                 ])
                //                 ->send();
                //         } else {
                //             Notification::make()
                //                 ->title('فشل في جلب البيانات')
                //                 ->body('لم يتم الاتصال بموقع iSagha بنجاح.')
                //                 ->danger()
                //                 ->send();
                //         }
                //     })
                //     ->requiresConfirmation()
                //     ->modalHeading('تشخيص جلب البيانات')
                //     ->modalDescription('هذا سيجلب البيانات من iSagha ويعرض جميع الأسعار المستخرجة للتشخيص.')
                //     ->modalSubmitActionLabel('تشخيص')
                //     ->modalCancelActionLabel('إلغاء'),

                // Tables\Actions\Action::make('view_all_prices')
                //     ->label('عرض جميع الأسعار المستخرجة')
                //     ->icon('heroicon-o-eye')
                //     ->color('info')
                //     ->url(static::getUrl('debug-prices'))
                //     ->openUrlInNewTab(false),

                ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMetalPrices::route('/'),
            'create' => Pages\CreateMetalPrice::route('/create'),
            'edit' => Pages\EditMetalPrice::route('/{record}/edit'),
            'review-isagha-prices' => Pages\ReviewISaghaPrices::route('/review-isagha-prices'),
            'manual-price-preview' => Pages\ManualPricePreview::route('/manual-price-preview'),
            'manual-silver-price-preview' => Pages\ManualSilverPricePreview::route('/manual-silver-price-preview'),
            'missing-purities' => Pages\MissingPurities::route('/missing-purities'),
            'debug-prices' => Pages\DebugPrices::route('/debug-prices'),
        ];
    }

    /**
     * جلب ومعالجة الأسعار من iSagha API
     */
    public static function fetchAndProcessISaghaPrices()
    {
        try {
            // جلب البيانات الحقيقية من موقع iSagha عبر web scraping
            $scrapedData = static::scrapeISaghaPrices();

            Log::info('Scraped iSagha data:', ['data' => $scrapedData]);

            if (empty($scrapedData)) {
                Notification::make()
                    ->title('لا توجد بيانات')
                    ->body('فشل في جلب البيانات من موقع iSagha. يرجى المحاولة لاحقاً.')
                    ->warning()
                    ->send();
                return [];
            }

            // معالجة البيانات المجلبة (البيانات تأتي من parseISaghaHTML بالتنسيق الصحيح)
            $processedPrices = static::processScrapedPrices($scrapedData);

            if (empty($processedPrices)) {
                Notification::make()
                    ->title('لا توجد أسعار متوافقة')
                    ->body('تم جلب البيانات من iSagha ولكن لم يتم العثور على أسعار متوافقة مع النظام.')
                    ->warning()
                    ->send();
                return [];
            }

            return $processedPrices;

        } catch (\Exception $e) {
            Notification::make()
                ->title('خطأ في النظام')
                ->body('حدث خطأ أثناء جلب الأسعار: ' . $e->getMessage())
                ->danger()
                ->send();
            return [];
        }
    }

    /**
     * جلب البيانات الحقيقية من موقع iSagha عبر web scraping
     */
    private static function scrapeISaghaPrices()
    {
        try {
            // جلب محتوى الصفحة
            $response = Http::timeout(30)
                ->withHeaders([
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                    'Accept-Language' => 'ar,en-US;q=0.7,en;q=0.3',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Connection' => 'keep-alive',
                ])
                ->get('https://market.isagha.com/prices');

            if (!$response->successful()) {
                Log::error('Failed to fetch iSagha page', ['status' => $response->status()]);
                return [];
            }

            $html = $response->body();
            Log::info('iSagha HTML fetched', ['length' => strlen($html)]);

            // استخراج الأسعار من HTML
            $prices = static::parseISaghaHTML($html);

            return $prices;

        } catch (\Exception $e) {
            Log::error('Error scraping iSagha prices', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * تحليل HTML واستخراج الأسعار بناءً على المعادن والعيارات الموجودة في النظام
     */
    private static function parseISaghaHTML($html)
    {
        $prices = [];

        try {
            Log::info("Starting HTML parsing", ['html_length' => strlen($html)]);

            // أولاً: استخراج جميع الأسعار من الموقع بطريقة عامة
            $allFoundPrices = static::extractAllPricesFromHTML($html);
            Log::info("All prices found in HTML", ['count' => count($allFoundPrices), 'prices' => $allFoundPrices]);

            // ثانياً: الحصول على جميع أنواع المعادن والعيارات من النظام
            $metalTypes = MetalType::active()->with('purities')->get();

            foreach ($metalTypes as $metalType) {
                // تخطي الجنيهات الذهبية في الجلب الفعلي
                if ($metalType->name === 'gold_coin') {
                    Log::info("Skipping gold_coin type - not fetched from iSagha", ['metal' => $metalType->name]);
                    continue;
                }

                Log::info("Processing metal type", ['metal' => $metalType->name, 'name_ar' => $metalType->name_ar]);

                foreach ($metalType->purities as $purity) {
                    Log::info("Processing purity", ['purity' => $purity->name, 'name_ar' => $purity->name_ar]);

                    // البحث عن هذا العيار في الأسعار المستخرجة
                    $foundPrices = static::findPriceForPurity($allFoundPrices, $purity->name, $purity->name_ar, $metalType->name);

                    if ($foundPrices && is_array($foundPrices) && isset($foundPrices['sell_price'])) {
                        $sellPrice = $foundPrices['sell_price'];
                        $buyPrice = $foundPrices['buy_price'] ?? null;

                        if (static::isPriceReasonable($metalType->name, $sellPrice)) {
                            $priceData = [
                                'name' => $metalType->name_ar . ' عيار ' . $purity->name_ar,
                                'metal' => $metalType->name,
                                'purity' => $purity->name,
                                'sell_price' => $sellPrice,
                            ];

                            if ($buyPrice !== null) {
                                $priceData['buy_price'] = $buyPrice;
                            }

                            $prices[] = $priceData;

                            Log::info("Found price for purity", [
                                'metal' => $metalType->name,
                                'purity' => $purity->name,
                                'sell_price' => $sellPrice,
                                'buy_price' => $buyPrice
                            ]);
                        } else {
                            Log::info("Price not reasonable for purity", [
                                'metal' => $metalType->name,
                                'purity' => $purity->name,
                                'sell_price' => $sellPrice
                            ]);
                        }
                    } else {
                        Log::info("No price found for purity", [
                            'metal' => $metalType->name,
                            'purity' => $purity->name
                        ]);
                    }
                }
            }

            // إذا لم نجد أسعار، نحاول patterns أخرى
            if (empty($prices)) {
                Log::info('No prices found with specific patterns, trying alternative methods');

                // محاولة البحث عن الأسعار في جدول HTML
                if (preg_match_all('/<td[^>]*>([0-9,]+\.?[0-9]*)\s*ج\.م<\/td>/u', $html, $tablePrices)) {
                    Log::info('Found table prices', ['prices' => $tablePrices[1]]);

                    foreach ($tablePrices[1] as $priceText) {
                        $price = floatval(str_replace(',', '', $priceText));
                        if ($price > 1000) {
                            // هذا على الأرجح سعر ذهب
                            $prices[] = [
                                'name' => 'ذهب (مستخرج من جدول)',
                                'metal' => 'gold',
                                'purity' => '21K', // افتراضي
                                'price' => $price,
                            ];
                        } elseif ($price > 10 && $price < 200) {
                            // هذا على الأرجح سعر فضة
                            $prices[] = [
                                'name' => 'فضة (مستخرج من جدول)',
                                'metal' => 'silver',
                                'purity' => '925', // افتراضي
                                'price' => $price,
                            ];
                        }
                    }
                }

                // محاولة أخرى: البحث عن أي أرقام مع "ج.م"
                if (empty($prices)) {
                    preg_match_all('/([0-9,]+\.?[0-9]*)\s*ج\.م/u', $html, $allPrices);

                    if (!empty($allPrices[1])) {
                        Log::info('Found all potential prices', ['count' => count($allPrices[1]), 'first_10' => array_slice($allPrices[1], 0, 10)]);

                        $foundPrices = array_map(function($price) {
                            return floatval(str_replace(',', '', $price));
                        }, $allPrices[1]);

                        // فلترة وتصنيف الأسعار
                        $goldPrices = array_filter($foundPrices, function($price) {
                            return $price >= 1500 && $price <= 8000; // نطاق أسعار الذهب المتوقع
                        });

                        $silverPrices = array_filter($foundPrices, function($price) {
                            return $price >= 20 && $price <= 100; // نطاق أسعار الفضة المتوقع
                        });

                        // إضافة أسعار الذهب
                        $goldPurities = ['24K', '22K', '21K', '18K', '14K'];
                        $goldPricesArray = array_values($goldPrices);
                        foreach ($goldPurities as $index => $purity) {
                            if (isset($goldPricesArray[$index])) {
                                $prices[] = [
                                    'name' => 'ذهب عيار ' . str_replace('K', '', $purity),
                                    'metal' => 'gold',
                                    'purity' => $purity,
                                    'price' => $goldPricesArray[$index],
                                ];
                            }
                        }

                        // إضافة أسعار الفضة
                        $silverPurities = ['999', '925', '900'];
                        $silverPricesArray = array_values($silverPrices);
                        foreach ($silverPurities as $index => $purity) {
                            if (isset($silverPricesArray[$index])) {
                                $prices[] = [
                                    'name' => 'فضة عيار ' . $purity,
                                    'metal' => 'silver',
                                    'purity' => $purity,
                                    'price' => $silverPricesArray[$index],
                                ];
                            }
                        }
                    }
                }
            }

            // تحليل العيارات المفقودة
            $missingPurities = static::findMissingPurities($allFoundPrices, $metalTypes);
            if (!empty($missingPurities)) {
                Log::info('Found missing purities', ['missing' => $missingPurities]);
                session(['missing_purities_from_isagha' => $missingPurities]);
            }

            Log::info('Parsed prices from HTML', [
                'count' => count($prices),
                'prices' => $prices,
                'missing_purities_count' => count($missingPurities)
            ]);
            return $prices;

        } catch (\Exception $e) {
            Log::error('Error parsing iSagha HTML', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * معالجة الأسعار المستخرجة من iSagha وتحويلها للتنسيق المطلوب
     */
    private static function processScrapedPrices($scrapedPrices)
    {
        $processedPrices = [];

        foreach ($scrapedPrices as $priceData) {
            Log::info('Processing scraped price:', $priceData);

            // الحصول على السعر الحالي من قاعدة البيانات
            $currentPrice = MetalPrice::where('metal_type', $priceData['metal'])
                ->where('purity', $priceData['purity'])
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->first();

            // الحصول على معلومات نوع المعدن والعيار
            $metalType = MetalType::where('name', $priceData['metal'])->first();
            $purity = null;

            if ($metalType) {
                $purity = MetalPurity::where('metal_type_id', $metalType->id)
                    ->where('name', $priceData['purity'])
                    ->first();
            }

            if (!$metalType) {
                Log::warning('Metal type not found in database:', [
                    'metal' => $priceData['metal'],
                    'price_data' => $priceData
                ]);
                continue;
            }

            if (!$purity) {
                Log::warning('Purity not found in database:', [
                    'metal' => $priceData['metal'],
                    'purity' => $priceData['purity'],
                    'price_data' => $priceData
                ]);
                continue;
            }

            $currentSellPrice = $currentPrice ? $currentPrice->price_per_gram : 0;
            $currentBuyPrice = $currentPrice ? $currentPrice->purchase_price_per_gram : 0;

            // معالجة سعر البيع
            $newSellPrice = $priceData['sell_price'] ?? $priceData['price'] ?? 0;
            $sellDifference = $newSellPrice - $currentSellPrice;
            $sellDifferencePercent = $currentSellPrice > 0 ? (($sellDifference / $currentSellPrice) * 100) : 0;

            // معالجة سعر الشراء
            $newBuyPrice = $priceData['buy_price'] ?? null;
            $buyDifference = 0;
            $buyDifferencePercent = 0;

            if ($newBuyPrice !== null) {
                $buyDifference = $newBuyPrice - $currentBuyPrice;
                $buyDifferencePercent = $currentBuyPrice > 0 ? (($buyDifference / $currentBuyPrice) * 100) : 0;
            }

            $processedData = [
                'metal_type' => $priceData['metal'],
                'metal_type_name' => $metalType->name_ar,
                'purity' => $priceData['purity'],
                'purity_name' => $purity->name_ar,
                'sell_price' => $newSellPrice,
                'new_price' => $newSellPrice, // للتوافق مع النسخة القديمة
                'current_price' => $currentSellPrice,
                'difference' => $sellDifference,
                'difference_percent' => $sellDifferencePercent,
                'formatted_new_price' => number_format($newSellPrice, 2),
                'formatted_current_price' => number_format($currentSellPrice, 2),
                'formatted_difference' => number_format($sellDifference, 2),
                'formatted_difference_percent' => number_format($sellDifferencePercent, 1),
                'direction' => $sellDifference > 0 ? 'up' : ($sellDifference < 0 ? 'down' : 'stable'),
                'source' => 'isagha_scraping',
            ];

            // إضافة معلومات سعر الشراء إذا كان متوفراً
            if ($newBuyPrice !== null) {
                $processedData['buy_price'] = $newBuyPrice;
                $processedData['current_buy_price'] = $currentBuyPrice;
                $processedData['buy_difference'] = $buyDifference;
                $processedData['buy_difference_percent'] = $buyDifferencePercent;
                $processedData['formatted_buy_price'] = $newBuyPrice ? number_format($newBuyPrice, 2) : '0.00';
                $processedData['formatted_current_buy_price'] = $currentBuyPrice ? number_format($currentBuyPrice, 2) : '0.00';
                $processedData['formatted_buy_difference'] = $buyDifference ? number_format($buyDifference, 2) : '0.00';
                $processedData['formatted_buy_difference_percent'] = $buyDifferencePercent ? number_format($buyDifferencePercent, 1) : '0.0';
                $processedData['buy_direction'] = $buyDifference > 0 ? 'up' : ($buyDifference < 0 ? 'down' : 'stable');
            }

            $processedPrices[] = $processedData;
        }

        Log::info('Processed scraped prices count:', ['count' => count($processedPrices)]);
        return $processedPrices;
    }

    /**
     * معالجة بيانات iSagha وتحويلها للتنسيق المطلوب (للاستخدام مع API)
     */
    private static function processISaghaData($data)
    {
        $processedPrices = [];

        // تسجيل هيكل البيانات للتشخيص
        Log::info('Processing iSagha data structure:', [
            'data_type' => gettype($data),
            'data_keys' => is_array($data) ? array_keys($data) : 'not_array',
            'first_item' => is_array($data) && !empty($data) ? $data[0] ?? 'no_first_item' : 'empty_or_not_array'
        ]);

        // التعامل مع تنسيقات مختلفة من البيانات
        $items = [];

        if (is_array($data)) {
            // إذا كانت البيانات مصفوفة مباشرة
            $items = $data;
        } elseif (is_object($data) || (is_array($data) && isset($data['data']))) {
            // إذا كانت البيانات داخل كائن أو مصفوفة بمفتاح 'data'
            $items = $data['data'] ?? $data['prices'] ?? $data['metals'] ?? [];
        }

        if (empty($items)) {
            Log::warning('No items found in iSagha data');
            return [];
        }

        // خريطة تحويل أسماء المعادن والعيارات من iSagha إلى النظام
        $metalMapping = [
            'gold' => 'gold',
            'silver' => 'silver',
            'ذهب' => 'gold',
            'فضة' => 'silver',
            'الذهب' => 'gold',
            'الفضة' => 'silver',
            'Gold' => 'gold',
            'Silver' => 'silver',
        ];

        $purityMapping = [
            // عيارات الذهب
            '24' => '24K',
            '22' => '22K',
            '21' => '21K',
            '18' => '18K',
            '14' => '14K',
            '12' => '12K',
            '9' => '9K',
            '24K' => '24K',
            '22K' => '22K',
            '21K' => '21K',
            '18K' => '18K',
            '14K' => '14K',
            '12K' => '12K',
            '9K' => '9K',
            // عيارات الفضة
            '999' => '999',
            '925' => '925',
            '900' => '900',
            '800' => '800',
            '600' => '600',
        ];

        foreach ($items as $item) {
            Log::info('Processing item:', ['item' => $item]);

            // تحديد نوع المعدن
            $metalType = null;
            $searchFields = ['name', 'metal', 'type', 'metal_type', 'title'];

            foreach ($metalMapping as $key => $value) {
                foreach ($searchFields as $field) {
                    if (isset($item[$field]) && stripos($item[$field], $key) !== false) {
                        $metalType = $value;
                        break 2;
                    }
                }
            }

            if (!$metalType) {
                Log::info('Metal type not found for item:', ['item' => $item]);
                continue;
            }

            // تحديد العيار
            $purity = null;
            $purityFields = ['purity', 'karat', 'grade', 'name', 'title'];

            foreach ($purityMapping as $key => $value) {
                foreach ($purityFields as $field) {
                    if (isset($item[$field]) && (
                        stripos($item[$field], $key) !== false ||
                        $item[$field] == $key
                    )) {
                        $purity = $value;
                        break 2;
                    }
                }
            }

            if (!$purity) {
                Log::info('Purity not found for item:', ['item' => $item]);
                continue;
            }

            // التحقق من وجود نوع المعدن والعيار في النظام
            $metalTypeRecord = MetalType::where('name', $metalType)->first();
            if (!$metalTypeRecord) {
                Log::info('Metal type not found in database:', ['metal_type' => $metalType]);
                continue;
            }

            $purityRecord = MetalPurity::where('metal_type_id', $metalTypeRecord->id)
                ->where('name', $purity)
                ->first();
            if (!$purityRecord) {
                Log::info('Purity not found in database:', ['metal_type' => $metalType, 'purity' => $purity]);
                continue;
            }

            // الحصول على السعر الحالي
            $currentPrice = MetalPrice::where('metal_type', $metalType)
                ->where('purity', $purity)
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->first();

            // البحث عن السعر في حقول مختلفة
            $priceFields = ['price', 'price_per_gram', 'sell_price', 'buy_price', 'value'];
            $newPrice = 0;

            foreach ($priceFields as $field) {
                if (isset($item[$field]) && is_numeric($item[$field])) {
                    $newPrice = floatval($item[$field]);
                    break;
                }
            }

            if ($newPrice <= 0) {
                Log::info('Price not found or invalid for item:', ['item' => $item]);
                continue;
            }

            $currentPriceValue = $currentPrice ? $currentPrice->price_per_gram : 0;
            $difference = $newPrice - $currentPriceValue;
            $differencePercent = $currentPriceValue > 0 ? (($difference / $currentPriceValue) * 100) : 0;

            $processedPrices[] = [
                'metal_type' => $metalType,
                'metal_type_name' => $metalTypeRecord->name_ar,
                'purity' => $purity,
                'purity_name' => $purityRecord->name_ar,
                'new_price' => $newPrice,
                'current_price' => $currentPriceValue,
                'difference' => $difference,
                'difference_percent' => $differencePercent,
                'formatted_new_price' => number_format($newPrice, 2),
                'formatted_current_price' => number_format($currentPriceValue, 2),
                'formatted_difference' => number_format($difference, 2),
                'formatted_difference_percent' => number_format($differencePercent, 1),
                'direction' => $difference > 0 ? 'up' : ($difference < 0 ? 'down' : 'stable'),
            ];
        }

        Log::info('Processed prices count:', ['count' => count($processedPrices)]);
        return $processedPrices;
    }

    /**
     * حفظ الأسعار الجديدة مع تطبيق منطق ذكي للحفظ
     * جميع الأسعار تُضاف كسجلات جديدة مع الاحتفاظ بالتاريخ الكامل
     */
    public static function savePrices($processedPrices)
    {
        $newPricesCount = 0;
        $changedPricesCount = 0;
        $ignoredCount = 0;
        $errors = [];

        foreach ($processedPrices as $priceData) {
            try {
                // تحديد المصدر
                $source = isset($priceData['source']) ? $priceData['source'] : 'isagha_api';
                $newSellPrice = $priceData['new_price'] ?? $priceData['sell_price'];
                $newBuyPrice = $priceData['buy_price'] ?? null;

                // البحث عن السعر الحالي في قاعدة البيانات
                $existingPrice = MetalPrice::where('metal_type', $priceData['metal_type'])
                    ->where('purity', $priceData['purity'])
                    ->where('currency', 'EGP')
                    ->where('is_active', true)
                    ->first();

                // تحديد ما إذا كان السعر قد تغير
                $sellPriceChanged = !$existingPrice || abs($existingPrice->price_per_gram - $newSellPrice) > 0.01;
                $buyPriceChanged = !$existingPrice ||
                    ($newBuyPrice !== null && (!$existingPrice->purchase_price_per_gram || abs($existingPrice->purchase_price_per_gram - $newBuyPrice) > 0.01)) ||
                    ($newBuyPrice === null && $existingPrice->purchase_price_per_gram !== null);

                if (!$existingPrice) {
                    // 1. إضافة السعر الجديد (المعدن/العيار غير موجود)
                    $newPriceData = [
                        'metal_type' => $priceData['metal_type'],
                        'purity' => $priceData['purity'],
                        'price_per_gram' => $newSellPrice,
                        'price_per_ounce' => $newSellPrice * 31.1035,
                        'currency' => 'EGP',
                        'price_date' => now(),
                        'source' => $source,
                        'is_active' => true,
                    ];

                    // إضافة سعر الشراء إذا كان متوفراً
                    if ($newBuyPrice !== null) {
                        $newPriceData['purchase_price_per_gram'] = $newBuyPrice;
                        $newPriceData['purchase_price_per_ounce'] = $newBuyPrice * 31.1035;
                    }

                    $newPrice = MetalPrice::create($newPriceData);

                    // تطبيق نظام التفعيل التلقائي
                    static::applyAutoActivation($newPrice);
                    $newPricesCount++;

                } elseif ($sellPriceChanged || $buyPriceChanged) {
                    // 2. إضافة السعر المتغير (موجود ولكن القيمة مختلفة)
                    // إنشاء سعر جديد والاحتفاظ بالسعر القديم (مع إلغاء تفعيله)
                    $newPriceData = [
                        'metal_type' => $priceData['metal_type'],
                        'purity' => $priceData['purity'],
                        'price_per_gram' => $newSellPrice,
                        'price_per_ounce' => $newSellPrice * 31.1035,
                        'currency' => 'EGP',
                        'price_date' => now(),
                        'source' => $source,
                        'is_active' => true,
                    ];

                    // إضافة سعر الشراء إذا كان متوفراً
                    if ($newBuyPrice !== null) {
                        $newPriceData['purchase_price_per_gram'] = $newBuyPrice;
                        $newPriceData['purchase_price_per_ounce'] = $newBuyPrice * 31.1035;
                    }

                    $newPrice = MetalPrice::create($newPriceData);

                    // تطبيق نظام التفعيل التلقائي (سيقوم بإلغاء تفعيل السعر القديم)
                    static::applyAutoActivation($newPrice);
                    $changedPricesCount++;

                } else {
                    // 3. تجاهل السعر المطابق (موجود ونفس القيمة)
                    $ignoredCount++;
                }

            } catch (\Exception $e) {
                $errors[] = "خطأ في حفظ {$priceData['metal_type_name']} {$priceData['purity_name']}: " . $e->getMessage();
            }
        }

        // إرسال إشعار مفصل بالنتيجة
        $totalProcessed = $newPricesCount + $changedPricesCount + $ignoredCount;

        if ($totalProcessed > 0) {
            $metalTypes = collect($processedPrices)->groupBy('metal_type_name')->keys()->implode(', ');

            $bodyParts = [];
            if ($newPricesCount > 0) {
                $bodyParts[] = "تم إضافة {$newPricesCount} سعر جديد";
            }
            if ($changedPricesCount > 0) {
                $bodyParts[] = "تم إضافة {$changedPricesCount} سعر متغير";
            }
            if ($ignoredCount > 0) {
                $bodyParts[] = "تم تجاهل {$ignoredCount} سعر مطابق";
            }

            $body = implode(' • ', $bodyParts) . "\nالمعادن: {$metalTypes}";

            Notification::make()
                ->title('تم معالجة الأسعار بنجاح')
                ->body($body)
                ->success()
                ->duration(12000)
                ->send();
        }

        if (!empty($errors)) {
            Notification::make()
                ->title('بعض الأخطاء في المعالجة')
                ->body(implode("\n", array_slice($errors, 0, 3)))
                ->warning()
                ->duration(15000)
                ->send();
        }

        // إذا لم يتم معالجة أي أسعار
        if ($totalProcessed === 0 && empty($errors)) {
            Notification::make()
                ->title('لم يتم معالجة أي أسعار')
                ->body('لم يتم العثور على أسعار صالحة للمعالجة من iSagha.')
                ->warning()
                ->send();
        }

        return [
            'new_prices' => $newPricesCount,
            'changed_prices' => $changedPricesCount,
            'ignored' => $ignoredCount,
            'errors' => count($errors),
            'total' => $totalProcessed
        ];
    }

    /**
     * تطبيق نظام التفعيل التلقائي
     * يجعل السعر الجديد مفعل ويلغي تفعيل الأسعار الأخرى لنفس العيار
     */
    public static function applyAutoActivation(MetalPrice $newPrice)
    {
        // إلغاء تفعيل جميع الأسعار الأخرى لنفس المعدن والعيار
        MetalPrice::where('metal_type', $newPrice->metal_type)
            ->where('purity', $newPrice->purity)
            ->where('currency', $newPrice->currency)
            ->where('id', '!=', $newPrice->id)
            ->update(['is_active' => false]);

        // التأكد من أن السعر الجديد مفعل
        $newPrice->update(['is_active' => true]);
    }

    /**
     * عرض صفحة مراجعة الأسعار قبل الحفظ
     */
    public static function showReviewModal($processedPrices)
    {
        // حفظ البيانات في الجلسة
        session(['pending_isagha_prices' => $processedPrices]);

        // التحقق من وجود عيارات مفقودة
        $missingPurities = session('missing_purities_from_isagha', []);
        $totalExtracted = session('debug_scraped_prices', []);
        $totalExtractedCount = count($totalExtracted);

        // إنشاء رسالة الإشعار
        $message = 'تم جلب ' . count($processedPrices) . ' سعر من iSagha';
        if ($totalExtractedCount > count($processedPrices)) {
            $message .= ' من أصل ' . $totalExtractedCount . ' سعر مستخرج';
        }

        if (!empty($missingPurities)) {
            $message .= '. تم العثور على ' . count($missingPurities) . ' عيار غير موجود في النظام';
        }

        $message .= '. سيتم توجيهك لصفحة المراجعة.';

        // إرسال إشعار
        $notification = Notification::make()
            ->title('تم جلب الأسعار بنجاح')
            ->body($message)
            ->success();

        // إضافة إجراءات إضافية إذا كانت هناك عيارات مفقودة
        if (!empty($missingPurities)) {
            $notification->actions([
                \Filament\Notifications\Actions\Action::make('view_missing')
                    ->label('عرض العيارات المفقودة')
                    ->url(static::getUrl('missing-purities'))
                    ->button()
                    ->color('warning'),
            ]);
        }

        $notification->send();

        // التوجيه لصفحة المراجعة
        return redirect()->to(static::getUrl('review-isagha-prices'));
    }

    /**
     * التحقق من معقولية السعر حسب نوع المعدن
     */
    private static function isPriceReasonable($metalType, $price)
    {
        $priceRanges = [
            'gold' => ['min' => 1000, 'max' => 10000],
            'silver' => ['min' => 10, 'max' => 200],
            'gold_coin' => ['min' => 1000, 'max' => 10000],
            'platinum' => ['min' => 500, 'max' => 5000],
        ];

        $range = $priceRanges[$metalType] ?? ['min' => 1, 'max' => 100000];

        return $price >= $range['min'] && $price <= $range['max'];
    }

    /**
     * استخراج جميع الأسعار من HTML بطريقة شاملة
     */
    public static function extractAllPricesFromHTML($html)
    {
        $foundPrices = [];

        try {
            // إزالة الـ HTML tags للحصول على النص فقط
            $text = strip_tags($html);

            // Debug: حفظ النص المستخرج لفحصه
            Log::info("HTML Text extracted", [
                'text_length' => strlen($text),
                'sample_text' => substr($text, 0, 2000) // أول 2000 حرف
            ]);

            // البحث عن الأسعار بطرق متعددة ومرنة

            // استعادة المنطق الأصلي الذي كان يعمل بشكل صحيح

            // Pattern 1: البحث عن أسعار العيارات مع البيع والشراء (الميزة الجديدة)
            // نمط: عيار 21 4640 ج.م بيع 4630 ج.م شراء
            preg_match_all('/عيار\s*(\d+)\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*شراء/u', $text, $matches1, PREG_SET_ORDER);

            foreach ($matches1 as $match) {
                $purity = trim($match[1]);
                $sellPrice = floatval(str_replace(',', '', $match[2]));
                $buyPrice = floatval(str_replace(',', '', $match[3]));

                if ($sellPrice > 0 && $buyPrice > 0) {
                    // تصنيف صحيح حسب العيار والسعر
                    if (intval($purity) <= 24 && $sellPrice >= 1000) {
                        // عيارات الذهب (24, 22, 21, 18, 14, 12, 9)
                        $foundPrices[] = [
                            'purity_text' => 'ذهب عيار ' . $purity,
                            'sell_price' => $sellPrice,
                            'buy_price' => $buyPrice,
                            'context' => 'عيار ' . $purity . ' - بيع: ' . $sellPrice . ' ج.م، شراء: ' . $buyPrice . ' ج.م'
                        ];
                        Log::info("Found gold price with buy/sell", ['purity' => $purity, 'sell' => $sellPrice, 'buy' => $buyPrice]);
                    } elseif (intval($purity) >= 600 && $sellPrice < 200) {
                        // عيارات الفضة (999, 925, 900, 800, 600)
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار ' . $purity,
                            'sell_price' => $sellPrice,
                            'buy_price' => $buyPrice,
                            'context' => 'عيار ' . $purity . ' - بيع: ' . $sellPrice . ' ج.م، شراء: ' . $buyPrice . ' ج.م'
                        ];
                        Log::info("Found silver price with buy/sell", ['purity' => $purity, 'sell' => $sellPrice, 'buy' => $buyPrice]);
                    }
                }
            }

            // Pattern 2: البحث عن أسعار العيارات العادية (المنطق الأصلي)
            // نمط: عيار 21 4640 ج.م
            preg_match_all('/عيار\s*(\d+)\s*([0-9,]+\.?[0-9]*)\s*ج\.م/u', $text, $matches2, PREG_SET_ORDER);

            foreach ($matches2 as $match) {
                $purity = trim($match[1]);
                $price = floatval(str_replace(',', '', $match[2]));

                // تحقق من عدم وجود هذا العيار مسبقاً مع سعرين
                $alreadyExists = false;
                foreach ($foundPrices as $existingPrice) {
                    if (strpos($existingPrice['purity_text'], 'عيار ' . $purity) !== false) {
                        $alreadyExists = true;
                        break;
                    }
                }

                if (!$alreadyExists && $price > 0) {
                    // تصنيف صحيح حسب العيار والسعر
                    if (intval($purity) <= 24 && $price >= 1000) {
                        // عيارات الذهب (24, 22, 21, 18, 14, 12, 9)
                        $foundPrices[] = [
                            'purity_text' => 'ذهب عيار ' . $purity,
                            'sell_price' => $price,
                            'buy_price' => null,
                            'context' => 'عيار ' . $purity . ' - ' . $price . ' ج.م'
                        ];
                        Log::info("Found gold price", ['purity' => $purity, 'price' => $price]);
                    } elseif (intval($purity) >= 600 && $price < 200) {
                        // عيارات الفضة (999, 925, 900, 800, 600)
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار ' . $purity,
                            'sell_price' => $price,
                            'buy_price' => null,
                            'context' => 'عيار ' . $purity . ' - ' . $price . ' ج.م'
                        ];
                        Log::info("Found silver price", ['purity' => $purity, 'price' => $price]);
                    }
                }
            }

            // Pattern 3: البحث في header الموقع (المنطق الأصلي المحسن)
            // البحث عن "السعر المحلى للذهب" متبوعاً بالسعر
            if (preg_match('/السعر المحلى للذهب.*?(\d+(?:,\d+)*(?:\.\d+)?)\s*ج\.م/u', $text, $goldMatch)) {
                $goldPrice = floatval(str_replace(',', '', $goldMatch[1]));
                if ($goldPrice > 1000) {
                    // تحقق من عدم وجود عيار 21 مسبقاً
                    $alreadyExists = false;
                    foreach ($foundPrices as $existingPrice) {
                        if (strpos($existingPrice['purity_text'], 'ذهب عيار 21') !== false) {
                            $alreadyExists = true;
                            break;
                        }
                    }

                    if (!$alreadyExists) {
                        $foundPrices[] = [
                            'purity_text' => 'ذهب عيار 21', // السعر المحلي عادة عيار 21
                            'sell_price' => $goldPrice,
                            'buy_price' => null,
                            'context' => 'السعر المحلي للذهب - ' . $goldPrice . ' ج.م'
                        ];
                        Log::info("Found gold price in header", ['price' => $goldPrice]);
                    }
                }
            }

            // البحث عن "السعر المحلى للفضة" متبوعاً بالسعر
            if (preg_match('/السعر المحلى للفضة.*?(\d+(?:,\d+)*(?:\.\d+)?)\s*ج\.م/u', $text, $silverMatch)) {
                $silverPrice = floatval(str_replace(',', '', $silverMatch[1]));
                if ($silverPrice > 10 && $silverPrice < 200) {
                    // تحقق من عدم وجود عيار 999 مسبقاً
                    $alreadyExists = false;
                    foreach ($foundPrices as $existingPrice) {
                        if (strpos($existingPrice['purity_text'], 'فضة عيار 999') !== false) {
                            $alreadyExists = true;
                            break;
                        }
                    }

                    if (!$alreadyExists) {
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار 999', // السعر المحلي عادة عيار 999
                            'sell_price' => $silverPrice,
                            'buy_price' => null,
                            'context' => 'السعر المحلي للفضة - ' . $silverPrice . ' ج.م'
                        ];
                        Log::info("Found silver price in header", ['price' => $silverPrice]);
                    }
                }
            }







            // Pattern 4: البحث عن أسعار الفضة بطرق متعددة (المنطق الأصلي)
            $silverPurities = ['999', '925', '900', '800', '600'];
            foreach ($silverPurities as $purity) {
                // البحث عن سعر البيع والشراء: عيار 999 59 ج.م بيع 57 ج.م شراء
                if (preg_match('/عيار\s*' . $purity . '\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*شراء/u', $text, $match)) {
                    $sellPrice = floatval(str_replace(',', '', $match[1]));
                    $buyPrice = floatval(str_replace(',', '', $match[2]));
                    if ($sellPrice > 0 && $buyPrice > 0 && $sellPrice < 200) {
                        $foundPrices[] = [
                            'purity_text' => 'فضة عيار ' . $purity,
                            'sell_price' => $sellPrice,
                            'buy_price' => $buyPrice,
                            'context' => 'عيار ' . $purity . ' - بيع: ' . $sellPrice . ' ج.م، شراء: ' . $buyPrice . ' ج.م'
                        ];
                        Log::info("Found silver price with buy/sell", ['purity' => $purity, 'sell' => $sellPrice, 'buy' => $buyPrice]);
                        continue; // تخطي البحث عن سعر واحد فقط
                    }
                }

                // البحث عن سعر البيع فقط: عيار 999 59 ج.م بيع
                if (preg_match('/عيار\s*' . $purity . '\s*([0-9,]+\.?[0-9]*)\s*ج\.م\s*بيع/u', $text, $match)) {
                    $sellPrice = floatval(str_replace(',', '', $match[1]));
                    if ($sellPrice > 0 && $sellPrice < 200) {
                        // تحقق من عدم وجود هذا العيار مسبقاً مع سعرين
                        $alreadyExists = false;
                        foreach ($foundPrices as $existingPrice) {
                            if (strpos($existingPrice['purity_text'], 'فضة عيار ' . $purity) !== false) {
                                $alreadyExists = true;
                                break;
                            }
                        }

                        if (!$alreadyExists) {
                            $foundPrices[] = [
                                'purity_text' => 'فضة عيار ' . $purity,
                                'sell_price' => $sellPrice,
                                'buy_price' => null,
                                'context' => 'عيار ' . $purity . ' - ' . $sellPrice . ' ج.م بيع'
                            ];
                            Log::info("Found silver price", ['purity' => $purity, 'price' => $sellPrice]);
                        }
                    }
                }
            }

            // Pattern 5: البحث عن الجنيهات الذهبية (المنطق الأصلي)
            // البحث عن: سعر الجنيه الذهب 37120 ج.م
            if (preg_match('/سعر\s*الجنيه\s*الذهب.*?(\d+(?:,\d+)*(?:\.\d+)?)\s*ج\.م/u', $text, $coinMatch)) {
                $coinPrice = floatval(str_replace(',', '', $coinMatch[1]));
                if ($coinPrice >= 30000 && $coinPrice <= 50000) { // نطاق معقول للجنيه الذهبي
                    $foundPrices[] = [
                        'purity_text' => 'جنيه ذهب',
                        'sell_price' => $coinPrice,
                        'buy_price' => null,
                        'context' => 'جنيه ذهب - ' . $coinPrice . ' ج.م'
                    ];
                    Log::info("Found gold coin price", ['price' => $coinPrice]);
                }
            }

            Log::info("Extracted prices from HTML", [
                'total_found' => count($foundPrices),
                'prices' => $foundPrices
            ]);

            return $foundPrices;

        } catch (\Exception $e) {
            Log::error('Error extracting prices from HTML', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * عرض معاينة الأسعار المحسوبة قبل الحفظ
     */
    public static function showPricePreview(array $data)
    {
        try {
            $gold21Price = floatval($data['gold_21_price']);
            $discountType = $data['discount_type'];
            $discountValue = floatval($data['discount_value']);

            // العيارات المطلوب حسابها مع نسبها
            $goldPurities = [
                '24K' => 24,
                '22K' => 22,
                '21K' => 21,
                '18K' => 18,
                '14K' => 14,
                '12K' => 12,
                '9K' => 9,
            ];

            $previewPrices = [];

            foreach ($goldPurities as $purity => $karatValue) {
                // حساب سعر البيع للعيار
                if ($purity === '21K') {
                    $sellPrice = $gold21Price;
                } else {
                    $sellPrice = ($gold21Price * $karatValue) / 21;
                }
                $sellPrice = round($sellPrice, 2);

                // حساب سعر الشراء
                if ($discountType === 'percentage') {
                    $buyPrice = $sellPrice - ($sellPrice * $discountValue / 100);
                } else {
                    $buyPrice = $sellPrice - $discountValue;
                }
                $buyPrice = round($buyPrice, 2);

                // التأكد من أن سعر الشراء لا يقل عن 50% من سعر البيع
                $minimumBuyPrice = $sellPrice * 0.5;
                if ($buyPrice < $minimumBuyPrice) {
                    Notification::make()
                        ->title('خطأ في الحسابات')
                        ->body("سعر الشراء المحسوب للعيار {$purity} ({$buyPrice} ج.م) أقل من الحد الأدنى المسموح ({$minimumBuyPrice} ج.م)")
                        ->danger()
                        ->send();
                    return;
                }

                $previewPrices[] = [
                    'purity' => $purity,
                    'sell_price' => $sellPrice,
                    'buy_price' => $buyPrice,
                    'discount_amount' => round($sellPrice - $buyPrice, 2),
                    'formatted_sell' => number_format($sellPrice, 2),
                    'formatted_buy' => number_format($buyPrice, 2),
                    'formatted_discount' => number_format(round($sellPrice - $buyPrice, 2), 2),
                ];
            }

            // حفظ البيانات في الجلسة للاستخدام في الحفظ النهائي
            session(['preview_gold_prices' => $data]);

            // عرض صفحة المعاينة المخصصة
            return static::showManualPriceReviewModal($previewPrices, $data);

        } catch (\Exception $e) {
            Notification::make()
                ->title('خطأ في حساب المعاينة')
                ->body('حدث خطأ أثناء حساب معاينة الأسعار: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * عرض صفحة مراجعة الأسعار المحسوبة يدوياً
     */
    public static function showManualPriceReviewModal(array $previewPrices, array $inputData)
    {
        $gold21Price = floatval($inputData['gold_21_price']);
        $discountType = $inputData['discount_type'];
        $discountValue = floatval($inputData['discount_value']);

        // حساب الملخص الإجمالي
        $totalSellValue = array_sum(array_column($previewPrices, 'sell_price'));
        $totalBuyValue = array_sum(array_column($previewPrices, 'buy_price'));
        $totalDiscount = $totalSellValue - $totalBuyValue;

        // حفظ البيانات في الجلسة للاستخدام في صفحة المراجعة
        session([
            'manual_price_preview' => $previewPrices,
            'manual_price_input' => $inputData,
            'manual_price_summary' => [
                'total_sell' => $totalSellValue,
                'total_buy' => $totalBuyValue,
                'total_discount' => $totalDiscount,
                'count' => count($previewPrices)
            ]
        ]);

        // إنشاء رسالة الإشعار
        $discountTypeText = $discountType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت';
        $discountValueText = $discountValue . ($discountType === 'percentage' ? '%' : ' ج.م');

        $message = 'تم حساب أسعار ' . count($previewPrices) . ' عيار ذهبي بناءً على سعر عيار 21: ' . number_format($gold21Price, 2) . ' ج.م';
        $message .= ' | نوع الخصم: ' . $discountTypeText . ' | قيمة الخصم: ' . $discountValueText;
        $message .= '. سيتم توجيهك لصفحة المعاينة.';

        // إرسال إشعار
        Notification::make()
            ->title('تم حساب الأسعار بنجاح')
            ->body($message)
            ->success()
            ->actions([
                \Filament\Notifications\Actions\Action::make('view_preview')
                    ->label('عرض المعاينة')
                    ->url(static::getUrl('manual-price-preview'))
                    ->button()
                    ->color('success'),
            ])
            ->send();

        // التوجيه لصفحة المعاينة
        return redirect()->to(static::getUrl('manual-price-preview'));
    }

    /**
     * عرض معاينة أسعار الفضة المحسوبة قبل الحفظ
     */
    public static function showSilverPricePreview(array $data)
    {
        try {
            $silver999Price = floatval($data['silver_999_price']);
            $discountType = $data['discount_type'];
            $discountValue = floatval($data['discount_value']);

            // العيارات المطلوب حسابها مع قيمها
            $silverPurities = [
                '999' => 999,
                '925' => 925,
                '900' => 900,
                '800' => 800,
                '600' => 600,
            ];

            $previewPrices = [];

            foreach ($silverPurities as $purity => $purityValue) {
                // حساب سعر البيع للعيار
                if ($purity === '999') {
                    $sellPrice = $silver999Price;
                } else {
                    $sellPrice = ($silver999Price * $purityValue) / 999;
                }
                $sellPrice = round($sellPrice, 2);

                // حساب سعر الشراء
                if ($discountType === 'percentage') {
                    $buyPrice = $sellPrice - ($sellPrice * $discountValue / 100);
                } else {
                    $buyPrice = $sellPrice - $discountValue;
                }
                $buyPrice = round($buyPrice, 2);

                // التأكد من أن سعر الشراء لا يقل عن 50% من سعر البيع
                $minimumBuyPrice = $sellPrice * 0.5;
                if ($buyPrice < $minimumBuyPrice) {
                    Notification::make()
                        ->title('خطأ في الحسابات')
                        ->body("سعر الشراء المحسوب للعيار {$purity} ({$buyPrice} ج.م) أقل من الحد الأدنى المسموح ({$minimumBuyPrice} ج.م)")
                        ->danger()
                        ->send();
                    return;
                }

                $previewPrices[] = [
                    'purity' => $purity,
                    'sell_price' => $sellPrice,
                    'buy_price' => $buyPrice,
                    'discount_amount' => round($sellPrice - $buyPrice, 2),
                    'formatted_sell' => number_format($sellPrice, 2),
                    'formatted_buy' => number_format($buyPrice, 2),
                    'formatted_discount' => number_format(round($sellPrice - $buyPrice, 2), 2),
                ];
            }

            // حفظ البيانات في الجلسة للاستخدام في الحفظ النهائي
            session(['preview_silver_prices' => $data]);

            // عرض صفحة المعاينة المخصصة
            return static::showManualSilverPriceReviewModal($previewPrices, $data);

        } catch (\Exception $e) {
            Notification::make()
                ->title('خطأ في حساب المعاينة')
                ->body('حدث خطأ أثناء حساب معاينة أسعار الفضة: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * عرض صفحة مراجعة أسعار الفضة المحسوبة يدوياً
     */
    public static function showManualSilverPriceReviewModal(array $previewPrices, array $inputData)
    {
        $silver999Price = floatval($inputData['silver_999_price']);
        $discountType = $inputData['discount_type'];
        $discountValue = floatval($inputData['discount_value']);

        // حساب الملخص الإجمالي
        $totalSellValue = array_sum(array_column($previewPrices, 'sell_price'));
        $totalBuyValue = array_sum(array_column($previewPrices, 'buy_price'));
        $totalDiscount = $totalSellValue - $totalBuyValue;

        // حفظ البيانات في الجلسة للاستخدام في صفحة المراجعة
        session([
            'manual_silver_price_preview' => $previewPrices,
            'manual_silver_price_input' => $inputData,
            'manual_silver_price_summary' => [
                'total_sell' => $totalSellValue,
                'total_buy' => $totalBuyValue,
                'total_discount' => $totalDiscount,
                'count' => count($previewPrices)
            ]
        ]);

        // إنشاء رسالة الإشعار
        $discountTypeText = $discountType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت';
        $discountValueText = $discountValue . ($discountType === 'percentage' ? '%' : ' ج.م');

        $message = 'تم حساب أسعار ' . count($previewPrices) . ' عيار فضة بناءً على سعر عيار 999: ' . number_format($silver999Price, 2) . ' ج.م';
        $message .= ' | نوع الخصم: ' . $discountTypeText . ' | قيمة الخصم: ' . $discountValueText;
        $message .= '. سيتم توجيهك لصفحة المعاينة.';

        // إرسال إشعار
        Notification::make()
            ->title('تم حساب أسعار الفضة بنجاح')
            ->body($message)
            ->success()
            ->actions([
                \Filament\Notifications\Actions\Action::make('view_silver_preview')
                    ->label('عرض المعاينة')
                    ->url(static::getUrl('manual-silver-price-preview'))
                    ->button()
                    ->color('info'),
            ])
            ->send();

        // التوجيه لصفحة المعاينة
        return redirect()->to(static::getUrl('manual-silver-price-preview'));
    }

    /**
     * معالجة تحديث أسعار الذهب يدوياً
     */
    public static function processManualGoldUpdate(array $data)
    {
        try {
            DB::transaction(function () use ($data) {
                $gold21Price = floatval($data['gold_21_price']);
                $discountType = $data['discount_type'];
                $discountValue = floatval($data['discount_value']);

                Log::info('Starting manual gold price update', [
                    'gold_21_price' => $gold21Price,
                    'discount_type' => $discountType,
                    'discount_value' => $discountValue
                ]);

                // التحقق من وجود نوع المعدن 'gold'
                $goldMetalType = MetalType::where('name', 'gold')->first();
                if (!$goldMetalType) {
                    throw new \Exception('نوع المعدن "ذهب" غير موجود في النظام');
                }

                // العيارات المطلوب حسابها مع نسبها
                $goldPurities = [
                    '24K' => 24,
                    '22K' => 22,
                    '21K' => 21,
                    '18K' => 18,
                    '14K' => 14,
                    '12K' => 12,
                    '9K' => 9,
                ];

                $processedPrices = [];
                $today = now()->format('Y-m-d');

                foreach ($goldPurities as $purity => $karatValue) {
                    // التحقق من وجود العيار في النظام
                    $purityRecord = MetalPurity::where('name', $purity)
                        ->where('metal_type_id', $goldMetalType->id)
                        ->first();

                    if (!$purityRecord) {
                        Log::warning("العيار {$purity} غير موجود في النظام، سيتم تخطيه");
                        continue;
                    }

                    // حساب سعر البيع للعيار
                    if ($purity === '21K') {
                        // عيار 21 يأخذ السعر المُدخل مباشرة
                        $sellPrice = $gold21Price;
                    } else {
                        // المعادلة: سعر العيار = (سعر عيار 21 × العيار المطلوب) ÷ 21
                        $sellPrice = ($gold21Price * $karatValue) / 21;
                    }
                    $sellPrice = round($sellPrice, 2);

                    // حساب سعر الشراء
                    if ($discountType === 'percentage') {
                        // سعر الشراء = سعر البيع - (سعر البيع × نسبة الخصم ÷ 100)
                        $buyPrice = $sellPrice - ($sellPrice * $discountValue / 100);
                    } else {
                        // سعر الشراء = سعر البيع - المبلغ الثابت
                        $buyPrice = $sellPrice - $discountValue;
                    }
                    $buyPrice = round($buyPrice, 2);

                    // التأكد من أن سعر الشراء لا يقل عن 50% من سعر البيع
                    $minimumBuyPrice = $sellPrice * 0.5;
                    if ($buyPrice < $minimumBuyPrice) {
                        throw new \Exception("سعر الشراء المحسوب للعيار {$purity} ({$buyPrice} ج.م) أقل من الحد الأدنى المسموح ({$minimumBuyPrice} ج.م)");
                    }

                    // البحث عن سجل موجود لنفس اليوم أو إنشاء جديد
                    $existingPrice = MetalPrice::where('metal_type', 'gold')
                        ->where('currency', 'EGP')
                        ->where('purity', $purity)
                        ->where('price_date', $today)
                        ->first();

                    if ($existingPrice) {
                        // تحديث السجل الموجود
                        $existingPrice->update([
                            'price_per_gram' => $sellPrice,
                            'purchase_price_per_gram' => $buyPrice,
                            'is_active' => true,
                            'source' => 'manual_update',
                            'updated_at' => now(),
                        ]);
                        $newPrice = $existingPrice;
                    } else {
                        // إنشاء سجل جديد
                        $newPrice = MetalPrice::create([
                            'metal_type' => 'gold',
                            'currency' => 'EGP',
                            'purity' => $purity,
                            'price_per_gram' => $sellPrice,
                            'purchase_price_per_gram' => $buyPrice,
                            'price_date' => $today,
                            'is_active' => true,
                            'source' => 'manual_update',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }

                    // تطبيق نظام التفعيل التلقائي
                    static::applyAutoActivation($newPrice);

                    $processedPrices[] = [
                        'purity' => $purity,
                        'sell_price' => $sellPrice,
                        'buy_price' => $buyPrice,
                        'formatted_sell' => number_format($sellPrice, 2),
                        'formatted_buy' => number_format($buyPrice, 2),
                    ];

                    Log::info("تم إنشاء سعر جديد", [
                        'purity' => $purity,
                        'sell_price' => $sellPrice,
                        'buy_price' => $buyPrice
                    ]);
                }

                // عرض رسالة النجاح
                $successMessage = 'تم تحديث أسعار الذهب بنجاح!' . "\n";
                $successMessage .= 'عدد العيارات المحدثة: ' . count($processedPrices) . "\n\n";
                $successMessage .= "ملخص الأسعار:\n";

                foreach ($processedPrices as $price) {
                    $successMessage .= "• {$price['purity']}: بيع {$price['formatted_sell']} ج.م | شراء {$price['formatted_buy']} ج.م\n";
                }

                Notification::make()
                    ->title('تم تحديث الأسعار بنجاح')
                    ->body($successMessage)
                    ->success()
                    ->duration(15000)
                    ->send();

                Log::info('Manual gold price update completed successfully', [
                    'total_updated' => count($processedPrices),
                    'prices' => $processedPrices
                ]);
            });

        } catch (\Exception $e) {
            Log::error('Error in manual gold price update', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            Notification::make()
                ->title('خطأ في تحديث الأسعار')
                ->body('حدث خطأ أثناء تحديث الأسعار: ' . $e->getMessage())
                ->danger()
                ->duration(10000)
                ->send();

            throw $e;
        }
    }

    /**
     * البحث عن سعر عيار معين في الأسعار المستخرجة
     */
    private static function findPriceForPurity($allFoundPrices, $purityName, $purityNameAr, $metalType)
    {
        // إنشاء قائمة بالكلمات المفتاحية للبحث
        $searchTerms = [];

        // إضافة اسم العيار
        $cleanPurity = str_replace(['K', 'k'], '', $purityName);
        $searchTerms[] = $cleanPurity;
        $searchTerms[] = $purityName;
        $searchTerms[] = $purityNameAr;

        // إضافة كلمات مفتاحية حسب نوع المعدن (للتشخيص - يشمل الجنيه الذهبي)
        $metalKeywords = [
            'gold' => ['ذهب', 'الذهب', 'gold'],
            'silver' => ['فضة', 'الفضة', 'silver'],
            'gold_coin' => ['جنيه ذهب', 'جنيه', 'الجنيه'],
        ];

        $keywords = $metalKeywords[$metalType] ?? [];

        foreach ($allFoundPrices as $priceItem) {
            $purityText = $priceItem['purity_text'];

            // البحث عن العيار في النص
            $foundPurity = false;
            foreach ($searchTerms as $term) {
                if (stripos($purityText, $term) !== false) {
                    $foundPurity = true;
                    break;
                }
            }

            // البحث عن نوع المعدن في النص
            $foundMetal = empty($keywords); // إذا لم تكن هناك كلمات مفتاحية، اعتبر أنه وُجد
            foreach ($keywords as $keyword) {
                if (stripos($purityText, $keyword) !== false) {
                    $foundMetal = true;
                    break;
                }
            }

            if ($foundPurity && $foundMetal) {
                // إرجاع كلا السعرين إذا كانا متوفرين
                $result = [];
                if (isset($priceItem['sell_price'])) {
                    $result['sell_price'] = $priceItem['sell_price'];
                } elseif (isset($priceItem['price'])) {
                    $result['sell_price'] = $priceItem['price']; // للتوافق مع النسخة القديمة
                }

                if (isset($priceItem['buy_price'])) {
                    $result['buy_price'] = $priceItem['buy_price'];
                }

                Log::info("Found matching price", [
                    'purity' => $purityName,
                    'metal' => $metalType,
                    'sell_price' => $result['sell_price'] ?? null,
                    'buy_price' => $result['buy_price'] ?? null,
                    'context' => $priceItem['purity_text']
                ]);
                return $result;
            } else {
                Log::info("Price not matched", [
                    'purity' => $purityName,
                    'metal' => $metalType,
                    'found_purity' => $foundPurity,
                    'found_metal' => $foundMetal,
                    'search_terms' => $searchTerms,
                    'keywords' => $keywords,
                    'price_text' => $priceItem['purity_text']
                ]);
            }
        }

        return null;
    }

    /**
     * العثور على العيارات المفقودة في النظام
     */
    private static function findMissingPurities($allFoundPrices, $metalTypes)
    {
        $missingPurities = [];
        $existingPurities = [];

        // جمع جميع العيارات الموجودة في النظام
        foreach ($metalTypes as $metalType) {
            foreach ($metalType->purities as $purity) {
                $existingPurities[] = [
                    'metal_type' => $metalType->name,
                    'purity_name' => $purity->name,
                    'purity_name_ar' => $purity->name_ar,
                ];
            }
        }

        // تحليل الأسعار المستخرجة للعثور على عيارات جديدة
        foreach ($allFoundPrices as $priceItem) {
            $purityText = $priceItem['purity_text'];
            $price = $priceItem['sell_price'] ?? $priceItem['price'] ?? null;

            if (!$price) continue;

            // تحديد نوع المعدن من النص
            $detectedMetal = static::detectMetalType($purityText);
            if (!$detectedMetal) continue;

            // استخراج العيار من النص
            $detectedPurity = static::extractPurityFromText($purityText);
            if (!$detectedPurity) continue;

            // التحقق من وجود هذا العيار في النظام
            $exists = false;
            foreach ($existingPurities as $existing) {
                if ($existing['metal_type'] === $detectedMetal &&
                    (stripos($detectedPurity, $existing['purity_name']) !== false ||
                     stripos($detectedPurity, $existing['purity_name_ar']) !== false)) {
                    $exists = true;
                    break;
                }
            }

            if (!$exists && static::isPriceReasonable($detectedMetal, $price)) {
                $key = $detectedMetal . '_' . $detectedPurity;
                if (!isset($missingPurities[$key])) {
                    $missingPurities[$key] = [
                        'metal_type' => $detectedMetal,
                        'detected_purity' => $detectedPurity,
                        'price' => $price,
                        'context' => $purityText,
                        'suggested_name' => static::suggestPurityName($detectedPurity),
                        'suggested_name_ar' => static::suggestPurityNameAr($detectedPurity),
                    ];
                }
            }
        }

        return array_values($missingPurities);
    }

    /**
     * تحديد نوع المعدن من النص (للتشخيص - يشمل الجنيه الذهبي)
     */
    private static function detectMetalType($text)
    {
        if (stripos($text, 'ذهب') !== false || stripos($text, 'gold') !== false) {
            if (stripos($text, 'جنيه') !== false) {
                return 'gold_coin';
            }
            return 'gold';
        }

        if (stripos($text, 'فضة') !== false || stripos($text, 'silver') !== false) {
            return 'silver';
        }

        return null;
    }

    /**
     * استخراج العيار من النص
     */
    private static function extractPurityFromText($text)
    {
        // البحث عن أرقام العيارات
        if (preg_match('/(\d+)\s*(?:قيراط|K|k|كيلو)/i', $text, $matches)) {
            return $matches[1] . 'K';
        }

        if (preg_match('/(\d+)/', $text, $matches)) {
            $number = $matches[1];
            // إذا كان الرقم كبير، فهو على الأرجح عيار فضة
            if ($number >= 600) {
                return $number;
            }
            // إذا كان صغير، فهو على الأرجح عيار ذهب
            if ($number <= 24) {
                return $number . 'K';
            }
        }

        return null;
    }

    /**
     * اقتراح اسم العيار بالإنجليزية
     */
    private static function suggestPurityName($detectedPurity)
    {
        return $detectedPurity;
    }

    /**
     * اقتراح اسم العيار بالعربية
     */
    private static function suggestPurityNameAr($detectedPurity)
    {
        if (strpos($detectedPurity, 'K') !== false) {
            $number = str_replace('K', '', $detectedPurity);
            return $number . ' عيار';
        }

        return 'عيار ' . $detectedPurity;
    }

    /**
     * معالجة تحديث أسعار الفضة يدوياً
     */
    public static function processManualSilverUpdate(array $data)
    {
        try {
            DB::transaction(function () use ($data) {
                $silver999Price = floatval($data['silver_999_price']);
                $discountType = $data['discount_type'];
                $discountValue = floatval($data['discount_value']);

                Log::info('Starting manual silver price update', [
                    'silver_999_price' => $silver999Price,
                    'discount_type' => $discountType,
                    'discount_value' => $discountValue
                ]);

                // التحقق من وجود نوع المعدن 'silver'
                $silverMetalType = MetalType::where('name', 'silver')->first();
                if (!$silverMetalType) {
                    throw new \Exception('نوع المعدن "فضة" غير موجود في النظام');
                }

                // العيارات المطلوب حسابها مع قيمها
                $silverPurities = [
                    '999' => 999,
                    '925' => 925,
                    '900' => 900,
                    '800' => 800,
                    '600' => 600,
                ];

                $processedPrices = [];
                $today = now()->format('Y-m-d');

                foreach ($silverPurities as $purity => $purityValue) {
                    // التحقق من وجود العيار في النظام
                    $purityRecord = MetalPurity::where('name', $purity)
                        ->where('metal_type_id', $silverMetalType->id)
                        ->first();

                    if (!$purityRecord) {
                        Log::warning("العيار {$purity} غير موجود في النظام، سيتم تخطيه");
                        continue;
                    }

                    // حساب سعر البيع للعيار
                    if ($purity === '999') {
                        // عيار 999 يأخذ السعر المُدخل مباشرة
                        $sellPrice = $silver999Price;
                    } else {
                        // المعادلة: سعر العيار = (سعر عيار 999 × العيار المطلوب) ÷ 999
                        $sellPrice = ($silver999Price * $purityValue) / 999;
                    }
                    $sellPrice = round($sellPrice, 2);

                    // حساب سعر الشراء
                    if ($discountType === 'percentage') {
                        // سعر الشراء = سعر البيع - (سعر البيع × نسبة الخصم ÷ 100)
                        $buyPrice = $sellPrice - ($sellPrice * $discountValue / 100);
                    } else {
                        // سعر الشراء = سعر البيع - المبلغ الثابت
                        $buyPrice = $sellPrice - $discountValue;
                    }
                    $buyPrice = round($buyPrice, 2);

                    // التأكد من أن سعر الشراء لا يقل عن 50% من سعر البيع
                    $minimumBuyPrice = $sellPrice * 0.5;
                    if ($buyPrice < $minimumBuyPrice) {
                        throw new \Exception("سعر الشراء المحسوب للعيار {$purity} ({$buyPrice} ج.م) أقل من الحد الأدنى المسموح ({$minimumBuyPrice} ج.م)");
                    }

                    // البحث عن سجل موجود لنفس اليوم أو إنشاء جديد
                    $existingPrice = MetalPrice::where('metal_type', 'silver')
                        ->where('currency', 'EGP')
                        ->where('purity', $purity)
                        ->where('price_date', $today)
                        ->first();

                    if ($existingPrice) {
                        // تحديث السجل الموجود
                        $existingPrice->update([
                            'price_per_gram' => $sellPrice,
                            'purchase_price_per_gram' => $buyPrice,
                            'is_active' => true,
                            'source' => 'manual_update',
                            'updated_at' => now(),
                        ]);
                        $newPrice = $existingPrice;
                    } else {
                        // إنشاء سجل جديد
                        $newPrice = MetalPrice::create([
                            'metal_type' => 'silver',
                            'currency' => 'EGP',
                            'purity' => $purity,
                            'price_per_gram' => $sellPrice,
                            'purchase_price_per_gram' => $buyPrice,
                            'price_date' => $today,
                            'is_active' => true,
                            'source' => 'manual_update',
                            'created_at' => now(),
                            'updated_at' => now(),
                        ]);
                    }

                    // تطبيق نظام التفعيل التلقائي
                    static::applyAutoActivation($newPrice);

                    $processedPrices[] = [
                        'purity' => $purity,
                        'sell_price' => $sellPrice,
                        'buy_price' => $buyPrice,
                        'formatted_sell' => number_format($sellPrice, 2),
                        'formatted_buy' => number_format($buyPrice, 2),
                    ];

                    Log::info("تم إنشاء سعر فضة جديد", [
                        'purity' => $purity,
                        'sell_price' => $sellPrice,
                        'buy_price' => $buyPrice
                    ]);
                }

                // عرض رسالة النجاح
                $successMessage = 'تم تحديث أسعار الفضة بنجاح!' . "\n";
                $successMessage .= 'عدد العيارات المحدثة: ' . count($processedPrices) . "\n\n";
                $successMessage .= "ملخص الأسعار:\n";

                foreach ($processedPrices as $price) {
                    $successMessage .= "• عيار {$price['purity']}: بيع {$price['formatted_sell']} ج.م | شراء {$price['formatted_buy']} ج.م\n";
                }

                Notification::make()
                    ->title('تم تحديث أسعار الفضة بنجاح')
                    ->body($successMessage)
                    ->success()
                    ->duration(15000)
                    ->send();

                Log::info('Manual silver price update completed successfully', [
                    'total_updated' => count($processedPrices),
                    'prices' => $processedPrices
                ]);
            });

        } catch (\Exception $e) {
            Log::error('Error in manual silver price update', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            Notification::make()
                ->title('خطأ في تحديث أسعار الفضة')
                ->body('حدث خطأ أثناء تحديث أسعار الفضة: ' . $e->getMessage())
                ->danger()
                ->duration(10000)
                ->send();

            throw $e;
        }
    }
}
