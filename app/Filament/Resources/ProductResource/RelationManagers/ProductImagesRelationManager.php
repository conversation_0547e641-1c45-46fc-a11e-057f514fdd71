<?php

namespace App\Filament\Resources\ProductResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;
use Filament\Notifications\Notification;

class ProductImagesRelationManager extends RelationManager
{
    protected static string $relationship = 'images';

    protected static ?string $title = 'صور المنتج';

    protected static ?string $modelLabel = 'صورة';

    protected static ?string $pluralModelLabel = 'صور';

    protected static ?string $recordTitleAttribute = 'alt_text_ar';

    /**
     * التأكد من وجود صورة رئيسية للمنتج
     */
    protected function ensurePrimaryImageExists($product): void
    {
        $primaryImage = $product->images()->where('is_primary', true)->first();

        if (!$primaryImage) {
            $firstImage = $product->images()->orderBy('sort_order')->first();
            if ($firstImage) {
                $firstImage->update(['is_primary' => true]);
            }
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات الصورة')
                    ->schema([
                        Forms\Components\FileUpload::make('image_path')
                            ->label('الصورة')
                            ->image()
                            ->directory('products/gallery')
                            ->maxSize(2048)
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '1:1',
                                '4:3',
                                '16:9',
                            ])
                            ->required()
                            ->columnSpanFull(),

                        Forms\Components\TextInput::make('alt_text_ar')
                            ->label('النص البديل (عربي)')
                            ->maxLength(255)
                            ->helperText('وصف الصورة باللغة العربية'),

                        Forms\Components\TextInput::make('alt_text_en')
                            ->label('النص البديل (إنجليزي)')
                            ->maxLength(255)
                            ->helperText('وصف الصورة باللغة الإنجليزية'),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_primary')
                                    ->label('صورة رئيسية')
                                    ->helperText('ستظهر كصورة رئيسية للمنتج')
                                    ->afterStateUpdated(function ($state, $record) {
                                        // إذا تم تحديدها كرئيسية، إلغاء تحديد الصور الأخرى
                                        if ($state && $record) {
                                            $record->product->images()
                                                ->where('id', '!=', $record->id)
                                                ->update(['is_primary' => false]);
                                        }
                                    })
                                    ->live(),

                                Forms\Components\TextInput::make('sort_order')
                                    ->label('ترتيب العرض')
                                    ->numeric()
                                    ->default(0)
                                    ->helperText('الأرقام الأصغر تظهر أولاً')
                                    ->minValue(0),
                            ]),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('alt_text_ar')
            ->columns([
                Tables\Columns\ImageColumn::make('image_path')
                    ->label('الصورة')
                    ->size(80)
                    ->square(),

                Tables\Columns\TextColumn::make('alt_text_ar')
                    ->label('النص البديل (عربي)')
                    ->searchable()
                    ->limit(30),

                Tables\Columns\IconColumn::make('is_primary')
                    ->label('رئيسية')
                    ->boolean()
                    ->trueIcon('heroicon-o-star')
                    ->falseIcon('heroicon-o-star')
                    ->trueColor('warning')
                    ->falseColor('gray'),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('الترتيب')
                    ->sortable()
                    ->badge()
                    ->color('info'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإضافة')
                    ->dateTime('d-m-Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_primary')
                    ->label('صورة رئيسية')
                    ->placeholder('الكل')
                    ->trueLabel('رئيسية')
                    ->falseLabel('ثانوية'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة صورة')
                    ->icon('heroicon-o-plus')
                    ->mutateFormDataUsing(function (array $data): array {
                        // إذا كانت هذه أول صورة، اجعلها رئيسية
                        $productId = $this->getOwnerRecord()->id;
                        $existingImagesCount = \App\Models\ProductImage::where('product_id', $productId)->count();

                        if ($existingImagesCount === 0) {
                            $data['is_primary'] = true;
                        }

                        // إذا تم تحديدها كرئيسية، إلغاء تحديد الصور الأخرى
                        if ($data['is_primary'] ?? false) {
                            \App\Models\ProductImage::where('product_id', $productId)
                                ->update(['is_primary' => false]);
                        }

                        // تعيين ترتيب افتراضي
                        if (!isset($data['sort_order'])) {
                            $data['sort_order'] = $existingImagesCount;
                        }

                        return $data;
                    })
                    ->after(function ($record) {
                        // التأكد من وجود صورة رئيسية
                        $this->ensurePrimaryImageExists($record->product);
                    }),

                Tables\Actions\Action::make('upload_multiple')
                    ->label('رفع عدة صور')
                    ->icon('heroicon-o-photo')
                    ->color('success')
                    ->form([
                        Forms\Components\FileUpload::make('images')
                            ->label('الصور')
                            ->multiple()
                            ->image()
                            ->directory('products/gallery')
                            ->maxSize(2048)
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '1:1',
                                '4:3',
                                '16:9',
                            ])
                            ->reorderable()
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->imagePreviewHeight('150')
                            ->panelAspectRatio('1:1')
                            ->panelLayout('grid')
                            ->required()
                            ->helperText('يمكنك رفع عدة صور في مرة واحدة. الصورة الأولى ستكون رئيسية إذا لم توجد صور أخرى.'),
                    ])
                    ->action(function (array $data) {
                        $product = $this->getOwnerRecord();
                        $images = $data['images'] ?? [];

                        if (empty($images)) {
                            return;
                        }

                        $existingImagesCount = $product->images()->count();
                        $addedCount = 0;

                        foreach ($images as $index => $imagePath) {
                            if (is_string($imagePath) && !empty($imagePath)) {
                                $product->images()->create([
                                    'image_path' => $imagePath,
                                    'is_primary' => $existingImagesCount === 0 && $index === 0, // أول صورة تكون رئيسية إذا لم توجد صور
                                    'sort_order' => $existingImagesCount + $index,
                                    'alt_text_ar' => $product->name_ar . ' - صورة ' . ($existingImagesCount + $index + 1),
                                    'alt_text_en' => $product->name_en . ' - Image ' . ($existingImagesCount + $index + 1),
                                ]);
                                $addedCount++;
                            }
                        }

                        // التأكد من وجود صورة رئيسية
                        $this->ensurePrimaryImageExists($product);

                        Notification::make()
                            ->title('تم رفع الصور بنجاح')
                            ->body("تم إضافة {$addedCount} صورة جديدة")
                            ->success()
                            ->send();
                    })
                    ->modalHeading('رفع عدة صور')
                    ->modalSubmitActionLabel('رفع الصور')
                    ->modalWidth('2xl'),
            ])
            ->actions([
                Action::make('set_primary')
                    ->label('جعل رئيسية')
                    ->icon('heroicon-o-star')
                    ->color('warning')
                    ->visible(fn ($record) => !$record->is_primary)
                    ->action(function ($record) {
                        // إلغاء تحديد جميع الصور الأخرى كرئيسية
                        $record->product->images()->update(['is_primary' => false]);

                        // تحديد هذه الصورة كرئيسية
                        $record->update(['is_primary' => true]);

                        Notification::make()
                            ->title('تم تحديد الصورة كرئيسية بنجاح')
                            ->success()
                            ->send();
                    }),

                Tables\Actions\EditAction::make()
                    ->label('تعديل')
                    ->icon('heroicon-o-pencil')
                    ->mutateFormDataUsing(function (array $data, $record): array {
                        // إذا تم تحديدها كرئيسية، إلغاء تحديد الصور الأخرى
                        if (($data['is_primary'] ?? false) && !$record->is_primary) {
                            $record->product->images()
                                ->where('id', '!=', $record->id)
                                ->update(['is_primary' => false]);
                        }

                        return $data;
                    }),

                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->icon('heroicon-o-trash')
                    ->after(function ($record) {
                        // التأكد من وجود صورة رئيسية بعد الحذف
                        $this->ensurePrimaryImageExists($record->product);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('حذف المحدد')
                        ->after(function ($records) {
                            // التأكد من وجود صورة رئيسية بعد الحذف المجمع
                            $product = $records->first()->product;
                            $this->ensurePrimaryImageExists($product);
                        }),

                    Tables\Actions\BulkAction::make('set_primary')
                        ->label('تعيين كرئيسية')
                        ->action(function ($records) {
                            // إزالة الصورة الرئيسية الحالية
                            $this->getOwnerRecord()->images()->update(['is_primary' => false]);
                            // تعيين أول صورة محددة كرئيسية
                            $records->first()->update(['is_primary' => true]);

                            Notification::make()
                                ->title('تم تعيين الصورة كرئيسية بنجاح')
                                ->success()
                                ->send();
                        })
                        ->icon('heroicon-o-star')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->modalHeading('تعيين صورة رئيسية')
                        ->modalDescription('سيتم تعيين أول صورة محددة كصورة رئيسية وإلغاء تعيين الصورة الرئيسية الحالية.')
                        ->modalSubmitActionLabel('تعيين'),
                ]),
            ])
            ->defaultSort('sort_order', 'asc')
            ->reorderable('sort_order')
            ->paginated([10, 25, 50]);
    }
}
