<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProduct extends EditRecord
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // التحقق من عدم تكرار الأسماء (مع استثناء المنتج الحالي)
        $this->validateUniqueNames($data);

        // التأكد من تفرد الـ slug عند التحديث
        if (isset($data['name_ar']) && !empty($data['name_ar'])) {
            $baseSlug = \Illuminate\Support\Str::slug($data['name_ar']);
            $slug = $baseSlug;
            $counter = 1;

            // التحقق من تفرد الـ slug (استثناء المنتج الحالي)
            while ($this->slugExistsExcludingCurrent($slug)) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $data['slug'] = $slug;
        }

        return $data;
    }

    /**
     * التحقق من وجود slug مع استثناء المنتج الحالي
     */
    private function slugExistsExcludingCurrent(string $slug): bool
    {
        return \App\Models\Product::where('slug', $slug)
            ->where('id', '!=', $this->record->id)
            ->exists();
    }

    /**
     * التحقق من عدم تكرار أسماء المنتجات (مع استثناء المنتج الحالي)
     */
    private function validateUniqueNames(array $data): void
    {
        // التحقق من الاسم العربي
        if (!empty($data['name_ar'])) {
            $existingProduct = \App\Models\Product::where('name_ar', $data['name_ar'])
                ->where('id', '!=', $this->record->id)
                ->first();
            if ($existingProduct) {
                $validator = \Illuminate\Support\Facades\Validator::make([], []);
                $validator->errors()->add('name_ar', 'الاسم العربي "' . $data['name_ar'] . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
                throw new \Illuminate\Validation\ValidationException($validator);
            }
        }

        // التحقق من الاسم الإنجليزي
        if (!empty($data['name_en'])) {
            $existingProduct = \App\Models\Product::where('name_en', $data['name_en'])
                ->where('id', '!=', $this->record->id)
                ->first();
            if ($existingProduct) {
                $validator = \Illuminate\Support\Facades\Validator::make([], []);
                $validator->errors()->add('name_en', 'الاسم الإنجليزي "' . $data['name_en'] . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
                throw new \Illuminate\Validation\ValidationException($validator);
            }
        }
    }
}
