<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use App\Models\Product;
use Filament\Resources\Pages\EditRecord;
use Filament\Actions;

class CreateProductWithImages extends EditRecord
{
    protected static string $resource = ProductResource::class;

    protected ?string $heading = 'إدارة المنتج والصور';

    protected ?string $subheading = 'تم إنشاء المنتج بنجاح! يمكنك الآن إدارة الصور من علامة التبويب "صور المنتج" أدناه.';

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);

        if (!$this->record) {
            $this->redirect(static::getResource()::getUrl('index'));
            return;
        }

        $this->fillForm();
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('finish')
                ->label('إنهاء وعرض المنتج')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->url(fn (): string => static::getResource()::getUrl('view', ['record' => $this->record])),

            Actions\Action::make('continue_editing')
                ->label('متابعة التعديل')
                ->icon('heroicon-o-pencil')
                ->color('gray')
                ->url(fn (): string => static::getResource()::getUrl('edit', ['record' => $this->record])),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return static::getResource()::getUrl('view', ['record' => $this->record]);
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'تم حفظ التغييرات بنجاح';
    }
}
