<?php

namespace App\Filament\Resources\ProductResource\Pages;

use App\Filament\Resources\ProductResource;
use Filament\Resources\Pages\CreateRecord;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // التحقق من عدم تكرار الأسماء
        $this->validateUniqueNames($data);

        // التأكد من وجود قيم افتراضية للحقول المطلوبة
        $data['weight'] = $data['weight'] ?? 0;
        $data['old_price'] = $data['old_price'] ?: null;
        $data['discount_percentage'] = $data['discount_percentage'] ?: null;

        // التأكد من وجود الوصف العربي (مطلوب)
        if (empty($data['description_ar'])) {
            $data['description_ar'] = 'وصف المنتج';
        }

        // التأكد من وجود الوصف الإنجليزي (مطلوب)
        if (empty($data['description_en'])) {
            $data['description_en'] = 'Product Description';
        }

        // إنشاء slug فريد إذا لم يكن موجوداً
        if (empty($data['slug']) && !empty($data['name_ar'])) {
            $baseSlug = \Illuminate\Support\Str::slug($data['name_ar']);
            $slug = $baseSlug;
            $counter = 1;

            // التحقق من تفرد الـ slug
            while (\App\Models\Product::where('slug', $slug)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $data['slug'] = $slug;
        }

        // التأكد من وجود قيمة افتراضية لـ stock_quantity
        $data['stock_quantity'] = $data['stock_quantity'] ?? 0;

        return $data;
    }

    /**
     * التحقق من عدم تكرار أسماء المنتجات
     */
    private function validateUniqueNames(array $data): void
    {
        // التحقق من الاسم العربي
        if (!empty($data['name_ar'])) {
            $existingProduct = \App\Models\Product::where('name_ar', $data['name_ar'])->first();
            if ($existingProduct) {
                $validator = \Illuminate\Support\Facades\Validator::make([], []);
                $validator->errors()->add('name_ar', 'الاسم العربي "' . $data['name_ar'] . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
                throw new \Illuminate\Validation\ValidationException($validator);
            }
        }

        // التحقق من الاسم الإنجليزي
        if (!empty($data['name_en'])) {
            $existingProduct = \App\Models\Product::where('name_en', $data['name_en'])->first();
            if ($existingProduct) {
                $validator = \Illuminate\Support\Facades\Validator::make([], []);
                $validator->errors()->add('name_en', 'الاسم الإنجليزي "' . $data['name_en'] . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
                throw new \Illuminate\Validation\ValidationException($validator);
            }
        }
    }

    protected function getRedirectUrl(): string
    {
        // توجيه المستخدم لصفحة إدارة المنتج والصور بعد الإنشاء
        return $this->getResource()::getUrl('create-with-images', ['record' => $this->record]);
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'تم إنشاء المنتج بنجاح! سيتم توجيهك لإدارة الصور.';
    }
}
