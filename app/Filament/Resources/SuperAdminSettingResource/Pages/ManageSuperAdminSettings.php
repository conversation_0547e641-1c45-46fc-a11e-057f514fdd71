<?php

namespace App\Filament\Resources\SuperAdminSettingResource\Pages;

use App\Filament\Resources\SuperAdminSettingResource;
use App\Models\SuperAdminSetting;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Filament\Notifications\Notification;

class ManageSuperAdminSettings extends ManageRecords
{
    protected static string $resource = SuperAdminSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('إنشاء إعدادات جديدة')
                ->visible(fn () => SuperAdminSetting::count() === 0)
                ->mutateFormDataUsing(function (array $data): array {
                    return array_merge(SuperAdminSetting::getDefaults(), $data);
                })
                ->after(function () {
                    Notification::make()
                        ->title('تم إنشاء الإعدادات بنجاح')
                        ->success()
                        ->send();
                }),
        ];
    }

    public function mount(): void
    {
        parent::mount();
        
        // إنشاء إعدادات افتراضية إذا لم تكن موجودة
        if (SuperAdminSetting::count() === 0) {
            SuperAdminSetting::create(SuperAdminSetting::getDefaults());
        }
    }

    protected function getTableQuery(): \Illuminate\Database\Eloquent\Builder
    {
        return SuperAdminSetting::query();
    }

    protected function getTableRecordsPerPageSelectOptions(): array
    {
        return [10, 25, 50];
    }

    protected function getTableEmptyStateHeading(): ?string
    {
        return 'لا توجد إعدادات';
    }

    protected function getTableEmptyStateDescription(): ?string
    {
        return 'لم يتم إنشاء أي إعدادات سوبر أدمن بعد. انقر على "إنشاء إعدادات جديدة" للبدء.';
    }

    protected function getTableEmptyStateIcon(): ?string
    {
        return 'heroicon-o-shield-check';
    }
}
