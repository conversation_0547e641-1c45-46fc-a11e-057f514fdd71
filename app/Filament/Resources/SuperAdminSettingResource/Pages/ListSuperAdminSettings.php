<?php

namespace App\Filament\Resources\SuperAdminSettingResource\Pages;

use App\Filament\Resources\SuperAdminSettingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSuperAdminSettings extends ListRecords
{
    protected static string $resource = SuperAdminSettingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
