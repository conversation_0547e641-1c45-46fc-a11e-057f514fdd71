<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ManageLanguages extends ManageRecords
{
    protected static string $resource = LanguageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    public function getTableRecordsPerPage(): int
    {
        return 50;
    }

    public function getTableRecords(): LengthAwarePaginator
    {
        // Get translations from JSON files
        $translations = collect(LanguageResource::getTranslations());

        // Convert to a format that Filament can work with
        $items = $translations->map(function ($item) {
            return (object) $item;
        });

        // Create a paginator
        $page = request()->get('page', 1);
        $perPage = $this->getTableRecordsPerPage();
        $items = $items->slice(($page - 1) * $perPage, $perPage)->values();

        return new LengthAwarePaginator(
            $items,
            $translations->count(),
            $perPage,
            $page,
            ['path' => request()->url(), 'query' => request()->query()]
        );
    }
}
