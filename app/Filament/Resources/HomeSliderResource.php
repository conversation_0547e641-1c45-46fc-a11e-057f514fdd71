<?php

namespace App\Filament\Resources;

use App\Filament\Resources\HomeSliderResource\Pages;
use App\Models\HomeSlider;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\ImageColumn;
use App\Traits\HasPermissionFiltering;

class HomeSliderResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = HomeSlider::class;
    protected static ?string $navigationIcon = 'heroicon-o-photo';
    protected static ?string $navigationLabel = 'شرائح الصفحة الرئيسية';
    protected static ?string $navigationGroup = 'إدارة المحتوى والمنتجات';
    protected static ?int $navigationSort = 19;
    protected static ?string $recordTitleAttribute = 'title_ar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معلومات الشريحة')
                    ->schema([
                        TextInput::make('title_ar')
                            ->label('العنوان (عربي)')
                            ->required(),
                        TextInput::make('title_en')
                            ->label('العنوان (إنجليزي)')
                            ->required(),
                        Textarea::make('description_ar')
                            ->label('الوصف (عربي)')
                            ->rows(3),
                        Textarea::make('description_en')
                            ->label('الوصف (إنجليزي)')
                            ->rows(3),
                        TextInput::make('button_text_ar')
                            ->label('نص الزر (عربي)'),
                        TextInput::make('button_text_en')
                            ->label('نص الزر (إنجليزي)'),
                        TextInput::make('button_link')
                            ->label('رابط الزر'),
                        FileUpload::make('image')
                            ->label('صورة الشريحة')
                            ->image()
                            ->required()
                            ->disk('public')
                            ->directory('sliders')
                            ->visibility('public')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '21:9',
                            ])
                            ->maxSize(2048)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->helperText('الحد الأقصى: 2MB. الأنواع المدعومة: JPEG, PNG, WebP'),
                        TextInput::make('order')
                            ->label('الترتيب')
                            ->numeric()
                            ->default(1),
                        Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->label('الصورة')
                    ->disk('public')
                    ->height(60)
                    ->width(100)
                    ->extraAttributes(['style' => 'object-fit: cover; border-radius: 8px;']),
                TextColumn::make('title_ar')
                    ->label('العنوان (عربي)')
                    ->searchable()
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    }),
                TextColumn::make('title_en')
                    ->label('العنوان (إنجليزي)')
                    ->searchable()
                    ->limit(30)
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('order')
                    ->label('الترتيب')
                    ->sortable()
                    ->alignCenter(),
                ToggleColumn::make('is_active')
                    ->label('نشط')
                    ->alignCenter(),
                TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('order')
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('الحالة')
                    ->boolean()
                    ->trueLabel('نشط فقط')
                    ->falseLabel('غير نشط فقط')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->label('عرض'),
                Tables\Actions\EditAction::make()
                    ->label('تحرير'),
                Tables\Actions\DeleteAction::make()
                    ->label('حذف')
                    ->requiresConfirmation()
                    ->modalHeading('حذف الشريحة')
                    ->modalDescription('هل أنت متأكد من حذف هذه الشريحة؟ لا يمكن التراجع عن هذا الإجراء.')
                    ->modalSubmitActionLabel('حذف')
                    ->modalCancelActionLabel('إلغاء'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListHomeSliders::route('/'),
            'create' => Pages\CreateHomeSlider::route('/create'),
            'view' => Pages\ViewHomeSlider::route('/{record}'),
            'edit' => Pages\EditHomeSlider::route('/{record}/edit'),
        ];
    }
}
