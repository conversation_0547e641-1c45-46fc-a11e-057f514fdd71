<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PrivacyPageResource\Pages;
use App\Models\SiteSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use App\Traits\HasPermissionFiltering;

class PrivacyPageResource extends Resource
{
    use HasPermissionFiltering;

    protected static ?string $model = SiteSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationLabel = 'سياسة الخصوصية';

    protected static ?string $modelLabel = 'سياسة الخصوصية';

    protected static ?string $pluralModelLabel = 'سياسة الخصوصية';

    protected static ?string $navigationGroup = 'الصفحات القانونية';

    protected static ?int $navigationSort = 23;

    protected static bool $shouldRegisterNavigation = true;

    public static function canDelete($record): bool
    {
        return false; // لا يمكن حذف إعدادات الموقع
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('محتوى سياسة الخصوصية')
                    ->description('إدارة محتوى صفحة سياسة الخصوصية')
                    ->schema([
                        Forms\Components\Tabs::make('اللغات')
                            ->tabs([
                                Forms\Components\Tabs\Tab::make('العربية')
                                    ->schema([
                                        Forms\Components\TextInput::make('privacy_title_ar')
                                            ->label('عنوان الصفحة بالعربية')
                                            ->default('سياسة الخصوصية')
                                            ->required()
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('privacy_content_ar')
                                            ->label('محتوى سياسة الخصوصية بالعربية')
                                            ->required()
                                            ->columnSpanFull()
                                            ->toolbarButtons([
                                                'bold',
                                                'italic',
                                                'underline',
                                                'strike',
                                                'link',
                                                'heading',
                                                'subheading',
                                                'bulletList',
                                                'orderedList',
                                                'blockquote',
                                                'codeBlock',
                                            ]),

                                        Forms\Components\Textarea::make('privacy_meta_description_ar')
                                            ->label('وصف الصفحة للمحركات البحث (عربي)')
                                            ->maxLength(160)
                                            ->rows(3),
                                    ]),

                                Forms\Components\Tabs\Tab::make('الإنجليزية')
                                    ->schema([
                                        Forms\Components\TextInput::make('privacy_title_en')
                                            ->label('عنوان الصفحة بالإنجليزية')
                                            ->default('Privacy Policy')
                                            ->maxLength(255),

                                        Forms\Components\RichEditor::make('privacy_content_en')
                                            ->label('محتوى سياسة الخصوصية بالإنجليزية')
                                            ->columnSpanFull()
                                            ->toolbarButtons([
                                                'bold',
                                                'italic',
                                                'underline',
                                                'strike',
                                                'link',
                                                'heading',
                                                'subheading',
                                                'bulletList',
                                                'orderedList',
                                                'blockquote',
                                                'codeBlock',
                                            ]),

                                        Forms\Components\Textarea::make('privacy_meta_description_en')
                                            ->label('وصف الصفحة للمحركات البحث (إنجليزي)')
                                            ->maxLength(160)
                                            ->rows(3),
                                    ]),
                            ]),
                    ]),

                Forms\Components\Section::make('إعدادات إضافية')
                    ->schema([
                        Forms\Components\TextInput::make('privacy_meta_keywords')
                            ->label('الكلمات المفتاحية')
                            ->placeholder('سياسة الخصوصية، حماية البيانات، الأمان')
                            ->helperText('افصل الكلمات بفواصل'),

                        Forms\Components\DateTimePicker::make('privacy_last_updated')
                            ->label('تاريخ آخر تحديث')
                            ->default(now())
                            ->displayFormat('Y-m-d H:i'),

                        Forms\Components\Toggle::make('privacy_show_last_updated')
                            ->label('إظهار تاريخ آخر تحديث')
                            ->default(true),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('site_name')
                    ->label('اسم الموقع')
                    ->searchable(),

                Tables\Columns\TextColumn::make('privacy_title_ar')
                    ->label('عنوان سياسة الخصوصية')
                    ->limit(50),

                Tables\Columns\IconColumn::make('privacy_show_last_updated')
                    ->label('إظهار تاريخ التحديث')
                    ->boolean(),

                Tables\Columns\TextColumn::make('privacy_last_updated')
                    ->label('آخر تحديث')
                    ->dateTime('Y-m-d H:i'),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('تاريخ التعديل')
                    ->dateTime('Y-m-d H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('preview')
                    ->label('معاينة الصفحة')
                    ->icon('heroicon-o-eye')
                    ->url(route('privacy'))
                    ->openUrlInNewTab(),

                Tables\Actions\EditAction::make()
                    ->label('تحرير'),
            ])
            ->bulkActions([
                // لا توجد إجراءات جماعية لإعدادات الموقع
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManagePrivacyPages::route('/'),
            'edit' => Pages\EditPrivacyPage::route('/edit'),
        ];
    }
}
