<?php

namespace App\Filament\Resources\PermissionResource\Pages;

use App\Filament\Resources\PermissionResource;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreatePermission extends CreateRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'تم إنشاء الصلاحية بنجاح';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterCreate(): void
    {
        Notification::make()
            ->title('تم إنشاء الصلاحية بنجاح')
            ->body("تم إنشاء الصلاحية '{$this->record->name}' بنجاح")
            ->success()
            ->send();
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction()
                ->label('إنشاء الصلاحية'),
            $this->getCancelFormAction()
                ->label('إلغاء'),
        ];
    }
}
