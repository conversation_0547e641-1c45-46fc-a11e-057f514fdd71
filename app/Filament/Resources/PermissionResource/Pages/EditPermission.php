<?php

namespace App\Filament\Resources\PermissionResource\Pages;

use App\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Spatie\Permission\Models\Permission;

class EditPermission extends EditRecord
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->label('حذف الصلاحية')
                ->color('danger')
                ->requiresConfirmation()
                ->modalHeading('حذف الصلاحية')
                ->modalDescription('هل أنت متأكد من حذف هذه الصلاحية؟ سيتم إزالتها من جميع الأدوار.')
                ->modalSubmitActionLabel('نعم، احذف')
                ->modalCancelActionLabel('إلغاء')
                ->before(function (Permission $record) {
                    // منع حذف الصلاحيات التي لها أدوار
                    if ($record->roles()->count() > 0) {
                        throw new \Exception('لا يمكن حذف هذه الصلاحية لأنها مُعيَّنة لـ ' . $record->roles()->count() . ' دور');
                    }
                }),
        ];
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'تم تحديث الصلاحية بنجاح';
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function afterSave(): void
    {
        Notification::make()
            ->title('تم تحديث الصلاحية بنجاح')
            ->body("تم تحديث الصلاحية '{$this->record->name}' بنجاح")
            ->success()
            ->send();
    }

    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction()
                ->label('حفظ التغييرات'),
            $this->getCancelFormAction()
                ->label('إلغاء'),
        ];
    }
}
