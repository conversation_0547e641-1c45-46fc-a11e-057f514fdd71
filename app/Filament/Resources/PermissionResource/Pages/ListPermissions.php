<?php

namespace App\Filament\Resources\PermissionResource\Pages;

use App\Filament\Resources\PermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPermissions extends ListRecords
{
    protected static string $resource = PermissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('إنشاء صلاحية جديدة')
                ->icon('heroicon-o-plus')
                ->color('primary'),
        ];
    }

    public function getTitle(): string
    {
        return 'إدارة الصلاحيات';
    }

    public function getHeading(): string
    {
        return 'إدارة الصلاحيات';
    }

    public function getSubheading(): ?string
    {
        return 'قم بإنشاء وإدارة الصلاحيات المختلفة في النظام';
    }
}
