<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use App\Traits\HasPagePermissionFiltering;

class SiteSettingsManager extends Page
{
    use HasPagePermissionFiltering;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'filament.pages.site-settings-manager';

    protected static ?string $navigationLabel = 'إدارة الإعدادات';

    protected static ?string $title = 'إدارة إعدادات الموقع';

    protected static ?string $navigationGroup = 'الإعدادات';

    protected static ?int $navigationSort = 2;

    // إخفاء الصفحة من التنقل
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('استيراد وتصدير الإعدادات')
                    ->description('يمكنك استيراد وتصدير إعدادات الموقع من هنا')
                    ->schema([
                        Actions::make([
                            Action::make('export')
                                ->label('تصدير الإعدادات')
                                ->icon('heroicon-o-arrow-down-tray')
                                ->color('success')
                                ->action(function () {
                                    $filename = 'settings_' . date('Y-m-d_His') . '.json';
                                    $path = storage_path('app/public/' . $filename);

                                    Artisan::call('site:export-settings', [
                                        'path' => $path
                                    ]);

                                    $url = Storage::url($filename);

                                    Notification::make()
                                        ->title('تم تصدير الإعدادات بنجاح')
                                        ->success()
                                        ->send();

                                    return redirect($url);
                                }),
                        ]),

                        FileUpload::make('settings_file')
                            ->label('ملف الإعدادات')
                            ->helperText('اختر ملف JSON يحتوي على إعدادات الموقع')
                            ->directory('settings-imports')
                            ->visibility('private')
                            ->acceptedFileTypes(['application/json'])
                            ->maxSize(1024),

                        Actions::make([
                            Action::make('import')
                                ->label('استيراد الإعدادات')
                                ->icon('heroicon-o-arrow-up-tray')
                                ->color('primary')
                                ->requiresConfirmation()
                                ->modalHeading('استيراد الإعدادات')
                                ->modalDescription('هل أنت متأكد من استيراد هذه الإعدادات؟ سيتم استبدال الإعدادات الحالية.')
                                ->modalSubmitActionLabel('نعم، استيراد')
                                ->disabled(fn ($get) => !$get('settings_file'))
                                ->action(function ($get) {
                                    $file = $get('settings_file');

                                    if (!$file) {
                                        Notification::make()
                                            ->title('يرجى اختيار ملف أولاً')
                                            ->danger()
                                            ->send();
                                        return;
                                    }

                                    $path = Storage::disk('local')->path('public/' . $file);

                                    Artisan::call('site:import-settings', [
                                        'path' => $path
                                    ]);

                                    Notification::make()
                                        ->title('تم استيراد الإعدادات بنجاح')
                                        ->success()
                                        ->send();

                                    $this->redirect(SiteSettingsManager::getUrl());
                                }),
                        ]),
                    ]),

                Section::make('تهيئة الإعدادات')
                    ->description('إعادة تعيين إعدادات الموقع إلى القيم الافتراضية')
                    ->schema([
                        Actions::make([
                            Action::make('reset')
                                ->label('إعادة تعيين الإعدادات')
                                ->icon('heroicon-o-arrow-path')
                                ->color('danger')
                                ->requiresConfirmation()
                                ->modalHeading('إعادة تعيين الإعدادات')
                                ->modalDescription('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟ لا يمكن التراجع عن هذا الإجراء.')
                                ->modalSubmitActionLabel('نعم، إعادة تعيين')
                                ->action(function () {
                                    Artisan::call('site:init-settings');

                                    Notification::make()
                                        ->title('تم إعادة تعيين الإعدادات بنجاح')
                                        ->success()
                                        ->send();

                                    $this->redirect(SiteSettingsManager::getUrl());
                                }),
                        ]),
                    ]),
            ])
            ->statePath('data');
    }
}
