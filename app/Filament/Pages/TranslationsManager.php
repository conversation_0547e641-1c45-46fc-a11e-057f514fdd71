<?php

namespace App\Filament\Pages;

use App\Models\Language;
use Filament\Pages\Page;
use Illuminate\Support\Facades\File;
use Filament\Notifications\Notification;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Support\Exceptions\Halt;
use App\Traits\HasPagePermissionFiltering;

class TranslationsManager extends Page
{
    use HasPagePermissionFiltering;

    protected static ?string $navigationIcon = 'heroicon-o-language';

    protected static ?string $navigationGroup = 'إدارة النظام';

    protected static ?int $navigationSort = 3;

    protected static string $view = 'filament.pages.translations-manager';

    public ?array $data = [];

    public $translations = [];

    public $searchQuery = '';

    public $editingTranslation = null;

    public $editData = [
        'ar' => '',
        'en' => '',
    ];

    public $confirmingDelete = false;

    public $deleteKey = '';

    public function mount(): void
    {
        $this->loadTranslations();
    }

    public static function getNavigationLabel(): string
    {
        return __('مدير الترجمات');
    }

    public function getTitle(): string
    {
        return __('مدير الترجمات');
    }

    protected function loadTranslations(): void
    {
        $translations = [];
        $languages = Language::where('is_active', true)->pluck('code')->toArray();

        // If no languages are active, use default languages
        if (empty($languages)) {
            $languages = ['ar', 'en'];
        }

        foreach ($languages as $lang) {
            $jsonPath = resource_path("lang/{$lang}.json");
            if (File::exists($jsonPath)) {
                $jsonTranslations = json_decode(File::get($jsonPath), true) ?: [];
                foreach ($jsonTranslations as $key => $value) {
                    if (!isset($translations[$key])) {
                        $translations[$key] = [
                            'key' => $key,
                        ];
                    }
                    $translations[$key][$lang] = $value;
                }
            } else {
                // Create empty language file if it doesn't exist
                File::put($jsonPath, json_encode([], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            }
        }

        // Sort translations by key
        ksort($translations);

        // Filter by search query if provided
        if (!empty($this->searchQuery)) {
            $query = strtolower($this->searchQuery);
            $translations = array_filter($translations, function ($item) use ($query) {
                $key = strtolower($item['key']);
                $ar = strtolower($item['ar'] ?? '');
                $en = strtolower($item['en'] ?? '');

                return strpos($key, $query) !== false ||
                       strpos($ar, $query) !== false ||
                       strpos($en, $query) !== false;
            });
        }

        $this->translations = array_values($translations);
    }

    public function search(): void
    {
        $this->loadTranslations();
    }

    public function clearSearch(): void
    {
        $this->searchQuery = '';
        $this->loadTranslations();
    }

    public function addTranslation(): void
    {
        $this->validate([
            'data.key' => 'required|string|max:255',
            'data.ar' => 'required|string|max:255',
            'data.en' => 'required|string|max:255',
        ]);

        $languages = Language::where('is_active', true)->pluck('code')->toArray();

        // If no languages are active, use default languages
        if (empty($languages)) {
            $languages = ['ar', 'en'];
        }

        foreach ($languages as $lang) {
            if (isset($this->data[$lang])) {
                $jsonPath = resource_path("lang/{$lang}.json");
                if (File::exists($jsonPath)) {
                    $jsonTranslations = json_decode(File::get($jsonPath), true) ?: [];
                    $jsonTranslations[$this->data['key']] = $this->data[$lang];
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                } else {
                    // Create file if it doesn't exist
                    $jsonTranslations = [$this->data['key'] => $this->data[$lang]];
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
            }
        }

        // Clear cache to apply changes immediately
        app('translator')->setLoaded([]);
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }

        Notification::make()
            ->title(__('Translation added successfully'))
            ->success()
            ->send();

        $this->data = [];
        $this->loadTranslations();
    }

    public function updateTranslation(string $key, array $data): void
    {
        $languages = Language::where('is_active', true)->pluck('code')->toArray();

        // If no languages are active, use default languages
        if (empty($languages)) {
            $languages = ['ar', 'en'];
        }

        foreach ($languages as $lang) {
            if (isset($data[$lang])) {
                $jsonPath = resource_path("lang/{$lang}.json");
                if (File::exists($jsonPath)) {
                    $jsonTranslations = json_decode(File::get($jsonPath), true) ?: [];
                    $jsonTranslations[$key] = $data[$lang];
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                } else {
                    // Create file if it doesn't exist
                    $jsonTranslations = [$key => $data[$lang]];
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
            }
        }

        // Clear cache to apply changes immediately
        app('translator')->setLoaded([]);
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }

        Notification::make()
            ->title(__('Translation updated successfully'))
            ->success()
            ->send();

        $this->loadTranslations();
    }

    public function confirmDelete(string $key): void
    {
        $this->deleteKey = $key;
        $this->deleteTranslation($key);
    }

    public function deleteTranslation(string $key): void
    {
        $languages = Language::where('is_active', true)->pluck('code')->toArray();

        // If no languages are active, use default languages
        if (empty($languages)) {
            $languages = ['ar', 'en'];
        }

        foreach ($languages as $lang) {
            $jsonPath = resource_path("lang/{$lang}.json");
            if (File::exists($jsonPath)) {
                $jsonTranslations = json_decode(File::get($jsonPath), true) ?: [];
                if (isset($jsonTranslations[$key])) {
                    unset($jsonTranslations[$key]);
                    File::put($jsonPath, json_encode($jsonTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                }
            }
        }

        // Clear cache to apply changes immediately
        app('translator')->setLoaded([]);
        if (function_exists('opcache_reset')) {
            opcache_reset();
        }

        Notification::make()
            ->title(__('Translation deleted successfully'))
            ->success()
            ->send();

        $this->loadTranslations();
        $this->confirmingDelete = false;
        $this->deleteKey = '';
    }

    public function updatedEditingTranslation($value): void
    {
        if ($value !== null && isset($this->translations[$value])) {
            $this->editData = [
                'ar' => $this->translations[$value]['ar'] ?? '',
                'en' => $this->translations[$value]['en'] ?? '',
            ];

            // Open edit modal using JavaScript
            $this->dispatch('open-edit-modal');
        }
    }

    public function saveEdit(): void
    {
        if ($this->editingTranslation !== null && isset($this->translations[$this->editingTranslation])) {
            $key = $this->translations[$this->editingTranslation]['key'];
            $this->updateTranslation($key, $this->editData);
            $this->editingTranslation = null;
            $this->editData = [
                'ar' => '',
                'en' => '',
            ];
        }
    }

    public function cancelEdit(): void
    {
        $this->editingTranslation = null;
        $this->editData = [
            'ar' => '',
            'en' => '',
        ];
    }
}
