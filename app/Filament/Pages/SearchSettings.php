<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Notifications\Notification;
use App\Models\SiteSetting;
use Illuminate\Support\Str;
use Filament\Support\Enums\IconPosition;
use App\Traits\HasPagePermissionFiltering;

class SearchSettings extends Page
{
    use HasPagePermissionFiltering;

    protected static ?string $navigationIcon = 'heroicon-o-magnifying-glass';

    protected static string $view = 'filament.pages.search-settings';

    protected static ?string $navigationLabel = 'بحث في الإعدادات';

    protected static ?string $title = 'بحث في إعدادات الموقع';

    protected static ?string $navigationGroup = 'الإعدادات';

    protected static ?int $navigationSort = 4;

    // إخفاء الصفحة من التنقل
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];

    public $searchQuery = '';
    public $searchResults = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('بحث في الإعدادات')
                    ->description('ابحث في إعدادات الموقع بالاسم أو القيمة')
                    ->schema([
                        TextInput::make('searchQuery')
                            ->label('كلمة البحث')
                            ->placeholder('أدخل كلمة البحث...')
                            ->helperText('أدخل جزءًا من اسم الإعداد أو قيمته للبحث')
                            ->required()
                            ->minLength(2)
                            ->live(debounce: 500)
                            ->afterStateUpdated(function ($state) {
                                $this->search($state);
                            }),

                        Actions::make([
                            Action::make('search')
                                ->label('بحث')
                                ->icon('heroicon-m-magnifying-glass')
                                ->iconPosition(IconPosition::After)
                                ->action(function (array $data) {
                                    $this->search($data['searchQuery'] ?? '');
                                }),
                        ]),
                    ]),
            ])
            ->statePath('data');
    }

    public function search($query)
    {
        $this->searchQuery = $query;

        if (strlen($query) < 2) {
            $this->searchResults = [];
            return;
        }

        $settings = SiteSetting::first();

        if (!$settings) {
            $this->searchResults = [];
            return;
        }

        $results = [];

        // Obtener todos los atributos del modelo
        $attributes = $settings->getAttributes();

        // Filtrar los atributos que coinciden con la consulta
        foreach ($attributes as $key => $value) {
            // Ignorar algunos campos
            if (in_array($key, ['id', 'created_at', 'updated_at'])) {
                continue;
            }

            // Convertir a string para búsqueda
            $stringValue = is_array($value) || is_object($value) ? json_encode($value) : (string) $value;

            // Buscar en la clave o el valor
            if (Str::contains(strtolower($key), strtolower($query)) ||
                Str::contains(strtolower($stringValue), strtolower($query))) {

                // Obtener el nombre legible
                $readableName = $this->getReadableName($key);

                // Formatear el valor para mostrar
                $formattedValue = $this->formatValue($value);

                $results[] = [
                    'key' => $key,
                    'name' => $readableName,
                    'value' => $formattedValue,
                    'tab' => $this->getTabForSetting($key),
                ];
            }
        }

        $this->searchResults = $results;
    }

    /**
     * Obtener un nombre legible para la clave de configuración
     */
    private function getReadableName($key)
    {
        // Convertir snake_case a palabras
        $name = str_replace('_', ' ', $key);

        // Capitalizar cada palabra
        return ucwords($name);
    }

    /**
     * Formatear el valor para mostrar
     */
    private function formatValue($value)
    {
        if (is_bool($value)) {
            return $value ? 'نعم' : 'لا';
        }

        if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }

        if (is_null($value)) {
            return 'غير محدد';
        }

        // Truncar valores largos
        $stringValue = (string) $value;
        if (strlen($stringValue) > 100) {
            return substr($stringValue, 0, 100) . '...';
        }

        return $stringValue;
    }

    /**
     * Obtener la pestaña para la configuración
     */
    private function getTabForSetting($key)
    {
        $tabMappings = [
            // Información básica
            'site_name' => 'معلومات أساسية',
            'site_description' => 'معلومات أساسية',

            // Información de contacto
            'contact_email' => 'معلومات الاتصال',
            'contact_phone' => 'معلومات الاتصال',
            'address' => 'معلومات الاتصال',

            // Redes sociales
            'facebook_url' => 'وسائل التواصل الاجتماعي',
            'instagram_url' => 'وسائل التواصل الاجتماعي',
            'twitter_url' => 'وسائل التواصل الاجتماعي',
            'whatsapp_number' => 'وسائل التواصل الاجتماعي',

            // Precios del oro
            'gold_price_24k' => 'أسعار الذهب',
            'gold_price_21k' => 'أسعار الذهب',
            'gold_price_18k' => 'أسعار الذهب',

            // SEO
            'meta_title' => 'تهيئة محركات البحث (SEO)',
            'meta_description' => 'تهيئة محركات البحث (SEO)',
            'meta_keywords' => 'تهيئة محركات البحث (SEO)',

            // Tienda
            'maintenance_mode' => 'إعدادات المتجر',
            'maintenance_message' => 'إعدادات المتجر',
            'shipping_cost' => 'إعدادات المتجر',
            'free_shipping_threshold' => 'إعدادات المتجر',
            'tax_rate' => 'إعدادات المتجر',
            'prices_include_tax' => 'إعدادات المتجر',

            // Página de inicio
            'show_featured_products' => 'إعدادات الصفحة الرئيسية',
            'show_new_arrivals' => 'إعدادات الصفحة الرئيسية',
            'show_categories' => 'إعدادات الصفحة الرئيسية',
            'show_gold_prices' => 'إعدادات الصفحة الرئيسية',

            // Correo electrónico
            'mail_from_address' => 'إعدادات البريد الإلكتروني',
            'mail_from_name' => 'إعدادات البريد الإلكتروني',
            'mail_host' => 'إعدادات البريد الإلكتروني',
            'mail_port' => 'إعدادات البريد الإلكتروني',
            'mail_username' => 'إعدادات البريد الإلكتروني',
            'mail_password' => 'إعدادات البريد الإلكتروني',
            'mail_encryption' => 'إعدادات البريد الإلكتروني',

            // Redes sociales adicionales
            'youtube_url' => 'وسائل التواصل الإضافية',
            'tiktok_url' => 'وسائل التواصل الإضافية',
            'linkedin_url' => 'وسائل التواصل الإضافية',

            // Marketing
            'google_analytics_id' => 'أدوات التسويق والتحليلات',
            'facebook_pixel_id' => 'أدوات التسويق والتحليلات',
            'custom_header_scripts' => 'أدوات التسويق والتحليلات',
            'custom_footer_scripts' => 'أدوات التسويق والتحليلات',

            // Pie de página
            'footer_text' => 'نص التذييل',

            // Métodos de pago
            'enable_credit_card' => 'إعدادات الدفع',
            'enable_paypal' => 'إعدادات الدفع',
            'enable_bank_transfer' => 'إعدادات الدفع',
            'enable_cash_on_delivery' => 'إعدادات الدفع',
            'enable_fawry' => 'إعدادات الدفع',
            'stripe_key' => 'إعدادات الدفع',
            'stripe_secret' => 'إعدادات الدفع',
            'stripe_sandbox_mode' => 'إعدادات الدفع',
            'paypal_client_id' => 'إعدادات الدفع',
            'paypal_secret' => 'إعدادات الدفع',
            'paypal_sandbox_mode' => 'إعدادات الدفع',

            // Envío
            'shipping_zones' => 'إعدادات الشحن المتقدمة',
            'enable_local_pickup' => 'إعدادات الشحن المتقدمة',
            'local_pickup_discount' => 'إعدادات الشحن المتقدمة',
            'shipping_policy' => 'إعدادات الشحن المتقدمة',
            'min_order_amount' => 'إعدادات الشحن المتقدمة',
            'order_prefix' => 'إعدادات الشحن المتقدمة',
            'enable_guest_checkout' => 'إعدادات الشحن المتقدمة',

            // Facturación
            'enable_invoices' => 'إعدادات الفواتير',
            'invoice_prefix' => 'إعدادات الفواتير',
            'company_name_invoice' => 'إعدادات الفواتير',
            'company_address_invoice' => 'إعدادات الفواتير',
            'company_tax_id' => 'إعدادات الفواتير',

            // Textos legales
            'privacy_policy' => 'النصوص القانونية',
            'terms_conditions' => 'النصوص القانونية',
            'return_policy' => 'النصوص القانونية',
            'shipping_policy_text' => 'النصوص القانونية',

            // Cookies y GDPR
            'show_cookie_banner' => 'ملفات تعريف الارتباط والخصوصية',
            'cookie_banner_text' => 'ملفات تعريف الارتباط والخصوصية',
            'cookie_banner_button_text' => 'ملفات تعريف الارتباط والخصوصية',
            'enable_gdpr_compliance' => 'ملفات تعريف الارتباط والخصوصية',
            'gdpr_compliance_text' => 'ملفات تعريف الارتباط والخصوصية',
            'require_marketing_consent' => 'ملفات تعريف الارتباط والخصوصية',
            'marketing_consent_text' => 'ملفات تعريف الارتباط والخصوصية',

            // Integración con redes sociales
            'enable_social_login' => 'تكامل وسائل التواصل الاجتماعي',
            'enable_facebook_login' => 'تكامل وسائل التواصل الاجتماعي',
            'enable_google_login' => 'تكامل وسائل التواصل الاجتماعي',
            'enable_twitter_login' => 'تكامل وسائل التواصل الاجتماعي',
            'facebook_app_id' => 'تكامل وسائل التواصل الاجتماعي',
            'facebook_app_secret' => 'تكامل وسائل التواصل الاجتماعي',
            'google_client_id' => 'تكامل وسائل التواصل الاجتماعي',
            'google_client_secret' => 'تكامل وسائل التواصل الاجتماعي',
            'twitter_client_id' => 'تكامل وسائل التواصل الاجتماعي',
            'twitter_client_secret' => 'تكامل وسائل التواصل الاجتماعي',
            'enable_social_sharing' => 'تكامل وسائل التواصل الاجتماعي',
            'share_on_facebook' => 'تكامل وسائل التواصل الاجتماعي',
            'share_on_twitter' => 'تكامل وسائل التواصل الاجتماعي',
            'share_on_whatsapp' => 'تكامل وسائل التواصل الاجتماعي',
            'share_on_pinterest' => 'تكامل وسائل التواصل الاجتماعي',
            'share_on_linkedin' => 'تكامل وسائل التواصل الاجتماعي',
            'show_instagram_feed' => 'تكامل وسائل التواصل الاجتماعي',
            'instagram_token' => 'تكامل وسائل التواصل الاجتماعي',
            'instagram_count' => 'تكامل وسائل التواصل الاجتماعي',
            'show_facebook_feed' => 'تكامل وسائل التواصل الاجتماعي',
            'facebook_page_id' => 'تكامل وسائل التواصل الاجتماعي',
            'facebook_count' => 'تكامل وسائل التواصل الاجتماعي',
        ];

        return $tabMappings[$key] ?? 'أخرى';
    }
}
