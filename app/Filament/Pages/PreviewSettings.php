<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Notifications\Notification;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use App\Traits\HasPagePermissionFiltering;

class PreviewSettings extends Page
{
    use HasPagePermissionFiltering;

    protected static ?string $navigationIcon = 'heroicon-o-eye';

    protected static string $view = 'filament.pages.preview-settings';

    protected static ?string $navigationLabel = 'معاينة الإعدادات';

    protected static ?string $title = 'معاينة إعدادات الموقع';

    protected static ?string $navigationGroup = 'الإعدادات';

    protected static ?int $navigationSort = 5;

    // إخفاء الصفحة من التنقل
    protected static bool $shouldRegisterNavigation = false;

    public ?array $data = [];
    public $selectedPreview = 'header';
    public $previewData = [];

    public function mount(): void
    {
        $settings = SiteSetting::first();

        if ($settings) {
            $this->data = $settings->toArray();
        }

        $this->updatePreview();

        $this->form->fill($this->data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('معاينة الإعدادات')
                    ->description('معاينة تأثير الإعدادات على الموقع')
                    ->schema([
                        Select::make('selectedPreview')
                            ->label('اختر العنصر للمعاينة')
                            ->options([
                                'header' => 'رأس الصفحة',
                                'footer' => 'تذييل الصفحة',
                                'home_slider' => 'شريط العرض الرئيسي',
                                'product_card' => 'بطاقة المنتج',
                                'cookie_banner' => 'شريط ملفات تعريف الارتباط',
                                'social_sharing' => 'مشاركة المنتجات',
                                'payment_methods' => 'طرق الدفع',
                            ])
                            ->default('header')
                            ->live()
                            ->afterStateUpdated(fn () => $this->updatePreview()),
                    ]),

                Tabs::make('إعدادات المعاينة')
                    ->tabs([
                        Tabs\Tab::make('معلومات أساسية')
                            ->schema([
                                TextInput::make('site_name')
                                    ->label('اسم الموقع')
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Textarea::make('site_description')
                                    ->label('وصف الموقع')
                                    ->rows(3)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),

                        Tabs\Tab::make('معلومات الاتصال')
                            ->schema([
                                TextInput::make('contact_email')
                                    ->label('البريد الإلكتروني للتواصل')
                                    ->email()
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                TextInput::make('contact_phone')
                                    ->label('رقم الهاتف للتواصل')
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),

                        Tabs\Tab::make('وسائل التواصل الاجتماعي')
                            ->schema([
                                TextInput::make('facebook_url')
                                    ->label('رابط فيسبوك')
                                    ->url()
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                TextInput::make('instagram_url')
                                    ->label('رابط انستغرام')
                                    ->url()
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                TextInput::make('twitter_url')
                                    ->label('رابط تويتر')
                                    ->url()
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),

                        Tabs\Tab::make('نص التذييل')
                            ->schema([
                                Textarea::make('footer_text')
                                    ->label('نص تذييل الصفحة')
                                    ->rows(3)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),

                        Tabs\Tab::make('ملفات تعريف الارتباط')
                            ->schema([
                                Toggle::make('show_cookie_banner')
                                    ->label('عرض شريط ملفات تعريف الارتباط')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Textarea::make('cookie_banner_text')
                                    ->label('نص شريط ملفات تعريف الارتباط')
                                    ->placeholder('هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.')
                                    ->rows(3)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                TextInput::make('cookie_banner_button_text')
                                    ->label('نص زر شريط ملفات تعريف الارتباط')
                                    ->placeholder('أوافق')
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),

                        Tabs\Tab::make('مشاركة المنتجات')
                            ->schema([
                                Toggle::make('enable_social_sharing')
                                    ->label('تفعيل مشاركة المنتجات')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Toggle::make('share_on_facebook')
                                    ->label('مشاركة على فيسبوك')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Toggle::make('share_on_twitter')
                                    ->label('مشاركة على تويتر')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Toggle::make('share_on_whatsapp')
                                    ->label('مشاركة على واتساب')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),

                        Tabs\Tab::make('طرق الدفع')
                            ->schema([
                                Toggle::make('enable_credit_card')
                                    ->label('تفعيل الدفع ببطاقة الائتمان')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Toggle::make('enable_paypal')
                                    ->label('تفعيل الدفع عبر PayPal')
                                    ->default(true)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Toggle::make('enable_bank_transfer')
                                    ->label('تفعيل التحويل البنكي')
                                    ->default(false)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                                Toggle::make('enable_cash_on_delivery')
                                    ->label('تفعيل الدفع عند الاستلام')
                                    ->default(false)
                                    ->live()
                                    ->afterStateUpdated(fn () => $this->updatePreview()),
                            ]),
                    ])
                    ->columnSpanFull(),

                Section::make('حفظ التغييرات')
                    ->schema([
                        Actions::make([
                            Action::make('save')
                                ->label('حفظ التغييرات')
                                ->color('primary')
                                ->action(function (array $data) {
                                    $settings = SiteSetting::first();

                                    if ($settings) {
                                        // Filtrar solo los campos que se han modificado
                                        $filteredData = array_intersect_key($data, array_flip([
                                            'site_name',
                                            'site_description',
                                            'contact_email',
                                            'contact_phone',
                                            'facebook_url',
                                            'instagram_url',
                                            'twitter_url',
                                            'footer_text',
                                            'show_cookie_banner',
                                            'cookie_banner_text',
                                            'cookie_banner_button_text',
                                            'enable_social_sharing',
                                            'share_on_facebook',
                                            'share_on_twitter',
                                            'share_on_whatsapp',
                                            'enable_credit_card',
                                            'enable_paypal',
                                            'enable_bank_transfer',
                                            'enable_cash_on_delivery',
                                        ]));

                                        $settings->update($filteredData);

                                        // Limpiar caché
                                        Cache::forget('site_settings');

                                        Notification::make()
                                            ->title('تم حفظ الإعدادات بنجاح')
                                            ->success()
                                            ->send();
                                    }
                                }),

                            Action::make('reset')
                                ->label('إعادة تعيين')
                                ->color('danger')
                                ->action(function () {
                                    $settings = SiteSetting::first();

                                    if ($settings) {
                                        $this->data = $settings->toArray();
                                        $this->form->fill($this->data);
                                        $this->updatePreview();

                                        Notification::make()
                                            ->title('تم إعادة تعيين الإعدادات')
                                            ->success()
                                            ->send();
                                    }
                                }),
                        ]),
                    ]),
            ])
            ->statePath('data');
    }

    public function updatePreview()
    {
        $this->previewData = [
            'selectedPreview' => $this->selectedPreview,
            'site_name' => $this->data['site_name'] ?? 'مكة جولد',
            'site_description' => $this->data['site_description'] ?? 'متجر مجوهرات مكة جولد - أفضل مجوهرات ذهبية وفضية',
            'contact_email' => $this->data['contact_email'] ?? '<EMAIL>',
            'contact_phone' => $this->data['contact_phone'] ?? '+20 ************',
            'facebook_url' => $this->data['facebook_url'] ?? '#',
            'instagram_url' => $this->data['instagram_url'] ?? '#',
            'twitter_url' => $this->data['twitter_url'] ?? '#',
            'footer_text' => $this->data['footer_text'] ?? '&copy; ' . date('Y') . ' مكة جولد للمجوهرات. جميع الحقوق محفوظة.',
            'show_cookie_banner' => $this->data['show_cookie_banner'] ?? true,
            'cookie_banner_text' => $this->data['cookie_banner_text'] ?? 'هذا الموقع يستخدم ملفات تعريف الارتباط لتحسين تجربتك. بالاستمرار في استخدام هذا الموقع، فإنك توافق على استخدامنا لملفات تعريف الارتباط.',
            'cookie_banner_button_text' => $this->data['cookie_banner_button_text'] ?? 'أوافق',
            'enable_social_sharing' => $this->data['enable_social_sharing'] ?? true,
            'share_on_facebook' => $this->data['share_on_facebook'] ?? true,
            'share_on_twitter' => $this->data['share_on_twitter'] ?? true,
            'share_on_whatsapp' => $this->data['share_on_whatsapp'] ?? true,
            'enable_credit_card' => $this->data['enable_credit_card'] ?? true,
            'enable_paypal' => $this->data['enable_paypal'] ?? true,
            'enable_bank_transfer' => $this->data['enable_bank_transfer'] ?? false,
            'enable_cash_on_delivery' => $this->data['enable_cash_on_delivery'] ?? false,
        ];
    }
}
