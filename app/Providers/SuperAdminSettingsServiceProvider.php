<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\Services\SuperAdminSettingsService;

class SuperAdminSettingsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تمرير إعدادات السوبر أدمن لجميع صفحات الواجهة الأمامية
        View::composer('*', function ($view) {
            // تجنب تطبيق الإعدادات على صفحات لوحة التحكم
            if (!request()->is('admin*') && !request()->is('filament*')) {
                $superAdminSettings = app(SuperAdminSettingsService::class);
                $view->with('superAdminSettings', $superAdminSettings);
            }
        });
    }
}
