<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class HelperServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Register helper files
        $this->loadHelpers();

        // Register PaymentHelper as singleton
        $this->app->singleton(\App\Helpers\PaymentHelper::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Load helper files
     */
    protected function loadHelpers(): void
    {
        $helperFiles = [
            app_path('Helpers/ImageHelper.php'),
        ];

        foreach ($helperFiles as $file) {
            if (file_exists($file)) {
                require_once $file;
            }
        }
    }
}
