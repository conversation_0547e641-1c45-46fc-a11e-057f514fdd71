<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Filament\Facades\Filament;
use Filament\Navigation\NavigationGroup;

class FilamentRolesServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تخصيص مجموعة التنقل للأدوار والصلاحيات
        Filament::serving(function () {
            // إضافة أيقونات مخصصة للمجموعات
            Filament::registerNavigationGroups([
                NavigationGroup::make('إدارة الصلاحيات')
                    ->icon('heroicon-o-shield-check')
                    ->collapsed(false),
            ]);

            // يمكن إضافة عناصر تنقل مخصصة هنا إذا لزم الأمر
        });

        // تخصيص ألوان النظام
        $this->customizeTheme();
    }

    /**
     * تخصيص ألوان وتصميم النظام
     */
    protected function customizeTheme(): void
    {
        // يمكن إضافة تخصيصات إضافية هنا
    }
}
