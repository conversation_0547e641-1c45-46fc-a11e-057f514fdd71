<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\SettingsService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;

class MailConfigServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        try {
            // Verificar si la tabla existe antes de intentar acceder a ella
            if (Schema::hasTable('site_settings')) {
                $settings = app(SettingsService::class)->all();
                
                // Configurar el correo electrónico si se han definido los valores
                if ($settings && 
                    $settings->mail_host && 
                    $settings->mail_port && 
                    $settings->mail_username && 
                    $settings->mail_from_address) {
                    
                    // Configurar el correo electrónico
                    Config::set('mail.default', 'smtp');
                    Config::set('mail.mailers.smtp.host', $settings->mail_host);
                    Config::set('mail.mailers.smtp.port', $settings->mail_port);
                    Config::set('mail.mailers.smtp.username', $settings->mail_username);
                    
                    // Configurar la contraseña solo si está definida
                    if ($settings->mail_password) {
                        Config::set('mail.mailers.smtp.password', $settings->mail_password);
                    }
                    
                    // Configurar el cifrado
                    Config::set('mail.mailers.smtp.encryption', $settings->mail_encryption ?: null);
                    
                    // Configurar la dirección de correo electrónico del remitente
                    Config::set('mail.from.address', $settings->mail_from_address);
                    Config::set('mail.from.name', $settings->mail_from_name ?: $settings->site_name);
                }
            }
        } catch (\Exception $e) {
            // Si hay un error, no configurar el correo electrónico
        }
    }
}
