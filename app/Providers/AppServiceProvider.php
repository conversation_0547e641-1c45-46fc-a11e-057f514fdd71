<?php

namespace App\Providers;

use App\Services\SettingsService;
use App\Services\SuperAdminSettingsService;
use App\Models\Product;
use App\Observers\ProductObserver;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Schema;
use Carbon\Carbon;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Registrar el servicio de configuraciones como singleton
        $this->app->singleton(SettingsService::class, function ($app) {
            return new SettingsService();
        });

        // تسجيل خدمة إعدادات السوبر أدمن كـ singleton
        $this->app->singleton(SuperAdminSettingsService::class, function ($app) {
            return new SuperAdminSettingsService();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // تسجيل Product Observer
        Product::observe(ProductObserver::class);

        // إعداد Carbon للعربية
        Carbon::setLocale('ar');

        // مشاركة إعدادات الموقع مع جميع العروض
        try {
            // التحقق من وجود الجدول قبل محاولة الوصول إليه
            if (Schema::hasTable('site_settings')) {
                // إجبار تحديث الإعدادات من قاعدة البيانات في بيئة التطوير
                $forceRefresh = app()->environment('local');
                $settings = app(SettingsService::class)->all($forceRefresh);
                View::share('settings', $settings);
            }

            // مشاركة إعدادات السوبر أدمن مع جميع العروض
            if (Schema::hasTable('super_admin_settings')) {
                $forceRefresh = app()->environment('local');
                $superAdminSettings = app(SuperAdminSettingsService::class)->all($forceRefresh);
                View::share('superAdminSettings', $superAdminSettings);
            }
        } catch (\Exception $e) {
            // إذا كان هناك خطأ، لا تشارك الإعدادات
        }
    }
}
