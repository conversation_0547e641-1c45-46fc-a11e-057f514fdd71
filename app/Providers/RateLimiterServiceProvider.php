<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;

class RateLimiterServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Define API rate limiter with very high limits (effectively disabled)
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(10000)->by($request->user()?->id ?: $request->ip());
        });

        // Define login rate limiter with very high limits (effectively disabled)
        RateLimiter::for('login', function (Request $request) {
            return Limit::perMinute(10000)->by($request->input('email').$request->ip());
        });

        // Define register rate limiter with very high limits (effectively disabled)
        RateLimiter::for('register', function (Request $request) {
            return Limit::perMinute(10000)->by($request->ip());
        });
    }
}
