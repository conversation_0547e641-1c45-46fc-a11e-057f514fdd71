<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use App\Services\SuperAdminSettingsService;
use App\Models\Language;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get super admin settings service
        $superAdminSettingsService = app(SuperAdminSettingsService::class);

        // Check if multilingual is enabled from super admin settings
        $multilingual = $superAdminSettingsService->isMultilingualEnabled();
        $defaultLanguage = $superAdminSettingsService->getDefaultLanguage();

        // If multilingual is disabled, use default language
        if (!$multilingual) {
            App::setLocale($defaultLanguage);
            Session::put('locale', $defaultLanguage);
            return $next($request);
        }

        // Check for locale in session
        if (Session::has('locale')) {
            $locale = Session::get('locale');
        }
        // Check for locale in cookie
        elseif ($request->cookie('locale')) {
            $locale = $request->cookie('locale');
        }
        // Use default language
        else {
            $locale = $defaultLanguage;
        }

        // Get available languages
        $availableLanguages = Language::where('is_active', true)->pluck('code')->toArray();

        // Ensure locale is valid
        if (!in_array($locale, $availableLanguages)) {
            $locale = $defaultLanguage;
        }

        // Set the application locale
        App::setLocale($locale);

        // Store in session
        Session::put('locale', $locale);

        return $next($request);
    }
}
