<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\SettingsService;
use Illuminate\Support\Facades\Auth;

class CheckMaintenanceMode
{
    /**
     * The settings service
     *
     * @var \App\Services\SettingsService
     */
    protected $settingsService;

    /**
     * Constructor
     *
     * @param \App\Services\SettingsService $settingsService
     */
    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Add debug logging
        \Illuminate\Support\Facades\Log::info('CheckMaintenanceMode middleware running');

        // Check if the site is in maintenance mode
        $maintenanceMode = $this->settingsService->get('maintenance_mode', false);
        \Illuminate\Support\Facades\Log::info('Maintenance mode status: ' . ($maintenanceMode ? 'Enabled' : 'Disabled'));

        if ($maintenanceMode) {
            // Allow access to administrators
            if (Auth::check() && (Auth::user()->isAdmin() || Auth::user()->isManager())) {
                \Illuminate\Support\Facades\Log::info('Admin user detected, allowing access');
                return $next($request);
            }

            // Allow access to login page
            if ($request->is('login') || $request->is('admin/login')) {
                \Illuminate\Support\Facades\Log::info('Login page detected, allowing access');
                return $next($request);
            }

            // Allow access to admin routes
            if ($request->is('admin*')) {
                \Illuminate\Support\Facades\Log::info('Admin route detected, allowing access');
                return $next($request);
            }

            // Show maintenance page
            \Illuminate\Support\Facades\Log::info('Showing maintenance page');
            $message = $this->settingsService->get('maintenance_message', __('The site is currently under maintenance. Please check back later.'));
            $settings = $this->settingsService->all();

            try {
                return response()->view('frontend.maintenance', [
                    'message' => $message,
                    'settings' => $settings
                ], 503);
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::error('Error rendering maintenance page: ' . $e->getMessage());
                // Fallback to a simple maintenance message
                return response('الموقع قيد الصيانة حالياً، يرجى العودة لاحقاً.', 503);
            }
        }

        return $next($request);
    }
}
