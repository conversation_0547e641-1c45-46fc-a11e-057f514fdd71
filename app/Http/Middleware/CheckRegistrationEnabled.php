<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\SuperAdminSettingsService;

class CheckRegistrationEnabled
{
    /**
     * The super admin settings service
     *
     * @var \App\Services\SuperAdminSettingsService
     */
    protected $superAdminSettingsService;

    /**
     * Constructor
     *
     * @param \App\Services\SuperAdminSettingsService $superAdminSettingsService
     */
    public function __construct(SuperAdminSettingsService $superAdminSettingsService)
    {
        $this->superAdminSettingsService = $superAdminSettingsService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // التحقق من تفعيل التسجيل
        if (!$this->superAdminSettingsService->isRegistrationEnabled()) {
            // إذا كان التسجيل معطل، إعادة توجيه إلى صفحة تسجيل الدخول مع رسالة خطأ
            $message = $this->superAdminSettingsService->getRegistrationDisabledMessage();

            return redirect()->route('login')->with('error', $message);
        }

        return $next($request);
    }
}
