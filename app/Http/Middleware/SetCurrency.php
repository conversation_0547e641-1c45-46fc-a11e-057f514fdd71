<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Services\CurrencyService;

class SetCurrency
{
    protected $currencyService;
    
    /**
     * Create a new middleware instance.
     *
     * @param  \App\Services\CurrencyService  $currencyService
     * @return void
     */
    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }
    
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Get currency from session or default to base currency
        $baseCurrency = config('services.currency_converter.base_currency', 'EGP');
        $currency = Session::get('currency', $baseCurrency);
        
        // Check if the currency is supported
        $supportedCurrencies = $this->currencyService->getSupportedCurrencies();
        
        if (!in_array($currency, $supportedCurrencies)) {
            $currency = $baseCurrency;
            Session::put('currency', $currency);
        }
        
        // Share currency with all views
        view()->share('currentCurrency', $currency);
        
        return $next($request);
    }
}
