<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\SuperAdminSettingsService;
use Symfony\Component\HttpFoundation\Response;

class CheckDisplayOnlyMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $superAdminSettingsService = app(SuperAdminSettingsService::class);

        $blockedRoutes = [];

        // If display-only mode is enabled, block access to shopping pages
        if ($superAdminSettingsService->isDisplayOnlyModeEnabled()) {
            $blockedRoutes = array_merge($blockedRoutes, [
                'cart',
                'checkout',
                'checkout.address',
                'checkout.payment',
                'checkout.complete',
                'orders.*',
                'account.orders',
                'account.orders.*',
            ]);
        }

        // If wishlist is disabled, block access to wishlist pages
        if (!$superAdminSettingsService->showWishlist()) {
            $blockedRoutes = array_merge($blockedRoutes, [
                'account.wishlist',
                'account.wishlist.*',
                'wishlist',
            ]);
        }

        $currentRoute = $request->route()->getName();

        // Check access to blocked pages
        foreach ($blockedRoutes as $route) {
            if ($route === $currentRoute ||
                (str_ends_with($route, '.*') && str_starts_with($currentRoute, substr($route, 0, -2)))) {

                // Define error message based on the blocked page type
                $errorMessage = __('Sorry, this page is currently unavailable.');

                if (str_starts_with($currentRoute, 'account.wishlist') || $currentRoute === 'wishlist') {
                    $errorMessage = __('Sorry, the wishlist is currently unavailable.');
                } elseif (in_array($currentRoute, ['cart', 'checkout']) || str_starts_with($currentRoute, 'checkout.')) {
                    $errorMessage = __('Sorry, shopping is currently unavailable. Products are for display only.');
                }

                return redirect()->route('home')->with('error', $errorMessage);
            }
        }

        return $next($request);
    }
}
