<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\SuperAdminProtectionService;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class SuperAdminProtectionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // التحقق من أن المستخدم مسجل دخول
        if (!Auth::check()) {
            return $next($request);
        }

        // إذا كان المستخدم Super Admin، السماح بالوصول لكل شيء
        if (SuperAdminProtectionService::isSuperAdmin()) {
            return $next($request);
        }

        // التحقق من محاولة الوصول لموارد Super Admin للمستخدمين العاديين فقط
        $this->checkSuperAdminAccess($request);

        return $next($request);
    }

    /**
     * التحقق من محاولة الوصول لموارد Super Admin
     */
    protected function checkSuperAdminAccess(Request $request): void
    {
        $path = $request->path();

        // مسارات محظورة للمستخدمين العاديين
        $restrictedPaths = [
            'admin/super-admin-settings', // صفحات إعدادات Super Admin فقط
        ];

        // التحقق من المسارات المحظورة
        foreach ($restrictedPaths as $restrictedPath) {
            if ($this->pathMatches($path, $restrictedPath)) {
                $this->handleRestrictedAccess($request);
                break;
            }
        }

        // التحقق من معاملات الطلب
        $this->checkRequestParameters($request);
    }

    /**
     * التحقق من تطابق المسار
     */
    protected function pathMatches(string $path, string $pattern): bool
    {
        $pattern = str_replace('*', '[^/]+', $pattern);
        return preg_match('#^' . $pattern . '$#', $path);
    }

    /**
     * التحقق من معاملات الطلب
     */
    protected function checkRequestParameters(Request $request): void
    {
        // التحقق من محاولة تعديل مستخدم Super Admin
        if ($request->route('record')) {
            $recordId = $request->route('record');

            // إذا كان المسار متعلق بالمستخدمين
            if (str_contains($request->path(), 'admin/users')) {
                $user = \App\Models\User::find($recordId);
                if ($user && !SuperAdminProtectionService::canAccessUser($user)) {
                    $this->handleRestrictedAccess($request);
                }
            }

            // إذا كان المسار متعلق بالأدوار
            if (str_contains($request->path(), 'admin/roles')) {
                $role = \Spatie\Permission\Models\Role::find($recordId);
                if ($role && !SuperAdminProtectionService::canAccessRole($role)) {
                    $this->handleRestrictedAccess($request);
                }
            }
        }

        // التحقق من محاولة تعيين دور Super Admin
        if ($request->has('roles')) {
            $roles = $request->input('roles', []);
            if (is_array($roles)) {
                foreach ($roles as $roleId) {
                    $role = \Spatie\Permission\Models\Role::find($roleId);
                    if ($role && $role->name === 'super_admin' && !SuperAdminProtectionService::isSuperAdmin()) {
                        $this->handleRestrictedAccess($request);
                    }
                }
            }
        }
    }

    /**
     * معالجة محاولة الوصول المحظور
     */
    protected function handleRestrictedAccess(Request $request): void
    {
        if ($request->expectsJson()) {
            abort(403, 'ليس لديك صلاحية للوصول لهذا المورد.');
        }

        // إعادة توجيه للوحة التحكم مع رسالة خطأ
        session()->flash('error', 'ليس لديك صلاحية للوصول لهذا المورد.');

        if ($request->header('HX-Request')) {
            // إذا كان الطلب من Livewire/HTMX
            abort(403);
        }

        redirect()->route('filament.admin.pages.dashboard')->send();
    }

    /**
     * التحقق من محاولة الوصول لصفحات محظورة
     */
    protected function checkPageAccess(Request $request): void
    {
        $routeName = $request->route()->getName();

        // فقط صفحات Super Admin محظورة، ليس جميع صفحات التعديل
        $restrictedRoutes = [
            'filament.admin.pages.super-admin-settings',
        ];

        if (in_array($routeName, $restrictedRoutes)) {
            $this->handleRestrictedAccess($request);
        }
    }

    /**
     * تسجيل محاولة الوصول المحظور
     */
    protected function logUnauthorizedAccess(Request $request): void
    {
        Log::warning('محاولة وصول غير مصرح بها لمورد Super Admin', [
            'user_id' => Auth::id(),
            'user_email' => Auth::user()->email ?? 'غير معروف',
            'path' => $request->path(),
            'method' => $request->method(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now(),
        ]);
    }

    /**
     * التحقق من محاولة تصدير بيانات Super Admin
     */
    protected function checkExportAccess(Request $request): void
    {
        if ($request->has('export') || str_contains($request->path(), 'export')) {
            // منع تصدير بيانات تحتوي على معلومات Super Admin
            if (!SuperAdminProtectionService::isSuperAdmin()) {
                $this->handleRestrictedAccess($request);
            }
        }
    }

    /**
     * التحقق من محاولة البحث عن Super Admin
     */
    protected function checkSearchAccess(Request $request): void
    {
        $searchTerms = [
            $request->input('search'),
            $request->input('q'),
            $request->input('query'),
        ];

        foreach ($searchTerms as $term) {
            if ($term && (
                str_contains(strtolower($term), 'super') ||
                str_contains(strtolower($term), 'admin') ||
                str_contains(strtolower($term), 'superadmin')
            )) {
                // تسجيل محاولة البحث المشبوهة
                $this->logUnauthorizedAccess($request);
            }
        }
    }
}
