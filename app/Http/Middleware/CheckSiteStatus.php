<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\SuperAdminSettingsService;
use Symfony\Component\HttpFoundation\Response;

class CheckSiteStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // تجنب تطبيق الفحص على صفحات لوحة التحكم
        if ($request->is('admin*') || $request->is('filament*')) {
            return $next($request);
        }

        $superAdminSettings = app(SuperAdminSettingsService::class);

        // فحص وضع الصيانة
        if ($superAdminSettings->isMaintenanceModeEnabled()) {
            // السماح للسوبر أدمن بالوصول حتى أثناء الصيانة
            if (auth()->check() && auth()->user()->hasRole('super_admin')) {
                return $next($request);
            }

            // عرض صفحة الصيانة للمستخدمين العاديين
            return response()->view('maintenance', [
                'message' => $superAdminSettings->getMaintenanceMessage()
            ], 503);
        }

        return $next($request);
    }
}
