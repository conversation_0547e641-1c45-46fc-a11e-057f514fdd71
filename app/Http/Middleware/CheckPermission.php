<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $permission): Response
    {
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح. يجب تسجيل الدخول أولاً.',
            ], 401);
        }

        if (!$request->user()->can($permission)) {
            return response()->json([
                'success' => false,
                'message' => 'غير مصرح. لا تملك الصلاحية المطلوبة.',
            ], 403);
        }

        return $next($request);
    }
}
