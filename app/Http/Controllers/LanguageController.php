<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\App;
use App\Models\SiteSetting;
use App\Models\Language;

class LanguageController extends Controller
{
    /**
     * Change the application language.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $locale
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switch(Request $request, $locale)
    {
        // Get site settings
        $settings = SiteSetting::first();

        // Check if multilingual is enabled
        $multilingual = $settings ? $settings->enable_multilingual : true;
        $defaultLanguage = $settings ? $settings->default_language : 'ar';

        // If multilingual is disabled, redirect back
        if (!$multilingual) {
            return redirect()->back();
        }

        // Get available languages
        $availableLanguages = Language::where('is_active', true)->pluck('code')->toArray();

        // Validate if the locale is supported
        if (!in_array($locale, $availableLanguages)) {
            $locale = $defaultLanguage;
        }

        // Store the locale in session
        Session::put('locale', $locale);

        // Set the application locale immediately
        App::setLocale($locale);

        // Create a cookie that lasts for 30 days
        $cookie = Cookie::make('locale', $locale, 43200);

        // Redirect back to the previous page
        return redirect()->back()->withCookie($cookie);
    }
}
