<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class StripeController extends Controller
{
    /**
     * Create a new Stripe checkout session
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createSession(Request $request)
    {
        try {
            // Set Stripe API key
            Stripe::setApiKey(config('services.stripe.secret'));
            
            // Get order details
            $order = Order::findOrFail($request->order_id);
            
            // Create line items for Stripe
            $lineItems = [];
            
            foreach ($order->items as $item) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => strtolower($order->currency),
                        'product_data' => [
                            'name' => app()->getLocale() == 'ar' ? $item->product->name_ar : $item->product->name_en,
                            'description' => app()->getLocale() == 'ar' ? $item->product->short_description_ar : $item->product->short_description_en,
                            'images' => [$item->product->image ? url('storage/' . $item->product->image) : ''],
                        ],
                        'unit_amount' => $item->price * 100, // Stripe requires amount in cents
                    ],
                    'quantity' => $item->quantity,
                ];
            }
            
            // Add shipping cost
            if ($order->shipping_amount > 0) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => strtolower($order->currency),
                        'product_data' => [
                            'name' => app()->getLocale() == 'ar' ? 'رسوم الشحن' : 'Shipping Fee',
                        ],
                        'unit_amount' => $order->shipping_amount * 100,
                    ],
                    'quantity' => 1,
                ];
            }
            
            // Add tax
            if ($order->tax_amount > 0) {
                $lineItems[] = [
                    'price_data' => [
                        'currency' => strtolower($order->currency),
                        'product_data' => [
                            'name' => app()->getLocale() == 'ar' ? 'الضريبة' : 'Tax',
                        ],
                        'unit_amount' => $order->tax_amount * 100,
                    ],
                    'quantity' => 1,
                ];
            }
            
            // Create checkout session
            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => $lineItems,
                'mode' => 'payment',
                'success_url' => route('payment.stripe.success', ['order_id' => $order->id]) . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('payment.stripe.cancel', ['order_id' => $order->id]),
                'client_reference_id' => $order->id,
                'customer_email' => $order->shipping_email,
                'locale' => app()->getLocale(),
            ]);
            
            // Update order with session ID
            $order->payment_session_id = $session->id;
            $order->save();
            
            return response()->json(['id' => $session->id]);
            
        } catch (ApiErrorException $e) {
            Log::error('Stripe API Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        } catch (\Exception $e) {
            Log::error('Stripe Session Error: ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Handle successful payment
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function success(Request $request)
    {
        try {
            // Set Stripe API key
            Stripe::setApiKey(config('services.stripe.secret'));
            
            // Get order
            $order = Order::findOrFail($request->order_id);
            
            // Verify the session
            $session = Session::retrieve($request->session_id);
            
            if ($session->payment_status === 'paid') {
                // Update order status
                $order->payment_status = 'paid';
                $order->status = 'processing';
                $order->payment_id = $session->payment_intent;
                $order->save();
                
                return redirect()->route('checkout.success', $order->id);
            } else {
                return redirect()->route('checkout.failed', $order->id);
            }
            
        } catch (\Exception $e) {
            Log::error('Stripe Success Error: ' . $e->getMessage());
            return redirect()->route('checkout.failed', $request->order_id);
        }
    }
    
    /**
     * Handle cancelled payment
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        
        return redirect()->route('checkout.failed', $order->id)->with('error', app()->getLocale() == 'ar' ? 'تم إلغاء عملية الدفع' : 'Payment was cancelled');
    }
}
