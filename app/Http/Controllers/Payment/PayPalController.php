<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use PayPal\Api\Amount;
use PayPal\Api\Details;
use PayPal\Api\Item;
use PayPal\Api\ItemList;
use PayPal\Api\Payer;
use PayPal\Api\Payment;
use PayPal\Api\RedirectUrls;
use PayPal\Api\Transaction;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;
use PayPal\Api\PaymentExecution;
use Illuminate\Support\Facades\Log;

class PayPalController extends Controller
{
    private $apiContext;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Setup PayPal API Context
        $this->apiContext = new ApiContext(
            new OAuthTokenCredential(
                config('services.paypal.client_id'),
                config('services.paypal.secret')
            )
        );
        
        $this->apiContext->setConfig([
            'mode' => config('services.paypal.mode', 'sandbox'),
            'log.LogEnabled' => true,
            'log.FileName' => storage_path('logs/paypal.log'),
            'log.LogLevel' => 'DEBUG',
        ]);
    }
    
    /**
     * Process payment with PayPal
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function process(Request $request)
    {
        try {
            // Get order
            $order = Order::findOrFail($request->order_id);
            
            // Create new payer and set payment method
            $payer = new Payer();
            $payer->setPaymentMethod('paypal');
            
            // Set redirect URLs
            $redirectUrls = new RedirectUrls();
            $redirectUrls->setReturnUrl(route('payment.paypal.success', ['order_id' => $order->id]))
                ->setCancelUrl(route('payment.paypal.cancel', ['order_id' => $order->id]));
            
            // Set payment details
            $details = new Details();
            $details->setShipping($order->shipping_amount)
                ->setTax($order->tax_amount)
                ->setSubtotal($order->total_amount - $order->tax_amount - $order->shipping_amount);
            
            // Set payment amount
            $amount = new Amount();
            $amount->setCurrency($order->currency)
                ->setTotal($order->total_amount)
                ->setDetails($details);
            
            // Set transaction object
            $transaction = new Transaction();
            $transaction->setAmount($amount)
                ->setDescription(app()->getLocale() == 'ar' ? 'طلب رقم: ' . $order->order_number : 'Order #' . $order->order_number)
                ->setInvoiceNumber($order->order_number);
            
            // Create payment
            $payment = new Payment();
            $payment->setIntent('sale')
                ->setPayer($payer)
                ->setRedirectUrls($redirectUrls)
                ->setTransactions([$transaction]);
            
            // Create payment with valid API context
            $payment->create($this->apiContext);
            
            // Get redirect URL
            $approvalUrl = $payment->getApprovalLink();
            
            // Update order with payment ID
            $order->payment_id = $payment->getId();
            $order->save();
            
            return redirect()->away($approvalUrl);
            
        } catch (\Exception $e) {
            Log::error('PayPal Process Error: ' . $e->getMessage());
            return redirect()->route('checkout.failed', $request->order_id)->with('error', app()->getLocale() == 'ar' ? 'حدث خطأ أثناء معالجة الدفع' : 'An error occurred while processing your payment');
        }
    }
    
    /**
     * Handle successful payment
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function success(Request $request)
    {
        try {
            // Get order
            $order = Order::findOrFail($request->order_id);
            
            // Get payment ID from URL
            $paymentId = $request->paymentId;
            $payerId = $request->PayerID;
            
            if (!$paymentId || !$payerId) {
                return redirect()->route('checkout.failed', $order->id);
            }
            
            // Execute payment with payer ID
            $payment = Payment::get($paymentId, $this->apiContext);
            
            $execution = new PaymentExecution();
            $execution->setPayerId($payerId);
            
            // Execute payment
            $result = $payment->execute($execution, $this->apiContext);
            
            if ($result->getState() === 'approved') {
                // Update order status
                $order->payment_status = 'paid';
                $order->status = 'processing';
                $order->save();
                
                return redirect()->route('checkout.success', $order->id);
            } else {
                return redirect()->route('checkout.failed', $order->id);
            }
            
        } catch (\Exception $e) {
            Log::error('PayPal Success Error: ' . $e->getMessage());
            return redirect()->route('checkout.failed', $request->order_id);
        }
    }
    
    /**
     * Handle cancelled payment
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request)
    {
        $order = Order::findOrFail($request->order_id);
        
        return redirect()->route('checkout.failed', $order->id)->with('error', app()->getLocale() == 'ar' ? 'تم إلغاء عملية الدفع' : 'Payment was cancelled');
    }
}
