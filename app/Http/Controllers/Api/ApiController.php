<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class ApiController extends Controller
{
    /**
     * Success response method.
     *
     * @param mixed $data
     * @param string $message
     * @param int $statusCode
     * @return JsonResponse
     */
    public function sendResponse($data, string $message = 'Success', int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ];

        return response()->json($response, $statusCode);
    }

    /**
     * Error response method.
     *
     * @param string $message
     * @param array $errors
     * @param int $statusCode
     * @return JsonResponse
     */
    public function sendError(string $message = 'Error', array $errors = [], int $statusCode = 404): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Pagination response method.
     *
     * @param mixed $data
     * @param array $meta
     * @param string $message
     * @param int $statusCode
     * @return JsonResponse
     */
    public function sendPaginatedResponse($data, array $meta, string $message = 'Success', int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'data'    => $data,
            'meta'    => $meta,
            'message' => $message,
        ];

        return response()->json($response, $statusCode);
    }
}
