<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;
use App\Services\SettingsService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AppSettingsController extends Controller
{
    protected $settingsService;

    public function __construct(SettingsService $settingsService)
    {
        $this->settingsService = $settingsService;
    }

    /**
     * جلب جميع إعدادات التطبيق
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $settings = $this->settingsService->all();

            // تنسيق البيانات للتطبيق
            $appSettings = [
                // معلومات التطبيق الأساسية
                'app_info' => [
                    'name' => $settings->site_name ?? 'مجوهرات مكة جولد جروب',
                    'version' => '2.0.0',
                    'description' => $settings->site_description ?? 'تطبيق مجوهرات مكة جولد جروب - أفضل مجوهرات ذهبية وفضية',
                    'logo' => $settings->logo ? asset('storage/' . $settings->logo) : null,
                ],

                // معلومات الشركة
                'company_info' => [
                    'name' => 'مكة جولد جروب',
                    'description' => 'شركة رائدة في مجال المجوهرات والمعادن الثمينة، نقدم أفضل المنتجات بأعلى معايير الجودة والحرفية',
                    'logo' => $settings->logo ? asset('storage/' . $settings->logo) : null,
                    'copyright' => 'جميع الحقوق محفوظة © 2024 مكة جولد جروب',
                ],

                // معلومات الاتصال
                'contact_info' => [
                    'email' => $settings->contact_email,
                    'phone' => $settings->contact_phone,
                    'address' => $settings->address,
                    'whatsapp' => $settings->whatsapp_number,
                ],

                // روابط وسائل التواصل الاجتماعي
                'social_links' => [
                    'facebook' => $settings->facebook_url,
                    'instagram' => $settings->instagram_url,
                    'twitter' => $settings->twitter_url,
                    'youtube' => $settings->youtube_url ?? null,
                    'tiktok' => $settings->tiktok_url ?? null,
                    'linkedin' => $settings->linkedin_url ?? null,
                ],

                // إعدادات المظهر
                'theme_settings' => [
                    'default_theme' => 'light',
                    'enable_dark_mode' => true,
                    'primary_color' => '#D4AF37',
                    'secondary_color' => null,
                ],

                // إعدادات الإشعارات (افتراضية)
                'notification_settings' => [
                    'enable_push_notifications' => true,
                    'enable_email_notifications' => true,
                    'enable_sms_notifications' => false,
                    'enable_marketing_notifications' => true,
                ],

                // إعدادات المحتوى
                'content_settings' => [
                    'show_promotional_banners' => true,
                    'show_featured_products' => $settings->show_featured_products ?? true,
                    'show_new_arrivals' => $settings->show_new_arrivals ?? true,
                    'show_categories' => $settings->show_categories ?? true,
                    'show_gold_prices' => $settings->show_gold_prices ?? true,
                    'show_price_alerts' => true,
                    'show_calculator_tips' => true,
                ],

                // إعدادات الخصوصية
                'privacy_settings' => [
                    'privacy_policy' => $settings->privacy_policy,
                    'terms_conditions' => $settings->terms_conditions,
                    'return_policy' => $settings->return_policy,
                    'require_privacy_consent' => true,
                    'enable_analytics' => true,
                ],

                // إعدادات اللغة
                'language_settings' => [
                    'default_language' => 'ar',
                    'supported_languages' => ['ar', 'en'],
                ],

                // إعدادات التطبيق المتقدمة
                'advanced_settings' => [
                    'enable_biometric_auth' => false,
                    'enable_offline_mode' => true,
                    'cache_duration_minutes' => 60,
                    'enable_crash_reporting' => true,
                    'api_version' => 'v1',
                    'api_timeout_seconds' => 30,
                    'enable_api_caching' => true,
                ],

                // روابط المتاجر
                'store_links' => [
                    'app_store_url' => null,
                    'play_store_url' => null,
                    'website_url' => config('app.url'),
                ],
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم جلب إعدادات التطبيق بنجاح',
                'data' => $appSettings,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب إعدادات التطبيق',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * تحديث إعدادات التطبيق
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'site_name' => 'sometimes|string|max:255',
                'site_description' => 'sometimes|string',
                'contact_email' => 'sometimes|nullable|email',
                'contact_phone' => 'sometimes|nullable|string|max:20',
                'address' => 'sometimes|nullable|string',
                'whatsapp_number' => 'sometimes|nullable|string|max:20',
                'facebook_url' => 'sometimes|nullable|url',
                'instagram_url' => 'sometimes|nullable|url',
                'twitter_url' => 'sometimes|nullable|url',
                'show_featured_products' => 'sometimes|boolean',
                'show_new_arrivals' => 'sometimes|boolean',
                'show_categories' => 'sometimes|boolean',
                'show_gold_prices' => 'sometimes|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // تحديث الإعدادات
            $this->settingsService->update($request->only([
                'site_name',
                'site_description',
                'contact_email',
                'contact_phone',
                'address',
                'whatsapp_number',
                'facebook_url',
                'instagram_url',
                'twitter_url',
                'show_featured_products',
                'show_new_arrivals',
                'show_categories',
                'show_gold_prices',
            ]));

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث إعدادات التطبيق بنجاح',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث إعدادات التطبيق',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * جلب معلومات الاتصال فقط
     *
     * @return JsonResponse
     */
    public function getContactInfo(): JsonResponse
    {
        try {
            $settings = $this->settingsService->all();

            $contactInfo = [
                'email' => $settings->contact_email,
                'phone' => $settings->contact_phone,
                'address' => $settings->address,
                'whatsapp' => $settings->whatsapp_number,
                'social_links' => [
                    'facebook' => $settings->facebook_url,
                    'instagram' => $settings->instagram_url,
                    'twitter' => $settings->twitter_url,
                ],
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم جلب معلومات الاتصال بنجاح',
                'data' => $contactInfo,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب معلومات الاتصال',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * جلب سياسة الخصوصية والشروط
     *
     * @return JsonResponse
     */
    public function getPrivacyPolicy(): JsonResponse
    {
        try {
            $settings = $this->settingsService->all();

            $privacyData = [
                'privacy_policy' => $settings->privacy_policy,
                'terms_conditions' => $settings->terms_conditions,
                'return_policy' => $settings->return_policy,
                'last_updated' => $settings->updated_at?->format('Y-m-d'),
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم جلب سياسة الخصوصية بنجاح',
                'data' => $privacyData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب سياسة الخصوصية',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
