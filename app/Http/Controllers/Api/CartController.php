<?php

namespace App\Http\Controllers\Api;

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CartController extends ApiController
{
    /**
     * Display the user's cart.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Get or create cart
        $cart = $this->getOrCreateCart($request->user()->id);

        // Load cart items with products
        $cart->load('items.product');

        // Calculate cart totals
        $cart->subtotal = $cart->items->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });

        return $this->sendResponse($cart, 'Cart retrieved successfully');
    }

    /**
     * Add a product to the cart.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function addItem(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Check if product exists and is active
        $product = Product::find($request->product_id);
        if (!$product || !$product->is_active) {
            return $this->sendError('Product Error', ['product_id' => ['Product not found or not available']], 422);
        }

        // تم إزالة فحص allow_purchase من الفئات - الآن يتم التحكم من خلال إعدادات SuperAdmin

        // Check if product has enough stock
        if ($product->stock < $request->quantity) {
            return $this->sendError('Product Error', ['quantity' => ['Not enough stock available']], 422);
        }

        // Get or create cart
        $cart = $this->getOrCreateCart($request->user()->id);

        // Check if product is already in cart
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('product_id', $request->product_id)
            ->first();

        if ($cartItem) {
            // Update quantity
            $newQuantity = $cartItem->quantity + $request->quantity;

            // Check if new quantity exceeds stock
            if ($newQuantity > $product->stock) {
                return $this->sendError('Product Error', ['quantity' => ['Not enough stock available']], 422);
            }

            $cartItem->quantity = $newQuantity;
            $cartItem->save();
        } else {
            // Add new item to cart
            $cartItem = CartItem::create([
                'cart_id' => $cart->id,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
            ]);
        }

        // Load cart with updated items
        $cart->load('items.product');

        // Calculate cart totals
        $cart->subtotal = $cart->items->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });

        return $this->sendResponse($cart, 'Product added to cart successfully');
    }

    /**
     * Update cart item quantity.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateItem(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Get cart
        $cart = Cart::where('user_id', $request->user()->id)->first();
        if (!$cart) {
            return $this->sendError('Cart not found');
        }

        // Find cart item
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('product_id', $id)
            ->first();

        if (!$cartItem) {
            return $this->sendError('Cart item not found');
        }

        // Check if product has enough stock
        $product = Product::find($id);
        if ($product->stock < $request->quantity) {
            return $this->sendError('Product Error', ['quantity' => ['Not enough stock available']], 422);
        }

        // Update quantity
        $cartItem->quantity = $request->quantity;
        $cartItem->save();

        // Load cart with updated items
        $cart->load('items.product');

        // Calculate cart totals
        $cart->subtotal = $cart->items->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });

        return $this->sendResponse($cart, 'Cart item updated successfully');
    }

    /**
     * Remove an item from the cart.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function removeItem(Request $request, $id): JsonResponse
    {
        // Get cart
        $cart = Cart::where('user_id', $request->user()->id)->first();
        if (!$cart) {
            return $this->sendError('Cart not found');
        }

        // Find cart item
        $cartItem = CartItem::where('cart_id', $cart->id)
            ->where('product_id', $id)
            ->first();

        if (!$cartItem) {
            return $this->sendError('Cart item not found');
        }

        // Remove item
        $cartItem->delete();

        // Load cart with updated items
        $cart->load('items.product');

        // Calculate cart totals
        $cart->subtotal = $cart->items->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });

        return $this->sendResponse($cart, 'Cart item removed successfully');
    }

    /**
     * Clear the cart.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clear(Request $request): JsonResponse
    {
        // Get cart
        $cart = Cart::where('user_id', $request->user()->id)->first();
        if (!$cart) {
            return $this->sendError('Cart not found');
        }

        // Remove all items
        CartItem::where('cart_id', $cart->id)->delete();

        // Return empty cart
        $cart->load('items');
        $cart->subtotal = 0;

        return $this->sendResponse($cart, 'Cart cleared successfully');
    }

    /**
     * Get or create a cart for the user.
     *
     * @param int $userId
     * @return Cart
     */
    private function getOrCreateCart($userId): Cart
    {
        $cart = Cart::where('user_id', $userId)->first();

        if (!$cart) {
            $cart = Cart::create([
                'user_id' => $userId,
            ]);
        }

        return $cart;
    }
}
