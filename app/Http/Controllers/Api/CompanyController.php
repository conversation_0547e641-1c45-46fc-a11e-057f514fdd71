<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * API Controller لإدارة الشركات ومنتجاتها
 */
class CompanyController extends Controller
{
    /**
     * جلب جميع الشركات
     */
    public function index(): JsonResponse
    {
        try {
            $companies = Company::orderBy('name')->get()->map(function ($company) {
                return [
                    'id' => $company->id,
                    'name' => $company->name,
                    'manufacturing_cost_per_gram' => (float) $company->manufacturing_cost_per_gram,
                    'refund_value' => (float) $company->refund_value,
                    'products_count' => $company->products()->count(),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $companies,
                'message' => 'تم جلب الشركات بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الشركات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب تفاصيل شركة معينة
     */
    public function show($id): JsonResponse
    {
        try {
            $company = Company::with(['products.productType' => function ($query) {
                $query->orderBy('type')->orderBy('metal_purity', 'desc')->orderBy('weight');
            }])->find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $formattedCompany = [
                'id' => $company->id,
                'name' => $company->name,
                'manufacturing_cost_per_gram' => (float) $company->manufacturing_cost_per_gram,
                'refund_value' => (float) $company->refund_value,
                'products' => $company->products->map(function ($product) {
                    return [
                        'id' => $product->id,
                        'name' => $product->name,
                        'description' => $product->description,
                        'manufacturing_cost_per_gram' => (float) $product->manufacturing_cost_per_gram,
                        'refund_value_per_gram' => (float) $product->refund_value_per_gram,
                        'product_type' => [
                            'id' => $product->productType?->id,
                            'name' => $product->productType?->name,
                            'type' => $product->productType?->type,
                            'weight' => (float) ($product->productType?->weight ?? 0),
                            'metal_purity' => $product->productType?->metal_purity,
                            'formatted_weight' => $product->formatted_weight,
                            'formatted_purity' => $product->formatted_purity,
                            'type_with_icon' => $product->product_type_with_icon,
                        ],
                        'total_cost' => (float) $product->total_cost,
                        'total_refund_value' => (float) $product->total_refund_value,
                        'refund_value' => (float) $product->refund_value, // للتوافق مع الكود القديم
                    ];
                }),
            ];

            return response()->json([
                'success' => true,
                'data' => $formattedCompany,
                'message' => 'تم جلب تفاصيل الشركة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تفاصيل الشركة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب منتجات شركة معينة مع فلترة حسب النوع والعيار
     */
    public function products($id, Request $request): JsonResponse
    {
        try {
            $company = Company::find($id);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $query = $company->products()->with('productType');

            // فلترة حسب نوع المنتج
            if ($request->has('product_type')) {
                $query->whereHas('productType', function ($q) use ($request) {
                    $q->where('type', $request->product_type);
                });
            }

            // فلترة حسب العيار
            if ($request->has('metal_purity')) {
                $query->whereHas('productType', function ($q) use ($request) {
                    $q->where('metal_purity', $request->metal_purity);
                });
            }

            $products = $query->get()->sortBy(function ($product) {
                return [
                    $product->productType?->type,
                    -($product->productType?->metal_purity ?? 0),
                    $product->productType?->weight ?? 0
                ];
            });

            $formattedProducts = $products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'manufacturing_cost_per_gram' => (float) $product->manufacturing_cost_per_gram,
                    'refund_value_per_gram' => (float) $product->refund_value_per_gram,
                    'product_type' => [
                        'id' => $product->productType?->id,
                        'name' => $product->productType?->name,
                        'type' => $product->productType?->type,
                        'weight' => (float) ($product->productType?->weight ?? 0),
                        'metal_purity' => $product->productType?->metal_purity,
                        'formatted_weight' => $product->formatted_weight,
                        'formatted_purity' => $product->formatted_purity,
                        'type_with_icon' => $product->product_type_with_icon,
                    ],
                    'total_cost' => (float) $product->total_cost,
                    'total_refund_value' => (float) $product->total_refund_value,
                    'refund_value' => (float) $product->refund_value, // للتوافق مع الكود القديم
                ];
            });

            // تجميع المنتجات حسب النوع والعيار
            $groupedProducts = [
                'سبائك' => $products->filter(function ($product) {
                    return $product->productType?->type === 'سبيكة';
                })->groupBy(function ($product) {
                    return $product->productType?->metal_purity;
                }),
                'عملات' => $products->filter(function ($product) {
                    return $product->productType?->type === 'عملة';
                })->groupBy(function ($product) {
                    return $product->productType?->metal_purity;
                }),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'company' => [
                        'id' => $company->id,
                        'name' => $company->name,
                        'manufacturing_cost_per_gram' => (float) $company->manufacturing_cost_per_gram,
                        'refund_value' => (float) $company->refund_value,
                    ],
                    'products' => $formattedProducts,
                    'grouped_products' => $groupedProducts,
                    'filters' => [
                        'product_types' => ['سبيكة', 'عملة'],
                        'metal_purities' => ['24', '21', '18'],
                    ]
                ],
                'message' => 'تم جلب منتجات الشركة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب منتجات الشركة',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
