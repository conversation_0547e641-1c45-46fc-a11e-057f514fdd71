<?php

namespace App\Http\Controllers\Api;

use App\Models\Review;
use App\Models\Product;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class ReviewController extends ApiController
{
    /**
     * Display a listing of the reviews.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Review::with(['user', 'product']);

        // Filter by product
        if ($request->has('product_id')) {
            $query->where('product_id', $request->product_id);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by rating
        if ($request->has('rating')) {
            $query->where('rating', $request->rating);
        }

        // Sort reviews
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');
        
        // Validate sort field
        $allowedSortFields = ['created_at', 'rating'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }
        
        // Validate sort direction
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'desc';
        }
        
        $query->orderBy($sortField, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $reviews = $query->paginate($perPage);

        return $this->sendPaginatedResponse(
            $reviews->items(),
            [
                'total' => $reviews->total(),
                'per_page' => $reviews->perPage(),
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
            ],
            'Reviews retrieved successfully'
        );
    }

    /**
     * Store a newly created review in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Check if product exists and is active
        $product = Product::find($request->product_id);
        if (!$product || !$product->is_active) {
            return $this->sendError('Product Error', ['product_id' => ['Product not found or not available']], 422);
        }

        // Check if user has purchased the product
        $hasPurchased = Order::where('user_id', $request->user()->id)
            ->where('status', 'completed')
            ->whereHas('items', function ($query) use ($request) {
                $query->where('product_id', $request->product_id);
            })
            ->exists();

        if (!$hasPurchased) {
            return $this->sendError('Validation Error', ['product_id' => ['You can only review products you have purchased']], 422);
        }

        // Check if user has already reviewed this product
        $existingReview = Review::where('user_id', $request->user()->id)
            ->where('product_id', $request->product_id)
            ->first();

        if ($existingReview) {
            return $this->sendError('Validation Error', ['product_id' => ['You have already reviewed this product']], 422);
        }

        // Create review
        $review = Review::create([
            'user_id' => $request->user()->id,
            'product_id' => $request->product_id,
            'rating' => $request->rating,
            'comment' => $request->comment,
        ]);

        // Update product average rating
        $this->updateProductRating($product);

        return $this->sendResponse($review, 'Review created successfully', 201);
    }

    /**
     * Display the specified review.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $review = Review::with(['user', 'product'])->find($id);

        if (is_null($review)) {
            return $this->sendError('Review not found');
        }

        return $this->sendResponse($review, 'Review retrieved successfully');
    }

    /**
     * Update the specified review in storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $review = Review::find($id);

        if (is_null($review)) {
            return $this->sendError('Review not found');
        }

        // Check if the review belongs to the user
        if ($review->user_id !== $request->user()->id) {
            return $this->sendError('Unauthorized', ['error' => 'You can only update your own reviews'], 403);
        }

        $validator = Validator::make($request->all(), [
            'rating' => 'integer|min:1|max:5',
            'comment' => 'string',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Update review
        $review->fill($request->only(['rating', 'comment']));
        $review->save();

        // Update product average rating
        $this->updateProductRating($review->product);

        return $this->sendResponse($review, 'Review updated successfully');
    }

    /**
     * Remove the specified review from storage.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $review = Review::find($id);

        if (is_null($review)) {
            return $this->sendError('Review not found');
        }

        // Check if the review belongs to the user
        if ($review->user_id !== $request->user()->id) {
            return $this->sendError('Unauthorized', ['error' => 'You can only delete your own reviews'], 403);
        }

        $product = $review->product;
        $review->delete();

        // Update product average rating
        $this->updateProductRating($product);

        return $this->sendResponse(null, 'Review deleted successfully');
    }

    /**
     * Update product average rating.
     *
     * @param Product $product
     * @return void
     */
    private function updateProductRating(Product $product): void
    {
        $averageRating = $product->reviews()->avg('rating');
        $product->rating = $averageRating ?? 0;
        $product->save();
    }
}
