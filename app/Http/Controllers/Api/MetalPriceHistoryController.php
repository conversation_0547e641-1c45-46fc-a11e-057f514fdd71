<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MetalPrice;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MetalPriceHistoryController extends Controller
{
    /**
     * الحصول على تاريخ أسعار المعادن
     */
    public function index(Request $request): JsonResponse
    {
        try {
            Log::info('MetalPriceHistory API called', $request->all());

            // اختبار بسيط أولاً
            $metalType = $request->get('metal_type', 'gold');
            $purity = $request->get('purity', '21K');

            Log::info('Searching for', ['metal_type' => $metalType, 'purity' => $purity]);

            // جلب آخر 10 أسعار بدون فلترة تاريخ
            $prices = MetalPrice::where('metal_type', $metalType)
                ->where('purity', $purity)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            if ($prices->isEmpty()) {
                $totalCount = MetalPrice::count();
                $goldCount = MetalPrice::where('metal_type', 'gold')->count();
                $purityCount = MetalPrice::where('purity', $purity)->count();
                $exactCount = MetalPrice::where('metal_type', $metalType)->where('purity', $purity)->count();

                // جلب عينة من البيانات
                $sampleData = MetalPrice::select('metal_type', 'purity')->distinct()->limit(5)->get();

                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد أسعار متاحة',
                    'debug' => [
                        'total_records' => $totalCount,
                        'gold_records' => $goldCount,
                        'purity_records' => $purityCount,
                        'exact_match_records' => $exactCount,
                        'requested_metal' => $metalType,
                        'requested_purity' => $purity,
                        'sample_data' => $sampleData->toArray()
                    ]
                ], 404);
            }

            // تحويل البيانات
            $data = $prices->map(function ($price) {
                return [
                    'id' => $price->id,
                    'metal_type' => $price->metal_type,
                    'purity' => $price->purity,
                    'price_per_gram' => $price->price_per_gram,
                    'currency' => 'EGP',
                    'date' => $price->created_at->format('Y-m-d'),
                    'time' => $price->created_at->format('H:i:s'),
                    'datetime' => $price->created_at->toISOString(),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $data->toArray(),
                'pagination' => [
                    'current_page' => 1,
                    'last_page' => 1,
                    'per_page' => $data->count(),
                    'total' => $data->count(),
                    'from' => 1,
                    'to' => $data->count(),
                ],
                'filters' => [
                    'metal_type' => $metalType,
                    'purity' => $purity,
                    'date_from' => null,
                    'date_to' => null,
                    'period' => 'test'
                ],
                'message' => 'تم جلب تاريخ الأسعار بنجاح'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('خطأ في جلب تاريخ أسعار المعادن: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تاريخ الأسعار',
                'error' => app()->environment('local') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * تحديد النطاق الزمني بناءً على المدخلات
     */
    private function getDateRange(array $validated): array
    {
        $now = Carbon::now();

        // إذا تم تحديد تواريخ محددة
        if (!empty($validated['date_from']) && !empty($validated['date_to'])) {
            return [
                'from' => Carbon::parse($validated['date_from'])->startOfDay(),
                'to' => Carbon::parse($validated['date_to'])->endOfDay()
            ];
        }

        // إذا تم تحديد فترة زمنية
        $period = $validated['period'] ?? 'month';

        switch ($period) {
            case 'week':
                return [
                    'from' => $now->copy()->subWeek()->startOfDay(),
                    'to' => $now->copy()->endOfDay()
                ];

            case '3months':
                return [
                    'from' => $now->copy()->subMonths(3)->startOfDay(),
                    'to' => $now->copy()->endOfDay()
                ];

            case '6months':
                return [
                    'from' => $now->copy()->subMonths(6)->startOfDay(),
                    'to' => $now->copy()->endOfDay()
                ];

            case 'year':
                return [
                    'from' => $now->copy()->subYear()->startOfDay(),
                    'to' => $now->copy()->endOfDay()
                ];

            case 'month':
            default:
                return [
                    'from' => $now->copy()->subMonth()->startOfDay(),
                    'to' => $now->copy()->endOfDay()
                ];
        }
    }

    /**
     * تجميع البيانات وحساب التغيرات المئوية
     */
    private function groupAndCalculateChanges(array $prices): array
    {
        if (empty($prices)) {
            return [];
        }

        $groupedData = [];
        $previousPrices = [];

        foreach ($prices as $price) {
            $key = $price->metal_type . '_' . $price->purity;

            // حساب التغيير المئوي
            $changePercentage = 0;
            $changeDirection = 'stable';

            if (isset($previousPrices[$key])) {
                $currentPrice = $price->price_per_gram;
                $previousPrice = $previousPrices[$key];

                if ($previousPrice > 0) {
                    $changePercentage = (($currentPrice - $previousPrice) / $previousPrice) * 100;
                    $changeDirection = $changePercentage > 0 ? 'up' : ($changePercentage < 0 ? 'down' : 'stable');
                }
            }

            $groupedData[] = [
                'id' => $price->id,
                'metal_type' => $price->metal_type,
                'metal_type_ar' => $price->metal_type === 'gold' ? 'الذهب' : 'الفضة',
                'purity' => $price->purity,
                'purity_ar' => $this->getPurityNameAr($price->purity),
                'price_per_gram' => $price->price_per_gram,
                'currency' => 'EGP',
                'date' => $price->created_at->format('Y-m-d'),
                'time' => $price->created_at->format('H:i:s'),
                'datetime' => $price->created_at->toISOString(),
                'formatted_date_ar' => $this->formatDateArabic($price->created_at),
                'change_percentage' => round($changePercentage, 2),
                'change_direction' => $changeDirection,
                'is_active' => $price->is_active
            ];

            // حفظ السعر الحالي للمقارنة التالية
            $previousPrices[$key] = $price->price_per_gram;
        }

        return $groupedData;
    }

    /**
     * الحصول على اسم العيار بالعربية
     */
    private function getPurityNameAr(string $purity): string
    {
        if (str_contains($purity, 'K')) {
            return "{$purity} عيار";
        } else {
            return "فضة {$purity}";
        }
    }

    /**
     * تنسيق التاريخ بالعربية
     */
    private function formatDateArabic(Carbon $date): string
    {
        $arabicMonths = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];

        $arabicDays = [
            'Sunday' => 'الأحد', 'Monday' => 'الاثنين', 'Tuesday' => 'الثلاثاء',
            'Wednesday' => 'الأربعاء', 'Thursday' => 'الخميس', 'Friday' => 'الجمعة',
            'Saturday' => 'السبت'
        ];

        $dayName = $arabicDays[$date->format('l')] ?? $date->format('l');
        $monthName = $arabicMonths[$date->month] ?? $date->format('F');

        return "{$dayName} {$date->day} {$monthName} {$date->year}";
    }

    /**
     * الحصول على إحصائيات سريعة للفترة المحددة
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'metal_type' => 'required|string|in:gold,silver',
                'purity' => 'required|string',
                'period' => 'nullable|string|in:week,month,3months,6months,year'
            ]);

            $dateRange = $this->getDateRange($validated);

            $prices = MetalPrice::where('metal_type', $validated['metal_type'])
                ->where('purity', $validated['purity'])
                ->where('is_active', true)
                ->whereBetween('created_at', [$dateRange['from'], $dateRange['to']])
                ->orderBy('created_at', 'asc')
                ->get();

            if ($prices->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا توجد بيانات للفترة المحددة'
                ], 404);
            }

            $priceValues = $prices->pluck('price_per_gram');
            $firstPrice = $prices->first()->price_per_gram;
            $lastPrice = $prices->last()->price_per_gram;

            $changeAmount = $lastPrice - $firstPrice;
            $changePercentage = $firstPrice > 0 ? (($changeAmount / $firstPrice) * 100) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'metal_type' => $validated['metal_type'],
                    'purity' => $validated['purity'],
                    'period' => $validated['period'] ?? 'month',
                    'highest_price' => $priceValues->max(),
                    'lowest_price' => $priceValues->min(),
                    'average_price' => round($priceValues->avg(), 2),
                    'first_price' => $firstPrice,
                    'last_price' => $lastPrice,
                    'change_amount' => round($changeAmount, 2),
                    'change_percentage' => round($changePercentage, 2),
                    'change_direction' => $changeAmount > 0 ? 'up' : ($changeAmount < 0 ? 'down' : 'stable'),
                    'total_records' => $prices->count(),
                    'date_from' => $dateRange['from']->format('Y-m-d'),
                    'date_to' => $dateRange['to']->format('Y-m-d')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('خطأ في جلب إحصائيات أسعار المعادن: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الإحصائيات'
            ], 500);
        }
    }
}
