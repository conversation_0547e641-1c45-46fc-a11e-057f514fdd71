<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SuperAdminSettingsService;
use Illuminate\Http\JsonResponse;

class SiteStatusController extends Controller
{
    protected $superAdminSettings;

    public function __construct(SuperAdminSettingsService $superAdminSettings)
    {
        $this->superAdminSettings = $superAdminSettings;
    }

    /**
     * التحقق من وضع العرض فقط
     *
     * @return JsonResponse
     */
    public function checkDisplayMode(): JsonResponse
    {
        return response()->json([
            'display_only_mode' => $this->superAdminSettings->isDisplayOnlyModeEnabled(),
            'maintenance_mode' => $this->superAdminSettings->isMaintenanceModeEnabled(),
            'message' => $this->superAdminSettings->isDisplayOnlyModeEnabled() 
                ? __('الموقع في وضع العرض فقط حالياً. لا يمكن إتمام عمليات الشراء.')
                : __('الموقع يعمل بشكل طبيعي'),
        ]);
    }

    /**
     * التحقق من حالة الميزات
     *
     * @return JsonResponse
     */
    public function checkFeatures(): JsonResponse
    {
        return response()->json([
            'features' => [
                'ratings' => $this->superAdminSettings->showRatings(),
                'wishlist' => $this->superAdminSettings->showWishlist(),
                'guest_checkout' => $this->superAdminSettings->isGuestCheckoutEnabled(),
                'local_pickup' => $this->superAdminSettings->isLocalPickupEnabled(),
                'social_login' => $this->superAdminSettings->isSocialLoginEnabled(),
                'social_sharing' => $this->superAdminSettings->isSocialSharingEnabled(),
                'shopping_enabled' => !$this->superAdminSettings->isDisplayOnlyModeEnabled(),
            ],
            'site_status' => [
                'maintenance_mode' => $this->superAdminSettings->isMaintenanceModeEnabled(),
                'display_only_mode' => $this->superAdminSettings->isDisplayOnlyModeEnabled(),
            ]
        ]);
    }

    /**
     * التحقق من طرق الدفع المتاحة
     *
     * @return JsonResponse
     */
    public function checkPaymentMethods(): JsonResponse
    {
        $paymentHelper = app(\App\Helpers\PaymentHelper::class);
        
        return response()->json([
            'payment_methods' => $paymentHelper->getAvailablePaymentMethods(),
            'has_available_methods' => $paymentHelper->hasAvailablePaymentMethods(),
            'shopping_enabled' => !$this->superAdminSettings->isDisplayOnlyModeEnabled(),
        ]);
    }
}
