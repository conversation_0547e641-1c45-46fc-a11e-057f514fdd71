<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\CompanyProduct;
use App\Models\ProductType;
use App\Models\MetalPrice;
use App\Models\SiteSetting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * API Controller لصفحة السبائك والعملات
 */
class BarsCoinsController extends Controller
{
    /**
     * جلب قائمة الشركات النشطة لنوع منتج معين (سبيكة أو عملة)
     */
    public function getCompanies(Request $request): JsonResponse
    {
        try {
            $type = $request->get('type', 'سبيكة'); // افتراضي: سبيكة

            $companies = Company::where('is_active', true)
                ->with(['products.productType' => function ($query) use ($type) {
                    $query->where('type', $type);
                }])
                ->whereHas('products.productType', function ($query) use ($type) {
                    $query->where('type', $type);
                })
                ->orderBy('name')
                ->get()
                ->map(function ($company) {
                    return [
                        'id' => $company->id,
                        'name' => $company->name,
                        'logo' => $company->logo ? asset('storage/' . $company->logo) : null,
                        'products_count' => $company->products()->count(),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $companies,
                'message' => 'تم جلب قائمة الشركات بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب قائمة الشركات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب أنواع المنتجات لشركة معينة ونوع منتج معين
     */
    public function getProductTypes(Request $request): JsonResponse
    {
        try {
            $companyId = $request->get('company_id');
            $type = $request->get('type', 'سبيكة');

            if (!$companyId) {
                // إذا لم يتم تحديد شركة، جلب جميع أنواع المنتجات للنوع المحدد
                $productTypes = ProductType::where('type', $type)
                    ->where('is_active', true)
                    ->orderBy('weight')
                    ->get();
            } else {
                // جلب أنواع المنتجات للشركة المحددة
                $productTypes = ProductType::where('type', $type)
                    ->where('is_active', true)
                    ->whereHas('companyProducts', function ($query) use ($companyId) {
                        $query->where('company_id', $companyId);
                    })
                    ->orderBy('weight')
                    ->get();
            }

            $formattedProductTypes = $productTypes->map(function ($productType) {
                return [
                    'id' => $productType->id,
                    'name' => $productType->name,
                    'type' => $productType->type,
                    'weight' => (float) $productType->weight,
                    'metal_purity' => $productType->metal_purity,
                    'formatted_weight' => $productType->formatted_weight,
                    'formatted_purity' => $productType->formatted_purity,
                    'type_with_icon' => $productType->type_with_icon,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $formattedProductTypes,
                'message' => 'تم جلب أنواع المنتجات بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب أنواع المنتجات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب تفاصيل منتج شركة معينة
     */
    public function getCompanyProduct(Request $request): JsonResponse
    {
        try {
            $companyId = $request->get('company_id');
            $productTypeId = $request->get('product_type_id');

            if (!$companyId || !$productTypeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تحديد الشركة ونوع المنتج'
                ], 400);
            }

            $companyProduct = CompanyProduct::with(['company', 'productType'])
                ->where('company_id', $companyId)
                ->where('product_type_id', $productTypeId)
                ->first();

            if (!$companyProduct) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            // جلب أسعار المعادن الحالية
            $currentPrices = $this->getCurrentMetalPrices();

            // حساب الأسعار مثل الويب
            $calculatedPrices = $this->calculateProductPrices($companyProduct, $currentPrices);

            $formattedProduct = [
                'id' => $companyProduct->id,
                'company' => [
                    'id' => $companyProduct->company->id,
                    'name' => $companyProduct->company->name,
                    'logo' => $companyProduct->company->logo ? asset('storage/' . $companyProduct->company->logo) : null,
                ],
                'product_type' => [
                    'id' => $companyProduct->productType->id,
                    'name' => $companyProduct->productType->name,
                    'type' => $companyProduct->productType->type,
                    'weight' => (float) $companyProduct->productType->weight,
                    'metal_purity' => $companyProduct->productType->metal_purity,
                    'formatted_weight' => $companyProduct->productType->formatted_weight,
                    'formatted_purity' => $companyProduct->productType->formatted_purity,
                ],
                'manufacturing_cost_per_gram' => (float) $companyProduct->manufacturing_cost_per_gram,
                'refund_value_per_gram' => (float) $companyProduct->refund_value_per_gram,
                // إضافة الحسابات المنسقة
                'calculated_prices' => $calculatedPrices,
            ];

            return response()->json([
                'success' => true,
                'data' => $formattedProduct,
                'message' => 'تم جلب تفاصيل المنتج بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب تفاصيل المنتج',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب أسعار المعادن الحالية
     */
    public function getCurrentPrices(): JsonResponse
    {
        try {
            // الحصول على أسعار الذهب عيار 24 و 21 من قاعدة البيانات
            $gold24k = MetalPrice::where('metal_type', 'gold')
                ->where('purity', '24K')
                ->where('is_active', true)
                ->latest('created_at')
                ->first();

            $gold21k = MetalPrice::where('metal_type', 'gold')
                ->where('purity', '21K')
                ->where('is_active', true)
                ->latest('created_at')
                ->first();

            $currentPrices = [
                'gold_24k' => [
                    'buy_price' => $gold24k?->purchase_price_per_gram ?? 5490,
                    'sell_price' => $gold24k?->price_per_gram ?? 5500,
                    'last_updated' => $gold24k?->created_at?->format('Y-m-d H:i:s'),
                ],
                'gold_21k' => [
                    'buy_price' => $gold21k?->purchase_price_per_gram ?? 4800,
                    'sell_price' => $gold21k?->price_per_gram ?? 4810,
                    'last_updated' => $gold21k?->created_at?->format('Y-m-d H:i:s'),
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $currentPrices,
                'message' => 'تم جلب أسعار المعادن الحالية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب أسعار المعادن',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * حساب أسعار منتج معين
     */
    public function calculatePrices(Request $request): JsonResponse
    {
        try {
            $companyId = $request->get('company_id');
            $productTypeId = $request->get('product_type_id');

            if (!$companyId || !$productTypeId) {
                return response()->json([
                    'success' => false,
                    'message' => 'يجب تحديد الشركة ونوع المنتج'
                ], 400);
            }

            // جلب المنتج
            $companyProduct = CompanyProduct::with(['company', 'productType'])
                ->where('company_id', $companyId)
                ->where('product_type_id', $productTypeId)
                ->first();

            if (!$companyProduct) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            // جلب أسعار المعادن الحالية
            $currentPricesResponse = $this->getCurrentPrices();
            $currentPricesData = json_decode($currentPricesResponse->getContent(), true);

            if (!$currentPricesData['success']) {
                return $currentPricesResponse;
            }

            $currentPrices = $currentPricesData['data'];
            $weight = $companyProduct->productType->weight;
            $purity = $companyProduct->productType->metal_purity;

            // تحديد سعر الذهب حسب العيار
            $goldPriceSell = $purity == '24' ? $currentPrices['gold_24k']['sell_price'] : $currentPrices['gold_21k']['sell_price'];
            $goldPriceBuy = $purity == '24' ? $currentPrices['gold_24k']['buy_price'] : $currentPrices['gold_21k']['buy_price'];

            // حساب الأسعار
            $rawGoldPricePerGram = $goldPriceSell;
            $totalPricePerGram = $rawGoldPricePerGram + $companyProduct->manufacturing_cost_per_gram;
            $resalePricePerGram = $goldPriceBuy + $companyProduct->refund_value_per_gram;

            $totalRawGoldPrice = $rawGoldPricePerGram * $weight;
            $totalPrice = $totalPricePerGram * $weight;
            $totalResalePrice = $resalePricePerGram * $weight;

            $calculatedPrices = [
                'raw_gold_price_per_gram' => (float) $rawGoldPricePerGram,
                'raw_gold_price' => (float) $totalRawGoldPrice,
                'manufacturing_per_gram' => (float) $companyProduct->manufacturing_cost_per_gram,
                'total_price' => (float) $totalPrice,
                'refund_per_gram' => (float) $companyProduct->refund_value_per_gram,
                'resale_price' => (float) $totalResalePrice,
                'weight' => (float) $weight,
                'purity' => $purity,
            ];

            return response()->json([
                'success' => true,
                'data' => $calculatedPrices,
                'message' => 'تم حساب أسعار المنتج بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في حساب أسعار المنتج',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب إعدادات الموقع (اللوجو الافتراضي)
     */
    public function getSiteSettings(): JsonResponse
    {
        try {
            $siteSettings = SiteSetting::first();

            $settings = [
                'default_logo' => $siteSettings?->logo ? asset('storage/' . $siteSettings->logo) : null,
                'site_name' => $siteSettings?->site_name ?? 'مجوهرات مكة جولد جروب',
            ];

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'تم جلب إعدادات الموقع بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب إعدادات الموقع',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب الشركات حسب النوع (للموبايل)
     */
    public function getCompaniesByType(string $type): JsonResponse
    {
        try {
            $companies = Company::where('is_active', true)
                ->with(['products.productType' => function ($query) use ($type) {
                    $query->where('type', $type);
                }])
                ->whereHas('products.productType', function ($query) use ($type) {
                    $query->where('type', $type);
                })
                ->orderBy('name')
                ->get()
                ->map(function ($company) {
                    return [
                        'id' => $company->id,
                        'name' => $company->name,
                        'logo' => $company->logo ? asset('storage/' . $company->logo) : null,
                        'manufacturing_cost_per_gram' => $company->manufacturing_cost_per_gram,
                        'refund_value' => $company->refund_value,
                    ];
                });

            return response()->json($companies);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب الشركات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب أنواع المنتجات للشركة والنوع المحدد (للموبايل)
     */
    public function getProductTypesForCompany(int $companyId, string $type): JsonResponse
    {
        try {
            $productTypes = ProductType::where('type', $type)
                ->where('is_active', true)
                ->whereHas('companyProducts', function ($query) use ($companyId) {
                    $query->where('company_id', $companyId);
                })
                ->orderBy('weight')
                ->get()
                ->map(function ($productType) {
                    return [
                        'id' => $productType->id,
                        'name' => $productType->name,
                        'type' => $productType->type,
                        'weight' => $productType->weight,
                        'metal_purity' => $productType->metal_purity,
                    ];
                });

            return response()->json($productTypes);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب أنواع المنتجات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * جلب أسعار المعادن الحالية للحسابات
     */
    private function getCurrentMetalPrices(): array
    {
        try {
            // استخدام نفس الطريقة المستخدمة في getCurrentPrices
            $response = $this->getCurrentPrices();
            $responseData = json_decode($response->getContent(), true);

            if ($responseData['success'] && isset($responseData['data'])) {
                $prices = $responseData['data'];
                return [
                    'gold_24k_sell' => (float) $prices['gold_24k']['sell_price'],
                    'gold_24k_buy' => (float) $prices['gold_24k']['buy_price'],
                    'gold_21k_sell' => (float) $prices['gold_21k']['sell_price'],
                    'gold_21k_buy' => (float) $prices['gold_21k']['buy_price'],
                ];
            }

            // أسعار افتراضية إذا فشل جلب البيانات
            return [
                'gold_24k_sell' => 5500,
                'gold_24k_buy' => 5490,
                'gold_21k_sell' => 4810,
                'gold_21k_buy' => 4800,
            ];
        } catch (\Exception $e) {
            // أسعار افتراضية في حالة الخطأ
            return [
                'gold_24k_sell' => 5500,
                'gold_24k_buy' => 5490,
                'gold_21k_sell' => 4810,
                'gold_21k_buy' => 4800,
            ];
        }
    }

    /**
     * حساب أسعار المنتج مثل الويب تماماً
     */
    private function calculateProductPrices($companyProduct, $currentPrices): array
    {
        $weight = (float) $companyProduct->productType->weight;
        $purity = $companyProduct->productType->metal_purity;

        // تحديد سعر الذهب حسب العيار
        $goldPriceSell = $purity == '24' ? $currentPrices['gold_24k_sell'] : $currentPrices['gold_21k_sell'];
        $goldPriceBuy = $purity == '24' ? $currentPrices['gold_24k_buy'] : $currentPrices['gold_21k_buy'];

        // حساب سعر الذهب الخام للجرام الواحد
        $rawGoldPricePerGram = $goldPriceSell;

        // حساب السعر الشامل للجرام الواحد (المصنعية والضريبة)
        $manufacturingPerGram = (float) $companyProduct->manufacturing_cost_per_gram;
        $totalPricePerGram = $rawGoldPricePerGram + $manufacturingPerGram;

        // حساب سعر إعادة البيع للجرام الواحد (سعر الشراء + قيمة الاسترداد)
        $refundPerGram = (float) $companyProduct->refund_value_per_gram;
        $resalePricePerGram = $goldPriceBuy + $refundPerGram;

        // حساب الأسعار الإجمالية للوزن المحدد
        $totalRawGoldPrice = $rawGoldPricePerGram * $weight;
        $totalPrice = $totalPricePerGram * $weight;
        $totalResalePrice = $resalePricePerGram * $weight;

        return [
            // بيانات المنتج
            'weight' => $weight,
            'purity' => $purity,
            // أسعار الجرام (منسقة مثل الويب)
            'raw_gold_price_per_gram' => number_format($rawGoldPricePerGram, 0, '.', ','),
            'manufacturing_per_gram' => number_format($manufacturingPerGram, 1, '.', ','),
            'refund_per_gram' => number_format($refundPerGram, 1, '.', ','),
            // الأسعار الإجمالية (منسقة مثل الويب)
            'raw_gold_price' => number_format($totalRawGoldPrice, 0, '.', ','),
            'total_price' => number_format($totalPrice, 0, '.', ','),
            'resale_price' => number_format($totalResalePrice, 0, '.', ','),
        ];
    }
}
