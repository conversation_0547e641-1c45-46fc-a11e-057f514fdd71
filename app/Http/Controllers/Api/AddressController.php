<?php

namespace App\Http\Controllers\Api;

use App\Models\Address;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class AddressController extends ApiController
{
    /**
     * Display a listing of the user's addresses.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $addresses = $request->user()->addresses;
        return $this->sendResponse($addresses, 'Addresses retrieved successfully');
    }

    /**
     * Store a newly created address in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'address_line1' => 'required|string|max:255',
            'address_line2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'phone' => 'required|string|max:20',
            'is_default' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Create address
        $address = new Address($request->all());
        $address->user_id = $request->user()->id;
        $address->save();

        // If this is the first address or is_default is true, make it the default
        if ($request->user()->addresses()->count() == 1 || $request->is_default) {
            $this->setDefaultAddress($request->user()->id, $address->id);
        }

        return $this->sendResponse($address, 'Address created successfully', 201);
    }

    /**
     * Display the specified address.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function show($id, Request $request): JsonResponse
    {
        $address = $request->user()->addresses()->find($id);

        if (is_null($address)) {
            return $this->sendError('Address not found');
        }

        return $this->sendResponse($address, 'Address retrieved successfully');
    }

    /**
     * Update the specified address in storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $address = $request->user()->addresses()->find($id);

        if (is_null($address)) {
            return $this->sendError('Address not found');
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'address_line1' => 'string|max:255',
            'address_line2' => 'nullable|string|max:255',
            'city' => 'string|max:100',
            'state' => 'string|max:100',
            'postal_code' => 'string|max:20',
            'country' => 'string|max:100',
            'phone' => 'string|max:20',
            'is_default' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Update address
        $address->fill($request->all());
        $address->save();

        // If is_default is true, make it the default
        if ($request->has('is_default') && $request->is_default) {
            $this->setDefaultAddress($request->user()->id, $address->id);
        }

        return $this->sendResponse($address, 'Address updated successfully');
    }

    /**
     * Remove the specified address from storage.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $address = $request->user()->addresses()->find($id);

        if (is_null($address)) {
            return $this->sendError('Address not found');
        }

        // Check if this is the only address
        if ($request->user()->addresses()->count() == 1) {
            return $this->sendError('Cannot delete the only address', ['error' => 'You must have at least one address'], 422);
        }

        // If this is the default address, set another address as default
        if ($address->is_default) {
            $newDefault = $request->user()->addresses()->where('id', '!=', $id)->first();
            if ($newDefault) {
                $this->setDefaultAddress($request->user()->id, $newDefault->id);
            }
        }

        $address->delete();

        return $this->sendResponse(null, 'Address deleted successfully');
    }

    /**
     * Set an address as the default address.
     *
     * @param int $userId
     * @param int $addressId
     * @return void
     */
    private function setDefaultAddress($userId, $addressId): void
    {
        // Set all addresses as not default
        Address::where('user_id', $userId)->update(['is_default' => false]);

        // Set the specified address as default
        Address::where('id', $addressId)->update(['is_default' => true]);
    }
}
