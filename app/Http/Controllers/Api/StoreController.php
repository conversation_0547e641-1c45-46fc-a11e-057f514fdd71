<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Store;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class StoreController extends Controller
{
    /**
     * جلب جميع الفروع النشطة
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            // جلب جميع الفروع النشطة
            $stores = Store::where('is_active', true)
                ->orderBy('name_ar')
                ->get()
                ->map(function ($store) {
                    return [
                        'id' => $store->id,
                        'name' => $store->name_ar,
                        'name_en' => $store->name_en,
                        'address' => $store->address_ar,
                        'address_en' => $store->address_en,
                        'city' => $store->city_ar,
                        'city_en' => $store->city_en,
                        'country' => $store->country_ar,
                        'country_en' => $store->country_en,
                        'phone' => $store->phone,
                        'email' => $store->email,
                        'working_hours' => $store->working_hours_ar,
                        'working_hours_en' => $store->working_hours_en,
                        'latitude' => (float) $store->latitude,
                        'longitude' => (float) $store->longitude,
                        'image' => $store->image ? asset('storage/' . $store->image) : null,
                        'is_active' => $store->is_active,
                        'created_at' => $store->created_at,
                        'updated_at' => $store->updated_at,
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب الفروع بنجاح',
                'data' => $stores,
                'meta' => [
                    'total' => $stores->count(),
                    'active_stores' => $stores->where('is_active', true)->count(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الفروع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * جلب فرع محدد
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $store = Store::where('is_active', true)->find($id);

            if (!$store) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع غير موجود أو غير نشط',
                ], 404);
            }

            $storeData = [
                'id' => $store->id,
                'name' => $store->name_ar,
                'name_en' => $store->name_en,
                'address' => $store->address_ar,
                'address_en' => $store->address_en,
                'city' => $store->city_ar,
                'city_en' => $store->city_en,
                'country' => $store->country_ar,
                'country_en' => $store->country_en,
                'phone' => $store->phone,
                'email' => $store->email,
                'working_hours' => $store->working_hours_ar,
                'working_hours_en' => $store->working_hours_en,
                'latitude' => (float) $store->latitude,
                'longitude' => (float) $store->longitude,
                'image' => $store->image ? asset('storage/' . $store->image) : null,
                'is_active' => $store->is_active,
                'created_at' => $store->created_at,
                'updated_at' => $store->updated_at,
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم جلب بيانات الفرع بنجاح',
                'data' => $storeData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب بيانات الفرع',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * البحث عن الفروع القريبة
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function nearby(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'latitude' => 'required|numeric|between:-90,90',
                'longitude' => 'required|numeric|between:-180,180',
                'radius' => 'nullable|numeric|min:1|max:100', // بالكيلومتر
            ]);

            $userLat = $request->latitude;
            $userLng = $request->longitude;
            $radius = $request->radius ?? 50; // افتراضي 50 كم

            // حساب المسافة باستخدام Haversine formula
            $stores = Store::where('is_active', true)
                ->selectRaw("
                    *,
                    (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance
                ", [$userLat, $userLng, $userLat])
                ->having('distance', '<=', $radius)
                ->orderBy('distance')
                ->get()
                ->map(function ($store) {
                    return [
                        'id' => $store->id,
                        'name' => $store->name_ar,
                        'name_en' => $store->name_en,
                        'address' => $store->address_ar,
                        'address_en' => $store->address_en,
                        'city' => $store->city_ar,
                        'city_en' => $store->city_en,
                        'phone' => $store->phone,
                        'email' => $store->email,
                        'working_hours' => $store->working_hours_ar,
                        'working_hours_en' => $store->working_hours_en,
                        'latitude' => (float) $store->latitude,
                        'longitude' => (float) $store->longitude,
                        'distance' => round($store->distance, 2), // بالكيلومتر
                        'image' => $store->image ? asset('storage/' . $store->image) : null,
                    ];
                });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب الفروع القريبة بنجاح',
                'data' => $stores,
                'meta' => [
                    'total' => $stores->count(),
                    'radius_km' => $radius,
                    'user_location' => [
                        'latitude' => $userLat,
                        'longitude' => $userLng,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء البحث عن الفروع القريبة',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
