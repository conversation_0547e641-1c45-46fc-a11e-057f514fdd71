<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MetalPrice;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

/**
 * API Controller لحاسبة الزكاة للتطبيق
 */
class ZakatCalculatorController extends Controller
{
    // نصاب الذهب بالجرام (85 جرام)
    const GOLD_NISAB_GRAMS = 85;

    // نصاب الفضة بالجرام (595 جرام)
    const SILVER_NISAB_GRAMS = 595;

    // معدل الزكاة (2.5%)
    const ZAKAT_RATE = 0.025;

    /**
     * حساب زكاة الذهب
     */
    public function calculateGoldZakat(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'items' => 'required|array|min:1',
                'items.*.weight' => 'required|numeric|min:0.001',
                'items.*.purity' => 'required|string',
                'items.*.name' => 'string|nullable',
            ]);

            $items = $request->items;
            $totalGoldWeight = 0;
            $totalValue = 0;
            $itemsDetails = [];

            // حساب الوزن الإجمالي للذهب الخالص
            foreach ($items as $item) {
                $weight = (float) $item['weight'];
                $purity = $item['purity'];
                $name = $item['name'] ?? 'قطعة ذهبية';

                // تحويل العيار إلى نسبة مئوية
                $purityPercentage = $this->getPurityPercentage($purity);

                // حساب وزن الذهب الخالص
                $pureGoldWeight = $weight * ($purityPercentage / 100);
                $totalGoldWeight += $pureGoldWeight;

                // حساب القيمة
                $currentPrice = MetalPrice::where('metal_type', 'gold')
                    ->where('purity', $purity)
                    ->where('is_active', true)
                    ->where('currency', 'EGP')
                    ->first();

                $itemValue = $currentPrice ? $weight * $currentPrice->price_per_gram : 0;
                $totalValue += $itemValue;

                $itemsDetails[] = [
                    'name' => $name,
                    'weight' => $weight,
                    'purity' => $purity,
                    'purity_name' => $this->getPurityName($purity),
                    'purity_percentage' => $purityPercentage,
                    'pure_gold_weight' => round($pureGoldWeight, 3),
                    'value' => round($itemValue, 2),
                    'price_per_gram' => $currentPrice ? (float) $currentPrice->price_per_gram : 0,
                ];
            }

            // تحديد ما إذا كان يجب دفع الزكاة
            $isZakatDue = $totalGoldWeight >= self::GOLD_NISAB_GRAMS;

            // حساب مقدار الزكاة
            $zakatAmount = $isZakatDue ? $totalGoldWeight * self::ZAKAT_RATE : 0;
            $zakatValue = $isZakatDue ? $totalValue * self::ZAKAT_RATE : 0;

            // الحصول على سعر الذهب عيار 24 للمرجع
            $gold24Price = MetalPrice::where('metal_type', 'gold')
                ->where('purity', '24K')
                ->where('is_active', true)
                ->where('currency', 'EGP')
                ->first();

            $data = [
                'items' => $itemsDetails,
                'summary' => [
                    'total_weight' => round($totalGoldWeight, 3),
                    'total_value' => round($totalValue, 2),
                    'nisab_weight' => self::GOLD_NISAB_GRAMS,
                    'nisab_value' => $gold24Price ? round(self::GOLD_NISAB_GRAMS * $gold24Price->price_per_gram, 2) : 0,
                    'is_zakat_due' => $isZakatDue,
                    'zakat_rate' => self::ZAKAT_RATE * 100 . '%',
                    'zakat_weight' => round($zakatAmount, 3),
                    'zakat_value' => round($zakatValue, 2),
                ],
                'currency' => 'EGP',
                'calculation_date' => now()->format('Y-m-d H:i:s'),
                'notes' => [
                    'نصاب الذهب هو 85 جرام من الذهب الخالص',
                    'معدل الزكاة هو 2.5% من القيمة الإجمالية',
                    'يجب أن يمر عام هجري كامل على ملكية الذهب',
                    'الحساب يعتمد على الأسعار الحالية للذهب',
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم حساب زكاة الذهب بنجاح',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في حساب زكاة الذهب',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * حساب زكاة الفضة
     */
    public function calculateSilverZakat(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'items' => 'required|array|min:1',
                'items.*.weight' => 'required|numeric|min:0.001',
                'items.*.purity' => 'required|string',
                'items.*.name' => 'string|nullable',
            ]);

            $items = $request->items;
            $totalSilverWeight = 0;
            $totalValue = 0;
            $itemsDetails = [];

            // حساب الوزن الإجمالي للفضة الخالصة
            foreach ($items as $item) {
                $weight = (float) $item['weight'];
                $purity = $item['purity'];
                $name = $item['name'] ?? 'قطعة فضية';

                // تحويل العيار إلى نسبة مئوية
                $purityPercentage = $this->getSilverPurityPercentage($purity);

                // حساب وزن الفضة الخالصة
                $pureSilverWeight = $weight * ($purityPercentage / 100);
                $totalSilverWeight += $pureSilverWeight;

                // حساب القيمة
                $currentPrice = MetalPrice::where('metal_type', 'silver')
                    ->where('purity', $purity)
                    ->where('is_active', true)
                    ->where('currency', 'EGP')
                    ->first();

                $itemValue = $currentPrice ? $weight * $currentPrice->price_per_gram : 0;
                $totalValue += $itemValue;

                $itemsDetails[] = [
                    'name' => $name,
                    'weight' => $weight,
                    'purity' => $purity,
                    'purity_name' => $this->getPurityName($purity),
                    'purity_percentage' => $purityPercentage,
                    'pure_silver_weight' => round($pureSilverWeight, 3),
                    'value' => round($itemValue, 2),
                    'price_per_gram' => $currentPrice ? (float) $currentPrice->price_per_gram : 0,
                ];
            }

            // تحديد ما إذا كان يجب دفع الزكاة
            $isZakatDue = $totalSilverWeight >= self::SILVER_NISAB_GRAMS;

            // حساب مقدار الزكاة
            $zakatAmount = $isZakatDue ? $totalSilverWeight * self::ZAKAT_RATE : 0;
            $zakatValue = $isZakatDue ? $totalValue * self::ZAKAT_RATE : 0;

            // الحصول على سعر الفضة 999 للمرجع
            $silver999Price = MetalPrice::where('metal_type', 'silver')
                ->where('purity', '999')
                ->where('is_active', true)
                ->where('currency', 'EGP')
                ->first();

            $data = [
                'items' => $itemsDetails,
                'summary' => [
                    'total_weight' => round($totalSilverWeight, 3),
                    'total_value' => round($totalValue, 2),
                    'nisab_weight' => self::SILVER_NISAB_GRAMS,
                    'nisab_value' => $silver999Price ? round(self::SILVER_NISAB_GRAMS * $silver999Price->price_per_gram, 2) : 0,
                    'is_zakat_due' => $isZakatDue,
                    'zakat_rate' => self::ZAKAT_RATE * 100 . '%',
                    'zakat_weight' => round($zakatAmount, 3),
                    'zakat_value' => round($zakatValue, 2),
                ],
                'currency' => 'EGP',
                'calculation_date' => now()->format('Y-m-d H:i:s'),
                'notes' => [
                    'نصاب الفضة هو 595 جرام من الفضة الخالصة',
                    'معدل الزكاة هو 2.5% من القيمة الإجمالية',
                    'يجب أن يمر عام هجري كامل على ملكية الفضة',
                    'الحساب يعتمد على الأسعار الحالية للفضة',
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم حساب زكاة الفضة بنجاح',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في حساب زكاة الفضة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على معلومات النصاب
     */
    public function getNisabInfo(): JsonResponse
    {
        try {
            // الحصول على أسعار الذهب والفضة الحالية
            $gold24Price = MetalPrice::where('metal_type', 'gold')
                ->where('purity', '24K')
                ->where('is_active', true)
                ->where('currency', 'EGP')
                ->first();

            $silver999Price = MetalPrice::where('metal_type', 'silver')
                ->where('purity', '999')
                ->where('is_active', true)
                ->where('currency', 'EGP')
                ->first();

            $data = [
                'gold_nisab' => [
                    'weight_grams' => self::GOLD_NISAB_GRAMS,
                    'current_value' => $gold24Price ? round(self::GOLD_NISAB_GRAMS * $gold24Price->price_per_gram, 2) : 0,
                    'price_per_gram' => $gold24Price ? (float) $gold24Price->price_per_gram : 0,
                    'last_updated' => $gold24Price ? $gold24Price->updated_at->format('Y-m-d H:i:s') : null,
                ],
                'silver_nisab' => [
                    'weight_grams' => self::SILVER_NISAB_GRAMS,
                    'current_value' => $silver999Price ? round(self::SILVER_NISAB_GRAMS * $silver999Price->price_per_gram, 2) : 0,
                    'price_per_gram' => $silver999Price ? (float) $silver999Price->price_per_gram : 0,
                    'last_updated' => $silver999Price ? $silver999Price->updated_at->format('Y-m-d H:i:s') : null,
                ],
                'zakat_rate' => self::ZAKAT_RATE * 100 . '%',
                'currency' => 'EGP',
                'general_info' => [
                    'النصاب هو الحد الأدنى من المال الذي تجب فيه الزكاة',
                    'يجب أن يمر عام هجري كامل على ملكية المال',
                    'معدل زكاة الذهب والفضة هو 2.5%',
                    'يُحسب النصاب بناءً على أقل القيمتين (ذهب أو فضة)',
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'تم جلب معلومات النصاب بنجاح',
                'data' => $data
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب معلومات النصاب',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تحويل عيار الذهب إلى نسبة مئوية
     */
    private function getPurityPercentage(string $purity): float
    {
        $purities = [
            '24K' => 100,
            '22K' => 91.7,
            '21K' => 87.5,
            '18K' => 75,
            '14K' => 58.3,
            '12K' => 50,
            '9K' => 37.5,
        ];

        return $purities[$purity] ?? 100;
    }

    /**
     * تحويل عيار الفضة إلى نسبة مئوية
     */
    private function getSilverPurityPercentage(string $purity): float
    {
        $purities = [
            '999' => 99.9,
            '925' => 92.5,
            '900' => 90,
            '800' => 80,
            '600' => 60,
        ];

        return $purities[$purity] ?? 99.9;
    }

    /**
     * الحصول على اسم العيار بالعربية
     */
    private function getPurityName(string $purity): string
    {
        $names = [
            // عيارات الذهب
            '24K' => '24 عيار',
            '22K' => '22 عيار',
            '21K' => '21 عيار',
            '18K' => '18 عيار',
            '14K' => '14 عيار',
            '12K' => '12 عيار',
            '9K' => '9 عيار',

            // عيارات الفضة
            '999' => 'فضة 999',
            '925' => 'فضة 925',
            '900' => 'فضة 900',
            '800' => 'فضة 800',
            '600' => 'فضة 600',
        ];

        return $names[$purity] ?? $purity;
    }
}
