<?php

namespace App\Http\Controllers\Api;

use App\Models\Wishlist;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class WishlistController extends ApiController
{
    /**
     * Display a listing of the user's wishlist items.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $wishlistItems = $request->user()->wishlist()->with('product')->get();
        return $this->sendResponse($wishlistItems, 'Wishlist items retrieved successfully');
    }

    /**
     * Add a product to the user's wishlist.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|exists:products,id',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Check if product exists and is active
        $product = Product::find($request->product_id);
        if (!$product || !$product->is_active) {
            return $this->sendError('Product Error', ['product_id' => ['Product not found or not available']], 422);
        }

        // Check if product is already in wishlist
        $existingItem = Wishlist::where('user_id', $request->user()->id)
            ->where('product_id', $request->product_id)
            ->first();

        if ($existingItem) {
            return $this->sendError('Validation Error', ['product_id' => ['Product is already in your wishlist']], 422);
        }

        // Add to wishlist
        $wishlistItem = Wishlist::create([
            'user_id' => $request->user()->id,
            'product_id' => $request->product_id,
        ]);

        // Load product
        $wishlistItem->load('product');

        return $this->sendResponse($wishlistItem, 'Product added to wishlist successfully', 201);
    }

    /**
     * Remove a product from the user's wishlist.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $wishlistItem = Wishlist::where('user_id', $request->user()->id)
            ->where('product_id', $id)
            ->first();

        if (is_null($wishlistItem)) {
            return $this->sendError('Wishlist item not found');
        }

        $wishlistItem->delete();

        return $this->sendResponse(null, 'Product removed from wishlist successfully');
    }

    /**
     * Check if a product is in the user's wishlist.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function check($id, Request $request): JsonResponse
    {
        $exists = Wishlist::where('user_id', $request->user()->id)
            ->where('product_id', $id)
            ->exists();

        return $this->sendResponse(['in_wishlist' => $exists], 'Wishlist check completed successfully');
    }

    /**
     * Clear the user's wishlist.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clear(Request $request): JsonResponse
    {
        Wishlist::where('user_id', $request->user()->id)->delete();
        return $this->sendResponse(null, 'Wishlist cleared successfully');
    }
}
