<?php

namespace App\Http\Controllers\Api;

use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductController extends ApiController
{
    /**
     * Display a listing of the products.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = Product::with(['category', 'images', 'primaryImage']);

        // Filter by category
        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // Search by name
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name_ar', 'LIKE', "%{$search}%")
                  ->orWhere('name_en', 'LIKE', "%{$search}%")
                  ->orWhere('description_ar', 'LIKE', "%{$search}%")
                  ->orWhere('description_en', 'LIKE', "%{$search}%");
            });
        }

        // Sort products
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate sort field
        $allowedSortFields = ['created_at', 'price', 'name_ar', 'name_en'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        // Validate sort direction
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'desc';
        }

        $query->orderBy($sortField, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $products = $query->paginate($perPage);

        return $this->sendPaginatedResponse(
            $products->items(),
            [
                'total' => $products->total(),
                'per_page' => $products->perPage(),
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
            ],
            'Products retrieved successfully'
        );
    }

    /**
     * Store a newly created product in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'description_ar' => 'required|string',
            'description_en' => 'required|string',
            'price' => 'required|numeric|min:0',
            'old_price' => 'nullable|numeric|min:0',
            'category_id' => 'required|exists:categories,id',
            'sku' => 'required|string|unique:products,sku',
            'stock' => 'required|integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string',

            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // التحقق من عدم تكرار الأسماء
        $this->validateUniqueNames($request);

        // Handle file upload
        $imagePath = null;
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('products', 'public');
        }

        // Create product
        $product = Product::create([
            'name_ar' => $request->name_ar,
            'name_en' => $request->name_en,
            'slug' => Str::slug($request->name_en),
            'description_ar' => $request->description_ar,
            'description_en' => $request->description_en,
            'price' => $request->price,
            'old_price' => $request->old_price,
            'category_id' => $request->category_id,
            'sku' => $request->sku,
            'stock' => $request->stock,
            'weight' => $request->weight,
            'dimensions' => $request->dimensions,

            'is_featured' => $request->is_featured ?? false,
            'is_active' => $request->is_active ?? true,
            'image' => $imagePath,
        ]);

        // Handle additional images
        if ($request->hasFile('additional_images')) {
            foreach ($request->file('additional_images') as $image) {
                $path = $image->store('products', 'public');
                $product->images()->create(['image' => $path]);
            }
        }

        return $this->sendResponse($product, 'Product created successfully', 201);
    }

    /**
     * Display the specified product.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        $product = Product::with(['category', 'images', 'primaryImage', 'reviews'])->find($id);

        if (is_null($product)) {
            return $this->sendError('Product not found');
        }

        return $this->sendResponse($product, 'Product retrieved successfully');
    }

    /**
     * Update the specified product in storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {
        $product = Product::find($id);

        if (is_null($product)) {
            return $this->sendError('Product not found');
        }

        $validator = Validator::make($request->all(), [
            'name_ar' => 'string|max:255',
            'name_en' => 'string|max:255',
            'description_ar' => 'string',
            'description_en' => 'string',
            'price' => 'numeric|min:0',
            'old_price' => 'nullable|numeric|min:0',
            'category_id' => 'exists:categories,id',
            'sku' => 'string|unique:products,sku,' . $id,
            'stock' => 'integer|min:0',
            'weight' => 'nullable|numeric|min:0',
            'dimensions' => 'nullable|string',

            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'additional_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Handle file upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($product->image) {
                Storage::disk('public')->delete($product->image);
            }
            $imagePath = $request->file('image')->store('products', 'public');
            $product->image = $imagePath;
        }

        // التحقق من عدم تكرار الأسماء
        $this->validateUniqueNames($request, $product->id);

        // Update product
        $product->fill($request->only([
            'name_ar', 'name_en', 'description_ar', 'description_en',
            'price', 'old_price', 'category_id', 'sku', 'stock',
            'weight', 'dimensions', 'is_featured', 'is_active'
        ]));

        // Update slug if name_en is changed
        if ($request->has('name_en')) {
            $baseSlug = Str::slug($request->name_en);
            $slug = $baseSlug;
            $counter = 1;

            // التحقق من تفرد الـ slug (استثناء المنتج الحالي)
            while (Product::where('slug', $slug)->where('id', '!=', $product->id)->exists()) {
                $slug = $baseSlug . '-' . $counter;
                $counter++;
            }

            $product->slug = $slug;
        }

        $product->save();

        // Handle additional images
        if ($request->hasFile('additional_images')) {
            foreach ($request->file('additional_images') as $image) {
                $path = $image->store('products', 'public');
                $product->images()->create(['image' => $path]);
            }
        }

        return $this->sendResponse($product, 'Product updated successfully');
    }

    /**
     * التحقق من عدم تكرار أسماء المنتجات
     */
    private function validateUniqueNames($request, ?int $excludeId = null): void
    {
        // التحقق من الاسم العربي
        if ($request->has('name_ar') && !empty($request->name_ar)) {
            $query = Product::where('name_ar', $request->name_ar);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            if ($query->exists()) {
                $validator = \Illuminate\Support\Facades\Validator::make([], []);
                $validator->errors()->add('name_ar', 'الاسم العربي "' . $request->name_ar . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
                throw new \Illuminate\Validation\ValidationException($validator);
            }
        }

        // التحقق من الاسم الإنجليزي
        if ($request->has('name_en') && !empty($request->name_en)) {
            $query = Product::where('name_en', $request->name_en);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
            if ($query->exists()) {
                $validator = \Illuminate\Support\Facades\Validator::make([], []);
                $validator->errors()->add('name_en', 'الاسم الإنجليزي "' . $request->name_en . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
                throw new \Illuminate\Validation\ValidationException($validator);
            }
        }
    }

    /**
     * Remove the specified product from storage.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        $product = Product::find($id);

        if (is_null($product)) {
            return $this->sendError('Product not found');
        }

        // Delete product image
        if ($product->image) {
            Storage::disk('public')->delete($product->image);
        }

        // Delete additional images
        foreach ($product->images as $image) {
            Storage::disk('public')->delete($image->image);
            $image->delete();
        }

        $product->delete();

        return $this->sendResponse(null, 'Product deleted successfully');
    }

    /**
     * الحصول على المنتجات للتطبيق
     */
    public function getForApp(Request $request): JsonResponse
    {
        try {
            $query = Product::where('is_active', true)
                ->with(['category:id,name_ar,name_en,slug', 'metal:id,name,name_ar', 'metalPurity:id,name,name_ar', 'primaryImage', 'images']);

            // فلترة حسب الفئة
            if ($request->has('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // فلترة حسب المنتجات المميزة
            if ($request->has('featured') && $request->featured) {
                $query->where('is_featured', true);
            }

            // فلترة حسب نوع المعدن
            if ($request->has('metal_type_id')) {
                $query->where('metal_type_id', $request->metal_type_id);
            }

            // فلترة حسب العيار
            if ($request->has('metal_purity_id')) {
                $query->where('metal_purity_id', $request->metal_purity_id);
            }

            // البحث في الاسم
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('description_ar', 'like', "%{$search}%");
                });
            }

            // ترتيب النتائج
            $sortBy = $request->get('sort_by', 'created_at');
            $sortDirection = $request->get('sort_direction', 'desc');

            switch ($sortBy) {
                case 'price':
                    $query->orderBy('price', $sortDirection);
                    break;
                case 'name':
                    $query->orderBy('name_ar', $sortDirection);
                    break;
                case 'weight':
                    $query->orderBy('weight', $sortDirection);
                    break;
                default:
                    $query->orderBy('created_at', $sortDirection);
            }

            // تحديد عدد النتائج
            $perPage = $request->get('per_page', 20);
            $products = $query->paginate($perPage);

            $formattedProducts = $products->getCollection()->map(function ($product) {
                return $this->formatProduct($product);
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتجات بنجاح',
                'data' => $formattedProducts,
                'pagination' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                    'from' => $products->firstItem(),
                    'to' => $products->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على منتج محدد للتطبيق
     */
    public function getProduct($id): JsonResponse
    {
        try {
            $product = Product::where('is_active', true)
                ->with(['category:id,name_ar,name_en,slug', 'metal:id,name,name_ar', 'metalPurity:id,name,name_ar', 'primaryImage', 'images'])
                ->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            $formattedProduct = $this->formatProduct($product, true);

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتج بنجاح',
                'data' => $formattedProduct
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتج',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على المنتجات المميزة
     */
    public function getFeaturedProducts(): JsonResponse
    {
        try {
            $products = Product::where('is_active', true)
                ->where('is_featured', true)
                ->with(['category:id,name_ar,name_en,slug', 'primaryImage'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $formattedProducts = $products->map(function ($product) {
                return $this->formatProduct($product);
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتجات المميزة بنجاح',
                'data' => $formattedProducts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات المميزة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * الحصول على المنتجات المشابهة للتطبيق
     */
    public function getRelatedProducts($id): JsonResponse
    {
        try {
            $product = Product::where('is_active', true)->find($id);

            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'المنتج غير موجود'
                ], 404);
            }

            // جلب المنتجات المشابهة من نفس الفئة
            $relatedProducts = Product::where('is_active', true)
                ->where('category_id', $product->category_id)
                ->where('id', '!=', $product->id)
                ->with(['category:id,name_ar,name_en,slug', 'primaryImage', 'images'])
                ->orderBy('created_at', 'desc')
                ->limit(6)
                ->get();

            $formattedProducts = $relatedProducts->map(function ($product) {
                return $this->formatProduct($product);
            });

            return response()->json([
                'success' => true,
                'message' => 'تم جلب المنتجات المشابهة بنجاح',
                'data' => $formattedProducts
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ في جلب المنتجات المشابهة',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * تنسيق بيانات المنتج للتطبيق
     */
    private function formatProduct($product, $detailed = false): array
    {
        // التأكد من وجود البيانات العربية
        $arabicName = $product->name_ar ?: $product->name_en ?: 'منتج غير محدد';
        $arabicDescription = $product->description_ar ?: $product->description_en ?: '';

        $formatted = [
            'id' => $product->id,
            'name' => $arabicName, // الاسم العربي كحقل أساسي
            'name_ar' => $product->name_ar,
            'name_en' => $product->name_en,
            'description' => $arabicDescription, // الوصف العربي كحقل أساسي
            'description_ar' => $product->description_ar,
            'slug' => $product->slug,
            'price' => (float) $product->price,
            'old_price' => $product->old_price ? (float) $product->old_price : null,
            'discount_percentage' => $product->discount_percentage ? (float) $product->discount_percentage : null,
            'weight' => $product->weight ? (float) $product->weight : null,
            'show_price' => (bool) ($product->show_price ?? true), // استخدام show_price من المنتج
            'metal' => $product->metal ? [
                'id' => $product->metal->id,
                'name' => $product->metal->name_ar ?: $product->metal->name,
                'name_ar' => $product->metal->name_ar,
                'name_en' => $product->metal->name,
            ] : null,
            'metal_purity_info' => $product->metalPurity ? [
                'id' => $product->metalPurity->id,
                'name' => $product->metalPurity->name_ar ?: $product->metalPurity->name,
                'name_ar' => $product->metalPurity->name_ar,
                'name_en' => $product->metalPurity->name,
            ] : null,
            'is_featured' => (bool) $product->is_featured,
            'is_active' => (bool) $product->is_active,
            'image' => $product->primaryImage && $product->primaryImage->image_path ?
                asset('storage/' . $product->primaryImage->image_path) : null,
            'images' => $product->images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'url' => asset('storage/' . $image->image_path),
                    'alt_text_ar' => $image->alt_text_ar,
                    'alt_text_en' => $image->alt_text_en,
                    'is_primary' => $image->is_primary,
                    'sort_order' => $image->sort_order,
                ];
            }),
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->name_ar ?: $product->category->name_en ?: 'فئة غير محددة',
                'name_ar' => $product->category->name_ar,
                'name_en' => $product->category->name_en,
                'slug' => $product->category->slug,
            ] : null,
        ];

        if ($detailed) {
            $formatted['description_en'] = $product->description_en;
            $formatted['sku'] = $product->sku;
            $formatted['dimensions'] = $product->dimensions;
            $formatted['gallery'] = $product->gallery ? array_map(function ($img) {
                return asset('storage/' . $img);
            }, $product->gallery) : [];
        }

        return $formatted;
    }
}
