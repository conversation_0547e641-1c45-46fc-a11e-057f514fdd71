<?php

namespace App\Http\Controllers\Api;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class OrderController extends ApiController
{
    /**
     * Display a listing of the orders.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        // Only allow users to see their own orders
        $query = Order::where('user_id', $request->user()->id)
                      ->with(['items', 'items.product']);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Sort orders
        $sortField = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Validate sort field
        $allowedSortFields = ['created_at', 'total', 'status'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        // Validate sort direction
        $allowedSortDirections = ['asc', 'desc'];
        if (!in_array($sortDirection, $allowedSortDirections)) {
            $sortDirection = 'desc';
        }

        $query->orderBy($sortField, $sortDirection);

        // Pagination
        $perPage = $request->get('per_page', 10);
        $orders = $query->paginate($perPage);

        return $this->sendPaginatedResponse(
            $orders->items(),
            [
                'total' => $orders->total(),
                'per_page' => $orders->perPage(),
                'current_page' => $orders->currentPage(),
                'last_page' => $orders->lastPage(),
            ],
            'Orders retrieved successfully'
        );
    }

    /**
     * Store a newly created order in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'shipping_address_id' => 'required|exists:addresses,id',
            'payment_method' => 'required|string|in:cash_on_delivery,credit_card,bank_transfer',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return $this->sendError('Validation Error.', $validator->errors()->toArray(), 422);
        }

        // Validate that the shipping address belongs to the user
        $address = $request->user()->addresses()->find($request->shipping_address_id);
        if (!$address) {
            return $this->sendError('Validation Error.', ['shipping_address_id' => ['The shipping address does not belong to the user']], 422);
        }

        // Calculate order total and validate products
        $total = 0;
        $items = [];

        foreach ($request->items as $item) {
            $product = Product::find($item['product_id']);

            // Check if product exists and is active
            if (!$product || !$product->is_active) {
                return $this->sendError('Product Error', ['product_id' => ['Product not found or not available']], 422);
            }

            // تم إزالة فحص allow_purchase من الفئات - الآن يتم التحكم من خلال إعدادات SuperAdmin

            // Check if product has enough stock
            if ($product->stock < $item['quantity']) {
                return $this->sendError('Product Error', ['quantity' => ['Not enough stock available for ' . $product->name]], 422);
            }

            // Calculate item total
            $itemTotal = $product->price * $item['quantity'];
            $total += $itemTotal;

            // Add to items array
            $items[] = [
                'product_id' => $product->id,
                'quantity' => $item['quantity'],
                'price' => $product->price,
                'total' => $itemTotal,
            ];

            // Reduce product stock
            $product->stock -= $item['quantity'];
            $product->save();
        }

        // Create order
        $order = Order::create([
            'user_id' => $request->user()->id,
            'shipping_address_id' => $request->shipping_address_id,
            'payment_method' => $request->payment_method,
            'status' => 'pending',
            'total' => $total,
            'notes' => $request->notes,
        ]);

        // Create order items
        foreach ($items as $item) {
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $item['product_id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'total' => $item['total'],
            ]);
        }

        // Load order items
        $order->load('items.product');

        return $this->sendResponse($order, 'Order created successfully', 201);
    }

    /**
     * Display the specified order.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function show($id, Request $request): JsonResponse
    {
        $order = Order::with(['items.product', 'shippingAddress'])
                      ->where('user_id', $request->user()->id)
                      ->find($id);

        if (is_null($order)) {
            return $this->sendError('Order not found');
        }

        return $this->sendResponse($order, 'Order retrieved successfully');
    }

    /**
     * Cancel the specified order.
     *
     * @param int $id
     * @param Request $request
     * @return JsonResponse
     */
    public function cancel($id, Request $request): JsonResponse
    {
        $order = Order::where('user_id', $request->user()->id)->find($id);

        if (is_null($order)) {
            return $this->sendError('Order not found');
        }

        // Check if order can be cancelled
        if (!in_array($order->status, ['pending', 'processing'])) {
            return $this->sendError('Order cannot be cancelled', ['status' => ['Only pending or processing orders can be cancelled']], 422);
        }

        // Update order status
        $order->status = 'cancelled';
        $order->save();

        // Restore product stock
        foreach ($order->items as $item) {
            $product = $item->product;
            $product->stock += $item->quantity;
            $product->save();
        }

        return $this->sendResponse($order, 'Order cancelled successfully');
    }
}
