<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\HomeSlider;
use Illuminate\Http\JsonResponse;

class HomeSliderController extends ApiController
{
    /**
     * الحصول على جميع الشرائح النشطة للتطبيق
     */
    public function index(): JsonResponse
    {
        try {
            $sliders = HomeSlider::where('is_active', true)
                ->orderBy('order')
                ->get()
                ->map(function ($slider) {
                    return [
                        'id' => $slider->id,
                        'title' => $slider->title_ar ?: $slider->title_en ?: '', // العنوان العربي كحقل أساسي
                        'title_ar' => $slider->title_ar,
                        'title_en' => $slider->title_en,
                        'description' => $slider->description_ar ?: $slider->description_en ?: '', // الوصف العربي كحقل أساسي
                        'description_ar' => $slider->description_ar,
                        'description_en' => $slider->description_en,
                        'button_text' => $slider->button_text_ar ?: $slider->button_text_en ?: '', // نص الزر العربي كحقل أساسي
                        'button_text_ar' => $slider->button_text_ar,
                        'button_text_en' => $slider->button_text_en,
                        'button_link' => $slider->button_link,
                        'image' => $slider->image ? asset('storage/' . $slider->image) : null,
                        'image_exists' => $slider->image ? file_exists(storage_path('app/public/' . $slider->image)) : false,
                        'order' => $slider->order,
                    ];
                });

            return $this->sendResponse($sliders, 'تم جلب الشرائح بنجاح');

        } catch (\Exception $e) {
            return $this->sendError('حدث خطأ في جلب الشرائح', ['error' => $e->getMessage()]);
        }
    }
}
