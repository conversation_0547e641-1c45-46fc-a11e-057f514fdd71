<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MetalPrice;
use App\Models\MetalType;
use App\Models\MetalPurity;
use Carbon\Carbon;

class HomeController extends Controller
{
    public function index()
    {
        return view('frontend.home');
    }

    public function about()
    {
        // جلب إعدادات الموقع (مصدر البيانات الرئيسي لصفحة من نحن)
        $siteSettings = \App\Models\SiteSetting::first();

        // إذا لم توجد إعدادات، إنشاء إعدادات افتراضية
        if (!$siteSettings) {
            $siteSettings = \App\Models\SiteSetting::create([
                'site_name' => 'مجوهرات مكة جولد',
                'site_description' => 'شركة رائدة في مجال المجوهرات والذهب',
                'about_hero_title_ar' => 'مجوهرات مكة جولد',
                'about_hero_title_en' => 'Makkah Gold Jewelry',
                'about_hero_subtitle_ar' => 'رحلة من التميز والجودة في عالم المجوهرات',
                'about_hero_subtitle_en' => 'A journey of excellence and quality in the world of jewelry',
                'company_founded_year' => '1980',
                'company_mission_ar' => 'نسعى لتقديم أجود أنواع المجوهرات والذهب بأعلى معايير الجودة والحرفية',
                'company_mission_en' => 'We strive to provide the finest jewelry and gold with the highest standards of quality and craftsmanship',
                'company_vision_ar' => 'أن نكون الخيار الأول للعملاء في عالم المجوهرات والذهب',
                'company_vision_en' => 'To be the first choice for customers in the world of jewelry and gold',
                'show_about_values' => true,
                'show_about_team' => true,
                'show_about_testimonials' => true,
                'show_about_cta' => true,
            ]);
        }

        return view('frontend.pages.about', compact('siteSettings'));
    }

    public function contact()
    {
        // إنشاء كائن صفحة وهمي للاتصال
        $page = (object) [
            'title' => __('اتصل بنا'),
            'translated_title' => __('اتصل بنا'),
            'content' => __('لا توجد معلومات اتصال حاليًا.'),
            'translated_content' => __('لا توجد معلومات اتصال حاليًا.'),
        ];

        return view('frontend.pages.contact', compact('page'));
    }

    public function metalPrices()
    {
        // تحديد التاريخ الحالي
        $today = now()->format('Y-m-d');

        // الحصول على آخر تاريخ متاح للأسعار (EGP فقط)
        $latestDate = MetalPrice::where('currency', 'EGP')->max('price_date');
        $priceDate = $latestDate ?? $today;

        // حساب التغييرات مقارنة بالأمس
        $yesterdayDate = now()->subDay()->format('Y-m-d');
        $priceChanges = $this->calculatePriceChanges($priceDate, $yesterdayDate);

        // الحصول على بيانات الرسم البياني لأسعار المعادن (EGP فقط)
        $chartData = $this->getMetalChartData();

        // إحصائيات إضافية
        $statistics = $this->getMetalStatistics($priceDate);

        // إعداد البيانات للعرض
        $viewData = [
            'goldPrices' => $this->getProcessedGoldPrices($priceDate, $priceChanges),
            'silverPrices' => $this->getProcessedSilverPrices($priceDate, $priceChanges),
            'goldCoinPrices' => $this->getProcessedGoldCoinPrices($priceDate, $priceChanges),
            'platinumPrices' => $this->getProcessedPlatinumPrices($priceDate, $priceChanges),
            'priceChanges' => $priceChanges,
            'chartData' => $chartData,
            'statistics' => $statistics,
            'priceDate' => $priceDate,
            'lastUpdate' => $this->formatLastUpdate($priceDate),
            'translations' => $this->getTranslations(),
        ];

        return view('frontend.metal-prices', $viewData);
    }

    /**
     * حساب التغييرات في الأسعار مقارنة بالأمس
     */
    private function calculatePriceChanges($currentDate, $yesterdayDate)
    {
        $changes = [];

        // حساب تغييرات أسعار الذهب (من جميع البيانات) - جميع العيارات الجديدة
        $goldPurities = ['24K', '22K', '21K', '18K', '14K', '12K', '9K', 'جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب'];
        foreach ($goldPurities as $purity) {
            // أحدث سعر لهذا العيار (من جميع البيانات)
            $currentPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->orderBy('created_at', 'desc')
                ->first();

            // أحدث سعر قبل اليوم الحالي (من جميع البيانات)
            $yesterdayPrice = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('purity', $purity)
                ->whereDate('created_at', '<', now()->format('Y-m-d'))
                ->orderBy('created_at', 'desc')
                ->first();

            if ($currentPrice && $yesterdayPrice) {
                $change = $currentPrice->price_per_gram - $yesterdayPrice->price_per_gram;
                $changePercent = ($change / $yesterdayPrice->price_per_gram) * 100;

                $changes['gold'][$purity] = [
                    'change' => $change,
                    'change_percent' => $changePercent,
                    'direction' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable')
                ];
            }
        }

        // حساب تغييرات أسعار الفضة (من جميع البيانات) - جميع العيارات الجديدة
        $metals = ['silver' => ['999', '925', '900', '800', '600']];
        foreach ($metals as $metal => $purities) {
            foreach ($purities as $purity) {
                // أحدث سعر لهذا العيار (من جميع البيانات)
                $currentPrice = MetalPrice::where('metal_type', $metal)
                    ->where('currency', 'EGP')
                    ->where('purity', $purity)
                    ->orderBy('created_at', 'desc')
                    ->first();

                // أحدث سعر قبل اليوم الحالي (من جميع البيانات)
                $yesterdayPrice = MetalPrice::where('metal_type', $metal)
                    ->where('currency', 'EGP')
                    ->where('purity', $purity)
                    ->whereDate('created_at', '<', now()->format('Y-m-d'))
                    ->orderBy('created_at', 'desc')
                    ->first();

                if ($currentPrice && $yesterdayPrice) {
                    $change = $currentPrice->price_per_gram - $yesterdayPrice->price_per_gram;
                    $changePercent = ($change / $yesterdayPrice->price_per_gram) * 100;

                    $changes[$metal][$purity] = [
                        'change' => $change,
                        'change_percent' => $changePercent,
                        'direction' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable')
                    ];
                }
            }
        }

        return $changes;
    }

    /**
     * الحصول على أسعار الذهب مع المعالجة
     */
    private function getProcessedGoldPrices($priceDate, $priceChanges)
    {
        $goldPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->whereNotIn('purity', ['جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب']) // استبعاد الجنيهات
            ->orderByRaw("FIELD(purity, '24K', '22K', '21K', '18K', '14K', '12K', '9K')")
            ->get();

        return $goldPrices->map(function ($price) use ($priceChanges) {
            $changeData = $priceChanges['gold'][$price->purity] ?? null;

            $result = [
                'id' => $price->id,
                'purity' => $price->purity,
                'purity_label' => __('metal_prices.purities.' . $price->purity),
                'price_per_gram' => $price->price_per_gram,
                'formatted_price_per_gram' => number_format($price->price_per_gram, 2),
                'formatted_date' => $price->created_at->format('Y/m/d'),
                'formatted_time_ago' => $price->created_at->diffForHumans(),
                'change' => $changeData ? $changeData['change'] : 0,
                'change_percent' => $changeData ? $changeData['change_percent'] : 0,
                'direction' => $changeData ? $changeData['direction'] : 'stable',
                'direction_label' => $changeData ? __('metal_prices.price_changes.' . $changeData['direction']) : __('metal_prices.price_changes.stable'),
                'formatted_change_percent' => $changeData ? number_format($changeData['change_percent'], 1) : '0.0',
                'currency' => $price->currency,
                'currency_symbol' => __('metal_prices.units.egp'),
            ];

            // إضافة أسعار الشراء إذا كانت متوفرة
            if ($price->purchase_price_per_gram) {
                $result['purchase_price_per_gram'] = $price->purchase_price_per_gram;
                $result['formatted_purchase_price_per_gram'] = number_format($price->purchase_price_per_gram, 2);
            }

            return $result;
        });
    }

    /**
     * الحصول على أسعار الفضة مع المعالجة
     */
    private function getProcessedSilverPrices($priceDate, $priceChanges)
    {
        $silverPrices = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->orderByRaw("FIELD(purity, '999', '925', '900', '800', '600')")
            ->get();

        return $silverPrices->map(function ($price) use ($priceChanges) {
            $changeData = $priceChanges['silver'][$price->purity] ?? null;

            $result = [
                'id' => $price->id,
                'purity' => $price->purity,
                'purity_label' => __('metal_prices.purities.' . $price->purity, [], null, $price->purity),
                'price_per_gram' => $price->price_per_gram,
                'formatted_price_per_gram' => number_format($price->price_per_gram, 2),
                'formatted_date' => $price->created_at->format('Y/m/d'),
                'formatted_time_ago' => $price->created_at->diffForHumans(),
                'change' => $changeData ? $changeData['change'] : 0,
                'change_percent' => $changeData ? $changeData['change_percent'] : 0,
                'direction' => $changeData ? $changeData['direction'] : 'stable',
                'direction_label' => $changeData ? __('metal_prices.price_changes.' . $changeData['direction']) : __('metal_prices.price_changes.stable'),
                'formatted_change_percent' => $changeData ? number_format($changeData['change_percent'], 1) : '0.0',
                'currency' => $price->currency,
                'currency_symbol' => __('metal_prices.units.egp'),
            ];

            // إضافة أسعار الشراء إذا كانت متوفرة
            if ($price->purchase_price_per_gram) {
                $result['purchase_price_per_gram'] = $price->purchase_price_per_gram;
                $result['formatted_purchase_price_per_gram'] = number_format($price->purchase_price_per_gram, 2);
            }

            return $result;
        });
    }

    /**
     * الحصول على أسعار الجنيهات الذهبية مع المعالجة
     */
    private function getProcessedGoldCoinPrices($priceDate, $priceChanges)
    {
        // الحصول على نوع الجنيهات الذهبية من الجدول الجديد
        $goldCoinType = MetalType::where('name', 'gold_coin')->first();
        if (!$goldCoinType) {
            return collect([]);
        }

        // الحصول على عيارات الجنيهات الذهبية النشطة
        $goldCoinPurities = MetalPurity::where('metal_type_id', $goldCoinType->id)
            ->active()
            ->ordered()
            ->get();

        $goldCoinPrices = collect();

        foreach ($goldCoinPurities as $purity) {
            $price = MetalPrice::where('metal_type', 'gold_coin')
                ->where('purity', $purity->name)
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->first();

            if ($price) {
                $changeData = $priceChanges['gold_coin'][$price->purity] ?? null;

                // استخدام الوزن من قاعدة البيانات أو القيمة الافتراضية
                $weight = $purity->weight_grams ?? 8.0;
                $pricePerPiece = $price->price_per_piece ?? ($price->price_per_gram * $weight);

                $goldCoinPrices->push([
                    'id' => $price->id,
                    'purity' => $price->purity,
                    'purity_label' => $purity->name_ar,
                    'price_per_gram' => $price->price_per_gram,
                    'price_per_piece' => $pricePerPiece,
                    'formatted_price_per_gram' => number_format($price->price_per_gram, 2),
                    'formatted_price_per_piece' => number_format($pricePerPiece, 2),
                    'formatted_date' => $price->created_at->format('Y/m/d'),
                    'formatted_time_ago' => $price->created_at->diffForHumans(),
                    'change' => $changeData ? $changeData['change'] : 0,
                    'change_percent' => $changeData ? $changeData['change_percent'] : 0,
                    'direction' => $changeData ? $changeData['direction'] : 'stable',
                    'direction_label' => $changeData ? __('metal_prices.price_changes.' . $changeData['direction']) : __('metal_prices.price_changes.stable'),
                    'formatted_change_percent' => $changeData ? number_format($changeData['change_percent'], 1) : '0.0',
                    'currency' => $price->currency,
                    'currency_symbol' => __('metal_prices.units.egp'),
                    'weight_grams' => $weight,
                ]);
            }
        }

        return $goldCoinPrices;
    }

    /**
     * الحصول على أسعار البلاتين مع المعالجة (غير مستخدم حسب تفضيلات المستخدم)
     */
    private function getProcessedPlatinumPrices($priceDate, $priceChanges)
    {
        // إرجاع مجموعة فارغة لأن المستخدم لا يريد عرض أسعار البلاتين
        return collect([]);
    }

    /**
     * الحصول على إحصائيات المعادن المحسنة
     */
    private function getMetalStatistics($priceDate)
    {
        $statistics = [];

        // إحصائيات الذهب المفصلة (الأسعار المفعلة)
        $goldPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->whereNotIn('purity', ['جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب'])
            ->orderByRaw("FIELD(purity, '24K', '22K', '21K', '18K', '14K', '12K', '9K')")
            ->get();

        if ($goldPrices->count() > 0) {
            $statistics['gold'] = [
                'title' => __('metal_prices.statistics.gold_title'),
                'highest' => $goldPrices->max('price_per_gram'),
                'lowest' => $goldPrices->min('price_per_gram'),
                'average' => $goldPrices->avg('price_per_gram'),
                'count' => $goldPrices->count(),
                'purities' => $goldPrices->map(function($price) {
                    return [
                        'purity' => $price->purity,
                        'purity_label' => __('metal_prices.purities.' . $price->purity, [], null, $price->purity),
                        'price' => $price->price_per_gram,
                        'formatted_price' => number_format($price->price_per_gram, 2),
                        'date' => $price->created_at->format('Y/m/d'),
                        'time_ago' => $price->created_at->diffForHumans(),
                    ];
                }),
                'last_update' => $goldPrices->max('created_at'),
                'formatted_last_update' => $goldPrices->max('created_at') ?
                    $goldPrices->max('created_at')->diffForHumans() : null,
            ];
        }

        // إحصائيات الفضة المفصلة (الأسعار المفعلة)
        $silverPrices = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->orderByRaw("FIELD(purity, '999', '925', '900', '800', '600')")
            ->get();

        if ($silverPrices->count() > 0) {
            $statistics['silver'] = [
                'title' => __('metal_prices.statistics.silver_title'),
                'highest' => $silverPrices->max('price_per_gram'),
                'lowest' => $silverPrices->min('price_per_gram'),
                'average' => $silverPrices->avg('price_per_gram'),
                'count' => $silverPrices->count(),
                'purities' => $silverPrices->map(function($price) {
                    return [
                        'purity' => $price->purity,
                        'purity_label' => __('metal_prices.purities.' . $price->purity, [], null, $price->purity),
                        'price' => $price->price_per_gram,
                        'formatted_price' => number_format($price->price_per_gram, 2),
                        'date' => $price->created_at->format('Y/m/d'),
                        'time_ago' => $price->created_at->diffForHumans(),
                    ];
                }),
                'last_update' => $silverPrices->max('created_at'),
                'formatted_last_update' => $silverPrices->max('created_at') ?
                    $silverPrices->max('created_at')->diffForHumans() : null,
            ];
        }

        // إحصائيات الجنيهات الذهبية
        $goldCoinPrices = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->whereIn('purity', ['جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب'])
            ->orderByRaw("FIELD(purity, 'جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب')")
            ->get();

        if ($goldCoinPrices->count() > 0) {
            $statistics['gold_coins'] = [
                'title' => __('metal_prices.statistics.gold_coins_title'),
                'highest' => $goldCoinPrices->max('price_per_gram'),
                'lowest' => $goldCoinPrices->min('price_per_gram'),
                'average' => $goldCoinPrices->avg('price_per_gram'),
                'count' => $goldCoinPrices->count(),
                'purities' => $goldCoinPrices->map(function($price) {
                    return [
                        'purity' => $price->purity,
                        'purity_label' => __('metal_prices.purities.' . $price->purity, [], null, str_replace('_', ' ', $price->purity)),
                        'price_per_gram' => $price->price_per_gram,
                        'price_per_piece' => $price->price_per_gram * 8.5, // وزن الجنيه تقريباً
                        'formatted_price_per_gram' => number_format($price->price_per_gram, 2),
                        'formatted_price_per_piece' => number_format($price->price_per_gram * 8.5, 2),
                        'date' => $price->created_at->format('Y/m/d'),
                        'time_ago' => $price->created_at->diffForHumans(),
                    ];
                }),
                'last_update' => $goldCoinPrices->max('created_at'),
                'formatted_last_update' => $goldCoinPrices->max('created_at') ?
                    $goldCoinPrices->max('created_at')->diffForHumans() : null,
            ];
        }

        return $statistics;
    }

    /**
     * الحصول على بيانات الرسم البياني لأسعار المعادن (EGP فقط) - البيانات الحقيقية فقط
     */
    private function getMetalChartData()
    {
        $periods = [
            '7' => now()->subDays(7),
            '30' => now()->subDays(30),
            '90' => now()->subDays(90),
            '180' => now()->subDays(180),
            '365' => now()->subDays(365),
        ];

        $chartData = [];

        // أنواع المعادن والعيارات التي سنعرضها في الرسم البياني (جميع العيارات الجديدة)
        $metalTypes = [
            // عيارات الذهب (استبعاد الجنيهات)
            'gold_24k' => ['type' => 'gold', 'purity' => '24K', 'name' => __('metal_prices.chart.metals.gold_24k'), 'color' => '#FFC107'],
            'gold_22k' => ['type' => 'gold', 'purity' => '22K', 'name' => __('metal_prices.chart.metals.gold_22k'), 'color' => '#FFD54F'],
            'gold_21k' => ['type' => 'gold', 'purity' => '21K', 'name' => __('metal_prices.chart.metals.gold_21k'), 'color' => '#FFB300'],
            'gold_18k' => ['type' => 'gold', 'purity' => '18K', 'name' => __('metal_prices.chart.metals.gold_18k'), 'color' => '#FFA000'],
            'gold_14k' => ['type' => 'gold', 'purity' => '14K', 'name' => __('metal_prices.chart.metals.gold_14k'), 'color' => '#FF8F00'],
            'gold_12k' => ['type' => 'gold', 'purity' => '12K', 'name' => __('metal_prices.chart.metals.gold_12k'), 'color' => '#FF6F00'],
            'gold_9k' => ['type' => 'gold', 'purity' => '9K', 'name' => __('metal_prices.chart.metals.gold_9k'), 'color' => '#E65100'],

            // عيارات الفضة
            'silver_999' => ['type' => 'silver', 'purity' => '999', 'name' => __('metal_prices.chart.metals.silver_999'), 'color' => '#9E9E9E'],
            'silver_925' => ['type' => 'silver', 'purity' => '925', 'name' => __('metal_prices.chart.metals.silver_925'), 'color' => '#757575'],
            'silver_900' => ['type' => 'silver', 'purity' => '900', 'name' => __('metal_prices.chart.metals.silver_900'), 'color' => '#616161'],
            'silver_800' => ['type' => 'silver', 'purity' => '800', 'name' => __('metal_prices.chart.metals.silver_800'), 'color' => '#424242'],
            'silver_600' => ['type' => 'silver', 'purity' => '600', 'name' => __('metal_prices.chart.metals.silver_600'), 'color' => '#212121'],
        ];

        foreach ($periods as $key => $startDate) {
            $periodData = [
                'labels' => [],
                'datasets' => []
            ];

            // جمع جميع التواريخ المتاحة لهذه الفترة أولاً
            $allDatesRaw = MetalPrice::where('currency', 'EGP')
                ->whereDate('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date')
                ->distinct()
                ->orderBy('date', 'asc')
                ->pluck('date');

            $allDates = $allDatesRaw->map(function($date) use ($key) {
                $carbonDate = \Carbon\Carbon::parse($date);
                if ($key == '7') {
                    return $carbonDate->locale('ar')->isoFormat('dddd');
                } elseif ($key == '30') {
                    return $carbonDate->format('d/m');
                } elseif ($key == '90') {
                    return $carbonDate->format('d/m');
                } elseif ($key == '180') {
                    return $carbonDate->locale('ar')->isoFormat('MMM');
                } else {
                    return $carbonDate->locale('ar')->isoFormat('MMM');
                }
            })->toArray();

            $periodData['labels'] = $allDates;

            // الحصول على بيانات لكل نوع من المعادن (جميع الأسعار التاريخية)
            foreach ($metalTypes as $metalKey => $metalInfo) {
                // التحقق من وجود بيانات للعيار المحدد أولاً
                $hasData = MetalPrice::where('metal_type', $metalInfo['type'])
                    ->where('currency', 'EGP')
                    ->where('purity', $metalInfo['purity'])
                    ->exists();

                if (!$hasData) {
                    continue; // تخطي العيارات غير المتاحة
                }

                // الحصول على أحدث سعر لكل يوم مع ضمان تضمين جميع الأيام
                $pricesByDate = MetalPrice::where('metal_type', $metalInfo['type'])
                    ->where('currency', 'EGP')
                    ->where('purity', $metalInfo['purity'])
                    ->whereDate('created_at', '>=', $startDate)
                    ->orderBy('created_at', 'asc')
                    ->get()
                    ->groupBy(function($price) {
                        return $price->created_at->format('Y-m-d');
                    })
                    ->map(function($dayPrices) {
                        // أحدث سعر في اليوم (آخر تحديث)
                        return $dayPrices->sortByDesc('created_at')->first();
                    })
                    ->sortBy(function($price) {
                        return $price->created_at->format('Y-m-d');
                    });

                $prices = $pricesByDate->values();

                if ($prices->isNotEmpty()) {
                    // إنشاء مصفوفة بيانات مطابقة للتسميات الموحدة
                    $priceValues = [];

                    // جمع البيانات حسب التاريخ
                    $pricesByDate = $prices->keyBy(function($price) {
                        return $price->created_at->format('Y-m-d');
                    });

                    // مطابقة البيانات مع التواريخ المتاحة
                    $lastValue = null;
                    foreach ($allDatesRaw as $date) {
                        if (isset($pricesByDate[$date])) {
                            $value = $pricesByDate[$date]->price_per_gram;
                            $priceValues[] = $value;
                            $lastValue = $value;
                        } else {
                            // استخدام القيمة السابقة إذا لم توجد بيانات لهذا التاريخ
                            $priceValues[] = $lastValue;
                        }
                    }

                    // إضافة مجموعة البيانات لهذا المعدن
                    $periodData['datasets'][$metalKey] = [
                        'label' => $metalInfo['name'],
                        'data' => $priceValues,
                        'borderColor' => $metalInfo['color'],
                        'backgroundColor' => $this->hexToRgba($metalInfo['color'], 0.1),
                    ];
                }
                // إذا لم تكن هناك بيانات، لا نضيف أي شيء للرسم البياني
            }

            // إذا لم تكن هناك بيانات كافية، نضع رسالة
            if (empty($periodData['datasets'])) {
                $periodData['labels'] = ['لا توجد بيانات'];
                $periodData['datasets'] = [];
                $periodData['no_data'] = true;
            }

            $chartData[$key] = $periodData;
        }

        return $chartData;
    }

    /**
     * تحويل لون HEX إلى RGBA
     */
    private function hexToRgba($hex, $opacity = 1)
    {
        $hex = str_replace('#', '', $hex);

        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }

        return "rgba($r, $g, $b, $opacity)";
    }

    /**
     * تنسيق تاريخ آخر تحديث
     */
    private function formatLastUpdate($priceDate)
    {
        return \Carbon\Carbon::parse($priceDate)->locale(app()->getLocale())->isoFormat('LLLL');
    }

    /**
     * الحصول على الترجمات المطلوبة
     */
    private function getTranslations()
    {
        return [
            'page_title' => __('metal_prices.page_title'),
            'page_subtitle' => __('metal_prices.page_subtitle'),
            'currency_badge' => __('metal_prices.currency_badge'),
            'last_update' => __('metal_prices.last_update'),
            'breadcrumb_home' => __('metal_prices.breadcrumb_home'),
            'breadcrumb_metal_prices' => __('metal_prices.breadcrumb_metal_prices'),

            'gold_prices_title' => __('metal_prices.gold_prices_title'),
            'silver_prices_title' => __('metal_prices.silver_prices_title'),
            'platinum_prices_title' => __('metal_prices.platinum_prices_title'),

            'table_headers' => [
                'purity' => __('metal_prices.table_headers.purity'),
                'price_per_gram' => __('metal_prices.table_headers.price_per_gram'),
                'price_per_ounce' => __('metal_prices.table_headers.price_per_ounce'),
                'change' => __('metal_prices.table_headers.change'),
            ],

            'messages' => [
                'no_gold_prices' => __('metal_prices.messages.no_gold_prices'),
                'no_silver_prices' => __('metal_prices.messages.no_silver_prices'),
                'no_platinum_prices' => __('metal_prices.messages.no_platinum_prices'),
            ],

            'chart' => [
                'title' => __('metal_prices.chart.title'),
                'metals' => [
                    'gold_24k' => __('metal_prices.chart.metals.gold_24k'),
                    'gold_22k' => __('metal_prices.chart.metals.gold_22k'),
                    'gold_21k' => __('metal_prices.chart.metals.gold_21k'),
                    'gold_18k' => __('metal_prices.chart.metals.gold_18k'),
                    'gold_14k' => __('metal_prices.chart.metals.gold_14k'),
                    'gold_12k' => __('metal_prices.chart.metals.gold_12k'),
                    'gold_9k' => __('metal_prices.chart.metals.gold_9k'),
                    'silver_999' => __('metal_prices.chart.metals.silver_999'),
                    'silver_925' => __('metal_prices.chart.metals.silver_925'),
                    'silver_900' => __('metal_prices.chart.metals.silver_900'),
                    'silver_800' => __('metal_prices.chart.metals.silver_800'),
                    'silver_600' => __('metal_prices.chart.metals.silver_600'),
                    'silver' => __('metal_prices.chart.metals.silver'),
                    'platinum' => __('metal_prices.chart.metals.platinum'),
                ],
                'periods' => [
                    '7' => __('metal_prices.chart.periods.7'),
                    '30' => __('metal_prices.chart.periods.30'),
                    '90' => __('metal_prices.chart.periods.90'),
                    '180' => __('metal_prices.chart.periods.180'),
                    '365' => __('metal_prices.chart.periods.365'),
                ],
                'no_data' => __('metal_prices.chart.no_data'),
                'no_data_period' => __('metal_prices.chart.no_data_period'),
                'no_data_initial' => __('metal_prices.chart.no_data_initial'),
            ],

            'disclaimer' => [
                'title' => __('metal_prices.disclaimer.title'),
                'content' => __('metal_prices.disclaimer.content'),
            ],

            'units' => [
                'egp' => __('metal_prices.units.egp'),
            ],
        ];
    }

    public function search(Request $request)
    {
        $query = $request->input('query');

        // الحصول على البحثات الشائعة
        $popularSearches = \App\Models\SearchLog::getPopularSearches(8);

        return view('frontend.search', compact('query', 'popularSearches'));
    }
}
