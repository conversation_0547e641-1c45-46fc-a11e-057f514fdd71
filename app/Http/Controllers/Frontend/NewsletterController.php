<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Newsletter;
use App\Models\SiteSetting;

class NewsletterController extends Controller
{
    /**
     * الاشتراك في النشرة الإخبارية
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function subscribe(Request $request)
    {
        // التحقق من تفعيل النشرة الإخبارية
        $siteSettings = app(\App\Services\SettingsService::class)->all();
        $showNewsletter = $siteSettings->show_newsletter ?? true;

        if (!$showNewsletter) {
            return redirect()->back()->with('error', __('النشرة الإخبارية غير متاحة حاليًا.'));
        }

        // التحقق من البيانات
        $request->validate([
            'email' => 'required|email|max:255|unique:newsletters,email',
        ]);

        // حفظ البريد الإلكتروني في قاعدة البيانات
        Newsletter::create([
            'email' => $request->email,
            'is_active' => true,
        ]);

        return redirect()->back()->with('success', __('تم الاشتراك في النشرة الإخبارية بنجاح.'));
    }

    /**
     * إلغاء الاشتراك في النشرة الإخبارية
     *
     * @param string $token
     * @return \Illuminate\Http\RedirectResponse
     */
    public function unsubscribe($token)
    {
        $newsletter = Newsletter::where('token', $token)->first();

        if (!$newsletter) {
            return redirect()->route('home')->with('error', __('رابط إلغاء الاشتراك غير صالح.'));
        }

        $newsletter->update([
            'is_active' => false,
        ]);

        return redirect()->route('home')->with('success', __('تم إلغاء اشتراكك في النشرة الإخبارية بنجاح.'));
    }
}
