<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

use App\Models\Job;
use App\Models\JobApplication;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class CareerController extends Controller
{
    /**
     * عرض صفحة الوظائف
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // إنشاء كائن صفحة وهمي للوظائف
        $page = (object) [
            'title' => __('الوظائف'),
            'translated_title' => __('الوظائف'),
            'content' => __('لا توجد وظائف شاغرة حاليًا.'),
            'translated_content' => __('لا توجد وظائف شاغرة حاليًا.'),
        ];

        // جلب الوظائف النشطة وغير المنتهية
        $query = Job::where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>=', now());
            });

        // فلترة حسب القسم إذا تم تحديده
        if ($request->has('department') && $request->department) {
            $query->where('department', $request->department);
        }

        // فلترة حسب الموقع إذا تم تحديده
        if ($request->has('location') && $request->location) {
            $query->where('location', $request->location);
        }

        // فلترة حسب نوع الوظيفة إذا تم تحديده
        if ($request->has('type') && $request->type) {
            $query->where('type', $request->type);
        }

        // فلترة حسب الكلمة المفتاحية إذا تم تحديدها
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('requirements', 'like', "%{$search}%")
                  ->orWhere('responsibilities', 'like', "%{$search}%");
            });
        }

        // ترتيب الوظائف (المميزة أولاً ثم الأحدث)
        $jobs = $query->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // جلب قائمة الأقسام والمواقع وأنواع الوظائف للفلترة
        $departments = Job::where('is_active', true)
            ->distinct()
            ->pluck('department')
            ->filter();

        $locations = Job::where('is_active', true)
            ->distinct()
            ->pluck('location')
            ->filter();

        $types = Job::where('is_active', true)
            ->distinct()
            ->pluck('type')
            ->filter();

        return view('frontend.pages.careers', compact('page', 'jobs', 'departments', 'locations', 'types'));
    }

    /**
     * عرض تفاصيل وظيفة محددة
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // جلب الوظيفة بناءً على المعرف الفريد (slug)
        $job = Job::where('slug', $slug)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>=', now());
            })
            ->firstOrFail();

        // جلب وظائف مشابهة
        $similarJobs = Job::where('id', '!=', $job->id)
            ->where('is_active', true)
            ->where(function($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>=', now());
            })
            ->where(function($query) use ($job) {
                $query->where('department', $job->department)
                      ->orWhere('type', $job->type)
                      ->orWhere('location', $job->location);
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->take(3)
            ->get();

        return view('frontend.pages.career-details', compact('job', 'similarJobs'));
    }

    /**
     * تقديم طلب وظيفة
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function apply(Request $request)
    {
        // التحقق من البيانات
        $request->validate([
            'job_id' => 'required|exists:career_jobs,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'resume' => 'required|file|mimes:pdf,doc,docx|max:2048',
            'cover_letter' => 'nullable|string',
        ]);

        // جلب الوظيفة
        $job = Job::findOrFail($request->job_id);

        // التأكد من أن الوظيفة نشطة وغير منتهية
        if (!$job->is_active || ($job->expires_at && $job->expires_at < now())) {
            return redirect()->back()->with('error', __('هذه الوظيفة غير متاحة للتقديم حالياً.'));
        }

        // رفع ملف السيرة الذاتية
        $resumePath = $request->file('resume')->store('resumes', 'public');

        // إنشاء طلب التوظيف
        $application = new JobApplication();
        $application->job_id = $job->id;
        $application->name = $request->name;
        $application->email = $request->email;
        $application->phone = $request->phone;
        $application->cover_letter = $request->cover_letter;
        $application->resume = $resumePath;
        $application->status = 'pending';

        // إذا كان المستخدم مسجل دخول
        if (Auth::check()) {
            $application->user_id = Auth::id();
        }

        // حفظ طلب التوظيف
        $application->save();

        // يمكن إضافة إرسال بريد إلكتروني للإشعار هنا

        return redirect()->back()->with('success', __('تم تقديم طلبك بنجاح. سنتواصل معك قريبًا.'));
    }
}
