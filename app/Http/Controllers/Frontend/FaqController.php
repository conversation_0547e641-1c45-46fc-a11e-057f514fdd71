<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use Illuminate\Http\Request;

class FaqController extends Controller
{
    /**
     * عرض صفحة الأسئلة الشائعة
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // إنشاء كائن صفحة وهمي للأسئلة الشائعة
        $page = (object) [
            'title' => __('الأسئلة الشائعة'),
            'translated_title' => __('الأسئلة الشائعة'),
            'translated_content' => __('إليك أهم الأسئلة الشائعة وإجاباتها'),
            'translated_meta_title' => __('الأسئلة الشائعة - مجوهرات مكة جولد'),
            'translated_meta_description' => __('تجد هنا إجابات على أهم الأسئلة الشائعة حول منتجاتنا وخدماتنا'),
            'meta_keywords' => __('أسئلة شائعة، مجوهرات، ذهب، خدمة العملاء'),
        ];

        // جلب الأسئلة النشطة مرتبة حسب الترتيب والتصنيف
        $query = Faq::where('is_active', true)
            ->orderBy('sort_order', 'asc')
            ->orderBy('created_at', 'desc');

        // فلترة حسب التصنيف إذا تم تحديده
        if ($request->has('category') && $request->category && $request->category !== 'all') {
            $query->where('category', $request->category);
        }

        // فلترة حسب البحث إذا تم تحديده
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('question_ar', 'like', "%{$search}%")
                  ->orWhere('question_en', 'like', "%{$search}%")
                  ->orWhere('answer_ar', 'like', "%{$search}%")
                  ->orWhere('answer_en', 'like', "%{$search}%");
            });
        }

        $faqs = $query->get();

        // تجميع الأسئلة حسب التصنيف
        $faqsByCategory = $faqs->groupBy('category');

        // قائمة التصنيفات مع الترجمة
        $categories = [
            'general' => __('عام'),
            'products' => __('المنتجات'),
            'orders' => __('الطلبات'),
            'shipping' => __('الشحن'),
            'returns' => __('الإرجاع والاستبدال'),
            'payment' => __('الدفع'),
            'warranty' => __('الضمان'),
            'care' => __('العناية بالمجوهرات'),
            'custom' => __('التصميم المخصص'),
            'sizing' => __('المقاسات'),
        ];

        // جلب الأسئلة المميزة
        $featuredFaqs = Faq::where('is_active', true)
            ->where('is_featured', true)
            ->orderBy('sort_order', 'asc')
            ->take(5)
            ->get();

        return view('frontend.pages.faq', compact(
            'page',
            'faqs',
            'faqsByCategory',
            'categories',
            'featuredFaqs'
        ));
    }

    /**
     * زيادة عدد المشاهدات للسؤال
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function incrementViews(Request $request)
    {
        $faq = Faq::find($request->faq_id);

        if ($faq) {
            $faq->incrementViews();
            return response()->json(['success' => true, 'views' => $faq->views]);
        }

        return response()->json(['success' => false], 404);
    }

    /**
     * زيادة الأصوات المفيدة للسؤال
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markHelpful(Request $request)
    {
        $faq = Faq::find($request->faq_id);

        if ($faq) {
            $faq->incrementHelpfulVotes();
            return response()->json(['success' => true, 'helpful_votes' => $faq->helpful_votes]);
        }

        return response()->json(['success' => false], 404);
    }
}
