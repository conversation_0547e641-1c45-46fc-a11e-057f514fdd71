<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Appointment;
use App\Models\SiteSetting;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Mail\AppointmentConfirmation;

class AppointmentController extends Controller
{
    /**
     * Display the appointment form with contact information from database.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // جلب معلومات الاتصال من قاعدة البيانات
        $siteSettings = SiteSetting::first();

        // معلومات الاتصال مع قيم افتراضية
        $contactInfo = [
            'phone' => $siteSettings->contact_phone ?? '+20 2 2391 0000',
            'email' => $siteSettings->contact_email ?? '<EMAIL>',
            'whatsapp' => $siteSettings->whatsapp_number ?? '+20 2 2391 0000',
            'address' => $siteSettings->address ?? 'القاهرة، مصر',
        ];

        return view('frontend.pages.appointment', compact('contactInfo'));
    }

    /**
     * Store a new appointment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:255',
            'store' => 'required|string|in:cairo,alexandria,citystars,maadi,mallofegypt,hurghada',
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required|string',
            'purpose' => 'required|string|in:browse,engagement,custom,repair,appraisal,other',
            'notes' => 'nullable|string',
        ]);

        // Create appointment
        $appointment = new Appointment();

        // إذا كان المستخدم مسجل دخول، استخدم user_id
        if (Auth::check()) {
            $appointment->user_id = Auth::id();
        }

        // البحث عن store_id بناءً على اسم المتجر
        $storeMapping = [
            'cairo' => 1,
            'alexandria' => 2,
            'citystars' => 3,
            'maadi' => 4,
            'mallofegypt' => 5,
            'hurghada' => 6,
        ];

        $appointment->store_id = $storeMapping[$request->store] ?? 1; // افتراضي القاهرة
        $appointment->name = $request->name;
        $appointment->email = $request->email;
        $appointment->phone = $request->phone;
        $appointment->store = $request->store;
        $appointment->appointment_date = $request->date;
        $appointment->appointment_time = $request->time;
        $appointment->purpose = $request->purpose;
        $appointment->notes = $request->notes;
        $appointment->status = 'pending';
        $appointment->confirmation_code = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

        // إضافة service_type بناءً على purpose
        $serviceTypeMapping = [
            'browse' => ['ar' => 'تصفح المنتجات', 'en' => 'Browse Products'],
            'engagement' => ['ar' => 'خطوبة', 'en' => 'Engagement'],
            'custom' => ['ar' => 'تصميم مخصوص', 'en' => 'Custom Design'],
            'repair' => ['ar' => 'إصلاح', 'en' => 'Repair'],
            'appraisal' => ['ar' => 'تقييم', 'en' => 'Appraisal'],
            'other' => ['ar' => 'أخرى', 'en' => 'Other'],
        ];

        $serviceType = $serviceTypeMapping[$request->purpose] ?? $serviceTypeMapping['other'];
        $appointment->service_type_ar = $serviceType['ar'];
        $appointment->service_type_en = $serviceType['en'];
        $appointment->save();

        // Send confirmation email
        try {
            Mail::to($appointment->email)->send(new AppointmentConfirmation($appointment));
        } catch (\Exception $e) {
            // Log email error but continue
            Log::error('Failed to send appointment confirmation email: ' . $e->getMessage());
        }

        return redirect()->route('appointment.confirmation', $appointment->id)->with('success',
            app()->getLocale() == 'ar' ? 'تم حجز موعدك بنجاح!' : 'Your appointment has been booked successfully!');
    }

    /**
     * Display the appointment confirmation page.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function confirmation($id)
    {
        $appointment = Appointment::findOrFail($id);

        return view('frontend.pages.appointment-confirmation', compact('appointment'));
    }

    /**
     * Cancel an appointment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request, $id)
    {
        $appointment = Appointment::findOrFail($id);

        // Verify confirmation code
        if ($request->code !== $appointment->confirmation_code) {
            return redirect()->back()->with('error',
                app()->getLocale() == 'ar' ? 'رمز التأكيد غير صحيح' : 'Invalid confirmation code');
        }

        $appointment->status = 'cancelled';
        $appointment->save();

        return redirect()->route('home')->with('success',
            app()->getLocale() == 'ar' ? 'تم إلغاء موعدك بنجاح' : 'Your appointment has been cancelled successfully');
    }
}
