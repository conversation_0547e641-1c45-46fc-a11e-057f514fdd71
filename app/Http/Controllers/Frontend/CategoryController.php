<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Services\SuperAdminSettingsService;
use App\Services\SettingsService;

class CategoryController extends Controller
{
    protected $superAdminSettings;
    protected $siteSettings;

    public function __construct(SuperAdminSettingsService $superAdminSettings, SettingsService $siteSettings)
    {
        $this->superAdminSettings = $superAdminSettings;
        $this->siteSettings = $siteSettings;
    }

    public function show($slug)
    {
        // التحقق من وضع العرض فقط
        $displayOnlyMode = $this->superAdminSettings->get('display_only_mode', false);

        $category = Category::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        // الحصول على إعدادات الموقع
        $settings = $this->siteSettings->all();
        $superAdminSettings = $this->superAdminSettings->all();

        return view('frontend.categories.show', compact(
            'category',
            'settings',
            'superAdminSettings',
            'displayOnlyMode'
        ));
    }
}
