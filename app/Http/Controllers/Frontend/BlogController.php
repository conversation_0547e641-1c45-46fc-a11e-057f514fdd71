<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BlogPost;
use App\Models\BlogCategory;
use Illuminate\Support\Facades\DB;

class BlogController extends Controller
{
    /**
     * عرض صفحة المدونة
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        // جلب المقالات النشطة
        $query = BlogPost::where('is_active', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->with(['category', 'author']);

        // فلترة حسب التصنيف إذا تم تحديده
        if ($request->has('category') && $request->category) {
            $query->where('category_id', $request->category);
        }

        // فلترة حسب الكلمة المفتاحية إذا تم تحديدها
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('excerpt', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // ترتيب المقالات حسب تاريخ النشر (الأحدث أولاً)
        $posts = $query->orderBy('published_at', 'desc')
            ->paginate(12);

        // جلب المقالات المميزة
        $featuredPosts = BlogPost::where('is_active', true)
            ->where('is_featured', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // جلب أحدث المقالات للـ sidebar
        $latestPosts = BlogPost::where('is_active', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->take(5)
            ->get();

        // جلب أكثر المقالات قراءة
        $popularPosts = BlogPost::where('is_active', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->orderBy('views', 'desc')
            ->take(5)
            ->get();

        // جلب التصنيفات مع عدد المقالات
        $categories = BlogCategory::withCount(['posts' => function($query) {
            $query->where('is_active', true)
                  ->whereNotNull('published_at')
                  ->where('published_at', '<=', now());
        }])->where('is_active', true)->get();

        // جلب الأرشيف (السنوات والشهور)
        $archives = BlogPost::where('is_active', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->select(DB::raw('YEAR(published_at) as year, MONTH(published_at) as month, COUNT(*) as count'))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get();

        // إنشاء كائن صفحة للمدونة مع meta tags محسنة
        $page = (object) [
            'title' => __('المدونة'),
            'translated_title' => __('المدونة'),
            'content' => $posts->count() > 0 ? __('اكتشف أحدث المقالات والأخبار في عالم المجوهرات والذهب.') : __('لا توجد مقالات حاليًا.'),
            'translated_content' => $posts->count() > 0 ? __('اكتشف أحدث المقالات والأخبار في عالم المجوهرات والذهب.') : __('لا توجد مقالات حاليًا.'),
            'translated_meta_description' => __('اطلع على أحدث المقالات والأخبار في عالم المجوهرات والذهب. نصائح، اتجاهات، ومعلومات قيمة من خبراء المجوهرات.'),
            'meta_keywords' => __('مدونة، مجوهرات، ذهب، أخبار، نصائح، اتجاهات، مقالات'),
        ];

        return view('frontend.pages.blog', compact('page', 'posts', 'featuredPosts', 'latestPosts', 'popularPosts', 'categories', 'archives'));
    }

    /**
     * عرض مقال محدد
     *
     * @param string $slug
     * @return \Illuminate\View\View
     */
    public function show($slug)
    {
        // جلب المقال بناءً على المعرف الفريد (slug)
        $post = BlogPost::where('slug', $slug)
            ->where('is_active', true)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->with(['category', 'author'])
            ->firstOrFail();

        // زيادة عدد المشاهدات
        $post->increment('views');

        // جلب المقالات ذات الصلة
        $relatedPosts = BlogPost::where('is_active', true)
            ->where('id', '!=', $post->id)
            ->where(function($query) use ($post) {
                $query->where('category_id', $post->category_id)
                    ->orWhere('author_id', $post->author_id);
            })
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->orderBy('published_at', 'desc')
            ->take(3)
            ->get();

        // جلب المقال السابق والتالي
        $previousPost = BlogPost::where('is_active', true)
            ->where('id', '<', $post->id)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->orderBy('id', 'desc')
            ->first();

        $nextPost = BlogPost::where('is_active', true)
            ->where('id', '>', $post->id)
            ->whereNotNull('published_at')
            ->where('published_at', '<=', now())
            ->orderBy('id', 'asc')
            ->first();

        // معلومات المشاركة الاجتماعية
        $shareUrl = route('blog.show', $post->slug);
        $shareTitle = app()->getLocale() == 'ar' ? $post->title : ($post->translations['en']['title'] ?? $post->title);
        $shareExcerpt = app()->getLocale() == 'ar' ? $post->excerpt : ($post->translations['en']['excerpt'] ?? $post->excerpt);

        return view('frontend.pages.blog-post', compact('post', 'relatedPosts', 'previousPost', 'nextPost', 'shareUrl', 'shareTitle', 'shareExcerpt'));
    }
}
