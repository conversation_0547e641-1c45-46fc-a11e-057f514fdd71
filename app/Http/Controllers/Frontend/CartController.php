<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use Illuminate\Support\Facades\Session;

class CartController extends Controller
{
    public function index()
    {
        return view('frontend.cart.index');
    }

    public function add(Request $request)
    {
        $product = Product::findOrFail($request->product_id);
        $cart = Session::get('cart', []);

        $rowId = uniqid();
        $cart[$rowId] = [
            'id' => $product->id,
            'name' => $product->name_ar,
            'quantity' => $request->quantity,
            'price' => $product->price,
            'weight' => $product->weight,
            'options' => [
                'image' => $product->image,
                'slug' => $product->slug,
                'metal_type' => $product->metal_type,
                'purity' => $product->purity,
            ]
        ];

        Session::put('cart', $cart);

        return redirect()->back()->with('success', app()->getLocale() == 'ar' ? 'تمت إضافة المنتج إلى سلة المشتريات' : 'Product added to cart');
    }

    public function addProduct(Request $request, $productId)
    {
        try {
            $product = Product::findOrFail($productId);
            $quantity = $request->input('quantity', 1);

            // التحقق من المخزون
            if ($product->stock_quantity < $quantity) {
                return response()->json([
                    'success' => false,
                    'message' => app()->getLocale() == 'ar' ? 'الكمية المطلوبة غير متوفرة في المخزون' : 'Requested quantity not available in stock'
                ]);
            }

            $cart = Session::get('cart', []);

            // البحث عن المنتج في السلة
            $existingRowId = null;
            foreach ($cart as $rowId => $item) {
                if ($item['id'] == $product->id) {
                    $existingRowId = $rowId;
                    break;
                }
            }

            if ($existingRowId) {
                // تحديث الكمية إذا كان المنتج موجود
                $cart[$existingRowId]['quantity'] += $quantity;
            } else {
                // إضافة منتج جديد
                $rowId = uniqid();
                $cart[$rowId] = [
                    'id' => $product->id,
                    'name' => $product->name_ar,
                    'quantity' => $quantity,
                    'price' => $product->price,
                    'weight' => $product->weight ?? 0,
                    'options' => [
                        'image' => $product->image,
                        'slug' => $product->slug,
                        'metal_type' => $product->metal->name_ar ?? '',
                        'purity' => $product->metalPurity->name_ar ?? '',
                    ]
                ];
            }

            Session::put('cart', $cart);

            // حساب إجمالي عدد العناصر في السلة
            $cartCount = array_sum(array_column($cart, 'quantity'));

            return response()->json([
                'success' => true,
                'message' => app()->getLocale() == 'ar' ? 'تم إضافة المنتج إلى السلة بنجاح' : 'Product added to cart successfully',
                'cart_count' => $cartCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => app()->getLocale() == 'ar' ? 'حدث خطأ أثناء إضافة المنتج' : 'Error adding product to cart'
            ]);
        }
    }

    public function update(Request $request)
    {
        $cart = Session::get('cart', []);

        if (isset($cart[$request->rowId])) {
            $cart[$request->rowId]['quantity'] = $request->quantity;
            Session::put('cart', $cart);
        }

        return redirect()->back()->with('success', app()->getLocale() == 'ar' ? 'تم تحديث سلة المشتريات' : 'Cart updated');
    }

    public function remove($rowId)
    {
        $cart = Session::get('cart', []);

        if (isset($cart[$rowId])) {
            unset($cart[$rowId]);
            Session::put('cart', $cart);
        }

        return redirect()->back()->with('success', app()->getLocale() == 'ar' ? 'تم حذف المنتج من سلة المشتريات' : 'Product removed from cart');
    }

    public function clear()
    {
        Session::forget('cart');

        return redirect()->back()->with('success', app()->getLocale() == 'ar' ? 'تم تفريغ سلة المشتريات' : 'Cart cleared');
    }
}
