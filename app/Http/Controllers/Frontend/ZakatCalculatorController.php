<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\MetalPrice;
use App\Models\SiteSetting;
use Carbon\Carbon;

class ZakatCalculatorController extends Controller
{
    /**
     * عرض صفحة حساب الزكاة للمجوهرات الشخصية
     */
    public function index()
    {
        // الحصول على إعدادات الموقع
        $siteSettings = SiteSetting::first();

        // الحصول على أحدث أسعار الذهب والفضة
        $latestPrices = $this->getLatestMetalPrices();

        // حساب النصاب بناءً على الأسعار الحالية
        $nisabCalculations = $this->calculateNisab($latestPrices);

        // الحصول على الترجمات
        $translations = $this->getTranslations();

        // معلومات الزكاة الأساسية
        $zakatInfo = $this->getZakatInfo();

        return view('frontend.zakat-calculator', compact(
            'siteSettings',
            'latestPrices',
            'nisabCalculations',
            'translations',
            'zakatInfo'
        ));
    }



    /**
     * الحصول على أحدث أسعار الذهب والفضة
     */
    private function getLatestMetalPrices()
    {
        // البحث عن أحدث الأسعار باستخدام created_at أو price_date
        // البحث عن السعر المفعل للذهب عيار 24
        $goldPrice = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('purity', '24K')
            ->where('is_active', true)
            ->first();

        // البحث عن السعر المفعل للفضة عيار 999
        $silverPrice = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->where('purity', '999')
            ->where('is_active', true)
            ->first();

        // تحديد آخر تحديث من أي من الأسعار
        $lastUpdate = null;
        if ($goldPrice && $silverPrice) {
            $lastUpdate = $goldPrice->created_at->max($silverPrice->created_at);
        } elseif ($goldPrice) {
            $lastUpdate = $goldPrice->created_at;
        } elseif ($silverPrice) {
            $lastUpdate = $silverPrice->created_at;
        }

        return [
            'gold' => $goldPrice,
            'silver' => $silverPrice,
            'last_update' => $lastUpdate ? $lastUpdate->locale(app()->getLocale())->diffForHumans() : null,
        ];
    }

    /**
     * حساب النصاب بناءً على الأسعار الحالية
     */
    private function calculateNisab($latestPrices)
    {
        // نصاب الذهب: 85 جرام من الذهب الخالص
        $goldNisabWeight = 85; // جرام
        $goldNisabValue = 0;

        if ($latestPrices['gold']) {
            $goldNisabValue = $goldNisabWeight * $latestPrices['gold']->price_per_gram;
        }

        // نصاب الفضة: 595 جرام من الفضة الخالصة
        $silverNisabWeight = 595; // جرام
        $silverNisabValue = 0;

        if ($latestPrices['silver']) {
            $silverNisabValue = $silverNisabWeight * $latestPrices['silver']->price_per_gram;
        }

        // النصاب المعتبر هو الأقل بين الذهب والفضة (الأنفع للفقراء)
        $applicableNisab = min($goldNisabValue, $silverNisabValue);
        if ($applicableNisab == 0) {
            $applicableNisab = max($goldNisabValue, $silverNisabValue);
        }

        return [
            'gold' => [
                'weight' => $goldNisabWeight,
                'value' => $goldNisabValue,
                'formatted_value' => number_format($goldNisabValue, 2),
            ],
            'silver' => [
                'weight' => $silverNisabWeight,
                'value' => $silverNisabValue,
                'formatted_value' => number_format($silverNisabValue, 2),
            ],
            'applicable' => [
                'value' => $applicableNisab,
                'formatted_value' => number_format($applicableNisab, 2),
                'type' => $applicableNisab == $goldNisabValue ? 'gold' : 'silver',
            ],
        ];
    }



    /**
     * الحصول على معلومات الزكاة الأساسية
     */
    private function getZakatInfo()
    {
        return [
            'conditions' => [
                __('zakat.conditions.nisab'),
                __('zakat.conditions.hawl'),
                __('zakat.conditions.ownership'),
                __('zakat.conditions.excess'),
            ],
            'rates' => [
                'money' => '2.5%',
                'gold' => '2.5%',
                'silver' => '2.5%',
                'trade' => '2.5%',
                'crops_irrigated' => '5%',
                'crops_rain' => '10%',
            ],
            'nisab_weights' => [
                'gold' => '85 ' . __('zakat.units.grams'),
                'silver' => '595 ' . __('zakat.units.grams'),
            ],
        ];
    }

    /**
     * الحصول على الترجمات
     */
    private function getTranslations()
    {
        return [
            'page_title' => __('zakat.page_title'),
            'page_subtitle' => __('zakat.page_subtitle'),
            'calculator_title' => __('zakat.calculator_title'),
            'results_title' => __('zakat.results_title'),
            'conditions_title' => __('zakat.conditions_title'),
            'disclaimer_title' => __('zakat.disclaimer_title'),
            'disclaimer_content' => __('zakat.disclaimer_content'),
        ];
    }
}
