<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class AccountController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function dashboard()
    {
        $user = Auth::user();
        $orders = Order::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $siteSettings = app(\App\Services\SettingsService::class)->all();
        $displayOnlyMode = $siteSettings->display_only_mode ?? false;
        $showWishlist = $siteSettings->show_wishlist ?? true;

        return view('frontend.account.dashboard', compact('user', 'orders', 'displayOnlyMode', 'showWishlist'));
    }

    public function orders()
    {
        return view('frontend.account.orders');
    }

    public function orderShow($id)
    {
        $user = Auth::user();
        $order = Order::where('user_id', $user->id)
            ->where('id', $id)
            ->with('items.product')
            ->firstOrFail();

        return view('frontend.account.order-show', compact('order'));
    }

    public function wishlist()
    {
        return view('frontend.account.wishlist');
    }

    public function settings()
    {
        return view('frontend.account.settings');
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:255',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'country' => 'nullable|string|max:255',
            'postal_code' => 'nullable|string|max:255',
        ]);

        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        $user->address = $request->address;
        $user->city = $request->city;
        $user->country = $request->country;
        $user->postal_code = $request->postal_code;

        $user->save();

        return redirect()->back()->with('success', 'تم تحديث الملف الشخصي بنجاح');
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user = Auth::user();

        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->with('error', 'كلمة المرور الحالية غير صحيحة');
        }

        $user->password = Hash::make($request->password);
        $user->save();

        return redirect()->back()->with('success', 'تم تحديث كلمة المرور بنجاح');
    }

    public function addToWishlist($productId)
    {
        $user = Auth::user();

        if (!$user->wishlist()->where('product_id', $productId)->exists()) {
            $user->wishlist()->create([
                'product_id' => $productId
            ]);

            return redirect()->back()->with('success', 'تمت إضافة المنتج إلى المفضلة');
        }

        return redirect()->back()->with('info', 'المنتج موجود بالفعل في المفضلة');
    }

    public function removeFromWishlist($wishlistId)
    {
        $user = Auth::user();
        $wishlist = $user->wishlist()->findOrFail($wishlistId);
        $wishlist->delete();

        return redirect()->back()->with('success', 'تم حذف المنتج من المفضلة');
    }

    public function toggleWishlist(Request $request, $productId)
    {
        try {
            if (!Auth::check()) {
                return response()->json([
                    'success' => false,
                    'message' => app()->getLocale() == 'ar' ? 'يجب تسجيل الدخول أولاً' : 'Please login first',
                    'redirect' => route('login')
                ]);
            }

            $user = Auth::user();
            $wishlistItem = $user->wishlist()->where('product_id', $productId)->first();

            if ($wishlistItem) {
                // إزالة من المفضلة
                $wishlistItem->delete();
                $added = false;
                $message = app()->getLocale() == 'ar' ? 'تم إزالة المنتج من المفضلة' : 'Product removed from wishlist';
            } else {
                // إضافة إلى المفضلة
                $user->wishlist()->create([
                    'product_id' => $productId
                ]);
                $added = true;
                $message = app()->getLocale() == 'ar' ? 'تم إضافة المنتج إلى المفضلة' : 'Product added to wishlist';
            }

            // حساب عدد العناصر في المفضلة
            $wishlistCount = $user->wishlist()->count();

            return response()->json([
                'success' => true,
                'added' => $added,
                'message' => $message,
                'wishlist_count' => $wishlistCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => app()->getLocale() == 'ar' ? 'حدث خطأ أثناء تحديث المفضلة' : 'Error updating wishlist'
            ]);
        }
    }
}
