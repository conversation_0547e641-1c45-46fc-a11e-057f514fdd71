<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;

class PrivacyController extends Controller
{
    /**
     * عرض صفحة سياسة الخصوصية
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // جلب إعدادات الموقع
        $siteSettings = SiteSetting::first();

        // إنشاء كائن صفحة لسياسة الخصوصية من قاعدة البيانات
        $page = (object) [
            'title' => $this->getPrivacyTitle($siteSettings),
            'translated_title' => $this->getPrivacyTitle($siteSettings),
            'translated_content' => $this->getPrivacyContent($siteSettings),
            'translated_meta_title' => $this->getPrivacyTitle($siteSettings) . ' - ' . ($siteSettings->site_name ?? 'مجوهرات مكة جولد'),
            'translated_meta_description' => $this->getPrivacyMetaDescription($siteSettings),
            'meta_keywords' => $siteSettings->privacy_meta_keywords ?? 'سياسة الخصوصية، حماية البيانات، الأمان، مجوهرات، ذهب',
            'last_updated' => $siteSettings->privacy_last_updated,
            'show_last_updated' => $siteSettings->privacy_show_last_updated ?? true,
        ];

        return view('frontend.pages.privacy', compact('page', 'siteSettings'));
    }

    /**
     * الحصول على عنوان سياسة الخصوصية
     *
     * @param $siteSettings
     * @return string
     */
    private function getPrivacyTitle($siteSettings)
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $siteSettings->privacy_title_ar ?? 'سياسة الخصوصية';
        }

        return $siteSettings->privacy_title_en ?? 'Privacy Policy';
    }

    /**
     * الحصول على محتوى سياسة الخصوصية
     *
     * @param $siteSettings
     * @return string
     */
    private function getPrivacyContent($siteSettings)
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $siteSettings->privacy_content_ar ?? $this->getDefaultPrivacyContentAr($siteSettings);
        }

        return $siteSettings->privacy_content_en ?? $this->getDefaultPrivacyContentEn($siteSettings);
    }

    /**
     * الحصول على وصف meta لسياسة الخصوصية
     *
     * @param $siteSettings
     * @return string
     */
    private function getPrivacyMetaDescription($siteSettings)
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $siteSettings->privacy_meta_description_ar ??
                'تعرف على سياسة الخصوصية الخاصة بنا وكيفية حماية بياناتك الشخصية في ' . ($siteSettings->site_name ?? 'مجوهرات مكة جولد');
        }

        return $siteSettings->privacy_meta_description_en ??
            'Learn about our privacy policy and how we protect your personal data at ' . ($siteSettings->site_name ?? 'Makkah Gold Jewelry');
    }

    /**
     * الحصول على المحتوى الافتراضي لسياسة الخصوصية بالعربية
     *
     * @param $siteSettings
     * @return string
     */
    private function getDefaultPrivacyContentAr($siteSettings)
    {
        $siteName = $siteSettings->site_name ?? 'مجوهرات مكة جولد';
        $contactEmail = $siteSettings->contact_email ?? '<EMAIL>';
        $contactPhone = $siteSettings->contact_phone ?? '+20 2 2391 0000';

        return "
        <div class='space-y-6'>
            <h2 class='text-2xl font-bold mb-4'>مقدمة</h2>
            <p>نحن في {$siteName} نقدر خصوصيتك ونلتزم بحماية معلوماتك الشخصية. توضح هذه السياسة كيفية جمع واستخدام وحماية المعلومات التي تقدمها لنا.</p>

            <h2 class='text-2xl font-bold mb-4'>المعلومات التي نجمعها</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>المعلومات الشخصية مثل الاسم والبريد الإلكتروني ورقم الهاتف</li>
                <li>معلومات الطلبات والمشتريات</li>
                <li>معلومات التصفح وملفات تعريف الارتباط</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>كيفية استخدام المعلومات</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>معالجة الطلبات وتقديم الخدمات</li>
                <li>التواصل معك بشأن طلباتك</li>
                <li>تحسين خدماتنا وتجربة المستخدم</li>
                <li>إرسال العروض والتحديثات (بموافقتك)</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>الاتصال بنا</h2>
            <p>إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه، يرجى التواصل معنا عبر:</p>
            <ul class='list-disc list-inside space-y-2'>
                <li>البريد الإلكتروني: {$contactEmail}</li>
                <li>الهاتف: {$contactPhone}</li>
            </ul>
        </div>";
    }

    /**
     * الحصول على المحتوى الافتراضي لسياسة الخصوصية بالإنجليزية
     *
     * @param $siteSettings
     * @return string
     */
    private function getDefaultPrivacyContentEn($siteSettings)
    {
        $siteName = $siteSettings->site_name ?? 'Makkah Gold Jewelry';
        $contactEmail = $siteSettings->contact_email ?? '<EMAIL>';
        $contactPhone = $siteSettings->contact_phone ?? '+20 2 2391 0000';

        return "
        <div class='space-y-6'>
            <h2 class='text-2xl font-bold mb-4'>Introduction</h2>
            <p>We at {$siteName} value your privacy and are committed to protecting your personal information. This policy explains how we collect, use, and protect the information you provide to us.</p>

            <h2 class='text-2xl font-bold mb-4'>Information We Collect</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>Personal information such as name, email, and phone number</li>
                <li>Order and purchase information</li>
                <li>Browsing information and cookies</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>How We Use Information</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>Process orders and provide services</li>
                <li>Communicate with you about your orders</li>
                <li>Improve our services and user experience</li>
                <li>Send offers and updates (with your consent)</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>Contact Us</h2>
            <p>If you have any questions about this privacy policy, please contact us via:</p>
            <ul class='list-disc list-inside space-y-2'>
                <li>Email: {$contactEmail}</li>
                <li>Phone: {$contactPhone}</li>
            </ul>
        </div>";
    }
}
