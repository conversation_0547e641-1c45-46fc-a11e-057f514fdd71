<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ContactMessage;
use App\Models\SiteSetting;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Mail\ContactConfirmation;
use App\Mail\ContactNotification;

class ContactController extends Controller
{
    /**
     * Display the contact page with contact information from database.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // جلب جميع إعدادات الموقع من قاعدة البيانات
        $siteSettings = SiteSetting::first();

        // إذا لم توجد إعدادات، إنشاء إعدادات افتراضية
        if (!$siteSettings) {
            $siteSettings = SiteSetting::create([
                'site_name' => 'مجوهرات مكة جولد',
                'contact_phone' => '+20 2 2391 0000',
                'contact_email' => '<EMAIL>',
                'whatsapp_number' => '+20 2 2391 0000',
                'address' => 'القاهرة - وسط البلد، شارع الجمهورية، مصر',
                'show_map' => true,
                'show_faqs' => true,
                'show_social_media' => true,
            ]);
        }

        // تحضير معلومات الاتصال الشاملة من قاعدة البيانات
        $contactInfo = [
            // معلومات الاتصال الأساسية (بدون تنسيق - سيتم التنسيق في الـ View)
            'phone' => $siteSettings->contact_phone,
            'email' => $siteSettings->contact_email,
            'whatsapp' => $siteSettings->whatsapp_number,
            'address' => $siteSettings->address,
            'headquarters_address_ar' => $siteSettings->headquarters_address_ar,
            'headquarters_address_en' => $siteSettings->headquarters_address_en,

            // ساعات العمل
            'working_hours_ar' => $siteSettings->working_hours_ar,
            'working_hours_en' => $siteSettings->working_hours_en,

            // إعدادات الخريطة
            'map_latitude' => $siteSettings->map_latitude,
            'map_longitude' => $siteSettings->map_longitude,
            'map_zoom' => $siteSettings->map_zoom ?? 15,
            'map_marker_title' => $siteSettings->map_marker_title,
            'show_map' => $siteSettings->show_map ?? true,

            // وسائل التواصل الاجتماعي
            'facebook' => $siteSettings->facebook_url,
            'instagram' => $siteSettings->instagram_url,
            'twitter' => $siteSettings->twitter_url,
            'youtube' => $siteSettings->youtube_url,
            'tiktok' => $siteSettings->tiktok_url,
            'linkedin' => $siteSettings->linkedin_url,
            'show_social_media' => $siteSettings->show_social_media ?? true,

            // الأسئلة الشائعة
            'faqs' => $siteSettings->contact_faqs ?? [],
            'show_faqs' => $siteSettings->show_faqs ?? true,

            // معلومات إضافية
            'site_name' => $siteSettings->site_name,
            'site_description' => $siteSettings->site_description,
        ];

        // إنشاء كائن صفحة وهمي للاتصال مع جميع الخصائص المطلوبة
        $pageTitle = $contactInfo['site_name'] ? 'اتصل بـ ' . $contactInfo['site_name'] : 'اتصل بنا';
        $pageContent = 'نحن هنا للإجابة على جميع استفساراتك ومساعدتك في اختيار المجوهرات المناسبة.';

        $page = (object) [
            'title' => $pageTitle,
            'translated_title' => $pageTitle,
            'content' => $pageContent,
            'translated_content' => $pageContent,
            'translated_meta_title' => $pageTitle . ' - ' . $contactInfo['site_name'],
            'translated_meta_description' => 'تواصل معنا في ' . $contactInfo['site_name'] . ' للاستفسار عن منتجاتنا من المجوهرات والذهب. نحن هنا لخدمتك.',
            'meta_keywords' => 'اتصل بنا، مجوهرات، ذهب، خدمة العملاء، ' . $contactInfo['site_name'],
        ];

        return view('frontend.pages.contact', compact('contactInfo', 'page', 'siteSettings'));
    }



    /**
     * Store a new contact message.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:5000',
        ]);

        try {
            // إنشاء رسالة جديدة
            $contactMessage = ContactMessage::create([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'subject' => $request->subject,
                'message' => $request->message,
                'status' => 'new',
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // إرسال تأكيد للعميل
            try {
                Mail::to($contactMessage->email)->send(new ContactConfirmation($contactMessage));
            } catch (\Exception $e) {
                Log::error('Failed to send contact confirmation email: ' . $e->getMessage());
            }

            // إرسال إشعار للإدارة
            try {
                $adminEmail = SiteSetting::first()->contact_email ?? '<EMAIL>';
                Mail::to($adminEmail)->send(new ContactNotification($contactMessage));
            } catch (\Exception $e) {
                Log::error('Failed to send contact notification email: ' . $e->getMessage());
            }

            return redirect()->route('contact')->with('success',
                app()->getLocale() == 'ar'
                    ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
                    : 'Your message has been sent successfully! We will contact you soon.');

        } catch (\Exception $e) {
            Log::error('Failed to store contact message: ' . $e->getMessage());

            return redirect()->route('contact')->with('error',
                app()->getLocale() == 'ar'
                    ? 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.'
                    : 'An error occurred while sending the message. Please try again.');
        }
    }
}
