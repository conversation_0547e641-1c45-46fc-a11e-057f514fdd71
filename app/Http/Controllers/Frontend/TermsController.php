<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\SiteSetting;

class TermsController extends Controller
{
    /**
     * عرض صفحة الشروط والأحكام
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // جلب إعدادات الموقع
        $siteSettings = SiteSetting::first();

        // إنشاء كائن صفحة للشروط والأحكام من قاعدة البيانات
        $page = (object) [
            'title' => $this->getTermsTitle($siteSettings),
            'translated_title' => $this->getTermsTitle($siteSettings),
            'translated_content' => $this->getTermsContent($siteSettings),
            'translated_meta_title' => $this->getTermsTitle($siteSettings) . ' - ' . ($siteSettings->site_name ?? 'مجوهرات مكة جولد'),
            'translated_meta_description' => $this->getTermsMetaDescription($siteSettings),
            'meta_keywords' => $siteSettings->terms_meta_keywords ?? 'الشروط والأحكام، قوانين الاستخدام، سياسة الإرجاع، مجوهرات، ذهب',
            'last_updated' => $siteSettings->terms_last_updated,
            'show_last_updated' => $siteSettings->terms_show_last_updated ?? true,
        ];

        return view('frontend.pages.terms', compact('page', 'siteSettings'));
    }

    /**
     * الحصول على عنوان الشروط والأحكام
     *
     * @param $siteSettings
     * @return string
     */
    private function getTermsTitle($siteSettings)
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $siteSettings->terms_title_ar ?? 'الشروط والأحكام';
        }

        return $siteSettings->terms_title_en ?? 'Terms and Conditions';
    }

    /**
     * الحصول على محتوى الشروط والأحكام
     *
     * @param $siteSettings
     * @return string
     */
    private function getTermsContent($siteSettings)
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $siteSettings->terms_content_ar ?? $this->getDefaultTermsContentAr($siteSettings);
        }

        return $siteSettings->terms_content_en ?? $this->getDefaultTermsContentEn($siteSettings);
    }

    /**
     * الحصول على وصف meta للشروط والأحكام
     *
     * @param $siteSettings
     * @return string
     */
    private function getTermsMetaDescription($siteSettings)
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $siteSettings->terms_meta_description_ar ??
                'اطلع على الشروط والأحكام الخاصة بالتسوق والخدمات في ' . ($siteSettings->site_name ?? 'مجوهرات مكة جولد');
        }

        return $siteSettings->terms_meta_description_en ??
            'Review our terms and conditions for shopping and services at ' . ($siteSettings->site_name ?? 'Makkah Gold Jewelry');
    }

    /**
     * الحصول على المحتوى الافتراضي للشروط والأحكام بالعربية
     *
     * @param $siteSettings
     * @return string
     */
    private function getDefaultTermsContentAr($siteSettings)
    {
        $siteName = $siteSettings->site_name ?? 'مجوهرات مكة جولد';
        $contactEmail = $siteSettings->contact_email ?? '<EMAIL>';
        $contactPhone = $siteSettings->contact_phone ?? '+20 2 2391 0000';
        $currentDate = now()->format('Y-m-d');

        return "
        <div class='space-y-6'>
            <h2 class='text-2xl font-bold mb-4'>مقدمة</h2>
            <p>مرحباً بك في {$siteName}. باستخدام موقعنا الإلكتروني وخدماتنا، فإنك توافق على الالتزام بهذه الشروط والأحكام.</p>

            <h2 class='text-2xl font-bold mb-4'>استخدام الموقع</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>يجب أن تكون بالغاً لاستخدام خدماتنا</li>
                <li>يجب تقديم معلومات صحيحة ودقيقة</li>
                <li>لا يجوز استخدام الموقع لأغراض غير قانونية</li>
                <li>نحتفظ بالحق في إنهاء حسابك في حالة انتهاك الشروط</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>الطلبات والدفع</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>جميع الأسعار معروضة بالجنيه المصري ما لم يُذكر خلاف ذلك</li>
                <li>نحتفظ بالحق في تغيير الأسعار دون إشعار مسبق</li>
                <li>الدفع مطلوب عند تأكيد الطلب</li>
                <li>نقبل طرق الدفع المختلفة المعروضة على الموقع</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>الشحن والتسليم</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>نقوم بالشحن إلى جميع أنحاء مصر</li>
                <li>مدة التسليم تتراوح من 3-7 أيام عمل</li>
                <li>رسوم الشحن تُحسب حسب الوزن والمنطقة</li>
                <li>العميل مسؤول عن استلام الطلب في الموعد المحدد</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>الإرجاع والاستبدال</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>يمكن إرجاع المنتجات خلال 14 يوم من تاريخ الاستلام</li>
                <li>يجب أن تكون المنتجات في حالتها الأصلية</li>
                <li>المنتجات المخصصة غير قابلة للإرجاع</li>
                <li>رسوم الإرجاع يتحملها العميل ما لم يكن هناك عيب في المنتج</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>الضمان</h2>
            <ul class='list-disc list-inside space-y-2'>
                <li>نقدم ضمان لمدة سنة على جميع منتجاتنا</li>
                <li>الضمان يغطي عيوب الصناعة فقط</li>
                <li>الضمان لا يغطي الأضرار الناتجة عن سوء الاستخدام</li>
                <li>يجب الاحتفاظ بفاتورة الشراء للاستفادة من الضمان</li>
            </ul>

            <h2 class='text-2xl font-bold mb-4'>المسؤولية</h2>
            <p>نحن غير مسؤولين عن أي أضرار مباشرة أو غير مباشرة قد تنتج عن استخدام موقعنا أو منتجاتنا، باستثناء ما ينص عليه القانون.</p>

            <h2 class='text-2xl font-bold mb-4'>تعديل الشروط</h2>
            <p>نحتفظ بالحق في تعديل هذه الشروط والأحكام في أي وقت. سيتم إشعار العملاء بأي تغييرات جوهرية.</p>

            <h2 class='text-2xl font-bold mb-4'>الاتصال بنا</h2>
            <p>إذا كان لديك أي أسئلة حول هذه الشروط والأحكام، يرجى التواصل معنا عبر:</p>
            <ul class='list-disc list-inside space-y-2'>
                <li>البريد الإلكتروني: {$contactEmail}</li>
                <li>الهاتف: {$contactPhone}</li>
            </ul>

            <p class='text-sm text-gray-600 mt-6'>آخر تحديث: {$currentDate}</p>
        </div>";
    }
}
