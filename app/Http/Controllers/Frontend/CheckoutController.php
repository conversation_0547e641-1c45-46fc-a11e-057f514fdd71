<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Product;
use App\Services\CurrencyService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CheckoutController extends Controller
{
    protected $currencyService;

    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\CurrencyService  $currencyService
     * @return void
     */
    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }

    public function index()
    {
        return view('frontend.checkout.index');
    }

    public function store(Request $request)
    {
        $request->validate([
            'shipping_name' => 'required|string|max:255',
            'shipping_address' => 'required|string|max:255',
            'shipping_city' => 'required|string|max:255',
            'shipping_country' => 'required|string|max:255',
            'shipping_phone' => 'required|string|max:255',
            'shipping_email' => 'required|email|max:255',
            'payment_method' => 'required|string|in:cash,stripe,paypal,bank_transfer',
        ]);

        // Get currency from session
        $currency = Session::get('currency', 'EGP');

        // Create order
        $order = new Order();
        $order->user_id = Auth::check() ? Auth::id() : null;
        $order->order_number = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $order->status = 'pending';
        $order->payment_status = 'pending';
        $order->payment_method = $request->payment_method;

        $cart = Session::get('cart');
        $total = 0;

        foreach ($cart as $item) {
            $product = Product::find($item['id']);
            $total += $product->price * $item['quantity'];
        }

        // Convert amounts if currency is not EGP
        if ($currency !== 'EGP') {
            $total = $this->currencyService->convert($total, 'EGP', $currency);
        }

        $order->total_amount = $total;
        $order->tax_amount = $total * 0.14; // 14% VAT
        $order->shipping_amount = $this->currencyService->convert(50, 'EGP', $currency); // Fixed shipping cost
        $order->discount_amount = 0;
        $order->currency = $currency;

        $order->shipping_name = $request->shipping_name;
        $order->shipping_address = $request->shipping_address;
        $order->shipping_city = $request->shipping_city;
        $order->shipping_country = $request->shipping_country;
        $order->shipping_phone = $request->shipping_phone;
        $order->shipping_email = $request->shipping_email;
        $order->shipping_postal_code = $request->shipping_postal_code;
        $order->order_notes = $request->order_notes;

        $order->is_guest = !Auth::check();
        $order->guest_email = Auth::check() ? null : $request->shipping_email;

        $order->save();

        // Create order items
        foreach ($cart as $item) {
            $product = Product::find($item['id']);
            $price = $product->price;

            // Convert price if currency is not EGP
            if ($currency !== 'EGP') {
                $price = $this->currencyService->convert($price, 'EGP', $currency);
            }

            $order->items()->create([
                'product_id' => $product->id,
                'quantity' => $item['quantity'],
                'price' => $price,
                'total' => $price * $item['quantity'],
            ]);
        }

        // Handle AJAX request for payment methods
        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'order_id' => $order->id,
                'message' => app()->getLocale() == 'ar' ? 'تم إنشاء الطلب بنجاح' : 'Order created successfully'
            ]);
        }

        // For cash and bank transfer, clear cart and redirect to success page
        if (in_array($request->payment_method, ['cash', 'bank_transfer'])) {
            // Clear cart
            Session::forget('cart');

            return redirect()->route('checkout.success', $order->id)->with('success',
                app()->getLocale() == 'ar' ? 'تم إنشاء الطلب بنجاح' : 'Order created successfully');
        }

        // For other payment methods, the JavaScript will handle the redirect
        return redirect()->route('checkout.success', $order->id);
    }

    public function success($id)
    {
        $order = Order::findOrFail($id);

        return view('frontend.checkout.success', compact('order'));
    }

    /**
     * Handle failed checkout.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function failed($id)
    {
        $order = Order::findOrFail($id);

        return view('frontend.checkout.failed', compact('order'));
    }
}
