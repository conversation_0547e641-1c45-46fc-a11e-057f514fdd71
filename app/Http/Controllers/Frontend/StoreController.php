<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Store;
use App\Models\SiteSetting;

class StoreController extends Controller
{
    /**
     * عرض صفحة المتاجر
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // جلب المتاجر النشطة من قاعدة البيانات
        $stores = Store::where('is_active', true)
                      ->orderBy('id', 'asc')
                      ->get();

        // جلب إعدادات الموقع
        $siteSettings = SiteSetting::first();

        return view('frontend.pages.stores', compact('stores', 'siteSettings'));
    }
}
