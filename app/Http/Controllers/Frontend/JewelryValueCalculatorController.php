<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\MetalPrice;

class JewelryValueCalculatorController extends Controller
{
    public function index()
    {
        // جلب أحدث الأسعار
        $latestPrices = $this->getLatestPrices();

        // إعداد الترجمات
        $translations = $this->getTranslations();

        // معلومات المعادن المدعومة
        $supportedMetals = $this->getSupportedMetals();

        return view('frontend.jewelry-value-calculator', compact(
            'latestPrices',
            'translations',
            'supportedMetals'
        ));
    }

    /**
     * جلب أحدث أسعار المعادن مع ترتيب منطقي
     */
    private function getLatestPrices()
    {
        // جلب الأسعار المفعلة لكل عيار من الذهب
        $goldPricesRaw = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->select('purity', 'price_per_gram', 'created_at')
            ->get()
            ->keyBy('purity');

        // ترتيب أسعار الذهب منطقي<|im_start|> (من الأعلى للأقل)
        $goldOrder = ['24K', '22K', '21K', '18K', '14K', '12K', '9K'];
        $goldPrices = collect();
        foreach ($goldOrder as $purity) {
            if ($goldPricesRaw->has($purity)) {
                $goldPrices->put($purity, $goldPricesRaw->get($purity));
            }
        }

        // جلب الأسعار المفعلة لكل عيار من الفضة
        $silverPricesRaw = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->select('purity', 'price_per_gram', 'created_at')
            ->get()
            ->keyBy('purity');

        // ترتيب أسعار الفضة منطقي<|im_start|> (من الأعلى للأقل)
        $silverOrder = ['999', '925', '900', '800', '600'];
        $silverPrices = collect();
        foreach ($silverOrder as $purity) {
            if ($silverPricesRaw->has($purity)) {
                $silverPrices->put($purity, $silverPricesRaw->get($purity));
            }
        }

        // تحديد آخر تحديث بطريقة أكثر دقة (بدون cache)
        $lastUpdate = MetalPrice::where('currency', 'EGP')
            ->where('is_active', true)
            ->latest('created_at')
            ->first(['created_at'])?->created_at;

        return [
            'gold' => $goldPrices,
            'silver' => $silverPrices,
            'last_update' => $lastUpdate ? $lastUpdate->locale('ar')->diffForHumans() : null,
        ];
    }

    /**
     * إعداد الترجمات
     */
    private function getTranslations()
    {
        return [
            'page_title' => __('jewelry_value.page_title'),
            'page_description' => __('jewelry_value.page_description'),
            'breadcrumb_home' => __('jewelry_value.breadcrumb.home'),
            'breadcrumb_calculator' => __('jewelry_value.breadcrumb.calculator'),

            'form' => [
                'title' => __('jewelry_value.form.title'),
                'metal_type' => __('jewelry_value.form.metal_type'),
                'purity' => __('jewelry_value.form.purity'),
                'weight' => __('jewelry_value.form.weight'),
                'description' => __('jewelry_value.form.description'),
                'add_item' => __('jewelry_value.form.add_item'),
                'clear_all' => __('jewelry_value.form.clear_all'),
                'select_metal' => __('jewelry_value.form.select_metal'),
                'select_purity' => __('jewelry_value.form.select_purity'),
                'weight_placeholder' => __('jewelry_value.form.weight_placeholder'),
                'description_placeholder' => __('jewelry_value.form.description_placeholder'),
            ],

            'results' => [
                'title' => __('jewelry_value.results.title'),
                'items_list' => __('jewelry_value.results.items_list'),
                'total_value' => __('jewelry_value.results.total_value'),
                'total_gold_weight' => __('jewelry_value.results.total_gold_weight'),
                'total_silver_weight' => __('jewelry_value.results.total_silver_weight'),
                'item_number' => __('jewelry_value.results.item_number'),
                'price_per_gram' => __('jewelry_value.results.price_per_gram'),
                'item_value' => __('jewelry_value.results.item_value'),
                'no_items' => __('jewelry_value.results.no_items'),
            ],

            'sidebar' => [
                'current_prices_title' => __('jewelry_value.sidebar.current_prices_title'),
                'gold_prices' => __('jewelry_value.sidebar.gold_prices'),
                'silver_prices' => __('jewelry_value.sidebar.silver_prices'),
                'last_update' => __('jewelry_value.sidebar.last_update'),
                'info_title' => __('jewelry_value.sidebar.info_title'),
                'info_content' => __('jewelry_value.sidebar.info_content'),
            ],

            'units' => [
                'egp' => __('jewelry_value.units.egp'),
                'grams' => __('jewelry_value.units.grams'),
            ],
        ];
    }

    /**
     * معلومات المعادن المدعومة
     */
    private function getSupportedMetals()
    {
        return [
            'gold' => [
                'name' => __('jewelry_value.metals.gold'),
                'icon' => '🥇',
                'purities' => [
                    '24K' => __('jewelry_value.purities.gold.24k'),
                    '22K' => __('jewelry_value.purities.gold.22k'),
                    '21K' => __('jewelry_value.purities.gold.21k'),
                    '18K' => __('jewelry_value.purities.gold.18k'),
                    '14K' => __('jewelry_value.purities.gold.14k'),
                    '12K' => __('jewelry_value.purities.gold.12k'),
                    '9K' => __('jewelry_value.purities.gold.9k'),
                ]
            ],
            'silver' => [
                'name' => __('jewelry_value.metals.silver'),
                'icon' => '🥈',
                'purities' => [
                    '999' => __('jewelry_value.purities.silver.999'),
                    '925' => __('jewelry_value.purities.silver.925'),
                    '900' => __('jewelry_value.purities.silver.900'),
                    '800' => __('jewelry_value.purities.silver.800'),
                    '600' => __('jewelry_value.purities.silver.600'),
                ]
            ]
        ];
    }
}
