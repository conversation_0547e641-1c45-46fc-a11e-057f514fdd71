<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;

class ProductController extends Controller
{
    public function index()
    {
        return view('frontend.products.index');
    }

    public function show($slug)
    {
        return view('frontend.products.show', compact('slug'));
    }

    public function filter(Request $request)
    {
        $query = Product::where('is_active', true);

        // Filter by category
        if ($request->has('category') && $request->category != '') {
            $category = Category::where('slug', $request->category)->first();
            if ($category) {
                $categoryIds = [$category->id];

                // Include child categories
                if ($category->children->count() > 0) {
                    $childIds = $category->children->pluck('id')->toArray();
                    $categoryIds = array_merge($categoryIds, $childIds);
                }

                $query->whereIn('category_id', $categoryIds);
            }
        }

        // Filter by price range
        if ($request->has('min_price') && $request->min_price != '') {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price != '') {
            $query->where('price', '<=', $request->max_price);
        }

        // Filter by metal type
        if ($request->has('metal_type') && $request->metal_type != '') {
            $query->where('metal_type', $request->metal_type);
        }

        // Filter by purity
        if ($request->has('purity') && $request->purity != '') {
            $query->where('purity', $request->purity);
        }

        // Sort products
        if ($request->has('sort') && $request->sort != '') {
            switch ($request->sort) {
                case 'price_asc':
                    $query->orderBy('price', 'asc');
                    break;
                case 'price_desc':
                    $query->orderBy('price', 'desc');
                    break;
                case 'newest':
                    $query->orderBy('created_at', 'desc');
                    break;
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                default:
                    $query->orderBy('created_at', 'desc');
            }
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $products = $query->with('category')->paginate(12);

        $categories = Category::where('is_active', true)->get();

        return view('frontend.products.index', compact('products', 'categories'));
    }
}
