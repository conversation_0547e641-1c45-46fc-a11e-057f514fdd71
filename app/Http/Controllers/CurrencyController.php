<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use App\Services\CurrencyService;

class CurrencyController extends Controller
{
    protected $currencyService;
    
    /**
     * Create a new controller instance.
     *
     * @param  \App\Services\CurrencyService  $currencyService
     * @return void
     */
    public function __construct(CurrencyService $currencyService)
    {
        $this->currencyService = $currencyService;
    }
    
    /**
     * Switch the application currency.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $currency
     * @return \Illuminate\Http\RedirectResponse
     */
    public function switch(Request $request, $currency)
    {
        // Check if the currency is supported
        $supportedCurrencies = $this->currencyService->getSupportedCurrencies();
        
        if (in_array($currency, $supportedCurrencies)) {
            Session::put('currency', $currency);
        }
        
        return redirect()->back();
    }
}
