<?php

namespace App\Livewire;

use Livewire\Component;

class CartCounter extends Component
{
    public $count = 0;

    protected $listeners = ['cart-updated' => 'updateCount', 'refresh-cart-count' => 'updateCount'];

    public function mount()
    {
        $this->updateCount();
    }

    public function updateCount()
    {
        // Get cart from session
        $cart = session()->get('cart', []);

        // Count cart items
        $this->count = count($cart);

        // Debug
        logger('Cart count updated: ' . $this->count);
        logger('Cart contents: ' . json_encode($cart));
    }

    public function render()
    {
        return view('livewire.cart-counter');
    }
}
