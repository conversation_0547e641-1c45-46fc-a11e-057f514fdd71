<?php

namespace App\Livewire;

use App\Models\Company;
use App\Models\CompanyProduct;
use App\Models\ProductType;
use App\Models\MetalPrice;
use App\Models\SiteSetting;
use Livewire\Component;

class CompanyProductSelector extends Component
{
    public $type; // 'سبيكة' أو 'عملة'
    public $selectedCompanyId = null;
    public $selectedProductTypeId = null;
    public $companies = [];
    public $productTypes = [];
    public $selectedProduct = null;
    public $currentPrices = [];
    public $defaultLogo = null;

    public function mount($type)
    {
        $this->type = $type;
        $this->loadDefaultLogo();
        $this->loadCompanies();
        $this->loadProductTypes();
        $this->loadCurrentPrices();
    }

    public function loadDefaultLogo()
    {
        $this->defaultLogo = SiteSetting::first()?->logo;
    }

    public function loadCompanies()
    {
        $this->companies = Company::where('is_active', true)
            ->with(['products.productType' => function ($query) {
                $query->where('type', $this->type);
            }])
            ->whereHas('products.productType', function ($query) {
                $query->where('type', $this->type);
            })
            ->orderBy('name')
            ->get();
    }

    public function loadProductTypes()
    {
        $this->productTypes = ProductType::where('type', $this->type)
            ->where('is_active', true)
            ->orderBy('weight')
            ->get();
    }

    public function loadCurrentPrices()
    {
        // الحصول على أسعار الذهب عيار 24 و 21 من قاعدة البيانات
        $gold24k = MetalPrice::where('metal_type', 'gold')
            ->where('purity', '24K')
            ->where('is_active', true)
            ->latest('created_at')
            ->first();

        $gold21k = MetalPrice::where('metal_type', 'gold')
            ->where('purity', '21K')
            ->where('is_active', true)
            ->latest('created_at')
            ->first();

        $this->currentPrices = [
            'gold_24k_buy' => $gold24k?->purchase_price_per_gram ?? 5490, // سعر الشراء
            'gold_24k_sell' => $gold24k?->price_per_gram ?? 5500, // سعر البيع
            'gold_21k_buy' => $gold21k?->purchase_price_per_gram ?? 4800,
            'gold_21k_sell' => $gold21k?->price_per_gram ?? 4810,
        ];
    }

    public function updatedSelectedCompanyId()
    {
        $this->selectedProductTypeId = null;
        $this->selectedProduct = null;

        // إعادة تحميل أنواع المنتجات للشركة المختارة
        if ($this->selectedCompanyId) {
            $this->loadProductTypesForCompany();
        }
    }

    public function loadProductTypesForCompany()
    {
        if (!$this->selectedCompanyId) {
            $this->productTypes = [];
            return;
        }

        $this->productTypes = ProductType::where('type', $this->type)
            ->where('is_active', true)
            ->whereHas('companyProducts', function ($query) {
                $query->where('company_id', $this->selectedCompanyId);
            })
            ->orderBy('weight')
            ->get();
    }

    public function updatedSelectedProductTypeId()
    {
        if ($this->selectedCompanyId && $this->selectedProductTypeId) {
            $this->loadSelectedProduct();
        } else {
            $this->selectedProduct = null;
        }
    }

    public function loadSelectedProduct()
    {
        $this->selectedProduct = CompanyProduct::with(['company', 'productType'])
            ->where('company_id', $this->selectedCompanyId)
            ->where('product_type_id', $this->selectedProductTypeId)
            ->first();
    }

    public function calculatePrices()
    {
        if (!$this->selectedProduct || empty($this->currentPrices)) {
            return null;
        }

        $product = $this->selectedProduct;

        // التأكد من تحميل العلاقة
        if (!$product->relationLoaded('productType') || !$product->productType) {
            $product->load('productType');
        }

        if (!$product->productType) {
            return null;
        }

        $weight = $product->productType->weight;
        $purity = $product->productType->metal_purity;

        // تحديد سعر الذهب حسب العيار
        $goldPriceSell = $purity == '24' ? $this->currentPrices['gold_24k_sell'] : $this->currentPrices['gold_21k_sell'];
        $goldPriceBuy = $purity == '24' ? $this->currentPrices['gold_24k_buy'] : $this->currentPrices['gold_21k_buy'];

        // حساب سعر الذهب الخام للجرام الواحد
        $rawGoldPricePerGram = $goldPriceSell;

        // حساب السعر الشامل للجرام الواحد (المصنعية والضريبة)
        $totalPricePerGram = $rawGoldPricePerGram + $product->manufacturing_cost_per_gram;

        // حساب سعر إعادة البيع للجرام الواحد (سعر الشراء + قيمة الاسترداد)
        $resalePricePerGram = $goldPriceBuy + $product->refund_value_per_gram;

        // حساب الأسعار الإجمالية للوزن المحدد
        $totalRawGoldPrice = $rawGoldPricePerGram * $weight;
        $totalPrice = $totalPricePerGram * $weight;
        $totalResalePrice = $resalePricePerGram * $weight;

        return [
            'raw_gold_price_per_gram' => $rawGoldPricePerGram,
            'raw_gold_price' => $totalRawGoldPrice,
            'manufacturing_per_gram' => $product->manufacturing_cost_per_gram,
            'total_price' => $totalPrice,
            'refund_per_gram' => $product->refund_value_per_gram,
            'resale_price' => $totalResalePrice,
        ];
    }

    public function render()
    {
        $calculatedPrices = $this->calculatePrices();

        return view('livewire.company-product-selector', [
            'calculatedPrices' => $calculatedPrices
        ]);
    }
}
