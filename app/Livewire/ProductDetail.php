<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use App\Models\MetalPrice;

use App\Traits\CategorySettingsHelper;
use App\Traits\SettingsHelper;

class ProductDetail extends Component
{
    use CategorySettingsHelper;
    use SettingsHelper;
    public $slug;
    public $product;
    public $relatedProducts = [];
    public $metalPrice;
    public $quantity = 1;

    // إعدادات الموقع
    public $whatsappPhone;
    public $showWishlist;
    public $showRatings;
    public $showWhatsapp;

    public function mount($slug)
    {
        $this->slug = $slug;
        $this->loadSiteSettings();
        $this->loadProduct();
    }

    public function loadSiteSettings()
    {
        // جلب إعدادات الموقع
        $this->whatsappPhone = $this->getWhatsappPhone();
        $this->showWishlist = $this->shouldShowWishlist();
        $this->showRatings = $this->shouldShowRatings();
        $this->showWhatsapp = $this->shouldShowWhatsapp();
    }

    public function loadProduct()
    {
        // Get product details with all relationships
        $this->product = Product::where('slug', $this->slug)
            ->where('is_active', true)
            ->with([
                'category',
                'images' => function($query) {
                    $query->orderBy('sort_order');
                },
                'primaryImage',
                'metal',
                'metalPurity'
            ])
            ->firstOrFail();

        // Get related products with smart algorithm
        $relatedQuery = Product::where('id', '!=', $this->product->id)
            ->where('is_active', true)
            ->with(['primaryImage', 'category', 'metal', 'metalPurity'])
            ->withCount('reviews');

        // Priority 1: Same category and metal type
        $sameMetalProducts = (clone $relatedQuery)
            ->where('category_id', $this->product->category_id)
            ->where('metal_type_id', $this->product->metal_type_id)
            ->take(3)
            ->get();

        // Priority 2: Same category, different metal
        $sameCategoryProducts = (clone $relatedQuery)
            ->where('category_id', $this->product->category_id)
            ->whereNotIn('id', $sameMetalProducts->pluck('id'))
            ->take(2)
            ->get();

        // Priority 3: Same metal type, different category
        $sameMetalDiffCategory = (clone $relatedQuery)
            ->where('metal_type_id', $this->product->metal_type_id)
            ->whereNotIn('id', $sameMetalProducts->pluck('id')->merge($sameCategoryProducts->pluck('id')))
            ->take(1)
            ->get();

        // Combine all related products
        $this->relatedProducts = $sameMetalProducts
            ->merge($sameCategoryProducts)
            ->merge($sameMetalDiffCategory)
            ->take(6);

        // إضافة تقييم افتراضي وإعدادات للمنتجات ذات الصلة
        foreach ($this->relatedProducts as $relatedProduct) {
            $relatedProduct->rating = $relatedProduct->reviews_count > 0 ? rand(4, 5) : rand(3, 5);

            // تطبيق إعدادات الفئة للمنتجات ذات الصلة
            $categorySettings = $this->getCategorySettings($relatedProduct);
            $relatedProduct->show_price = $categorySettings['show_price'];

            // تطبيق إعدادات الموقع على كل منتج
            $relatedProduct->site_show_wishlist = $this->showWishlist;
            $relatedProduct->site_show_ratings = $this->showRatings;
            $relatedProduct->site_show_whatsapp = $this->showWhatsapp;
            $relatedProduct->whatsapp_phone = $this->whatsappPhone;
        }

        // إضافة تقييم افتراضي وإعدادات للمنتج الحالي
        $this->product->rating = rand(4, 5);

        // تطبيق إعدادات الفئة للمنتج الحالي
        $categorySettings = $this->getCategorySettings($this->product);
        $this->product->show_price = $categorySettings['show_price'];

        // Get latest gold price for product metal type
        $this->metalPrice = MetalPrice::where('metal_type', $this->product->metal_type)
            ->where('purity', $this->product->purity)
            ->where('currency', 'EGP')
            ->whereDate('price_date', now())
            ->first();
    }

    public function incrementQuantity()
    {
        $this->quantity++;
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }

    public function addToCart()
    {
        // Implementación real de añadir al carrito
        $cart = session()->get('cart', []);

        $rowId = uniqid();
        $cart[$rowId] = [
            'id' => $this->product->id,
            'name' => $this->product->name_ar,
            'quantity' => $this->quantity,
            'price' => $this->product->price,
            'weight' => $this->product->weight,
            'options' => [
                'image' => $this->product->image,
                'slug' => $this->product->slug,
                'metal_type' => $this->product->metal_type,
                'purity' => $this->product->purity,
            ]
        ];

        // Guardar el carrito en la sesión
        session()->put('cart', $cart);

        // Depuración
        logger('Product added to cart from detail page: ' . $this->product->id);
        logger('Cart after adding: ' . json_encode($cart));

        // Emitir eventos
        $this->dispatch('cart-updated');
        $this->dispatch('refresh-cart-count');
        $this->dispatch('notify', [
            'message' => 'تمت إضافة المنتج إلى سلة التسوق',
            'type' => 'success'
        ]);
    }

    public function addToWishlist()
    {
        // التحقق مما إذا كانت المفضلة متاحة
        if (!$this->shouldShowWishlist()) {
            $this->dispatch('notify', [
                'message' => 'عذراً، المفضلة غير متاحة حالياً',
                'type' => 'error'
            ]);
            return;
        }

        // التحقق من تسجيل دخول المستخدم
        if (!\Illuminate\Support\Facades\Auth::check()) {
            // إعادة توجيه المستخدم لصفحة تسجيل الدخول
            return redirect()->route('login');
        }

        $user = \Illuminate\Support\Facades\Auth::user();

        // Verificar si el producto ya está en la lista de deseos
        if (!$user->wishlist()->where('product_id', $this->product->id)->exists()) {
            // Añadir el producto a la lista de deseos
            $user->wishlist()->create([
                'product_id' => $this->product->id
            ]);

            // Depuración
            logger('Product added to wishlist from detail page: ' . $this->product->id . ' for user ' . $user->id);

            // Emitir eventos
            $this->dispatch('notify', [
                'message' => 'تمت إضافة المنتج إلى قائمة الرغبات',
                'type' => 'success'
            ]);

            // Emitir evento para actualizar el contador de la lista de deseos
            $this->dispatch('wishlistUpdated');
            $this->dispatch('refresh-wishlist-count');
        } else {
            $this->dispatch('notify', [
                'message' => 'المنتج موجود بالفعل في قائمة الرغبات',
                'type' => 'info'
            ]);
        }
    }

    public function render()
    {
        $viewData = $this->getViewSettings();

        // إضافة متغيرات إضافية للـ View
        $viewData['whatsappPhone'] = $this->whatsappPhone;
        $viewData['showWishlist'] = $this->showWishlist;
        $viewData['showRatings'] = $this->showRatings;
        $viewData['showWhatsapp'] = $this->showWhatsapp;

        return view('livewire.product-detail', $viewData);
    }
}
