<?php

namespace App\Livewire;

use App\Models\Company;
use Livewire\Component;
use Livewire\WithPagination;

/**
 * مكون Livewire لعرض منتجات شركة معينة
 */
class CompanyProductList extends Component
{
    use WithPagination;

    /**
     * الشركة المحددة
     */
    public Company $company;

    /**
     * خاصية البحث
     */
    public string $search = '';

    /**
     * ترتيب النتائج
     */
    public string $sortBy = 'created_at';
    public string $sortDirection = 'desc';

    /**
     * فلتر الوزن
     */
    public ?float $minWeight = null;
    public ?float $maxWeight = null;

    /**
     * فلتر نوع المنتج
     */
    public string $productType = 'all';

    /**
     * فلتر العيار
     */
    public string $metalPurity = 'all';

    /**
     * التبويب النشط
     */
    public string $activeTab = 'سبائك';

    /**
     * عدد العناصر في الصفحة
     */
    public int $perPage = 12;

    /**
     * تهيئة المكون
     */
    public function mount(Company $company): void
    {
        $this->company = $company;
    }

    /**
     * إعادة تعيين الصفحة عند تغيير البحث
     */
    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    /**
     * إعادة تعيين الصفحة عند تغيير فلاتر الوزن
     */
    public function updatingMinWeight(): void
    {
        $this->resetPage();
    }

    public function updatingMaxWeight(): void
    {
        $this->resetPage();
    }

    /**
     * إعادة تعيين الصفحة عند تغيير فلاتر نوع المنتج والعيار
     */
    public function updatingProductType(): void
    {
        $this->resetPage();
    }

    public function updatingMetalPurity(): void
    {
        $this->resetPage();
    }

    /**
     * تغيير التبويب النشط
     */
    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;

        // تحديث فلتر نوع المنتج حسب التبويب
        if ($tab === 'سبائك') {
            $this->productType = 'سبيكة';
            $this->metalPurity = '24'; // السبائك عادة عيار 24
        } elseif ($tab === 'عملات') {
            $this->productType = 'عملة';
            $this->metalPurity = '21'; // العملات عادة عيار 21
        } else {
            $this->productType = 'all';
            $this->metalPurity = 'all';
        }

        $this->resetPage();
    }

    /**
     * تغيير ترتيب النتائج
     */
    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    /**
     * مسح جميع الفلاتر
     */
    public function clearFilters(): void
    {
        $this->search = '';
        $this->minWeight = null;
        $this->maxWeight = null;
        $this->productType = 'all';
        $this->metalPurity = 'all';
        $this->activeTab = 'الكل';
        $this->resetPage();
    }

    /**
     * الحصول على منتجات الشركة مع التصفية والترتيب
     */
    public function getProductsProperty()
    {
        return $this->company->products()
            ->with('productType')
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhereHas('productType', function ($q) {
                          $q->where('name', 'like', '%' . $this->search . '%');
                      });
            })
            ->when($this->minWeight, function ($query) {
                $query->whereHas('productType', function ($q) {
                    $q->where('weight', '>=', $this->minWeight);
                });
            })
            ->when($this->maxWeight, function ($query) {
                $query->whereHas('productType', function ($q) {
                    $q->where('weight', '<=', $this->maxWeight);
                });
            })
            ->when($this->productType !== 'all', function ($query) {
                $query->whereHas('productType', function ($q) {
                    $q->where('type', $this->productType);
                });
            })
            ->when($this->metalPurity !== 'all', function ($query) {
                $query->whereHas('productType', function ($q) {
                    $q->where('metal_purity', $this->metalPurity);
                });
            })
            ->orderBy('company_products.' . $this->sortBy, $this->sortDirection)
            ->paginate($this->perPage);
    }

    /**
     * الحصول على إحصائيات المنتجات حسب النوع
     */
    public function getProductStatsProperty()
    {
        $allProducts = $this->company->products()->with('productType')->get();

        return [
            'total' => $allProducts->count(),
            'سبائك' => $allProducts->filter(function ($product) {
                return $product->productType?->type === 'سبيكة';
            })->count(),
            'عملات' => $allProducts->filter(function ($product) {
                return $product->productType?->type === 'عملة';
            })->count(),
            'عيار_24' => $allProducts->filter(function ($product) {
                return $product->productType?->metal_purity === '24';
            })->count(),
            'عيار_21' => $allProducts->filter(function ($product) {
                return $product->productType?->metal_purity === '21';
            })->count(),
            'عيار_18' => $allProducts->filter(function ($product) {
                return $product->productType?->metal_purity === '18';
            })->count(),
        ];
    }

    /**
     * عرض المكون
     */
    public function render()
    {
        return view('livewire.company-product-list', [
            'products' => $this->products,
            'productStats' => $this->productStats,
        ]);
    }
}
