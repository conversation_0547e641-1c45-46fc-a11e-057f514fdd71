<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use App\Models\Product;
use App\Models\Category;
use App\Traits\SettingsHelper;
use App\Traits\CategorySettingsHelper;

class CategoryProducts extends Component
{
    use WithPagination;
    use SettingsHelper;
    use CategorySettingsHelper;

    // خصائص الفئة
    public $categorySlug;
    public $category;

    // خصائص الفلترة
    public $minPrice = null;
    public $maxPrice = null;
    public $metalType = '';
    public $purity = '';
    public $sort = 'newest';

    // خصائص الفلترة المتاحة
    public $availableMetalTypes = [];
    public $availablePurities = [];
    public $priceRange = [];

    // خصائص إضافية
    public $search = '';
    public $perPage = 12;

    // إعدادات الموقع
    public $whatsappPhone;
    public $showWishlist;
    public $showRatings;
    public $showWhatsapp;

    // تحديث عنوان URL عند تغيير الفلاتر
    protected $queryString = [
        'minPrice' => ['except' => null],
        'maxPrice' => ['except' => null],
        'metalType' => ['except' => ''],
        'purity' => ['except' => ''],
        'sort' => ['except' => 'newest'],
        'search' => ['except' => ''],
    ];



    // تهيئة المكون
    public function mount($categorySlug)
    {
        $this->categorySlug = $categorySlug;
        $this->category = Category::where('slug', $categorySlug)->firstOrFail();

        // تحميل إعدادات الموقع
        $this->loadSiteSettings();

        // تحميل خيارات الفلترة المتاحة
        $this->loadFilterOptions();
    }

    public function loadSiteSettings()
    {
        // جلب إعدادات الموقع باستخدام SettingsHelper
        $this->whatsappPhone = $this->getWhatsappPhone();
        $this->showWishlist = $this->shouldShowWishlist();
        $this->showRatings = $this->shouldShowRatings();
        $this->showWhatsapp = $this->shouldShowWhatsapp();
    }

    // معالجة تغيير الفلاتر من كومبونانت الفلاتر
    #[On('filtersChanged')]
    public function handleFiltersChanged($filters)
    {
        $this->search = $filters['search'] ?? '';
        $this->minPrice = $filters['minPrice'];
        $this->maxPrice = $filters['maxPrice'];
        $this->metalType = $filters['metalType'] ?? '';
        $this->purity = $filters['purity'] ?? '';
        $this->sort = $filters['sort'] ?? 'newest';

        // إعادة تعيين الصفحة للصفحة الأولى عند تغيير الفلاتر
        $this->resetPage();
    }

    #[On('filtersUpdated')]
    public function handleFiltersUpdated($filters)
    {
        // يمكن استخدامها لأي معالجة إضافية
        $this->handleFiltersChanged($filters);
    }

    /**
     * تحميل خيارات الفلترة المتاحة من قاعدة البيانات
     */
    private function loadFilterOptions()
    {
        // الحصول على المنتجات في هذه الفئة
        $query = $this->getCategoryProductsQuery();

        // الحصول على أنواع المعادن المتاحة
        $this->availableMetalTypes = $query->clone()
            ->join('metal_types', 'products.metal_type_id', '=', 'metal_types.id')
            ->where('products.is_active', true)
            ->where('metal_types.is_active', true)
            ->whereNotNull('products.metal_type_id')
            ->distinct()
            ->pluck('metal_types.name')
            ->filter()
            ->toArray();

        // الحصول على العيارات المتاحة
        $this->availablePurities = $query->clone()
            ->join('metal_purities', 'products.metal_purity_id', '=', 'metal_purities.id')
            ->where('products.is_active', true)
            ->where('metal_purities.is_active', true)
            ->whereNotNull('products.metal_purity_id')
            ->distinct()
            ->pluck('metal_purities.name')
            ->filter()
            ->toArray();

        // الحصول على نطاق الأسعار
        $priceStats = $query->clone()
            ->selectRaw('MIN(price) as min_price, MAX(price) as max_price')
            ->first();

        if ($priceStats) {
            $this->priceRange = [
                'min' => floor($priceStats->min_price),
                'max' => ceil($priceStats->max_price)
            ];

            // تعيين القيم الافتراضية إذا لم يتم تعيينها
            if ($this->minPrice === null) {
                $this->minPrice = $this->priceRange['min'];
            }

            if ($this->maxPrice === null) {
                $this->maxPrice = $this->priceRange['max'];
            }
        }
    }

    // إعادة تعيين الصفحة عند تغيير الفلاتر
    public function updatedMinPrice() { $this->resetPage(); }
    public function updatedMaxPrice() { $this->resetPage(); }
    public function updatedMetalType() { $this->resetPage(); }
    public function updatedPurity() { $this->resetPage(); }
    public function updatedSearch() { $this->resetPage(); }
    public function updatedSort() { $this->resetPage(); }

    // إعادة تعيين جميع الفلاتر
    public function resetFilters()
    {
        $this->reset(['minPrice', 'maxPrice', 'metalType', 'purity', 'sort', 'search']);
        $this->resetPage();
    }

    /**
     * الحصول على استعلام المنتجات في هذه الفئة
     */
    private function getCategoryProductsQuery()
    {
        // بناء استعلام المنتجات
        $query = Product::where('products.is_active', true);

        // فلترة حسب الفئة
        $categoryIds = [$this->category->id];

        // تضمين الفئات الفرعية
        if ($this->category->children->count() > 0) {
            $childIds = $this->category->children->pluck('id')->toArray();
            $categoryIds = array_merge($categoryIds, $childIds);
        }

        return $query->whereIn('products.category_id', $categoryIds);
    }

    public function getActiveFiltersCountProperty()
    {
        return ($this->minPrice ? 1 : 0) +
               ($this->maxPrice ? 1 : 0) +
               ($this->metalType ? 1 : 0) +
               ($this->purity ? 1 : 0) +
               ($this->search ? 1 : 0);
    }

    public function render()
    {

        // بناء استعلام المنتجات
        $query = $this->getCategoryProductsQuery();

        // فلترة حسب نطاق السعر
        if ($this->minPrice) {
            $query->where('price', '>=', $this->minPrice);
        }

        if ($this->maxPrice) {
            $query->where('price', '<=', $this->maxPrice);
        }

        // فلترة حسب نوع المعدن
        if ($this->metalType) {
            $query->whereHas('metal', function($q) {
                $q->where('name', $this->metalType);
            });
        }

        // فلترة حسب العيار
        if ($this->purity) {
            $query->whereHas('metalPurity', function($q) {
                $q->where('name', $this->purity);
            });
        }

        // فلترة حسب البحث
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name_ar', 'like', '%' . $this->search . '%')
                  ->orWhere('name_en', 'like', '%' . $this->search . '%')
                  ->orWhere('description_ar', 'like', '%' . $this->search . '%')
                  ->orWhere('description_en', 'like', '%' . $this->search . '%');
            });
        }

        // ترتيب المنتجات
        switch ($this->sort) {
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // الحصول على المنتجات مع الفئات وعدد التقييمات
        $products = $query->with('category')->withCount('reviews')->paginate($this->perPage);

        // إضافة تقييم افتراضي وتطبيق الإعدادات
        foreach ($products as $product) {
            $product->rating = $product->reviews_count > 0 ? rand(4, 5) : rand(3, 5);

            // تطبيق إعدادات الفئة
            $categorySettings = $this->getCategorySettings($product);
            $product->show_price = $categorySettings['show_price'];

            // تطبيق إعدادات الموقع
            $product->site_show_wishlist = $this->showWishlist;
            $product->site_show_ratings = $this->showRatings;
            $product->site_show_whatsapp = $this->showWhatsapp;
            $product->whatsapp_phone = $this->whatsappPhone;
        }

        // الحصول على المعادن والعيارات
        $metals = \App\Models\MetalType::where('is_active', true)->get();
        $metalPurities = \App\Models\MetalPurity::where('is_active', true)->get();

        return view('livewire.category-products', array_merge([
            'products' => $products,
            'category' => $this->category,
            'metals' => $metals,
            'metalPurities' => $metalPurities,
            'showPrice' => $this->category->show_price,
            'activeFiltersCount' => $this->activeFiltersCount,
        ], $this->getViewSettings()));
    }
}
