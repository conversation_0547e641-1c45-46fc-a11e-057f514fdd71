<?php

namespace App\Livewire;

use App\Models\Company;
use Livewire\Component;

/**
 * مكون Livewire لعرض تفاصيل شركة معينة
 */
class CompanyDetails extends Component
{
    /**
     * الشركة المحددة
     */
    public Company $company;

    /**
     * تهيئة المكون
     */
    public function mount(Company $company): void
    {
        $this->company = $company->load('products.productType');
    }

    /**
     * الحصول على إحصائيات الشركة
     */
    public function getStatsProperty()
    {
        $products = $this->company->products()->with('productType')->get();

        return [
            'total_products' => $products->count(),
            'bars_count' => $products->filter(fn($p) => $p->productType?->type === 'سبيكة')->count(),
            'coins_count' => $products->filter(fn($p) => $p->productType?->type === 'عملة')->count(),
            'total_weight' => $products->sum(fn($p) => $p->productType?->weight ?? 0),
            'avg_weight' => $products->avg(fn($p) => $p->productType?->weight ?? 0),
            'product_types' => $products->groupBy(fn($p) => $p->productType?->type)->map->count(),
            'metal_purities' => $products->groupBy(fn($p) => $p->productType?->metal_purity)->map->count(),
        ];
    }

    /**
     * عرض المكون
     */
    public function render()
    {
        return view('livewire.company-details', [
            'recentProducts' => $this->company->products()->with('productType')->latest()->take(6)->get(),
            'stats' => $this->stats,
        ]);
    }
}
