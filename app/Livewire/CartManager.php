<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use Illuminate\Support\Facades\Session;
use App\Traits\SettingsHelper;

class CartManager extends Component
{
    use SettingsHelper;

    public $productId;
    public $quantity = 1;
    public $cartCount = 0;
    public $isInCart = false;
    public $isDisplayOnlyMode = false;

    public function mount($productId = null)
    {
        $this->productId = $productId;
        $this->isDisplayOnlyMode = $this->isDisplayOnlyMode();
        $this->updateCartStatus();
    }

    public function addToCart()
    {
        try {
            // التحقق من وضع العرض فقط
            if ($this->isDisplayOnlyMode) {
                $this->dispatch('show-message', [
                    'type' => 'info',
                    'message' => 'الموقع في وضع العرض فقط حالياً'
                ]);
                return;
            }

            $product = Product::findOrFail($this->productId);

            // التحقق من المخزون
            if ($product->stock_quantity < $this->quantity) {
                $this->dispatch('show-message', [
                    'type' => 'error',
                    'message' => 'الكمية المطلوبة غير متوفرة في المخزون'
                ]);
                return;
            }

            $cart = Session::get('cart', []);

            // البحث عن المنتج في السلة
            $existingRowId = null;
            foreach ($cart as $rowId => $item) {
                if ($item['id'] == $product->id) {
                    $existingRowId = $rowId;
                    break;
                }
            }

            if ($existingRowId) {
                // تحديث الكمية إذا كان المنتج موجود
                $cart[$existingRowId]['quantity'] += $this->quantity;
                $message = 'تم تحديث كمية المنتج في السلة';
            } else {
                // إضافة منتج جديد
                $rowId = uniqid();
                $cart[$rowId] = [
                    'id' => $product->id,
                    'name' => $product->name_ar,
                    'quantity' => $this->quantity,
                    'price' => $product->price,
                    'weight' => $product->weight ?? 0,
                    'options' => [
                        'image' => $product->image,
                        'slug' => $product->slug,
                        'metal_type' => $product->metal->name_ar ?? '',
                        'purity' => $product->metalPurity->name_ar ?? '',
                    ]
                ];
                $message = 'تم إضافة المنتج إلى السلة بنجاح';
            }

            Session::put('cart', $cart);
            $this->updateCartStatus();

            $this->dispatch('show-message', [
                'type' => 'success',
                'message' => $message
            ]);

            $this->dispatch('cart-updated', ['count' => $this->cartCount]);

        } catch (\Exception $e) {
            $this->dispatch('show-message', [
                'type' => 'error',
                'message' => 'حدث خطأ أثناء إضافة المنتج إلى السلة'
            ]);
        }
    }

    public function removeFromCart()
    {
        try {
            $cart = Session::get('cart', []);

            foreach ($cart as $rowId => $item) {
                if ($item['id'] == $this->productId) {
                    unset($cart[$rowId]);
                    break;
                }
            }

            Session::put('cart', $cart);
            $this->updateCartStatus();

            $this->dispatch('show-message', [
                'type' => 'success',
                'message' => 'تم إزالة المنتج من السلة'
            ]);

            $this->dispatch('cart-updated', ['count' => $this->cartCount]);

        } catch (\Exception $e) {
            $this->dispatch('show-message', [
                'type' => 'error',
                'message' => 'حدث خطأ أثناء إزالة المنتج من السلة'
            ]);
        }
    }

    public function updateCartStatus()
    {
        $cart = Session::get('cart', []);
        $this->cartCount = array_sum(array_column($cart, 'quantity'));

        // التحقق من وجود المنتج في السلة
        $this->isInCart = false;
        foreach ($cart as $item) {
            if ($item['id'] == $this->productId) {
                $this->isInCart = true;
                break;
            }
        }
    }

    public function render()
    {
        return view('livewire.cart-manager');
    }
}
