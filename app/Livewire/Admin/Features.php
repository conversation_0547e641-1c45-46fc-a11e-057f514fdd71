<?php

namespace App\Livewire\Admin;

use App\Models\Feature;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\WithFileUploads;

class Features extends Component
{
    use WithPagination;

    public $title_ar;
    public $title_en;
    public $description_ar;
    public $description_en;
    public $icon;
    public $order = 1;
    public $is_active = true;
    public $feature_id;
    public $isEditing = false;
    public $confirmingDelete = false;
    public $search = '';

    protected $rules = [
        'title_ar' => 'required|string|max:255',
        'title_en' => 'required|string|max:255',
        'description_ar' => 'required|string',
        'description_en' => 'required|string',
        'icon' => 'nullable|string|max:255',
        'order' => 'required|integer|min:1',
        'is_active' => 'boolean',
    ];

    public function mount()
    {
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->title_ar = '';
        $this->title_en = '';
        $this->description_ar = '';
        $this->description_en = '';
        $this->icon = 'fas fa-gem';
        $this->order = Feature::max('order') + 1 ?? 1;
        $this->is_active = true;
        $this->feature_id = null;
        $this->isEditing = false;
        $this->confirmingDelete = false;
        $this->resetValidation();
    }

    public function save()
    {
        $this->validate();

        if ($this->isEditing) {
            $feature = Feature::findOrFail($this->feature_id);
            $feature->update([
                'title_ar' => $this->title_ar,
                'title_en' => $this->title_en,
                'description_ar' => $this->description_ar,
                'description_en' => $this->description_en,
                'icon' => $this->icon,
                'order' => $this->order,
                'is_active' => $this->is_active,
            ]);

            session()->flash('message', 'تم تحديث الميزة بنجاح.');
        } else {
            Feature::create([
                'title_ar' => $this->title_ar,
                'title_en' => $this->title_en,
                'description_ar' => $this->description_ar,
                'description_en' => $this->description_en,
                'icon' => $this->icon,
                'order' => $this->order,
                'is_active' => $this->is_active,
            ]);

            session()->flash('message', 'تم إضافة الميزة بنجاح.');
        }

        $this->resetForm();
    }

    public function edit($id)
    {
        $this->isEditing = true;
        $this->feature_id = $id;
        $feature = Feature::findOrFail($id);

        $this->title_ar = $feature->title_ar;
        $this->title_en = $feature->title_en;
        $this->description_ar = $feature->description_ar;
        $this->description_en = $feature->description_en;
        $this->icon = $feature->icon;
        $this->order = $feature->order;
        $this->is_active = $feature->is_active;
    }

    public function confirmDelete($id)
    {
        $this->confirmingDelete = true;
        $this->feature_id = $id;
    }

    public function delete()
    {
        Feature::findOrFail($this->feature_id)->delete();
        session()->flash('message', 'تم حذف الميزة بنجاح.');
        $this->confirmingDelete = false;
        $this->resetForm();
    }

    public function cancelDelete()
    {
        $this->confirmingDelete = false;
    }

    public function toggleActive($id)
    {
        $feature = Feature::findOrFail($id);
        $feature->update([
            'is_active' => !$feature->is_active
        ]);
    }

    public function render()
    {
        $features = Feature::when($this->search, function($query) {
                $query->where('title_ar', 'like', '%' . $this->search . '%')
                    ->orWhere('title_en', 'like', '%' . $this->search . '%');
            })
            ->orderBy('order')
            ->paginate(10);

        return view('livewire.admin.features', [
            'features' => $features
        ]);
    }
}
