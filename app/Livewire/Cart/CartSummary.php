<?php

namespace App\Livewire\Cart;

use Livewire\Component;
use App\Services\CartService;

class CartSummary extends Component
{
    public $cart;
    public $couponCode = '';
    public $couponError = '';
    public $couponSuccess = '';

    protected $cartService;

    protected $listeners = ['cart-updated' => 'refreshCart'];

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount()
    {
        $this->refreshCart();
    }

    public function refreshCart()
    {
        $this->cart = $this->cartService->getCart();
    }

    public function applyCoupon()
    {
        if (empty($this->couponCode)) {
            $this->couponError = 'يرجى إدخال كود الخصم';
            $this->couponSuccess = '';
            return;
        }

        // Here you would validate the coupon code
        // For now, we'll just apply a simple 10% discount
        try {
            $this->cartService->applyCoupon($this->couponCode);
            $this->refreshCart();
            $this->couponSuccess = 'تم تطبيق كود الخصم بنجاح';
            $this->couponError = '';
            $this->dispatch('cart-updated');
        } catch (\Exception $exception) {
            $this->couponError = 'كود الخصم غير صالح: ' . $exception->getMessage();
            $this->couponSuccess = '';
        }
    }

    public function removeCoupon()
    {
        $this->cartService->removeCoupon();
        $this->refreshCart();
        $this->couponCode = '';
        $this->couponSuccess = '';
        $this->couponError = '';
        $this->dispatch('cart-updated');
    }

    public function proceedToCheckout()
    {
        return redirect()->route('checkout');
    }

    public function render()
    {
        return view('livewire.cart.cart-summary');
    }
}
