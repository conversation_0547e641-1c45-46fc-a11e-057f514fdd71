<?php

namespace App\Livewire\Cart;

use Livewire\Component;
use App\Models\Product;

class SessionCartPage extends Component
{
    public $cartItems = [];
    public $subtotal = 0;
    public $tax = 0;
    public $shipping = 0;
    public $total = 0;
    public $couponCode = '';
    public $couponError = '';
    public $couponSuccess = '';
    public $discount = 0;

    public function mount()
    {
        $this->loadCart();
    }

    public function loadCart()
    {
        $cart = session()->get('cart', []);
        $this->cartItems = $cart;
        
        // Calcular totales
        $this->calculateTotals();
        
        // Depuración
        logger('Cart loaded from session: ' . count($this->cartItems) . ' items');
        logger('Cart contents: ' . json_encode($this->cartItems));
    }

    public function calculateTotals()
    {
        $this->subtotal = 0;
        
        foreach ($this->cartItems as $item) {
            $this->subtotal += $item['price'] * $item['quantity'];
        }
        
        $this->tax = $this->subtotal * 0.14; // 14% impuesto para Egipto
        $this->shipping = $this->subtotal > 1000 ? 0 : 50; // Envío gratis para pedidos superiores a 1000 EGP
        
        // Aplicar descuento si hay un cupón
        if (!empty($this->couponCode)) {
            $this->discount = $this->subtotal * 0.1; // 10% de descuento
        } else {
            $this->discount = 0;
        }
        
        $this->total = $this->subtotal + $this->tax + $this->shipping - $this->discount;
    }

    public function updateCartItem($rowId, $quantity)
    {
        $cart = session()->get('cart', []);
        
        if (isset($cart[$rowId])) {
            $cart[$rowId]['quantity'] = max(1, $quantity);
            session()->put('cart', $cart);
            
            // Depuración
            logger('Cart item updated: ' . $rowId . ' - Quantity: ' . $quantity);
            
            $this->loadCart();
            $this->dispatch('cart-updated');
            $this->dispatch('refresh-cart-count');
        }
    }

    public function removeCartItem($rowId)
    {
        $cart = session()->get('cart', []);
        
        if (isset($cart[$rowId])) {
            // Depuración
            logger('Cart item removed: ' . $rowId);
            
            unset($cart[$rowId]);
            session()->put('cart', $cart);
            
            $this->loadCart();
            $this->dispatch('cart-updated');
            $this->dispatch('refresh-cart-count');
            $this->dispatch('notify', [
                'message' => 'تم حذف المنتج من سلة التسوق',
                'type' => 'success'
            ]);
        }
    }

    public function clearCart()
    {
        session()->forget('cart');
        
        // Depuración
        logger('Cart cleared');
        
        $this->loadCart();
        $this->dispatch('cart-updated');
        $this->dispatch('refresh-cart-count');
        $this->dispatch('notify', [
            'message' => 'تم تفريغ سلة التسوق',
            'type' => 'success'
        ]);
    }

    public function applyCoupon()
    {
        if (empty($this->couponCode)) {
            $this->couponError = 'يرجى إدخال كود الخصم';
            $this->couponSuccess = '';
            return;
        }

        // Aquí validarías el código de cupón
        // Por ahora, simplemente aplicamos un descuento del 10%
        try {
            $this->couponSuccess = 'تم تطبيق كود الخصم بنجاح';
            $this->couponError = '';
            $this->calculateTotals();
            $this->dispatch('cart-updated');
        } catch (\Exception $exception) {
            $this->couponError = 'كود الخصم غير صالح: ' . $exception->getMessage();
            $this->couponSuccess = '';
        }
    }

    public function removeCoupon()
    {
        $this->couponCode = '';
        $this->couponSuccess = '';
        $this->couponError = '';
        $this->calculateTotals();
        $this->dispatch('cart-updated');
    }

    public function render()
    {
        return view('livewire.cart.session-cart-page');
    }
}
