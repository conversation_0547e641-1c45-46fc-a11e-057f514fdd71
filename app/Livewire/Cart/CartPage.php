<?php

namespace App\Livewire\Cart;

use Livewire\Component;
use App\Services\CartService;

class CartPage extends Component
{
    public $cart;
    public $couponCode = '';
    public $couponError = '';
    public $couponSuccess = '';

    protected $cartService;

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount()
    {
        $this->cart = $this->cartService->getCart();
    }

    public function updateCartItem($cartItemId, $quantity)
    {
        $cartItem = \App\Models\CartItem::find($cartItemId);
        if ($cartItem) {
            $this->cartService->updateCartItem($cartItem, $quantity);
            $this->cart = $this->cartService->getCart();
            $this->dispatch('cart-updated');
        }
    }

    public function removeCartItem($cartItemId)
    {
        $cartItem = \App\Models\CartItem::find($cartItemId);
        if ($cartItem) {
            $this->cartService->removeCartItem($cartItem);
            $this->cart = $this->cartService->getCart();
            $this->dispatch('cart-updated');
            $this->dispatch('notify', [
                'message' => 'تم حذف المنتج من سلة التسوق',
                'type' => 'success'
            ]);
        }
    }

    public function clearCart()
    {
        $this->cartService->clearCart();
        $this->cart = $this->cartService->getCart();
        $this->dispatch('cart-updated');
        $this->dispatch('notify', [
            'message' => 'تم تفريغ سلة التسوق',
            'type' => 'success'
        ]);
    }

    public function applyCoupon()
    {
        if (empty($this->couponCode)) {
            $this->couponError = 'يرجى إدخال كود الخصم';
            $this->couponSuccess = '';
            return;
        }

        // Here you would validate the coupon code
        // For now, we'll just apply a simple 10% discount
        try {
            $this->cartService->applyCoupon($this->couponCode);
            $this->cart = $this->cartService->getCart();
            $this->couponSuccess = 'تم تطبيق كود الخصم بنجاح';
            $this->couponError = '';
            $this->dispatch('cart-updated');
        } catch (\Exception $exception) {
            $this->couponError = 'كود الخصم غير صالح: ' . $exception->getMessage();
            $this->couponSuccess = '';
        }
    }

    public function removeCoupon()
    {
        $this->cartService->removeCoupon();
        $this->cart = $this->cartService->getCart();
        $this->couponCode = '';
        $this->couponSuccess = '';
        $this->couponError = '';
        $this->dispatch('cart-updated');
    }

    public function render()
    {
        return view('livewire.cart.cart-page');
    }
}
