<?php

namespace App\Livewire\Cart;

use Livewire\Component;
use App\Models\CartItem as CartItemModel;
use App\Services\CartService;

class CartItem extends Component
{
    public CartItemModel $item;
    public $quantity;

    protected $cartService;

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount(CartItemModel $item)
    {
        $this->item = $item;
        $this->quantity = $item->quantity;
    }

    public function updatedQuantity($value)
    {
        if ($value < 1) {
            $this->quantity = 1;
        } elseif ($value > $this->item->product->stock_quantity) {
            $this->quantity = $this->item->product->stock_quantity;
            $this->dispatch('notify', [
                'message' => 'لا يمكن إضافة كمية أكبر من المخزون المتاح',
                'type' => 'error'
            ]);
        }

        $this->cartService->updateCartItem($this->item, $this->quantity);
        $this->dispatch('cart-updated');
    }

    public function incrementQuantity()
    {
        if ($this->quantity < $this->item->product->stock_quantity) {
            $this->quantity++;
            $this->cartService->updateCartItem($this->item, $this->quantity);
            $this->dispatch('cart-updated');
        } else {
            $this->dispatch('notify', [
                'message' => 'لا يمكن إضافة كمية أكبر من المخزون المتاح',
                'type' => 'error'
            ]);
        }
    }

    public function decrementQuantity()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
            $this->cartService->updateCartItem($this->item, $this->quantity);
            $this->dispatch('cart-updated');
        }
    }

    public function removeItem()
    {
        $this->cartService->removeCartItem($this->item);
        $this->dispatch('cart-updated');
        $this->dispatch('notify', [
            'message' => 'تم حذف المنتج من سلة التسوق',
            'type' => 'success'
        ]);
    }

    public function render()
    {
        return view('livewire.cart.cart-item');
    }
}
