<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use App\Models\Wishlist;
use Illuminate\Support\Facades\Auth;
use App\Traits\SettingsHelper;

class WishlistManager extends Component
{
    use SettingsHelper;

    public $productId;
    public $wishlistCount = 0;
    public $isInWishlist = false;
    public $showWishlist = true;

    public function mount($productId = null)
    {
        $this->productId = $productId;
        $this->showWishlist = $this->shouldShowWishlist();
        $this->updateWishlistStatus();
    }

    public function toggleWishlist()
    {
        try {
            // التحقق من تسجيل الدخول
            if (!Auth::check()) {
                $this->dispatch('show-message', [
                    'type' => 'info',
                    'message' => 'يجب تسجيل الدخول أولاً لإضافة المنتجات إلى المفضلة'
                ]);

                // إعادة توجيه لصفحة تسجيل الدخول
                return redirect()->route('login');
            }

            // التحقق من إعدادات المفضلة
            if (!$this->showWishlist) {
                $this->dispatch('show-message', [
                    'type' => 'info',
                    'message' => 'خدمة المفضلة غير متاحة حالياً'
                ]);
                return;
            }

            $user = Auth::user();
            $wishlistItem = $user->wishlist()->where('product_id', $this->productId)->first();

            if ($wishlistItem) {
                // إزالة من المفضلة
                $wishlistItem->delete();
                $this->isInWishlist = false;
                $message = 'تم إزالة المنتج من المفضلة';
            } else {
                // إضافة إلى المفضلة
                $user->wishlist()->create([
                    'product_id' => $this->productId
                ]);
                $this->isInWishlist = true;
                $message = 'تم إضافة المنتج إلى المفضلة';
            }

            $this->updateWishlistStatus();

            $this->dispatch('show-message', [
                'type' => 'success',
                'message' => $message
            ]);

            $this->dispatch('wishlist-updated', ['count' => $this->wishlistCount]);

        } catch (\Exception $e) {
            $this->dispatch('show-message', [
                'type' => 'error',
                'message' => 'حدث خطأ أثناء تحديث المفضلة'
            ]);
        }
    }

    public function updateWishlistStatus()
    {
        if (Auth::check()) {
            $user = Auth::user();
            $this->wishlistCount = $user->wishlist()->count();
            $this->isInWishlist = $user->wishlist()->where('product_id', $this->productId)->exists();
        } else {
            $this->wishlistCount = 0;
            $this->isInWishlist = false;
        }
    }

    public function render()
    {
        return view('livewire.wishlist-manager');
    }
}
