<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Category;
use App\Models\MetalType;
use App\Models\MetalPurity;
use App\Models\Product;
use App\Traits\SettingsHelper;
use App\Traits\CategorySettingsHelper;

class ProductFilters extends Component
{
    use SettingsHelper, CategorySettingsHelper;

    // خصائص الفلترة الأساسية
    public $search = '';
    public $category = '';
    public $minPrice = null;
    public $maxPrice = null;
    public $metalType = '';
    public $purity = '';
    public $sort = 'newest';

    // خصائص التحكم في العرض
    public $showSearch = true;
    public $showPriceFilter = true;
    public $showCategoryFilter = true;
    public $showMetalFilter = true;
    public $showPurityFilter = true;

    // خصائص الصفحة المستهدفة
    public $targetComponent = '';
    public $currentPage = '';

    // البيانات الديناميكية
    public $availableCategories = [];
    public $availableMetals = [];
    public $availablePurities = [];
    public $priceRange = ['min' => 0, 'max' => 0];

    // إعدادات الموقع للعرض
    public $displaySettings = [];



    public function mount(
        $targetComponent = '',
        $currentPage = '',
        $showSearch = true,
        $showPriceFilter = true,
        $showCategoryFilter = true,
        $showMetalFilter = true,
        $showPurityFilter = true,
        $initialFilters = []
    ) {
        $this->targetComponent = $targetComponent;
        $this->currentPage = $currentPage;
        $this->showSearch = $showSearch;
        $this->showPriceFilter = $showPriceFilter;
        $this->showCategoryFilter = $showCategoryFilter;
        $this->showMetalFilter = $showMetalFilter;
        $this->showPurityFilter = $showPurityFilter;

        // تطبيق الفلاتر الأولية
        if (!empty($initialFilters)) {
            $this->applyInitialFilters($initialFilters);
        }

        // تحميل البيانات الديناميكية
        $this->loadDynamicData();

        // تحميل إعدادات الموقع
        $this->loadSiteSettings();
    }

    protected function applyInitialFilters($filters)
    {
        $this->search = $filters['search'] ?? '';
        $this->category = $filters['category'] ?? '';
        $this->minPrice = $filters['minPrice'] ?? null;
        $this->maxPrice = $filters['maxPrice'] ?? null;
        $this->metalType = $filters['metalType'] ?? '';
        $this->purity = $filters['purity'] ?? '';
        $this->sort = $filters['sort'] ?? 'newest';
    }

    protected function loadSiteSettings()
    {
        $this->displaySettings = [
            'showRatings' => $this->shouldShowRatings(),
            'showWishlist' => $this->shouldShowWishlist(),
            'showWhatsapp' => $this->shouldShowWhatsapp(),
            'whatsappPhone' => $this->getWhatsappPhone(),
            'displayOnlyMode' => $this->isDisplayOnlyMode(),
            'showPriceGlobally' => $this->shouldShowPriceGlobally(),
        ];
    }

    protected function shouldShowPriceGlobally()
    {
        // التحقق من إعدادات السوبر أدمن
        $superAdminSettings = $this->getSuperAdminSettings();
        $siteSettings = $this->getSiteSettings();

        // إذا كان في وضع العرض فقط، لا نعرض الأسعار
        if ($superAdminSettings->isDisplayOnlyModeEnabled() ||
            ($siteSettings && $siteSettings->display_only_mode)) {
            return false;
        }

        return true;
    }

    // دالة لتطبيق إعدادات المنتج والفئة
    public function applyProductSettings($product)
    {
        // تطبيق إعدادات الفئة
        $categorySettings = $this->getCategorySettings($product);
        $product->show_price = $categorySettings['show_price'] && $this->shouldShowPriceGlobally();

        // تطبيق إعدادات الموقع
        $product->site_show_wishlist = $this->shouldShowWishlist();
        $product->site_show_ratings = $this->shouldShowRatings();
        $product->site_show_whatsapp = $this->shouldShowWhatsapp();
        $product->whatsapp_phone = $this->getWhatsappPhone();

        return $product;
    }

    // دالة لتطبيق الإعدادات على مجموعة من المنتجات
    public function applySettingsToProducts($products)
    {
        foreach ($products as $product) {
            $this->applyProductSettings($product);
        }

        return $products;
    }

    protected function loadDynamicData()
    {
        // بناء استعلام أساسي للمنتجات النشطة
        $baseQuery = Product::where('is_active', true);

        // تطبيق فلاتر الصفحة الحالية إذا كانت متوفرة
        $this->applyPageSpecificFilters($baseQuery);

        // تحميل الفئات المتاحة بناءً على المنتجات الموجودة
        $this->loadAvailableCategories($baseQuery);

        // تحميل أنواع المعادن المتاحة
        $this->loadAvailableMetals($baseQuery);

        // تحميل عيارات المعادن المتاحة
        $this->loadAvailablePurities($baseQuery);

        // حساب نطاق الأسعار الفعلي
        $this->calculatePriceRange($baseQuery);
    }

    protected function applyPageSpecificFilters($query)
    {
        // تطبيق فلاتر خاصة بالصفحة الحالية
        switch ($this->currentPage) {
            case 'category':
                // إذا كنا في صفحة فئة معينة، نحدد المنتجات لهذه الفئة فقط
                if (!empty($this->category)) {
                    $category = Category::where('slug', $this->category)->first();
                    if ($category) {
                        $categoryIds = [$category->id];
                        // تضمين الفئات الفرعية
                        if ($category->children->count() > 0) {
                            $childIds = $category->children->pluck('id')->toArray();
                            $categoryIds = array_merge($categoryIds, $childIds);
                        }
                        $query->whereIn('category_id', $categoryIds);
                    }
                }
                break;

            case 'search':
                // في صفحة البحث، نطبق البحث الأساسي إذا كان موجوداً
                if (!empty($this->search)) {
                    $query->where(function($q) {
                        $q->where('name_ar', 'like', '%' . $this->search . '%')
                          ->orWhere('name_en', 'like', '%' . $this->search . '%')
                          ->orWhere('description_ar', 'like', '%' . $this->search . '%')
                          ->orWhere('description_en', 'like', '%' . $this->search . '%');
                    });
                }
                break;
        }
    }

    protected function loadAvailableCategories($baseQuery)
    {
        // الحصول على IDs الفئات المتاحة من المنتجات الموجودة
        $availableCategoryIds = (clone $baseQuery)->distinct()->pluck('category_id')->filter();

        if ($availableCategoryIds->isNotEmpty()) {
            $this->availableCategories = Category::whereIn('id', $availableCategoryIds)
                ->where('is_active', true)
                ->orderBy('name_ar')
                ->get();
        } else {
            // إذا لم توجد منتجات، نعرض جميع الفئات النشطة
            $this->availableCategories = Category::where('is_active', true)
                ->orderBy('name_ar')
                ->get();
        }
    }

    protected function loadAvailableMetals($baseQuery)
    {
        // الحصول على IDs أنواع المعادن المتاحة
        $availableMetalIds = (clone $baseQuery)->distinct()->pluck('metal_type_id')->filter();

        if ($availableMetalIds->isNotEmpty()) {
            $this->availableMetals = MetalType::whereIn('id', $availableMetalIds)
                ->where('is_active', true)
                ->orderBy('name_ar')
                ->get();
        } else {
            $this->availableMetals = MetalType::where('is_active', true)
                ->orderBy('name_ar')
                ->get();
        }
    }

    protected function loadAvailablePurities($baseQuery)
    {
        // الحصول على IDs عيارات المعادن المتاحة
        $availablePurityIds = (clone $baseQuery)->distinct()->pluck('metal_purity_id')->filter();

        if ($availablePurityIds->isNotEmpty()) {
            $this->availablePurities = MetalPurity::whereIn('id', $availablePurityIds)
                ->where('is_active', true)
                ->orderBy('purity_percentage', 'desc')
                ->get();
        } else {
            $this->availablePurities = MetalPurity::where('is_active', true)
                ->orderBy('purity_percentage', 'desc')
                ->get();
        }
    }

    protected function calculatePriceRange($baseQuery)
    {
        // حساب نطاق الأسعار الفعلي من المنتجات المتاحة
        $priceStats = (clone $baseQuery)->selectRaw('MIN(price) as min_price, MAX(price) as max_price')->first();

        $this->priceRange = [
            'min' => $priceStats->min_price ?? 0,
            'max' => $priceStats->max_price ?? 10000
        ];
    }

    // تحديث البحث مع debounce
    public function updatedSearch()
    {
        $this->emitFiltersChanged();
    }

    // تحديث الفئة
    public function updatedCategory()
    {
        $this->emitFiltersChanged();
    }

    // تحديث السعر الأدنى
    public function updatedMinPrice()
    {
        if ($this->minPrice !== null && $this->minPrice < 0) {
            $this->minPrice = 0;
        }
        $this->emitFiltersChanged();
    }

    // تحديث السعر الأقصى
    public function updatedMaxPrice()
    {
        if ($this->maxPrice !== null && $this->maxPrice < 0) {
            $this->maxPrice = null;
        }
        $this->emitFiltersChanged();
    }

    // تحديث نوع المعدن
    public function updatedMetalType()
    {
        $this->emitFiltersChanged();
    }

    // تحديث العيار
    public function updatedPurity()
    {
        $this->emitFiltersChanged();
    }

    // تحديث الترتيب
    public function updatedSort()
    {
        $this->emitFiltersChanged();
    }

    protected function emitFiltersChanged()
    {
        // إعادة تحميل البيانات الديناميكية عند تغيير الفلاتر
        $this->refreshDynamicData();

        $filters = $this->getCurrentFilters();

        // إرسال الفلاتر للكومبونانت المستهدف
        if ($this->targetComponent) {
            $this->dispatch('filtersChanged', filters: $filters)->to($this->targetComponent);
        }

        // إرسال عام للصفحة
        $this->dispatch('filtersUpdated', filters: $filters);
    }

    protected function refreshDynamicData()
    {
        // إعادة تحميل البيانات بناءً على الفلاتر الحالية
        $this->loadDynamicData();
    }

    // دالة للحصول على استعلام المنتجات مع الفلاتر المطبقة
    public function getFilteredProductsQuery()
    {
        $query = Product::where('is_active', true);

        // تطبيق فلاتر الصفحة الحالية
        $this->applyPageSpecificFilters($query);

        // تطبيق فلاتر البحث
        if (!empty($this->search)) {
            $query->where(function($q) {
                $q->where('name_ar', 'like', '%' . $this->search . '%')
                  ->orWhere('name_en', 'like', '%' . $this->search . '%')
                  ->orWhere('description_ar', 'like', '%' . $this->search . '%')
                  ->orWhere('description_en', 'like', '%' . $this->search . '%');
            });
        }

        // تطبيق فلتر الفئة
        if (!empty($this->category)) {
            $category = Category::where('slug', $this->category)->first();
            if ($category) {
                $categoryIds = [$category->id];
                if ($category->children->count() > 0) {
                    $childIds = $category->children->pluck('id')->toArray();
                    $categoryIds = array_merge($categoryIds, $childIds);
                }
                $query->whereIn('category_id', $categoryIds);
            }
        }

        // تطبيق فلتر نطاق السعر
        if ($this->minPrice !== null) {
            $query->where('price', '>=', $this->minPrice);
        }
        if ($this->maxPrice !== null) {
            $query->where('price', '<=', $this->maxPrice);
        }

        // تطبيق فلتر نوع المعدن
        if (!empty($this->metalType)) {
            $query->whereHas('metal', function($q) {
                $q->where('name', $this->metalType);
            });
        }

        // تطبيق فلتر العيار
        if (!empty($this->purity)) {
            $query->whereHas('metalPurity', function($q) {
                $q->where('name', $this->purity);
            });
        }

        return $query;
    }

    // دالة للحصول على عدد النتائج المفلترة
    public function getFilteredProductsCount()
    {
        return $this->getFilteredProductsQuery()->count();
    }

    public function getCurrentFilters()
    {
        return [
            'search' => $this->search,
            'category' => $this->category,
            'minPrice' => $this->minPrice,
            'maxPrice' => $this->maxPrice,
            'metalType' => $this->metalType,
            'purity' => $this->purity,
            'sort' => $this->sort,
        ];
    }

    public function getActiveFiltersCount()
    {
        $count = 0;

        if (!empty($this->search)) $count++;
        if (!empty($this->category)) $count++;
        if ($this->minPrice !== null) $count++;
        if ($this->maxPrice !== null) $count++;
        if (!empty($this->metalType)) $count++;
        if (!empty($this->purity)) $count++;
        if ($this->sort !== 'newest') $count++;

        return $count;
    }

    public function resetAllFilters()
    {
        $this->search = '';
        $this->category = '';
        $this->minPrice = null;
        $this->maxPrice = null;
        $this->metalType = '';
        $this->purity = '';
        $this->sort = 'newest';

        $this->emitFiltersChanged();
    }

    public function resetPriceFilter()
    {
        $this->minPrice = null;
        $this->maxPrice = null;
        $this->emitFiltersChanged();
    }



    public function render()
    {
        return view('livewire.product-filters', [
            'activeFiltersCount' => $this->getActiveFiltersCount(),
        ]);
    }
}
