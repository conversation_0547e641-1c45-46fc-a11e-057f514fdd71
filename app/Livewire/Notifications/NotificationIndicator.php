<?php

namespace App\Livewire\Notifications;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Models\Notification;

class NotificationIndicator extends Component
{
    public $unreadCount = 0;
    public $notifications = [];
    public $showDropdown = false;

    protected $listeners = ['refreshNotifications' => 'getNotifications'];

    public function mount()
    {
        $this->getNotifications();
    }

    public function getNotifications()
    {
        if (Auth::check()) {
            $this->unreadCount = Notification::where('user_id', Auth::id())
                ->where('read', false)
                ->count();

            $this->notifications = Notification::where('user_id', Auth::id())
                ->latest()
                ->take(5)
                ->get();
        }
    }

    public function toggleDropdown()
    {
        $this->showDropdown = !$this->showDropdown;
    }

    public function markAsRead($id)
    {
        $notification = Notification::where('user_id', Auth::id())
            ->where('id', $id)
            ->first();

        if ($notification) {
            $notification->read = true;
            $notification->save();
            $this->getNotifications();
        }
    }

    public function markAllAsRead()
    {
        Notification::where('user_id', Auth::id())
            ->where('read', false)
            ->update(['read' => true]);

        $this->getNotifications();
    }

    public function render()
    {
        return view('livewire.notifications.notification-indicator');
    }
}
