<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use App\Models\Product;
use App\Models\SearchLog;
use Illuminate\Support\Facades\Schema;
use App\Traits\CategorySettingsHelper;
use App\Traits\SettingsHelper;

class SearchResults extends Component
{
    use WithPagination;
    use CategorySettingsHelper;
    use SettingsHelper;

    // خصائص البحث
    public $query = '';

    // خصائص الفلترة
    public $category = '';
    public $minPrice = null;
    public $maxPrice = null;
    public $metalType = '';
    public $purity = '';
    public $sort = 'newest';

    // خصائص إضافية
    public $perPage = 12;

    // إعدادات الموقع
    public $whatsappPhone;
    public $showWishlist;
    public $showRatings;
    public $showWhatsapp;

    // تحديث عنوان URL عند تغيير الفلاتر
    protected $queryString = [
        'query' => ['except' => ''],
        'category' => ['except' => ''],
        'minPrice' => ['except' => null],
        'maxPrice' => ['except' => null],
        'metalType' => ['except' => ''],
        'purity' => ['except' => ''],
        'sort' => ['except' => 'newest'],
    ];



    // تهيئة المكون
    public function mount($query = '')
    {
        $this->query = $query;
        $this->loadSiteSettings();
    }

    // معالجة تغيير الفلاتر من كومبونانت الفلاتر
    #[On('filtersChanged')]
    public function handleFiltersChanged($filters)
    {
        $this->query = $filters['search'] ?? $this->query;
        $this->category = $filters['category'] ?? '';
        $this->minPrice = $filters['minPrice'];
        $this->maxPrice = $filters['maxPrice'];
        $this->metalType = $filters['metalType'] ?? '';
        $this->purity = $filters['purity'] ?? '';
        $this->sort = $filters['sort'] ?? 'newest';

        // إعادة تعيين الصفحة للصفحة الأولى عند تغيير الفلاتر
        $this->resetPage();
    }

    #[On('filtersUpdated')]
    public function handleFiltersUpdated($filters)
    {
        // يمكن استخدامها لأي معالجة إضافية
        $this->handleFiltersChanged($filters);
    }

    public function loadSiteSettings()
    {
        // جلب إعدادات الموقع باستخدام SettingsHelper
        $this->whatsappPhone = $this->getWhatsappPhone();
        $this->showWishlist = $this->shouldShowWishlist();
        $this->showRatings = $this->shouldShowRatings();
        $this->showWhatsapp = $this->shouldShowWhatsapp();
    }

    // إعادة تعيين الصفحة عند تغيير الفلاتر
    public function updatedQuery() { $this->resetPage(); }
    public function updatedCategory() { $this->resetPage(); }
    public function updatedMinPrice() { $this->resetPage(); }
    public function updatedMaxPrice() { $this->resetPage(); }
    public function updatedMetalType() { $this->resetPage(); }
    public function updatedPurity() { $this->resetPage(); }

    // إعادة تعيين جميع الفلاتر
    public function resetFilters()
    {
        $this->reset(['category', 'minPrice', 'maxPrice', 'metalType', 'purity', 'sort']);
        $this->resetPage();
    }

    // البحث التقليدي كبديل
    private function fallbackSearch($query)
    {
        $query->where(function($q) {
            $q->where('name_ar', 'like', '%' . $this->query . '%')
              ->orWhere('name_en', 'like', '%' . $this->query . '%')
              ->orWhere('description_ar', 'like', '%' . $this->query . '%')
              ->orWhere('description_en', 'like', '%' . $this->query . '%');

            // البحث في SKU فقط إذا كان العمود موجود
            if (Schema::hasColumn('products', 'sku')) {
                $q->orWhere('sku', 'like', '%' . $this->query . '%');
            }
        });
    }

    public function getActiveFiltersCountProperty()
    {
        return ($this->category ? 1 : 0) +
               ($this->minPrice ? 1 : 0) +
               ($this->maxPrice ? 1 : 0) +
               ($this->metalType ? 1 : 0) +
               ($this->purity ? 1 : 0) +
               ($this->query ? 1 : 0);
    }

    public function render()
    {
        // بناء استعلام المنتجات
        $query = Product::where('is_active', true);

        // فلترة حسب البحث
        if ($this->query) {
            // استخدام FULLTEXT search إذا كان متاحاً (MySQL)
            if (config('database.default') === 'mysql') {
                try {
                    $query->whereRaw(
                        "MATCH(name_ar, name_en, description_ar, description_en) AGAINST(? IN BOOLEAN MODE)",
                        ['+' . $this->query . '*']
                    );
                } catch (\Exception) {
                    // العودة للبحث التقليدي إذا فشل FULLTEXT
                    $this->fallbackSearch($query);
                }
            } else {
                $this->fallbackSearch($query);
            }
        }

        // فلترة حسب الفئة
        if ($this->category) {
            $category = \App\Models\Category::where('slug', $this->category)->first();
            if ($category) {
                $categoryIds = [$category->id];

                // تضمين الفئات الفرعية
                if ($category->children->count() > 0) {
                    $childIds = $category->children->pluck('id')->toArray();
                    $categoryIds = array_merge($categoryIds, $childIds);
                }

                $query->whereIn('category_id', $categoryIds);
            }
        }

        // فلترة حسب نطاق السعر
        if ($this->minPrice) {
            $query->where('price', '>=', $this->minPrice);
        }

        if ($this->maxPrice) {
            $query->where('price', '<=', $this->maxPrice);
        }

        // فلترة حسب نوع المعدن
        if ($this->metalType) {
            $query->whereHas('metal', function($q) {
                $q->where('name', $this->metalType);
            });
        }

        // فلترة حسب العيار
        if ($this->purity) {
            $query->whereHas('metalPurity', function($q) {
                $q->where('name', $this->purity);
            });
        }

        // ترتيب المنتجات
        switch ($this->sort) {
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // الحصول على المنتجات مع العلاقات
        $products = $query->with(['category', 'primaryImage', 'metal', 'metalPurity'])->paginate($this->perPage);

        // تطبيق إعدادات الفئة والموقع على المنتجات
        foreach ($products as $product) {
            // تطبيق إعدادات الفئة
            $categorySettings = $this->getCategorySettings($product);
            $product->show_price = $categorySettings['show_price'];

            // تطبيق إعدادات الموقع
            $product->site_show_wishlist = $this->showWishlist;
            $product->site_show_ratings = $this->showRatings;
            $product->site_show_whatsapp = $this->showWhatsapp;
            $product->whatsapp_phone = $this->whatsappPhone;
        }

        // تسجيل البحث إذا كان هناك استعلام
        if ($this->query && $products->currentPage() === 1) {
            $filters = array_filter([
                'category' => $this->category,
                'metalType' => $this->metalType,
                'purity' => $this->purity,
                'minPrice' => $this->minPrice,
                'maxPrice' => $this->maxPrice,
                'sort' => $this->sort !== 'newest' ? $this->sort : null,
            ]);

            SearchLog::logSearch($this->query, $filters, $products->total());
        }

        // الحصول على الفئات والمعادن والعيارات
        $categories = \App\Models\Category::where('is_active', true)->get();
        $metals = \App\Models\MetalType::where('is_active', true)->get();
        $metalPurities = \App\Models\MetalPurity::where('is_active', true)->get();

        // الحصول على إعدادات العرض
        $viewSettings = $this->getViewSettings();

        return view('livewire.search-results', array_merge([
            'products' => $products,
            'categories' => $categories,
            'metals' => $metals,
            'metalPurities' => $metalPurities,
            'activeFiltersCount' => $this->activeFiltersCount
        ], $viewSettings));
    }
}
