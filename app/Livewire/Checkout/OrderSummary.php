<?php

namespace App\Livewire\Checkout;

use Livewire\Component;
use App\Services\CartService;

class OrderSummary extends Component
{
    public $cart;
    public $step;
    public $shippingData = [];
    public $paymentMethod = 'cash';
    public $termsAccepted = false;

    protected $cartService;

    protected $listeners = ['cart-updated' => 'refreshCart'];

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount($cart, $step = 1, $shippingData = [], $paymentMethod = 'cash')
    {
        $this->cart = $cart;
        $this->step = $step;
        $this->shippingData = $shippingData;
        $this->paymentMethod = $paymentMethod;
    }

    public function refreshCart()
    {
        $this->cart = $this->cartService->getCart();
    }

    public function updatedTermsAccepted($value)
    {
        $this->termsAccepted = $value;
    }

    public function placeOrder()
    {
        if (!$this->termsAccepted) {
            $this->dispatch('notify', [
                'message' => 'يرجى الموافقة على الشروط والأحكام',
                'type' => 'error'
            ]);
            return;
        }

        $this->dispatch('placeOrder');
    }

    public function render()
    {
        return view('livewire.checkout.order-summary');
    }
}
