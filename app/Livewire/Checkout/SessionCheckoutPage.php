<?php

namespace App\Livewire\Checkout;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use App\Models\Order;
use App\Models\Product;

class SessionCheckoutPage extends Component
{
    public $cartItems = [];
    public $step = 1; // 1: Shipping, 2: Payment, 3: Review
    public $shippingData = [];
    public $paymentMethod = 'cash';
    public $termsAccepted = false;
    public $orderNotes = '';
    
    // Totales
    public $subtotal = 0;
    public $tax = 0;
    public $shipping = 0;
    public $discount = 0;
    public $total = 0;

    protected $listeners = ['shippingComplete', 'paymentComplete', 'cart-updated' => 'refreshCart'];

    public function mount()
    {
        $this->loadCart();

        // Pre-fill shipping data if user is logged in
        if (Auth::check()) {
            $user = Auth::user();
            $this->shippingData = [
                'name' => $user->name ?? '',
                'email' => $user->email ?? '',
                'phone' => $user->phone ?? '',
                'address' => $user->address ?? '',
                'city' => $user->city ?? '',
                'country' => $user->country ?? 'مصر',
                'postal_code' => $user->postal_code ?? '',
            ];
        }
    }

    public function loadCart()
    {
        $cart = session()->get('cart', []);
        $this->cartItems = $cart;
        
        // Calcular totales
        $this->calculateTotals();
        
        // Depuración
        logger('Cart loaded in checkout: ' . count($this->cartItems) . ' items');
        logger('Cart contents: ' . json_encode($this->cartItems));
    }

    public function calculateTotals()
    {
        $this->subtotal = 0;
        
        foreach ($this->cartItems as $item) {
            $this->subtotal += $item['price'] * $item['quantity'];
        }
        
        $this->tax = $this->subtotal * 0.14; // 14% impuesto para Egipto
        $this->shipping = $this->subtotal > 1000 ? 0 : 50; // Envío gratis para pedidos superiores a 1000 EGP
        
        // Aplicar descuento si hay un cupón
        if (session()->has('coupon')) {
            $this->discount = $this->subtotal * 0.1; // 10% de descuento
        } else {
            $this->discount = 0;
        }
        
        $this->total = $this->subtotal + $this->tax + $this->shipping - $this->discount;
    }

    public function refreshCart()
    {
        $this->loadCart();
    }

    public function shippingComplete($data)
    {
        $this->shippingData = $data;
        $this->step = 2;
    }

    public function paymentComplete($method)
    {
        $this->paymentMethod = $method;
        $this->step = 3;
    }

    public function goToStep($step)
    {
        if ($step < $this->step) {
            $this->step = $step;
        }
    }

    public function placeOrder()
    {
        if (!$this->termsAccepted) {
            $this->dispatch('notify', [
                'message' => 'يرجى الموافقة على الشروط والأحكام',
                'type' => 'error'
            ]);
            return;
        }

        // Crear el pedido
        $order = new Order();
        $order->user_id = Auth::check() ? Auth::id() : null;
        $order->order_number = 'ORD-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
        $order->status = 'pending';
        $order->payment_status = 'pending';
        $order->payment_method = $this->paymentMethod;
        
        $order->total_amount = $this->total;
        $order->tax_amount = $this->tax;
        $order->shipping_amount = $this->shipping;
        $order->discount_amount = $this->discount;
        $order->currency = 'EGP';
        
        $order->shipping_name = $this->shippingData['name'];
        $order->shipping_address = $this->shippingData['address'];
        $order->shipping_city = $this->shippingData['city'];
        $order->shipping_country = $this->shippingData['country'];
        $order->shipping_phone = $this->shippingData['phone'];
        $order->shipping_email = $this->shippingData['email'];
        $order->shipping_postal_code = $this->shippingData['postal_code'] ?? null;
        $order->order_notes = $this->orderNotes;
        
        $order->is_guest = !Auth::check();
        $order->guest_email = Auth::check() ? null : $this->shippingData['email'];
        
        $order->save();
        
        // Crear los elementos del pedido
        foreach ($this->cartItems as $item) {
            $order->items()->create([
                'product_id' => $item['id'],
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'total' => $item['price'] * $item['quantity'],
            ]);
        }
        
        // Limpiar el carrito
        session()->forget('cart');
        
        // Depuración
        logger('Order placed: ' . $order->id);
        logger('Order details: ' . json_encode($order->toArray()));
        
        // Redirigir a la página de éxito
        return redirect()->route('checkout.success', $order->id)->with('success', 'تم تقديم طلبك بنجاح');
    }

    public function render()
    {
        return view('livewire.checkout.session-checkout-page');
    }
}
