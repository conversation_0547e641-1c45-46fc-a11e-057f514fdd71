<?php

namespace App\Livewire\Checkout;

use Livewire\Component;

class PaymentForm extends Component
{
    public $paymentMethod = 'cash';
    public $availableMethods = [
        'cash' => [
            'name' => 'الدفع عند الاستلام',
            'description' => 'ادفع نقدًا عند استلام طلبك',
            'icon' => 'fa-money-bill-wave',
        ],
        'credit_card' => [
            'name' => 'بطاقة ائتمان',
            'description' => 'ادفع باستخدام بطاقة الائتمان الخاصة بك',
            'icon' => 'fa-credit-card',
        ],
        'paypal' => [
            'name' => 'PayPal',
            'description' => 'ادفع باستخدام حساب PayPal الخاص بك',
            'icon' => 'fa-paypal',
        ],
        'bank_transfer' => [
            'name' => 'تحويل بنكي',
            'description' => 'قم بالتحويل البنكي مباشرة إلى حسابنا',
            'icon' => 'fa-university',
        ],
    ];

    public function mount($selectedMethod = 'cash')
    {
        $this->paymentMethod = $selectedMethod;
    }

    public function continue()
    {
        $this->dispatch('paymentComplete', $this->paymentMethod);
    }

    public function render()
    {
        return view('livewire.checkout.payment-form');
    }
}
