<?php

namespace App\Livewire\Checkout;

use Livewire\Component;
use App\Services\CartService;
use Illuminate\Support\Facades\Auth;

class CheckoutPage extends Component
{
    public $cart;
    public $step = 1; // 1: Shipping, 2: Payment, 3: Review
    public $shippingData = [];
    public $paymentMethod = 'cash';
    public $termsAccepted = false;
    public $orderNotes = '';

    protected $cartService;

    protected $listeners = ['shippingComplete', 'paymentComplete', 'cart-updated' => 'refreshCart'];

    public function boot(CartService $cartService)
    {
        $this->cartService = $cartService;
    }

    public function mount()
    {
        $this->cart = $this->cartService->getCart();

        // Pre-fill shipping data if user is logged in
        if (Auth::check()) {
            $user = Auth::user();
            $this->shippingData = [
                'name' => $user->name ?? '',
                'email' => $user->email ?? '',
                'phone' => $user->phone ?? '',
                'address' => $user->address ?? '',
                'city' => $user->city ?? '',
                'country' => $user->country ?? 'مصر',
                'postal_code' => $user->postal_code ?? '',
            ];
        }
    }

    public function refreshCart()
    {
        $this->cart = $this->cartService->getCart();
    }

    public function shippingComplete($data)
    {
        $this->shippingData = $data;
        $this->step = 2;
    }

    public function paymentComplete($method)
    {
        $this->paymentMethod = $method;
        $this->step = 3;
    }

    public function goToStep($step)
    {
        if ($step < $this->step) {
            $this->step = $step;
        }
    }

    public function placeOrder()
    {
        if (!$this->termsAccepted) {
            $this->dispatch('notify', [
                'message' => 'يرجى الموافقة على الشروط والأحكام',
                'type' => 'error'
            ]);
            return;
        }

        // Here we would normally process the order
        // For now, we'll just show a success message

        // Clear the cart
        $this->cartService->clearCart();

        // Redirect to success page
        return redirect()->route('checkout.success')->with('success', 'تم تقديم طلبك بنجاح');
    }

    public function render()
    {
        return view('livewire.checkout.checkout-page');
    }
}
