<?php

namespace App\Livewire\Checkout;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;

class ShippingForm extends Component
{
    public $name;
    public $email;
    public $phone;
    public $address;
    public $city;
    public $country = 'مصر';
    public $postalCode;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'required|string|max:20',
        'address' => 'required|string|max:255',
        'city' => 'required|string|max:100',
        'country' => 'required|string|max:100',
        'postalCode' => 'nullable|string|max:20',
    ];

    protected $messages = [
        'name.required' => 'يرجى إدخال الاسم الكامل',
        'email.required' => 'يرجى إدخال البريد الإلكتروني',
        'email.email' => 'يرجى إدخال بريد إلكتروني صحيح',
        'phone.required' => 'يرجى إدخال رقم الهاتف',
        'address.required' => 'يرجى إدخال العنوان',
        'city.required' => 'يرجى إدخال المدينة',
        'country.required' => 'يرجى إدخال البلد',
    ];

    public function mount($shippingData = [])
    {
        if (!empty($shippingData)) {
            $this->name = $shippingData['name'] ?? '';
            $this->email = $shippingData['email'] ?? '';
            $this->phone = $shippingData['phone'] ?? '';
            $this->address = $shippingData['address'] ?? '';
            $this->city = $shippingData['city'] ?? '';
            $this->country = $shippingData['country'] ?? 'مصر';
            $this->postalCode = $shippingData['postal_code'] ?? '';
        } elseif (Auth::check()) {
            $user = Auth::user();
            $this->name = $user->name ?? '';
            $this->email = $user->email ?? '';
            $this->phone = $user->phone ?? '';
            $this->address = $user->address ?? '';
            $this->city = $user->city ?? '';
            $this->country = $user->country ?? 'مصر';
            $this->postalCode = $user->postal_code ?? '';
        }
    }

    public function continue()
    {
        $this->validate();

        $shippingData = [
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'city' => $this->city,
            'country' => $this->country,
            'postal_code' => $this->postalCode,
        ];

        $this->dispatch('shippingComplete', $shippingData);
    }

    public function render()
    {
        return view('livewire.checkout.shipping-form');
    }
}
