<?php

namespace App\Livewire\Account;

use Livewire\Component;
use App\Models\Wishlist as WishlistModel;
use Illuminate\Support\Facades\Auth;
use App\Traits\DisplayModeHelper;
class Wishlist extends Component
{
    use DisplayModeHelper;

    public $wishlistItems = [];

    public function mount()
    {
        $this->loadWishlist();
    }

    public function loadWishlist()
    {
        if (Auth::check()) {
            $this->wishlistItems = WishlistModel::where('user_id', Auth::id())
                ->with('product')
                ->get();

            // Depuración
            logger('Wishlist items loaded: ' . $this->wishlistItems->count());
            logger('Wishlist items: ' . json_encode($this->wishlistItems->pluck('product_id')->toArray()));
        }
    }

    public function removeFromWishlist($wishlistId)
    {
        $wishlist = WishlistModel::where('user_id', Auth::id())
            ->where('id', $wishlistId)
            ->first();

        if ($wishlist) {
            $wishlist->delete();
            $this->loadWishlist();

            // Depuración
            logger('Product removed from wishlist: ' . $wishlistId);

            // Emitir eventos
            $this->dispatch('notify', [
                'message' => 'تم حذف المنتج من المفضلة',
                'type' => 'success'
            ]);

            // Emitir evento para actualizar el contador de la lista de deseos
            $this->dispatch('wishlistUpdated');
            $this->dispatch('refresh-wishlist-count');
        }
    }

    public function addToCart($productId)
    {
        if ($this->isDisplayOnlyMode()) {
            return;
        }

        $wishlist = WishlistModel::where('user_id', Auth::id())
            ->where('product_id', $productId)
            ->first();

        if ($wishlist && $wishlist->product) {
            // Usar la sesión para el carrito en lugar del servicio CartService
            $cart = session()->get('cart', []);

            $rowId = uniqid();
            $cart[$rowId] = [
                'id' => $wishlist->product->id,
                'name' => $wishlist->product->name_ar,
                'quantity' => 1,
                'price' => $wishlist->product->price,
                'weight' => $wishlist->product->weight,
                'options' => [
                    'image' => $wishlist->product->image,
                    'slug' => $wishlist->product->slug,
                    'metal_type' => $wishlist->product->material_type,
                    'purity' => $wishlist->product->metal_purity,
                ]
            ];

            session()->put('cart', $cart);

            // Depuración
            logger('Product added to cart from wishlist: ' . $productId);
            logger('Cart after adding: ' . json_encode($cart));

            // Emitir eventos
            $this->dispatch('cart-updated');
            $this->dispatch('refresh-cart-count');
            $this->dispatch('notify', [
                'message' => 'تمت إضافة المنتج إلى سلة التسوق',
                'type' => 'success'
            ]);
        }
    }

    public function render()
    {
        $displayOnlyMode = $this->isDisplayOnlyMode();
        $showWishlist = $this->shouldShowWishlist();

        // إذا كانت المفضلة معطلة، قم بتحويل المستخدم إلى الصفحة الرئيسية
        if (!$showWishlist) {
            return redirect()->route('home')->with('error', 'عذراً، المفضلة غير متاحة حالياً.');
        }

        return view('livewire.account.wishlist', [
            'displayOnlyMode' => $displayOnlyMode,
            'showWishlist' => $showWishlist
        ]);
    }
}
