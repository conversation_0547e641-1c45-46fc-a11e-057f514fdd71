<?php

namespace App\Livewire\Account;

use Livewire\Component;
use App\Models\Address;
use Illuminate\Support\Facades\Auth;

class AddressBook extends Component
{
    public $addresses = [];
    public $showAddressForm = false;
    public $editAddressId = null;

    // Address form
    public $name;
    public $phone;
    public $address;
    public $city;
    public $country = 'مصر';
    public $postalCode;
    public $isDefault = false;

    protected $rules = [
        'name' => 'required|string|max:255',
        'phone' => 'required|string|max:20',
        'address' => 'required|string|max:255',
        'city' => 'required|string|max:100',
        'country' => 'required|string|max:100',
        'postalCode' => 'nullable|string|max:20',
        'isDefault' => 'boolean',
    ];

    protected $messages = [
        'name.required' => 'يرجى إدخال الاسم الكامل',
        'phone.required' => 'يرجى إدخال رقم الهاتف',
        'address.required' => 'يرجى إدخال العنوان',
        'city.required' => 'يرجى إدخال المدينة',
        'country.required' => 'يرجى إدخال البلد',
    ];

    public function mount()
    {
        $this->loadAddresses();
    }

    public function loadAddresses()
    {
        if (Auth::check()) {
            $this->addresses = Address::where('user_id', Auth::id())
                ->orderBy('is_default', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();
        }
    }

    public function showForm()
    {
        $this->resetForm();
        $this->showAddressForm = true;
    }

    public function hideForm()
    {
        $this->showAddressForm = false;
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->editAddressId = null;
        $this->name = '';
        $this->phone = '';
        $this->address = '';
        $this->city = '';
        $this->country = 'مصر';
        $this->postalCode = '';
        $this->isDefault = false;
        $this->resetValidation();
    }

    public function editAddress($addressId)
    {
        $address = Address::where('user_id', Auth::id())
            ->where('id', $addressId)
            ->first();

        if ($address) {
            $this->editAddressId = $address->id;
            $this->name = $address->name;
            $this->phone = $address->phone;
            $this->address = $address->address;
            $this->city = $address->city;
            $this->country = $address->country;
            $this->postalCode = $address->postal_code;
            $this->isDefault = $address->is_default;

            $this->showAddressForm = true;
        }
    }

    public function saveAddress()
    {
        $this->validate();

        if ($this->editAddressId) {
            // Update existing address
            $address = Address::where('user_id', Auth::id())
                ->where('id', $this->editAddressId)
                ->first();

            if (!$address) {
                $this->dispatch('notify', [
                    'message' => 'حدث خطأ أثناء تحديث العنوان',
                    'type' => 'error'
                ]);
                return;
            }
        } else {
            // Create new address
            $address = new Address();
            $address->user_id = Auth::id();
        }

        $address->name = $this->name;
        $address->phone = $this->phone;
        $address->address = $this->address;
        $address->city = $this->city;
        $address->country = $this->country;
        $address->postal_code = $this->postalCode;
        $address->is_default = $this->isDefault;

        // If this is set as default, unset other defaults
        if ($this->isDefault) {
            Address::where('user_id', Auth::id())
                ->where('id', '!=', $address->id)
                ->update(['is_default' => false]);
        }

        $address->save();

        $this->loadAddresses();
        $this->hideForm();

        $this->dispatch('notify', [
            'message' => $this->editAddressId ? 'تم تحديث العنوان بنجاح' : 'تمت إضافة العنوان بنجاح',
            'type' => 'success'
        ]);
    }

    public function deleteAddress($addressId)
    {
        $address = Address::where('user_id', Auth::id())
            ->where('id', $addressId)
            ->first();

        if ($address) {
            $address->delete();
            $this->loadAddresses();

            $this->dispatch('notify', [
                'message' => 'تم حذف العنوان بنجاح',
                'type' => 'success'
            ]);
        }
    }

    public function setDefaultAddress($addressId)
    {
        // Unset all defaults
        Address::where('user_id', Auth::id())
            ->update(['is_default' => false]);

        // Set new default
        $address = Address::where('user_id', Auth::id())
            ->where('id', $addressId)
            ->first();

        if ($address) {
            $address->is_default = true;
            $address->save();
            $this->loadAddresses();

            $this->dispatch('notify', [
                'message' => 'تم تعيين العنوان الافتراضي بنجاح',
                'type' => 'success'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.account.address-book');
    }
}
