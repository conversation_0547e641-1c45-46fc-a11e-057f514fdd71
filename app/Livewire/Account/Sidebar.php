<?php

namespace App\Livewire\Account;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use App\Traits\DisplayModeHelper;

class Sidebar extends Component
{
    use DisplayModeHelper;

    public $user;
    public $currentRoute;

    public function mount()
    {
        $this->user = Auth::user();
        $this->currentRoute = request()->route()->getName();
    }

    public function render()
    {
        $displayOnlyMode = $this->isDisplayOnlyMode();
        $showWishlist = $this->shouldShowWishlist();

        return view('livewire.account.sidebar', [
            'displayOnlyMode' => $displayOnlyMode,
            'showWishlist' => $showWishlist
        ]);
    }
}
