<?php

namespace App\Livewire\Account;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Traits\DisplayModeHelper;

class ProfilePage extends Component
{
    use DisplayModeHelper;

    public $activeTab = 'profile';
    public $user;

    // Profile form
    public $name;
    public $email;
    public $phone;
    public $address;
    public $city;
    public $country;
    public $postalCode;

    // Password form
    public $currentPassword;
    public $password;
    public $passwordConfirmation;

    protected $rules = [
        'name' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'phone' => 'nullable|string|max:20',
        'address' => 'nullable|string|max:255',
        'city' => 'nullable|string|max:100',
        'country' => 'nullable|string|max:100',
        'postalCode' => 'nullable|string|max:20',
    ];

    protected $messages = [
        'name.required' => 'يرجى إدخال الاسم الكامل',
        'email.required' => 'يرجى إدخال البريد الإلكتروني',
        'email.email' => 'يرجى إدخال بريد إلكتروني صحيح',
    ];

    protected $validationAttributes = [
        'name' => 'الاسم الكامل',
        'email' => 'البريد الإلكتروني',
        'phone' => 'رقم الهاتف',
        'address' => 'العنوان',
        'city' => 'المدينة',
        'country' => 'البلد',
        'postalCode' => 'الرمز البريدي',
        'currentPassword' => 'كلمة المرور الحالية',
        'password' => 'كلمة المرور الجديدة',
        'passwordConfirmation' => 'تأكيد كلمة المرور',
    ];

    public function mount()
    {
        $this->user = Auth::user();
        $this->name = $this->user->name;
        $this->email = $this->user->email;
        $this->phone = $this->user->phone;
        $this->address = $this->user->address;
        $this->city = $this->user->city;
        $this->country = $this->user->country;
        $this->postalCode = $this->user->postal_code;
    }

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function updateProfile()
    {
        $this->validate();

        $this->user->name = $this->name;
        $this->user->email = $this->email;
        $this->user->phone = $this->phone;
        $this->user->address = $this->address;
        $this->user->city = $this->city;
        $this->user->country = $this->country;
        $this->user->postal_code = $this->postalCode;

        $this->user->save();

        $this->dispatch('notify', [
            'message' => 'تم تحديث الملف الشخصي بنجاح',
            'type' => 'success'
        ]);
    }

    public function updatePassword()
    {
        $this->validate([
            'currentPassword' => 'required',
            'password' => 'required|min:8|confirmed',
            'passwordConfirmation' => 'required'
        ]);

        if (!Hash::check($this->currentPassword, $this->user->password)) {
            $this->addError('currentPassword', 'كلمة المرور الحالية غير صحيحة');
            return;
        }

        $this->user->password = Hash::make($this->password);
        $this->user->save();

        $this->currentPassword = '';
        $this->password = '';
        $this->passwordConfirmation = '';

        $this->dispatch('notify', [
            'message' => 'تم تحديث كلمة المرور بنجاح',
            'type' => 'success'
        ]);
    }

    public function render()
    {
        $displayOnlyMode = $this->isDisplayOnlyMode();
        $showWishlist = $this->shouldShowWishlist();

        return view('livewire.account.profile-page', [
            'displayOnlyMode' => $displayOnlyMode,
            'showWishlist' => $showWishlist
        ]);
    }
}
