<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;
use App\Models\Product;
use App\Models\Category;
use App\Traits\CategorySettingsHelper;
use App\Traits\SettingsHelper;

class ProductList extends Component
{
    use WithPagination;
    use CategorySettingsHelper;
    use SettingsHelper;

    // خصائص الفلترة
    public $category = '';
    public $minPrice = null;
    public $maxPrice = null;
    public $metalType = '';
    public $purity = '';
    public $sort = 'newest';

    // خصائص إضافية
    public $search = '';
    public $perPage = 12;

    // خصائص حالة التحميل
    public $isLoading = false;

    // إعدادات الموقع
    public $whatsappPhone;
    public $showWishlist;
    public $showRatings;
    public $showWhatsapp;

    // تحديث عنوان URL عند تغيير الفلاتر
    protected $queryString = [
        'category' => ['except' => ''],
        'minPrice' => ['except' => null],
        'maxPrice' => ['except' => null],
        'metalType' => ['except' => ''],
        'purity' => ['except' => ''],
        'sort' => ['except' => 'newest'],
        'search' => ['except' => ''],
    ];



    // تهيئة المكون
    public function mount()
    {
        $this->loadSiteSettings();
    }

    public function loadSiteSettings()
    {
        // جلب إعدادات الموقع باستخدام SettingsHelper
        $this->whatsappPhone = $this->getWhatsappPhone();
        $this->showWishlist = $this->shouldShowWishlist();
        $this->showRatings = $this->shouldShowRatings();
        $this->showWhatsapp = $this->shouldShowWhatsapp();
    }

    // معالجة تغيير الفلاتر من كومبونانت الفلاتر
    #[On('filtersChanged')]
    public function handleFiltersChanged($filters)
    {
        $this->search = $filters['search'] ?? '';
        $this->category = $filters['category'] ?? '';
        $this->minPrice = $filters['minPrice'];
        $this->maxPrice = $filters['maxPrice'];
        $this->metalType = $filters['metalType'] ?? '';
        $this->purity = $filters['purity'] ?? '';
        $this->sort = $filters['sort'] ?? 'newest';

        // إعادة تعيين الصفحة للصفحة الأولى عند تغيير الفلاتر
        $this->resetPage();
    }

    #[On('filtersUpdated')]
    public function handleFiltersUpdated($filters)
    {
        // يمكن استخدامها لأي معالجة إضافية
        $this->handleFiltersChanged($filters);
    }

    // إعادة تعيين الصفحة عند تغيير الفلاتر
    public function updatedCategory() {
        $this->resetPage();
        $this->dispatch('filter-updated');
    }

    public function updatedMinPrice() {
        $this->resetPage();
        $this->dispatch('filter-updated');
    }

    public function updatedMaxPrice() {
        $this->resetPage();
        $this->dispatch('filter-updated');
    }

    public function updatedMetalType() {
        $this->resetPage();
        $this->dispatch('filter-updated');
    }

    public function updatedPurity() {
        $this->resetPage();
        $this->dispatch('filter-updated');
    }

    public function updatedSearch() {
        $this->resetPage();
        $this->dispatch('filter-updated');
    }

    // إعادة تعيين جميع الفلاتر
    public function resetFilters()
    {
        $this->reset(['category', 'minPrice', 'maxPrice', 'metalType', 'purity', 'sort', 'search']);
        $this->resetPage();
        $this->dispatch('filters-reset');
        $this->dispatch('filter-updated');
    }

    // الحصول على إحصائيات الفلاتر
    public function getFilterStats()
    {
        $activeFilters = 0;

        if ($this->category) $activeFilters++;
        if ($this->minPrice) $activeFilters++;
        if ($this->maxPrice) $activeFilters++;
        if ($this->metalType) $activeFilters++;
        if ($this->purity) $activeFilters++;
        if ($this->search) $activeFilters++;

        return [
            'active_filters' => $activeFilters,
            'has_filters' => $activeFilters > 0
        ];
    }

    public function getActiveFiltersCountProperty()
    {
        return ($this->category ? 1 : 0) +
               ($this->minPrice ? 1 : 0) +
               ($this->maxPrice ? 1 : 0) +
               ($this->metalType ? 1 : 0) +
               ($this->purity ? 1 : 0) +
               ($this->search ? 1 : 0);
    }

    public function render()
    {

        // الحصول على الفئات
        $categories = Category::where('is_active', true)->get();

        // بناء استعلام المنتجات مع العلاقات المطلوبة
        $query = Product::where('is_active', true)
            ->with(['category', 'primaryImage', 'metal', 'metalPurity']);

        // فلترة حسب الفئة
        if ($this->category) {
            $category = Category::where('slug', $this->category)->first();
            if ($category) {
                $categoryIds = [$category->id];

                // تضمين الفئات الفرعية
                if ($category->children->count() > 0) {
                    $childIds = $category->children->pluck('id')->toArray();
                    $categoryIds = array_merge($categoryIds, $childIds);
                }

                $query->whereIn('category_id', $categoryIds);
            }
        }

        // فلترة حسب نطاق السعر
        if ($this->minPrice) {
            $query->where('price', '>=', $this->minPrice);
        }

        if ($this->maxPrice) {
            $query->where('price', '<=', $this->maxPrice);
        }

        // فلترة حسب نوع المعدن
        if ($this->metalType) {
            $query->whereHas('metal', function($q) {
                $q->where('name', $this->metalType);
            });
        }

        // فلترة حسب العيار
        if ($this->purity) {
            $query->whereHas('metalPurity', function($q) {
                $q->where('name', $this->purity);
            });
        }

        // فلترة حسب البحث
        if ($this->search) {
            $query->where(function($q) {
                $q->where('name_ar', 'like', '%' . $this->search . '%')
                  ->orWhere('name_en', 'like', '%' . $this->search . '%')
                  ->orWhere('description_ar', 'like', '%' . $this->search . '%')
                  ->orWhere('description_en', 'like', '%' . $this->search . '%');
            });
        }

        // ترتيب المنتجات
        switch ($this->sort) {
            case 'price_asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price_desc':
                $query->orderBy('price', 'desc');
                break;
            case 'oldest':
                $query->orderBy('created_at', 'asc');
                break;
            case 'newest':
            default:
                $query->orderBy('created_at', 'desc');
                break;
        }

        // الحصول على المنتجات مع pagination
        $products = $query->paginate($this->perPage);

        // تطبيق إعدادات الفئة والموقع على المنتجات
        foreach ($products as $product) {
            // تطبيق إعدادات الفئة
            $categorySettings = $this->getCategorySettings($product);
            $product->show_price = $categorySettings['show_price'];

            // تطبيق إعدادات الموقع
            $product->site_show_wishlist = $this->showWishlist;
            $product->site_show_ratings = $this->showRatings;
            $product->site_show_whatsapp = $this->showWhatsapp;
            $product->whatsapp_phone = $this->whatsappPhone;
        }

        // الحصول على إعدادات الفئة الحالية إذا كانت محددة
        $selectedCategory = null;
        $showPriceFilter = true;

        if ($this->category) {
            $selectedCategory = Category::where('slug', $this->category)->first();
            if ($selectedCategory) {
                $showPriceFilter = $selectedCategory->show_price;
            }
        }

        // إحصائيات الفلاتر
        $filterStats = $this->getFilterStats();

        // الحصول على إعدادات العرض
        $viewSettings = $this->getViewSettings();

        // الحصول على المعادن والعيارات
        $metals = \App\Models\MetalType::where('is_active', true)->get();
        $metalPurities = \App\Models\MetalPurity::where('is_active', true)->get();

        return view('livewire.product-list', array_merge([
            'products' => $products,
            'categories' => $categories,
            'metals' => $metals,
            'metalPurities' => $metalPurities,
            'showPriceFilter' => $showPriceFilter && !$this->isDisplayOnlyMode(),
            'filterStats' => $filterStats,
            'totalProducts' => $products->total(),
            'currentPage' => $products->currentPage(),
            'lastPage' => $products->lastPage(),
            'activeFiltersCount' => $this->activeFiltersCount,
        ], $viewSettings));
    }
}
