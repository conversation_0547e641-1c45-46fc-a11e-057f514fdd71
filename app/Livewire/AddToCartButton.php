<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use App\Traits\DisplayModeHelper;
use App\Services\SuperAdminSettingsService;

class AddToCartButton extends Component
{
    use DisplayModeHelper;

    public $productId;
    public $quantity = 1;

    public function mount($productId)
    {
        $this->productId = $productId;
    }

    public function addToCart()
    {
        // التحقق من وضع العرض فقط
        $superAdminSettings = app(SuperAdminSettingsService::class);
        if ($superAdminSettings->isDisplayOnlyModeEnabled()) {
            $this->dispatch('notify', [
                'message' => 'عذراً، الموقع في وضع العرض فقط حالياً. لا يمكن إضافة المنتجات إلى السلة.',
                'type' => 'warning'
            ]);
            return;
        }

        $product = Product::findOrFail($this->productId);

        $cart = session()->get('cart', []);

        $rowId = uniqid();
        $cart[$rowId] = [
            'id' => $product->id,
            'name' => $product->name_ar,
            'quantity' => $this->quantity,
            'price' => $product->price,
            'weight' => $product->weight,
            'options' => [
                'image' => $product->image,
                'slug' => $product->slug,
                'metal_type' => $product->metal_type,
                'purity' => $product->purity,
            ]
        ];

        // Guardar el carrito en la sesión
        session()->put('cart', $cart);

        // Depuración
        logger('Product added to cart: ' . $this->productId);
        logger('Cart after adding: ' . json_encode($cart));

        // Emitir eventos
        $this->dispatch('cart-updated');
        $this->dispatch('refresh-cart-count');
        $this->dispatch('notify', [
            'message' => 'تمت إضافة المنتج إلى سلة التسوق',
            'type' => 'success'
        ]);
    }

    public function render()
    {
        $displayOnlyMode = $this->isDisplayOnlyMode();

        return view('livewire.add-to-cart-button', [
            'displayOnlyMode' => $displayOnlyMode
        ]);
    }
}
