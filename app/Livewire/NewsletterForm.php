<?php

namespace App\Livewire;

use App\Models\Newsletter;
use Livewire\Component;
use Illuminate\Support\Facades\Validator;

class NewsletterForm extends Component
{
    public $email = '';
    public $name = '';

    public function subscribe()
    {
        // Validar datos
        $validator = Validator::make(
            [
                'email' => $this->email,
                'name' => $this->name,
            ],
            [
                'email' => 'required|email|max:255',
                'name' => 'nullable|string|max:255',
            ],
            [
                'email.required' => __('البريد الإلكتروني مطلوب'),
                'email.email' => __('يرجى إدخال بريد إلكتروني صحيح'),
                'email.max' => __('البريد الإلكتروني طويل جدًا'),
                'name.max' => __('الاسم طويل جدًا'),
            ]
        );

        if ($validator->fails()) {
            $this->dispatch('swal', [
                'icon' => 'error',
                'title' => $validator->errors()->first(),
                'timer' => 3000,
                'position' => 'center',
                'toast' => false,
            ]);
            return;
        }

        // Verificar si el correo ya existe
        $existingSubscriber = Newsletter::where('email', $this->email)->first();

        if ($existingSubscriber) {
            // Si el suscriptor está inactivo, activarlo
            if (!$existingSubscriber->is_active) {
                $existingSubscriber->update([
                    'is_active' => true,
                    'unsubscribed_at' => null,
                    'name' => $this->name ?: $existingSubscriber->name,
                ]);
                $this->dispatch('swal', [
                    'icon' => 'success',
                    'title' => __('تم إعادة الاشتراك في النشرة البريدية بنجاح!'),
                    'timer' => 3000,
                    'position' => 'center',
                    'toast' => false,
                ]);
            } else {
                $this->dispatch('swal', [
                    'icon' => 'info',
                    'title' => __('أنت مشترك بالفعل في النشرة البريدية!'),
                    'timer' => 3000,
                    'position' => 'center',
                    'toast' => false,
                ]);
            }
        } else {
            // Crear nuevo suscriptor
            Newsletter::create([
                'email' => $this->email,
                'name' => $this->name,
                'is_active' => true,
                'subscribed_at' => now(),
            ]);
            $this->dispatch('swal', [
                'icon' => 'success',
                'title' => __('تم الاشتراك في النشرة البريدية بنجاح!'),
                'timer' => 3000,
                'position' => 'center',
                'toast' => false,
            ]);
        }

        // Resetear formulario
        $this->reset(['email', 'name']);
    }

    public function render()
    {
        return view('livewire.newsletter-form');
    }
}
