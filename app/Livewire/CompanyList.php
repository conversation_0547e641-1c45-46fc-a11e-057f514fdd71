<?php

namespace App\Livewire;

use App\Models\Company;
use Livewire\Component;
use Livewire\WithPagination;

/**
 * مكون Livewire لعرض قائمة الشركات
 */
class CompanyList extends Component
{
    use WithPagination;

    /**
     * خاصية البحث
     */
    public string $search = '';

    /**
     * ترتيب النتائج
     */
    public string $sortBy = 'name';
    public string $sortDirection = 'asc';

    /**
     * عدد العناصر في الصفحة
     */
    public int $perPage = 12;

    /**
     * إعادة تعيين الصفحة عند تغيير البحث
     */
    public function updatingSearch(): void
    {
        $this->resetPage();
    }

    /**
     * تغيير ترتيب النتائج
     */
    public function sortBy(string $field): void
    {
        if ($this->sortBy === $field) {
            $this->sortDirection = $this->sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            $this->sortBy = $field;
            $this->sortDirection = 'asc';
        }
    }

    /**
     * الحصول على الشركات مع التصفية والترتيب
     */
    public function getCompaniesProperty()
    {
        return Company::query()
            ->when($this->search, function ($query) {
                $query->where('name', 'like', '%' . $this->search . '%');
            })
            ->withCount(['products as total_products'])
            ->withCount(['products as bars_count' => function ($query) {
                $query->whereHas('productType', function ($q) {
                    $q->where('type', 'سبيكة');
                });
            }])
            ->withCount(['products as coins_count' => function ($query) {
                $query->whereHas('productType', function ($q) {
                    $q->where('type', 'عملة');
                });
            }])
            ->orderBy($this->sortBy, $this->sortDirection)
            ->paginate($this->perPage);
    }

    /**
     * عرض المكون
     */
    public function render()
    {
        return view('livewire.company-list', [
            'companies' => $this->companies,
        ]);
    }
}
