<?php

namespace App\Livewire;

use Livewire\Component;
use App\Traits\DisplayModeHelper;
use Illuminate\Support\Facades\Auth;

class AccountMenu extends Component
{
    use DisplayModeHelper;

    public function render()
    {
        $user = Auth::user();
        $displayOnlyMode = $this->isDisplayOnlyMode();
        $showWishlist = $this->shouldShowWishlist();

        return view('livewire.account-menu', [
            'user' => $user,
            'displayOnlyMode' => $displayOnlyMode,
            'showWishlist' => $showWishlist
        ]);
    }
}
