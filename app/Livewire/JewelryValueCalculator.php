<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\MetalPrice;

class JewelryValueCalculator extends Component
{
    // خصائص المجوهرات
    public $jewelryItems = [];
    public $currentItem = [
        'metal_type' => '',
        'purity' => '',
        'weight' => '',
        'description' => ''
    ];

    // خصائص الحساب
    public $totalValue = 0;
    public $totalGoldWeight = 0;
    public $totalSilverWeight = 0;
    public $latestPrices = [];

    // خصائص الواجهة
    public $showResults = false;

    protected $rules = [
        'currentItem.metal_type' => 'required|in:gold,silver',
        'currentItem.purity' => 'required',
        'currentItem.weight' => 'required|numeric|min:0.01|max:10000',
        'currentItem.description' => 'nullable|string|max:100'
    ];

    protected $messages = [
        'currentItem.metal_type.required' => 'يرجى اختيار نوع المعدن',
        'currentItem.metal_type.in' => 'نوع المعدن غير صحيح',
        'currentItem.purity.required' => 'يرجى اختيار العيار',
        'currentItem.weight.required' => 'يرجى إدخال الوزن',
        'currentItem.weight.numeric' => 'الوزن يجب أن يكون رقماً',
        'currentItem.weight.min' => 'الوزن يجب أن يكون أكبر من 0.01 جرام',
        'currentItem.weight.max' => 'الوزن لا يمكن أن يتجاوز 10000 جرام',
        'currentItem.description.max' => 'الوصف لا يمكن أن يتجاوز 100 حرف'
    ];

    public function mount()
    {
        $this->loadLatestPrices();
        $this->loadSessionData();
    }

    public function updated($propertyName)
    {
        // تحديث العيارات المتاحة عند تغيير نوع المعدن
        if ($propertyName === 'currentItem.metal_type') {
            $this->currentItem['purity'] = '';
            // إرسال حدث لإعادة تهيئة العناصر
            $this->dispatch('metal-type-changed');
        }

        // إعادة حساب النتائج عند تغيير أي بيانات
        if (str_starts_with($propertyName, 'currentItem.')) {
            $this->validateOnly($propertyName);
        }

        $this->calculateTotals();
    }

    public function addJewelryItem()
    {
        $this->validate();

        // التحقق من وجود سعر للعيار المحدد
        $price = $this->getMetalPrice($this->currentItem['metal_type'], $this->currentItem['purity']);
        if (!$price) {
            $this->addError('currentItem.purity', 'لا يمكن العثور على سعر لهذا العيار حالياً');
            return;
        }

        // إضافة معرف فريد للقطعة
        $item = $this->currentItem;
        $item['id'] = uniqid();
        $item['price_per_gram'] = $price->price_per_gram;
        $item['total_value'] = $item['weight'] * $price->price_per_gram;
        $item['created_at'] = now()->format('H:i:s');

        $this->jewelryItems[] = $item;

        // حفظ في الجلسة
        $this->saveToSession();

        // إعادة تعيين النموذج
        $this->resetCurrentItem();

        // إعادة حساب المجاميع
        $this->calculateTotals();

        // إظهار النتائج
        $this->showResults = true;

        // إرسال حدث لإعادة تهيئة العناصر
        $this->dispatch('jewelry-added', ['message' => 'تم إضافة القطعة بنجاح']);

        // إعادة تحميل الأسعار للتأكد من التحديث
        $this->loadLatestPrices();
    }

    public function removeJewelryItem($itemId)
    {
        $this->jewelryItems = array_filter($this->jewelryItems, function($item) use ($itemId) {
            return $item['id'] !== $itemId;
        });

        // إعادة ترقيم المصفوفة
        $this->jewelryItems = array_values($this->jewelryItems);

        // حفظ في الجلسة
        $this->saveToSession();

        // إعادة حساب المجاميع
        $this->calculateTotals();

        // إخفاء النتائج إذا لم تعد هناك قطع
        if (empty($this->jewelryItems)) {
            $this->showResults = false;
        }

        $this->dispatch('jewelry-removed', ['message' => 'تم حذف القطعة']);
    }

    public function clearAllItems()
    {
        $this->jewelryItems = [];
        $this->resetCurrentItem();
        $this->resetCalculations();
        $this->showResults = false;

        // مسح من الجلسة
        session()->forget('jewelry_value_calculator_items');

        $this->dispatch('all-jewelry-cleared', ['message' => 'تم مسح جميع القطع']);
    }

    public function getAvailablePurities()
    {
        if ($this->currentItem['metal_type'] === 'gold') {
            // عيارات الذهب (استبعاد الجنيهات الذهبية)
            $goldPurities = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->whereNotIn('purity', ['جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب'])
                ->distinct()
                ->pluck('purity')
                ->toArray();

            // ترتيب عيارات الذهب (تنازلي)
            $orderedGoldPurities = ['24K', '22K', '21K', '18K', '14K', '12K', '9K'];
            $purities = [];
            foreach ($orderedGoldPurities as $purity) {
                if (in_array($purity, $goldPurities)) {
                    $purities[$purity] = __('metal_prices.purities.' . $purity, [], null, $purity);
                }
            }
            return $purities;
        } elseif ($this->currentItem['metal_type'] === 'silver') {
            // عيارات الفضة
            $silverPurities = MetalPrice::silver()
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->distinct()
                ->pluck('purity')
                ->toArray();

            // ترتيب عيارات الفضة (تنازلي)
            $orderedSilverPurities = ['999', '925', '900', '800', '600'];
            $purities = [];
            foreach ($orderedSilverPurities as $purity) {
                if (in_array($purity, $silverPurities)) {
                    $purities[$purity] = __('metal_prices.purities.' . $purity, [], null, $purity);
                }
            }
            return $purities;
        }

        return [];
    }

    private function loadLatestPrices()
    {
        // جلب الأسعار المفعلة لكل عيار من الذهب
        $goldPricesRaw = MetalPrice::gold()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->get()
            ->keyBy('purity');

        // ترتيب أسعار الذهب منطقي<|im_start|> (من الأعلى للأقل)
        $goldOrder = ['24K', '22K', '21K', '18K', '14K', '12K', '9K'];
        $goldPrices = collect();
        foreach ($goldOrder as $purity) {
            if ($goldPricesRaw->has($purity)) {
                $goldPrices->put($purity, $goldPricesRaw->get($purity));
            }
        }

        // جلب الأسعار المفعلة لكل عيار من الفضة
        $silverPricesRaw = MetalPrice::silver()
            ->where('currency', 'EGP')
            ->where('is_active', true)
            ->get()
            ->keyBy('purity');

        // ترتيب أسعار الفضة منطقي<|im_start|> (من الأعلى للأقل)
        $silverOrder = ['999', '925', '900', '800', '600'];
        $silverPrices = collect();
        foreach ($silverOrder as $purity) {
            if ($silverPricesRaw->has($purity)) {
                $silverPrices->put($purity, $silverPricesRaw->get($purity));
            }
        }

        // تحديد آخر تحديث بطريقة أكثر دقة (بدون cache)
        $lastUpdate = MetalPrice::where('currency', 'EGP')
            ->where('is_active', true)
            ->latest('created_at')
            ->first(['created_at'])?->created_at;

        $this->latestPrices = [
            'gold' => $goldPrices,
            'silver' => $silverPrices,
            'last_update' => $lastUpdate ? $lastUpdate->locale('ar')->diffForHumans() : null,
        ];
    }

    private function getMetalPrice($metalType, $purity)
    {
        return $this->latestPrices[$metalType][$purity] ?? null;
    }

    private function calculateTotals()
    {
        $this->totalValue = 0;
        $this->totalGoldWeight = 0;
        $this->totalSilverWeight = 0;

        foreach ($this->jewelryItems as $item) {
            $this->totalValue += $item['total_value'];

            if ($item['metal_type'] === 'gold') {
                $this->totalGoldWeight += $item['weight'];
            } elseif ($item['metal_type'] === 'silver') {
                $this->totalSilverWeight += $item['weight'];
            }
        }
    }

    private function resetCurrentItem()
    {
        $this->currentItem = [
            'metal_type' => '',
            'purity' => '',
            'weight' => '',
            'description' => ''
        ];
        $this->resetErrorBag();
    }

    private function resetCalculations()
    {
        $this->totalValue = 0;
        $this->totalGoldWeight = 0;
        $this->totalSilverWeight = 0;
    }

    private function saveToSession()
    {
        session(['jewelry_value_calculator_items' => $this->jewelryItems]);
    }

    private function loadSessionData()
    {
        $sessionItems = session('jewelry_value_calculator_items', []);
        if (!empty($sessionItems)) {
            $this->jewelryItems = $sessionItems;
            $this->calculateTotals();
            $this->showResults = true;
        }
    }

    public function render()
    {
        return view('livewire.jewelry-value-calculator', [
            'availablePurities' => $this->getAvailablePurities(),
            'metalTypes' => [
                'gold' => '🥇 ذهب',
                'silver' => '🥈 فضة'
            ]
        ]);
    }
}
