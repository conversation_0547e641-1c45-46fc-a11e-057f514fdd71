<?php

namespace App\Livewire;

use App\Models\Newsletter;
use Livewire\Component;
use Illuminate\Support\Facades\Validator;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Jantinnerezo\LivewireAlert\WithAlert;

class NewsletterSubscription extends Component
{
    use WithAlert;

    public $email = '';
    public $name = '';

    /**
     * اشتراك في النشرة البريدية
     */
    public function subscribe()
    {
        // التحقق من صحة البيانات
        $validator = Validator::make(
            [
                'email' => $this->email,
                'name' => $this->name,
            ],
            [
                'email' => 'required|email|max:255',
                'name' => 'nullable|string|max:255',
            ],
            [
                'email.required' => __('البريد الإلكتروني مطلوب'),
                'email.email' => __('يرجى إدخال بريد إلكتروني صحيح'),
                'email.max' => __('البريد الإلكتروني طويل جدًا'),
                'name.max' => __('الاسم طويل جدًا'),
            ]
        );

        if ($validator->fails()) {
            $this->alert('error', $validator->errors()->first(), [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
            ]);
            return;
        }

        // التحقق من وجود البريد الإلكتروني
        $existingSubscriber = Newsletter::where('email', $this->email)->first();

        if ($existingSubscriber) {
            // إذا كان المشترك غير نشط، قم بتنشيطه
            if (!$existingSubscriber->is_active) {
                $existingSubscriber->update([
                    'is_active' => true,
                    'unsubscribed_at' => null,
                    'name' => $this->name ?: $existingSubscriber->name,
                ]);
                $this->alert('success', __('تم إعادة الاشتراك في النشرة البريدية بنجاح!'), [
                    'position' => 'center',
                    'timer' => 3000,
                    'toast' => false,
                ]);
            } else {
                $this->alert('info', __('أنت مشترك بالفعل في النشرة البريدية!'), [
                    'position' => 'center',
                    'timer' => 3000,
                    'toast' => false,
                ]);
            }
        } else {
            // إنشاء مشترك جديد
            Newsletter::create([
                'email' => $this->email,
                'name' => $this->name,
                'is_active' => true,
                'subscribed_at' => now(),
            ]);
            $this->alert('success', __('تم الاشتراك في النشرة البريدية بنجاح!'), [
                'position' => 'center',
                'timer' => 3000,
                'toast' => false,
            ]);
        }

        // إعادة تعيين النموذج
        $this->reset(['email', 'name']);
    }

    public function render()
    {
        return view('livewire.newsletter-subscription');
    }
}
