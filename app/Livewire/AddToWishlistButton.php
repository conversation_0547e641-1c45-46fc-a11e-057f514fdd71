<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use App\Models\SiteSetting;
use App\Traits\DisplayModeHelper;

class AddToWishlistButton extends Component
{
    use DisplayModeHelper;

    public $productId;

    public function mount($productId)
    {
        $this->productId = $productId;
    }

    public function addToWishlist()
    {
        // التحقق مما إذا كانت المفضلة متاحة
        if (!$this->shouldShowWishlist()) {
            $this->dispatch('notify', [
                'message' => 'عذراً، المفضلة غير متاحة حالياً',
                'type' => 'error'
            ]);
            return;
        }

        // Verificar si el usuario está autenticado
        if (!auth()->check()) {
            // Redirigir al usuario a la página de inicio de sesión
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Verificar si el producto ya está en la lista de deseos
        if (!$user->wishlist()->where('product_id', $this->productId)->exists()) {
            // Añadir el producto a la lista de deseos
            $user->wishlist()->create([
                'product_id' => $this->productId
            ]);

            // Depuración
            logger('Product added to wishlist: ' . $this->productId . ' for user ' . $user->id);

            // Emitir eventos
            $this->dispatch('notify', [
                'message' => 'تمت إضافة المنتج إلى قائمة الرغبات',
                'type' => 'success'
            ]);

            // Emitir evento para actualizar el contador de la lista de deseos
            $this->dispatch('wishlistUpdated');
            $this->dispatch('refresh-wishlist-count');
        } else {
            $this->dispatch('notify', [
                'message' => 'المنتج موجود بالفعل في قائمة الرغبات',
                'type' => 'info'
            ]);
        }
    }

    public function render()
    {
        $showWishlist = $this->shouldShowWishlist();

        return view('livewire.add-to-wishlist-button', [
            'showWishlist' => $showWishlist
        ]);
    }
}
