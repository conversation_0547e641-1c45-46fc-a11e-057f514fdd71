<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use Illuminate\Support\Facades\Schema;

class ProductSearch extends Component
{
    public $query = '';
    public $products = [];

    public function updatedQuery()
    {
        if (strlen($this->query) < 2) {
            $this->products = [];
            return;
        }

        $this->products = Product::where('is_active', true)
            ->where(function ($q) {
                $q->where('name_ar', 'like', '%' . $this->query . '%')
                    ->orWhere('name_en', 'like', '%' . $this->query . '%');

                // البحث في SKU فقط إذا كان العمود موجود
                if (Schema::hasColumn('products', 'sku')) {
                    $q->orWhere('sku', 'like', '%' . $this->query . '%');
                }
            })
            ->with(['category', 'primaryImage'])
            ->take(10)
            ->get();
    }

    public function selectProduct($slug)
    {
        $this->redirect(route('product.show', $slug));
    }

    public function search()
    {
        if (strlen($this->query) >= 2) {
            $this->redirect(route('search', ['query' => $this->query]));
        }
    }

    public function render()
    {
        return view('livewire.product-search');
    }
}
