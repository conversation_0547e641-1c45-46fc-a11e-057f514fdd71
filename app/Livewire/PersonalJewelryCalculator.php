<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\MetalPrice;
use Carbon\Carbon;

class PersonalJewelryCalculator extends Component
{
    // خصائص المجوهرات
    public $jewelryItems = [];
    public $currentItem = [
        'metal_type' => '',
        'purity' => '',
        'weight' => '',
        'description' => ''
    ];

    // خصائص الحساب
    public $totalValue = 0;
    public $totalGoldWeight = 0;
    public $totalSilverWeight = 0;
    public $zakatAmount = 0;
    public $nisabReached = false;
    public $nisabInfo = [];
    public $latestPrices = [];

    // خصائص الواجهة
    public $showResults = false;

    protected $rules = [
        'currentItem.metal_type' => 'required|in:gold,silver',
        'currentItem.purity' => 'required',
        'currentItem.weight' => 'required|numeric|min:0.01|max:10000',
        'currentItem.description' => 'nullable|string|max:100'
    ];

    protected $messages = [
        'currentItem.metal_type.required' => 'يرجى اختيار نوع المعدن',
        'currentItem.metal_type.in' => 'نوع المعدن غير صحيح',
        'currentItem.purity.required' => 'يرجى اختيار العيار',
        'currentItem.weight.required' => 'يرجى إدخال الوزن',
        'currentItem.weight.numeric' => 'الوزن يجب أن يكون رقماً',
        'currentItem.weight.min' => 'الوزن يجب أن يكون أكبر من 0.01 جرام',
        'currentItem.weight.max' => 'الوزن لا يمكن أن يتجاوز 10000 جرام',
        'currentItem.description.max' => 'الوصف لا يمكن أن يتجاوز 100 حرف'
    ];

    public function mount()
    {
        $this->loadLatestPrices();
        $this->calculateNisab();
        $this->loadSessionData();
    }

    public function updated($propertyName)
    {
        // تحديث العيارات المتاحة عند تغيير نوع المعدن
        if ($propertyName === 'currentItem.metal_type') {
            $this->currentItem['purity'] = '';
            // إرسال حدث لإعادة تهيئة العناصر
            $this->dispatch('metal-type-changed');
        }

        // إعادة حساب النتائج عند تغيير أي بيانات
        if (str_starts_with($propertyName, 'currentItem.')) {
            $this->validateOnly($propertyName);
        }

        $this->calculateTotals();
    }

    public function addJewelryItem()
    {
        $this->validate();

        // التحقق من وجود سعر للعيار المحدد
        $price = $this->getMetalPrice($this->currentItem['metal_type'], $this->currentItem['purity']);
        if (!$price) {
            $this->addError('currentItem.purity', 'لا يمكن العثور على سعر لهذا العيار حالياً');
            return;
        }

        // إضافة معرف فريد للقطعة
        $item = $this->currentItem;
        $item['id'] = uniqid();
        $item['price_per_gram'] = $price->price_per_gram;
        $item['total_value'] = $item['weight'] * $price->price_per_gram;
        $item['created_at'] = now()->format('H:i:s');

        $this->jewelryItems[] = $item;

        // حفظ في الجلسة
        $this->saveToSession();

        // إعادة تعيين النموذج
        $this->resetCurrentItem();

        // إعادة حساب المجاميع
        $this->calculateTotals();

        // إظهار النتائج
        $this->showResults = true;

        // إرسال حدث لإعادة تهيئة العناصر
        $this->dispatch('jewelry-added', ['message' => 'تم إضافة القطعة بنجاح']);

        // إعادة تحميل الأسعار للتأكد من التحديث
        $this->loadLatestPrices();
    }

    public function removeJewelryItem($itemId)
    {
        $this->jewelryItems = array_filter($this->jewelryItems, function($item) use ($itemId) {
            return $item['id'] !== $itemId;
        });

        // إعادة ترقيم المصفوفة
        $this->jewelryItems = array_values($this->jewelryItems);

        // حفظ في الجلسة
        $this->saveToSession();

        // إعادة حساب المجاميع
        $this->calculateTotals();

        // إخفاء النتائج إذا لم تعد هناك قطع
        if (empty($this->jewelryItems)) {
            $this->showResults = false;
        }

        $this->dispatch('jewelry-removed', ['message' => 'تم حذف القطعة']);
    }

    public function clearAllItems()
    {
        $this->jewelryItems = [];
        $this->resetCurrentItem();
        $this->resetCalculations();
        $this->showResults = false;

        // مسح من الجلسة
        session()->forget('jewelry_calculator_items');

        $this->dispatch('all-jewelry-cleared', ['message' => 'تم مسح جميع القطع']);
    }

    public function getAvailablePurities()
    {
        if ($this->currentItem['metal_type'] === 'gold') {
            // عيارات الذهب (استبعاد الجنيهات الذهبية)
            $goldPurities = MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->whereNotIn('purity', ['جنيه_ذهب', 'نصف_جنيه_ذهب', 'ربع_جنيه_ذهب'])
                ->distinct()
                ->pluck('purity')
                ->toArray();

            // ترتيب عيارات الذهب (تنازلي)
            $orderedGoldPurities = ['24K', '22K', '21K', '18K', '14K', '12K', '9K'];
            $purities = [];
            foreach ($orderedGoldPurities as $purity) {
                if (in_array($purity, $goldPurities)) {
                    $purities[$purity] = __('metal_prices.purities.' . $purity, [], null, $purity);
                }
            }
            return $purities;
        } elseif ($this->currentItem['metal_type'] === 'silver') {
            // عيارات الفضة
            $silverPurities = MetalPrice::silver()
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->distinct()
                ->pluck('purity')
                ->toArray();

            // ترتيب عيارات الفضة (تنازلي)
            $orderedSilverPurities = ['999', '925', '900', '800', '600'];
            $purities = [];
            foreach ($orderedSilverPurities as $purity) {
                if (in_array($purity, $silverPurities)) {
                    $purities[$purity] = __('metal_prices.purities.' . $purity, [], null, $purity);
                }
            }
            return $purities;
        }

        return [];
    }

    private function loadLatestPrices()
    {
        // جلب الأسعار المفعلة لكل عيار
        $this->latestPrices = [
            'gold' => MetalPrice::gold()
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->get()
                ->keyBy('purity'),
            'silver' => MetalPrice::silver()
                ->where('currency', 'EGP')
                ->where('is_active', true)
                ->get()
                ->keyBy('purity'),
            'last_update' => MetalPrice::where('currency', 'EGP')->max('created_at'),
        ];

        // تحويل آخر تحديث إلى نص
        if ($this->latestPrices['last_update']) {
            $this->latestPrices['last_update'] = Carbon::parse($this->latestPrices['last_update'])
                ->locale(app()->getLocale())
                ->diffForHumans();
        }
    }

    private function calculateNisab()
    {
        // نصاب الذهب: 85 جرام (هو المعتبر شرعياً)
        $goldNisabWeight = 85;
        $goldPrice = $this->latestPrices['gold']['24K'] ?? null;
        $goldNisabValue = $goldPrice ? $goldNisabWeight * $goldPrice->price_per_gram : 0;

        // نصاب الفضة: 595 جرام (للمعلومات فقط)
        $silverNisabWeight = 595;
        $silverPrice = $this->latestPrices['silver']['999'] ?? null;
        $silverNisabValue = $silverPrice ? $silverNisabWeight * $silverPrice->price_per_gram : 0;

        // النصاب المعتبر شرعياً هو نصاب الذهب فقط
        // يتم تحويل جميع المعادن إلى قيمة الذهب عيار 24 ومقارنتها بنصاب الذهب
        $applicableNisab = $goldNisabValue;

        $this->nisabInfo = [
            'gold' => [
                'weight' => $goldNisabWeight,
                'value' => $goldNisabValue,
                'formatted_value' => number_format($goldNisabValue, 2),
                'price_per_gram' => $goldPrice ? $goldPrice->price_per_gram : 0,
            ],
            'silver' => [
                'weight' => $silverNisabWeight,
                'value' => $silverNisabValue,
                'formatted_value' => number_format($silverNisabValue, 2),
                'price_per_gram' => $silverPrice ? $silverPrice->price_per_gram : 0,
            ],
            'applicable' => [
                'value' => $applicableNisab,
                'formatted_value' => number_format($applicableNisab, 2),
                'type' => 'gold', // دائماً نصاب الذهب
                'weight' => $goldNisabWeight,
            ],
        ];
    }

    private function getMetalPrice($metalType, $purity)
    {
        return $this->latestPrices[$metalType][$purity] ?? null;
    }

    private function calculateTotals()
    {
        $this->totalValue = 0;
        $this->totalGoldWeight = 0;
        $this->totalSilverWeight = 0;

        // حساب القيم والأوزان
        foreach ($this->jewelryItems as $item) {
            $this->totalValue += $item['total_value'];

            if ($item['metal_type'] === 'gold') {
                $this->totalGoldWeight += $item['weight'];
            } elseif ($item['metal_type'] === 'silver') {
                $this->totalSilverWeight += $item['weight'];
            }
        }

        // حساب الزكاة بالطريقة الشرعية الصحيحة
        $this->calculateZakatCorrectly();
    }

    /**
     * حساب الزكاة بالطريقة الشرعية الصحيحة
     * تحويل جميع المعادن إلى قيمة الذهب عيار 24 ومقارنة المجموع بنصاب الذهب
     */
    private function calculateZakatCorrectly()
    {
        $goldPrice24K = $this->nisabInfo['gold']['price_per_gram'] ?? 0;
        $goldNisabWeight = $this->nisabInfo['applicable']['weight'] ?? 85;

        if ($goldPrice24K <= 0) {
            $this->nisabReached = false;
            $this->zakatAmount = 0;
            return;
        }

        // تحويل جميع المعادن إلى وزن ذهب عيار 24 مكافئ
        $totalGoldEquivalentWeight = 0;

        foreach ($this->jewelryItems as $item) {
            if ($item['metal_type'] === 'gold') {
                // تحويل الذهب إلى عيار 24
                $goldEquivalentWeight = $this->convertGoldTo24K($item['weight'], $item['purity']);
                $totalGoldEquivalentWeight += $goldEquivalentWeight;
            } elseif ($item['metal_type'] === 'silver') {
                // تحويل الفضة إلى قيمة ذهب عيار 24
                $silverValue = $item['total_value'];
                $goldEquivalentWeight = $silverValue / $goldPrice24K;
                $totalGoldEquivalentWeight += $goldEquivalentWeight;
            }
        }

        // فحص النصاب: هل المجموع يساوي أو يزيد عن 85 جرام ذهب عيار 24؟
        if ($totalGoldEquivalentWeight >= $goldNisabWeight) {
            $this->nisabReached = true;
            // حساب الزكاة على كامل القيمة (2.5%)
            $this->zakatAmount = $this->totalValue * 0.025;
        } else {
            $this->nisabReached = false;
            $this->zakatAmount = 0;
        }

        // حفظ معلومات إضافية للعرض
        $this->nisabInfo['calculation'] = [
            'total_gold_equivalent_weight' => $totalGoldEquivalentWeight,
            'formatted_gold_equivalent' => number_format($totalGoldEquivalentWeight, 3),
            'nisab_weight' => $goldNisabWeight,
            'nisab_reached' => $this->nisabReached,
            'shortage' => $this->nisabReached ? 0 : ($goldNisabWeight - $totalGoldEquivalentWeight),
            'formatted_shortage' => $this->nisabReached ? '0' : number_format($goldNisabWeight - $totalGoldEquivalentWeight, 3),
        ];
    }

    /**
     * تحويل وزن الذهب إلى عيار 24
     */
    private function convertGoldTo24K($weight, $purity)
    {
        // استخراج الرقم من العيار (مثل 21 من 21K)
        $purityNumber = (float) str_replace('K', '', $purity);

        // تحويل إلى عيار 24: (الوزن × العيار الحالي) ÷ 24
        return ($weight * $purityNumber) / 24;
    }

    private function resetCurrentItem()
    {
        $this->currentItem = [
            'metal_type' => '',
            'purity' => '',
            'weight' => '',
            'description' => ''
        ];
        $this->resetErrorBag();
    }

    private function resetCalculations()
    {
        $this->totalValue = 0;
        $this->totalGoldWeight = 0;
        $this->totalSilverWeight = 0;
        $this->zakatAmount = 0;
        $this->nisabReached = false;
    }

    private function saveToSession()
    {
        session(['jewelry_calculator_items' => $this->jewelryItems]);
    }

    private function loadSessionData()
    {
        $sessionItems = session('jewelry_calculator_items', []);
        if (!empty($sessionItems)) {
            $this->jewelryItems = $sessionItems;
            $this->calculateTotals();
            $this->showResults = true;
        }
    }

    public function render()
    {
        return view('livewire.personal-jewelry-calculator', [
            'availablePurities' => $this->getAvailablePurities(),
            'metalTypes' => [
                'gold' => '🥇 ذهب',
                'silver' => '🥈 فضة'
            ]
        ]);
    }
}
