<?php

namespace App\Livewire;

use Livewire\Component;

class WishlistCounter extends Component
{
    public $count = 0;

    protected $listeners = ['wishlistUpdated' => 'updateCount', 'refresh-wishlist-count' => 'updateCount'];

    public function mount()
    {
        $this->updateCount();
    }

    public function updateCount()
    {
        if (auth()->check()) {
            // Contar los elementos de la lista de deseos del usuario
            $this->count = auth()->user()->wishlist()->count();

            // Depuración
            logger('Wishlist count updated for user ' . auth()->id() . ': ' . $this->count);
            logger('Wishlist items: ' . json_encode(auth()->user()->wishlist()->pluck('product_id')->toArray()));
        } else {
            $this->count = 0;
            logger('Wishlist count updated: 0 (user not logged in)');
        }
    }

    public function render()
    {
        return view('livewire.wishlist-counter');
    }
}
