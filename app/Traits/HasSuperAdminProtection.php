<?php

namespace App\Traits;

use App\Services\SuperAdminProtectionService;
use Illuminate\Database\Eloquent\Builder;

trait HasSuperAdminProtection
{
    /**
     * تطبيق فلترة Super Admin على الاستعلامات
     */
    public function applySuperAdminFilter(Builder $query): Builder
    {
        // تحديد نوع النموذج وتطبيق الفلترة المناسبة
        $modelClass = $query->getModel();
        
        if ($modelClass instanceof \App\Models\User) {
            return SuperAdminProtectionService::filterUsers($query);
        }
        
        if ($modelClass instanceof \Spatie\Permission\Models\Role) {
            return SuperAdminProtectionService::filterRoles($query);
        }
        
        if ($modelClass instanceof \Spatie\Permission\Models\Permission) {
            return SuperAdminProtectionService::filterPermissions($query);
        }
        
        return $query;
    }

    /**
     * التحقق من إمكانية الوصول للسجل
     */
    public function canAccessRecord($record): bool
    {
        if ($record instanceof \App\Models\User) {
            return SuperAdminProtectionService::canAccessUser($record);
        }
        
        if ($record instanceof \Spatie\Permission\Models\Role) {
            return SuperAdminProtectionService::canAccessRole($record);
        }
        
        if ($record instanceof \Spatie\Permission\Models\Permission) {
            return SuperAdminProtectionService::canAccessPermission($record);
        }
        
        return true;
    }

    /**
     * التحقق من إمكانية تعديل السجل
     */
    public function canEditRecord($record): bool
    {
        if ($record instanceof \App\Models\User) {
            return SuperAdminProtectionService::canEditUser($record);
        }
        
        if ($record instanceof \Spatie\Permission\Models\Role) {
            return SuperAdminProtectionService::canEditRole($record);
        }
        
        return true;
    }

    /**
     * التحقق من إمكانية حذف السجل
     */
    public function canDeleteRecord($record): bool
    {
        if ($record instanceof \App\Models\User) {
            return SuperAdminProtectionService::canDeleteUser($record);
        }
        
        if ($record instanceof \Spatie\Permission\Models\Role) {
            return SuperAdminProtectionService::canDeleteRole($record);
        }
        
        return true;
    }

    /**
     * فلترة البيانات في الجداول
     */
    protected function getFilteredTableQuery(): Builder
    {
        $query = static::getModel()::query();
        return $this->applySuperAdminFilter($query);
    }

    /**
     * فلترة البيانات في النماذج
     */
    protected function getFilteredFormOptions(string $relationship): array
    {
        if ($relationship === 'roles') {
            return SuperAdminProtectionService::getAvailableRolesForForms();
        }
        
        if ($relationship === 'permissions') {
            return SuperAdminProtectionService::getAvailablePermissionsForForms();
        }
        
        return [];
    }

    /**
     * إخفاء الحقول الحساسة في النماذج
     */
    protected function hideSensitiveFields(): array
    {
        $hiddenFields = [];
        
        // إخفاء حقول Super Admin للمستخدمين العاديين
        if (!SuperAdminProtectionService::isSuperAdmin()) {
            $hiddenFields[] = 'super_admin_settings';
            $hiddenFields[] = 'system_permissions';
            $hiddenFields[] = 'advanced_settings';
        }
        
        return $hiddenFields;
    }

    /**
     * فلترة الإجراءات المتاحة
     */
    protected function getFilteredActions($record = null): array
    {
        $actions = [];
        
        // إجراء العرض
        if (!$record || $this->canAccessRecord($record)) {
            $actions[] = \Filament\Tables\Actions\ViewAction::make()
                ->visible(fn ($record) => $this->canAccessRecord($record));
        }
        
        // إجراء التعديل
        if (!$record || $this->canEditRecord($record)) {
            $actions[] = \Filament\Tables\Actions\EditAction::make()
                ->visible(fn ($record) => $this->canEditRecord($record));
        }
        
        // إجراء الحذف
        if (!$record || $this->canDeleteRecord($record)) {
            $actions[] = \Filament\Tables\Actions\DeleteAction::make()
                ->visible(fn ($record) => $this->canDeleteRecord($record));
        }
        
        return $actions;
    }

    /**
     * فلترة الإجراءات المجمعة
     */
    protected function getFilteredBulkActions(): array
    {
        $bulkActions = [];
        
        // إجراء الحذف المجمع (فقط للسجلات المسموحة)
        $bulkActions[] = \Filament\Tables\Actions\DeleteBulkAction::make()
            ->action(function ($records) {
                foreach ($records as $record) {
                    if ($this->canDeleteRecord($record)) {
                        $record->delete();
                    }
                }
            });
        
        return $bulkActions;
    }

    /**
     * تخصيص رسائل الخطأ
     */
    protected function getSuperAdminProtectionMessages(): array
    {
        return [
            'access_denied' => 'ليس لديك صلاحية للوصول لهذا المورد.',
            'edit_denied' => 'ليس لديك صلاحية لتعديل هذا السجل.',
            'delete_denied' => 'ليس لديك صلاحية لحذف هذا السجل.',
            'super_admin_protected' => 'هذا المورد محمي ولا يمكن الوصول إليه.',
        ];
    }

    /**
     * معالجة الاستثناءات المتعلقة بحماية Super Admin
     */
    protected function handleSuperAdminProtectionException(\Exception $e): void
    {
        $messages = $this->getSuperAdminProtectionMessages();
        
        // يمكن إضافة منطق معالجة الأخطاء هنا
        throw new \Illuminate\Auth\Access\AuthorizationException(
            $messages['access_denied']
        );
    }

    /**
     * فلترة البيانات في الودجات
     */
    protected function getFilteredWidgetData(): array
    {
        return [
            'users_count' => SuperAdminProtectionService::getFilteredUserCount(),
            'roles_count' => SuperAdminProtectionService::getFilteredRoleCount(),
            'permissions_count' => SuperAdminProtectionService::getFilteredPermissionCount(),
        ];
    }

    /**
     * التحقق من إمكانية عرض الحقل
     */
    protected function canShowField(string $fieldName, $record = null): bool
    {
        // حقول محظورة للمستخدمين العاديين
        $restrictedFields = [
            'super_admin_role',
            'system_permissions',
            'advanced_settings',
            'super_admin_settings',
        ];
        
        if (!SuperAdminProtectionService::isSuperAdmin() && in_array($fieldName, $restrictedFields)) {
            return false;
        }
        
        return true;
    }

    /**
     * فلترة الأعمدة في الجداول
     */
    protected function getFilteredTableColumns(): array
    {
        $columns = [];
        
        // إضافة الأعمدة المسموحة فقط
        $allowedColumns = $this->getAllowedColumns();
        
        foreach ($allowedColumns as $column) {
            if ($this->canShowField($column)) {
                $columns[] = $column;
            }
        }
        
        return $columns;
    }

    /**
     * الحصول على الأعمدة المسموحة (يجب تنفيذها في كل مورد)
     */
    protected function getAllowedColumns(): array
    {
        // يجب تنفيذ هذه الدالة في كل مورد
        return [];
    }
}
