<?php

namespace App\Traits;

use App\Services\FilamentPermissionService;
use Illuminate\Support\Facades\Auth;

trait HasPermissionFiltering
{
    /**
     * تحديد ما إذا كان يجب إظهار المورد في التنقل
     */
    public static function shouldRegisterNavigation(): bool
    {
        // التحقق من الصلاحية الأساسية للمورد
        return FilamentPermissionService::canAccessResource(static::class);
    }

    /**
     * تحديد ما إذا كان يمكن الوصول للمورد
     */
    public static function canAccess(): bool
    {
        return FilamentPermissionService::canAccessResource(static::class);
    }

    /**
     * تحديد ما إذا كان يمكن عرض أي سجل
     */
    public static function canViewAny(): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم المورد
        $resourceName = FilamentPermissionService::extractResourceName(static::class);

        // التحقق من صلاحية view_any
        return $currentUser->can("view_any_{$resourceName}");
    }

    /**
     * تحديد ما إذا كان يمكن إنشاء سجل جديد
     */
    public static function canCreate(): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم المورد
        $resourceName = FilamentPermissionService::extractResourceName(static::class);

        // التحقق من صلاحية create
        return $currentUser->can("create_{$resourceName}");
    }

    /**
     * تحديد ما إذا كان يمكن تعديل سجل
     */
    public static function canEdit($record = null): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم المورد
        $resourceName = FilamentPermissionService::extractResourceName(static::class);

        // التحقق من صلاحية update
        return $currentUser->can("update_{$resourceName}");
    }

    /**
     * تحديد ما إذا كان يمكن حذف سجل
     */
    public static function canDelete($record = null): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم المورد
        $resourceName = FilamentPermissionService::extractResourceName(static::class);

        // التحقق من صلاحية delete
        return $currentUser->can("delete_{$resourceName}");
    }

    /**
     * تحديد ما إذا كان يمكن عرض سجل
     */
    public static function canView($record = null): bool
    {
        $currentUser = Auth::user();

        if (!$currentUser) {
            return false;
        }

        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }

        // استخراج اسم المورد
        $resourceName = FilamentPermissionService::extractResourceName(static::class);

        // التحقق من صلاحية view_any (نفس منطق العرض)
        return $currentUser->can("view_any_{$resourceName}");
    }

    /**
     * فلترة الإجراءات حسب الصلاحيات
     */
    public static function getFilteredActions(): array
    {
        $actions = [];

        // إضافة إجراء العرض إذا كان مسموحاً
        if (static::canViewAny()) {
            $actions[] = \Filament\Tables\Actions\ViewAction::make();
        }

        // إضافة إجراء التعديل إذا كان مسموحاً
        if (static::canEdit(null)) {
            $actions[] = \Filament\Tables\Actions\EditAction::make();
        }

        // إضافة إجراء الحذف إذا كان مسموحاً
        if (static::canDelete(null)) {
            $actions[] = \Filament\Tables\Actions\DeleteAction::make();
        }

        return $actions;
    }

    /**
     * فلترة الإجراءات المجمعة حسب الصلاحيات
     */
    public static function getFilteredBulkActions(): array
    {
        $bulkActions = [];

        // إضافة إجراء الحذف المجمع إذا كان مسموحاً
        if (static::canDelete(null)) {
            $bulkActions[] = \Filament\Tables\Actions\DeleteBulkAction::make();
        }

        return $bulkActions;
    }

    /**
     * فلترة أزرار الرأس حسب الصلاحيات
     */
    public static function getFilteredHeaderActions(): array
    {
        $headerActions = [];

        // إضافة زر الإنشاء إذا كان مسموحاً
        if (static::canCreate()) {
            $headerActions[] = \Filament\Actions\CreateAction::make();
        }

        return $headerActions;
    }
}
