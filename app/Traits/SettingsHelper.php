<?php

namespace App\Traits;

use App\Services\SuperAdminSettingsService;
use App\Models\SiteSetting;

trait SettingsHelper
{
    /**
     * خدمة إعدادات السوبر أدمن المخزنة مؤقتاً
     */
    protected $superAdminSettingsService;

    /**
     * إعدادات الموقع المخزنة مؤقتاً
     */
    protected $siteSettings;

    /**
     * الحصول على خدمة إعدادات السوبر أدمن
     *
     * @return SuperAdminSettingsService
     */
    protected function getSuperAdminSettings()
    {
        if (!$this->superAdminSettingsService) {
            $this->superAdminSettingsService = app(SuperAdminSettingsService::class);
        }

        return $this->superAdminSettingsService;
    }

    /**
     * الحصول على إعدادات الموقع
     *
     * @return \App\Models\SiteSetting|null
     */
    protected function getSiteSettings()
    {
        if (!$this->siteSettings) {
            $this->siteSettings = SiteSetting::first();
        }

        return $this->siteSettings;
    }

    /**
     * التحقق مما إذا كان الموقع في وضع العرض فقط
     *
     * @return bool
     */
    public function isDisplayOnlyMode()
    {
        $superAdminSettings = $this->getSuperAdminSettings();
        $siteSettings = $this->getSiteSettings();

        // إذا كان أي من الجدولين في وضع العرض فقط، فالموقع في وضع العرض فقط
        $superAdminDisplayOnly = $superAdminSettings->isDisplayOnlyModeEnabled();
        $siteDisplayOnly = $siteSettings && $siteSettings->display_only_mode;

        return $superAdminDisplayOnly || $siteDisplayOnly;
    }

    /**
     * التحقق مما إذا كان يجب عرض تقييمات النجوم
     *
     * @return bool
     */
    public function shouldShowRatings()
    {
        $superAdminSettings = $this->getSuperAdminSettings();
        $siteSettings = $this->getSiteSettings();

        // الشروط من super_admin_settings
        $superAdminAllows = $superAdminSettings->showRatings();

        // الشروط من site_settings
        $siteAllows = $siteSettings && $siteSettings->show_features;

        // يجب أن تكون الشروط محققة في كلا الجدولين
        return $superAdminAllows && $siteAllows;
    }

    /**
     * التحقق مما إذا كان يجب عرض المفضلة
     *
     * @return bool
     */
    public function shouldShowWishlist()
    {
        $superAdminSettings = $this->getSuperAdminSettings();
        $siteSettings = $this->getSiteSettings();

        // الشروط من super_admin_settings
        $superAdminAllows = $superAdminSettings->showWishlist();

        // الشروط من site_settings
        $siteAllows = $siteSettings && $siteSettings->show_features;

        // يجب أن تكون الشروط محققة في كلا الجدولين
        return $superAdminAllows && $siteAllows;
    }

    /**
     * التحقق من تفعيل الشراء كزائر
     *
     * @return bool
     */
    public function isGuestCheckoutEnabled()
    {
        return $this->getSuperAdminSettings()->isGuestCheckoutEnabled();
    }

    /**
     * التحقق من تفعيل الاستلام من المتجر
     *
     * @return bool
     */
    public function isLocalPickupEnabled()
    {
        return $this->getSuperAdminSettings()->isLocalPickupEnabled();
    }

    /**
     * الحصول على رقم الهاتف للتواصل
     *
     * @return string
     */
    public function getContactPhone()
    {
        $siteSettings = $this->getSiteSettings();
        return $siteSettings ? $siteSettings->contact_phone : '';
    }

    /**
     * الحصول على رقم الواتساب (منسق)
     *
     * @return string
     */
    public function getWhatsappPhone()
    {
        $siteSettings = $this->getSiteSettings();

        // أولوية للـ whatsapp_number إذا كان موجود
        if ($siteSettings && $siteSettings->whatsapp_number) {
            return preg_replace('/[^0-9]/', '', $siteSettings->whatsapp_number);
        }

        // إذا لم يكن موجود، استخدم contact_phone
        $contactPhone = $this->getContactPhone();
        return $contactPhone ? preg_replace('/[^0-9]/', '', $contactPhone) : '';
    }

    /**
     * التحقق من تفعيل WhatsApp
     *
     * @return bool
     */
    public function shouldShowWhatsapp()
    {
        $superAdminSettings = $this->getSuperAdminSettings();
        $siteSettings = $this->getSiteSettings();

        // الشروط من super_admin_settings
        $superAdminAllows = !$superAdminSettings->isDisplayOnlyModeEnabled() &&
                           $superAdminSettings->isShareOnWhatsappEnabled();

        // الشروط من site_settings
        $siteAllows = $siteSettings &&
                     !$siteSettings->display_only_mode &&
                     $siteSettings->show_features;

        // يجب أن تكون الشروط محققة في كلا الجدولين ويوجد رقم WhatsApp
        return $superAdminAllows && $siteAllows && !empty($this->getWhatsappPhone());
    }

    /**
     * الحصول على جميع الإعدادات المطلوبة للعرض
     *
     * @return array
     */
    public function getViewSettings()
    {
        return [
            'displayOnlyMode' => $this->isDisplayOnlyMode(),
            'showRatings' => $this->shouldShowRatings(),
            'showWishlist' => $this->shouldShowWishlist(),
            'showWhatsapp' => $this->shouldShowWhatsapp(),
            'contactPhone' => $this->getContactPhone(),
            'whatsappPhone' => $this->getWhatsappPhone(),
        ];
    }
}
