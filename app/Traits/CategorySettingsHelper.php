<?php

namespace App\Traits;

use App\Models\Product;
use App\Models\Category;

trait CategorySettingsHelper
{
    /**
     * Get product settings (now using product's own show_price instead of category)
     *
     * @param Product $product
     * @return array
     */
    public function getCategorySettings(Product $product)
    {
        // الآن نستخدم show_price من المنتج نفسه بدلاً من الفئة
        return [
            'show_price' => $product->show_price ?? true
        ];
    }

    /**
     * Apply product settings to a collection of products
     * (تم تحديث الاسم ليعكس أن الإعدادات الآن من المنتج وليس الفئة)
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function applyCategorySettings($products)
    {
        foreach ($products as $product) {
            // لا نحتاج لتطبيق أي إعدادات إضافية لأن show_price موجود بالفعل في المنتج
            // نحتفظ بالدالة للتوافق مع الكود الموجود
        }

        return $products;
    }

    /**
     * Get product settings directly (new method)
     *
     * @param Product $product
     * @return array
     */
    public function getProductSettings(Product $product)
    {
        return [
            'show_price' => $product->show_price ?? true
        ];
    }

    /**
     * Apply product settings to a collection of products (new method)
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function applyProductSettings($products)
    {
        // المنتجات تحتوي بالفعل على show_price، لا نحتاج لتطبيق شيء إضافي
        return $products;
    }
}
