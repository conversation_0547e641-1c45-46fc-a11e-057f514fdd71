<?php

namespace App\Traits;

use App\Services\FilamentPermissionService;
use Illuminate\Support\Facades\Auth;

trait HasWidgetPermissionFiltering
{
    /**
     * تحديد ما إذا كان يمكن عرض الودجة
     */
    public static function canView(): bool
    {
        return FilamentPermissionService::canAccessWidget(static::class);
    }

    /**
     * التحقق من صلاحية الوصول للودجة
     */
    protected function canViewWidget(): bool
    {
        $currentUser = Auth::user();
        
        if (!$currentUser) {
            return false;
        }
        
        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }
        
        // استخراج اسم الودجة
        $widgetName = FilamentPermissionService::extractWidgetName(static::class);
        
        // التحقق من صلاحية الودجة
        $permission = "widget_{$widgetName}";
        
        return $currentUser->can($permission);
    }

    /**
     * فلترة البيانات حسب الصلاحيات
     */
    protected function getFilteredData(): array
    {
        // يمكن تخصيص هذا حسب نوع الودجة
        return [];
    }
}
