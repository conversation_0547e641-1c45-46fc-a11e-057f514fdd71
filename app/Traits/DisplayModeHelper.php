<?php

namespace App\Traits;

use App\Services\SuperAdminSettingsService;

trait DisplayModeHelper
{
    /**
     * الحصول على خدمة إعدادات السوبر أدمن
     *
     * @return SuperAdminSettingsService
     */
    protected function getSuperAdminSettings()
    {
        return app(SuperAdminSettingsService::class);
    }

    /**
     * التحقق مما إذا كان الموقع في وضع العرض فقط
     *
     * @return bool
     */
    public function isDisplayOnlyMode()
    {
        return $this->getSuperAdminSettings()->isDisplayOnlyModeEnabled();
    }

    /**
     * التحقق مما إذا كان يجب عرض تقييمات النجوم
     *
     * @return bool
     */
    public function shouldShowRatings()
    {
        return $this->getSuperAdminSettings()->showRatings();
    }

    /**
     * التحقق مما إذا كان يجب عرض المفضلة
     *
     * @return bool
     */
    public function shouldShowWishlist()
    {
        return $this->getSuperAdminSettings()->showWishlist();
    }

    /**
     * تطبيق وضع العرض فقط على المنتجات
     * ملاحظة: تم إزالة allow_purchase من الفئات، الآن يتم التحكم في الشراء من خلال إعدادات SuperAdmin فقط
     *
     * @param \Illuminate\Database\Eloquent\Collection $products
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function applyDisplayOnlyMode($products)
    {
        // لا حاجة لتطبيق أي تغييرات على المنتجات
        // التحكم في عرض أزرار الشراء يتم من خلال feature-check component
        return $products;
    }
}
