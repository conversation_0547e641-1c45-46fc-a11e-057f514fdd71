<?php

namespace App\Traits;

use App\Services\FilamentPermissionService;
use Illuminate\Support\Facades\Auth;

trait HasPagePermissionFiltering
{
    /**
     * تحديد ما إذا كان يجب إظهار الصفحة في التنقل
     */
    public static function shouldRegisterNavigation(): bool
    {
        return FilamentPermissionService::canAccessPage(static::class);
    }

    /**
     * تحديد ما إذا كان يمكن الوصول للصفحة
     */
    public static function canAccess(): bool
    {
        return FilamentPermissionService::canAccessPage(static::class);
    }

    /**
     * التحقق من صلاحية الوصول للصفحة
     */
    public function canView(): bool
    {
        $currentUser = Auth::user();
        
        if (!$currentUser) {
            return false;
        }
        
        // Super Admin يمكنه الوصول لكل شيء
        if ($currentUser->hasRole('super_admin')) {
            return true;
        }
        
        // استخراج اسم الصفحة
        $pageName = FilamentPermissionService::extractPageName(static::class);
        
        // التحقق من صلاحية الصفحة
        $permission = "page_{$pageName}";
        
        return $currentUser->can($permission);
    }

    /**
     * فلترة الإجراءات حسب الصلاحيات
     */
    public static function getFilteredHeaderActions(): array
    {
        // يمكن تخصيص هذا حسب نوع الصفحة
        return [];
    }
}
