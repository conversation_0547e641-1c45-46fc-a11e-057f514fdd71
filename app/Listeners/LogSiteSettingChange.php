<?php

namespace App\Listeners;

use App\Events\SiteSettingUpdated;
use App\Models\SettingChange;
use Illuminate\Support\Facades\Cache;

class LogSiteSettingChange
{
    /**
     * Manejar el evento.
     *
     * @param  \App\Events\SiteSettingUpdated  $event
     * @return void
     */
    public function handle(SiteSettingUpdated $event)
    {
        $siteSetting = $event->siteSetting;
        
        // Obtener los cambios
        $changes = $siteSetting->getChanges();
        
        // Eliminar campos que no queremos registrar
        unset($changes['updated_at']);
        
        // Registrar cada cambio
        foreach ($changes as $key => $newValue) {
            // Obtener el valor anterior
            $oldValue = $siteSetting->getOriginal($key);
            
            // Registrar el cambio
            SettingChange::log($key, $oldValue, $newValue);
        }
        
        // Limpiar la caché
        Cache::forget('site_settings');
    }
}
