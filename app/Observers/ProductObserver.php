<?php

namespace App\Observers;

use App\Models\Product;

use Illuminate\Support\Facades\Storage;

class ProductObserver
{
    /**
     * Handle the Product "creating" event.
     */
    public function creating(Product $product): void
    {
        // التحقق من عدم تكرار الأسماء
        $this->validateUniqueNames($product);

        // إنشاء slug تلقائياً إذا لم يكن موجوداً
        if (empty($product->slug) && !empty($product->name_ar)) {
            $product->slug = Product::generateUniqueSlug($product->name_ar);
        }
    }

    /**
     * Handle the Product "updating" event.
     */
    public function updating(Product $product): void
    {
        // التحقق من عدم تكرار الأسماء (مع استثناء المنتج الحالي)
        $this->validateUniqueNames($product, true);

        // تحديث slug تلقائياً عند تغيير الاسم العربي
        if ($product->isDirty('name_ar') && !empty($product->name_ar)) {
            $product->slug = Product::generateUniqueSlug($product->name_ar, $product->id);
        }
    }

    /**
     * التحقق من عدم تكرار أسماء المنتجات
     */
    private function validateUniqueNames(Product $product, bool $isUpdating = false): void
    {
        $excludeId = $isUpdating ? $product->id : null;

        // التحقق من الاسم العربي
        if (!empty($product->name_ar) && Product::nameArExists($product->name_ar, $excludeId)) {
            throw new \InvalidArgumentException('الاسم العربي "' . $product->name_ar . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
        }

        // التحقق من الاسم الإنجليزي
        if (!empty($product->name_en) && Product::nameEnExists($product->name_en, $excludeId)) {
            throw new \InvalidArgumentException('الاسم الإنجليزي "' . $product->name_en . '" مستخدم مسبقاً، يرجى اختيار اسم آخر');
        }
    }



    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Product $product): void
    {
        // حذف جميع صور المنتج من قاعدة البيانات
        $images = $product->images;

        // حذف الملفات من التخزين
        foreach ($images as $image) {
            if ($image->image_path) {
                Storage::disk('public')->delete($image->image_path);
            }
        }

        // حذف السجلات من قاعدة البيانات
        $product->images()->delete();
    }
}
