<?php

namespace App\Events;

use App\Models\SiteSetting;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SiteSettingUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * La configuración que se actualizó.
     *
     * @var \App\Models\SiteSetting
     */
    public $siteSetting;

    /**
     * Crear una nueva instancia del evento.
     *
     * @param  \App\Models\SiteSetting  $siteSetting
     * @return void
     */
    public function __construct(SiteSetting $siteSetting)
    {
        $this->siteSetting = $siteSetting;
    }
}
