<?php

namespace App\Events;

use App\Models\SuperAdminSetting;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;

class SuperAdminSettingUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public SuperAdminSetting $superAdminSetting;

    /**
     * Create a new event instance.
     */
    public function __construct(SuperAdminSetting $superAdminSetting)
    {
        $this->superAdminSetting = $superAdminSetting;

        // مسح التخزين المؤقت عند تحديث الإعدادات
        Cache::forget('super_admin_settings');
    }
}
